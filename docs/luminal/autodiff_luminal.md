# Automatic Differentiation in Luminal

This document provides a detailed exploration of Luminal's automatic differentiation system and how it enables neural network training. It focuses on the internal implementation mechanisms that power gradient-based optimization.

## 1. Automatic Differentiation Overview

Automatic differentiation (autodiff) is the backbone of neural network training. Luminal implements reverse-mode autodiff, which efficiently computes gradients for functions with many parameters and few outputs - the exact scenario encountered in deep learning.

### 1.1. Computational Graph Approach

Luminal's autodiff system is tightly integrated with its computational graph representation:

1. The **forward pass** constructs the computational graph and computes the output values
2. The **backward pass** traverses the graph in reverse to compute gradients
3. **Gradients** are accumulated and used to update parameters

The key insight of reverse-mode autodiff is that it computes all partial derivatives with respect to a single output value in one backward pass, making it highly efficient for neural networks.

### 1.2. Core Data Structures

The core data structure of Luminal's autodiff system is the `Autograd` struct, which is implemented as a compiler:

```rust
#[derive(Clone, Debug)]
pub struct Autograd(Vec<NodeIndex>, NodeIndex);

impl Autograd {
    pub fn new<W: ToIds>(params: W, loss: GraphTensor) -> Self {
        Self(params.to_ids(), loss.id)
    }
}

impl Compiler for Autograd {
    type Output = Vec<(NodeIndex, ShapeTracker)>;
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, _: T) -> Vec<(NodeIndex, ShapeTracker)> {
        // Implementation of backward pass...
    }
}
```

This implementation takes a set of parameter node IDs and a loss node ID, and when compiled, it produces gradients for each parameter with respect to the loss.

### 1.3. Graph.compile Integration

When you invoke `cx.compile(Autograd::new(params, loss), remap)`, Luminal calls the graph’s `compile` method, which runs your `Autograd::compile`, then topo-sorts and resets the graph:

```rust
impl Graph {
    pub fn compile<T: ToIdsMut, C: Compiler>(
        &mut self, compiler: C, remap: T
    ) -> C::Output {
        let output = compiler.compile(self, remap);
        self.toposort();
        self.reset();
        output
    }
}
```

- `compiler.compile` builds the backward pass and accumulates gradients via `add_grad`.
- `toposort` orders nodes for correct execution order.
- `reset` clears temporary compile state from the graph.

This integration ensures that gradient computation leverages the same graph structure as forward operations.

## 2. Forward Pass and Graph Construction

During the forward pass, Luminal builds the computational graph representation of the model:

```rust
// Example of a forward pass in a training loop
let output = model.forward(input);
let loss = loss_fn(output, target);

// Mark loss for retrieval
let mut loss_value = loss.retrieve();

// Compile gradients using Autograd
let gradients = cx.compile(Autograd::new(params, loss), ());
```

Operations are added to the graph as nodes, and tensor connections are represented as edges between nodes.

## 3. Backward Pass Implementation

The backward pass is implemented in the `compile` method of the `Autograd` struct. It traverses the graph in reverse topological order and computes gradients for all parameters.

### 3.1. Backward Graph Construction

The backward pass begins by determining which nodes in the graph are relevant for gradient computation:

```rust
// From the Autograd::compile method
let Autograd(params, loss) = self;
// Build up valid set for nodes we want to pay attention to
let forward_set = build_dfs_set(&mut params.clone(), graph, Direction::Outgoing);
let backward_set = build_dfs_set(&mut vec![*loss], graph, Direction::Incoming);
let valid_set: FxHashSet<_> = forward_set.intersection(&backward_set).copied().collect();
```

This approach performs a depth-first search to find all nodes that are in both:
1. The forward path from parameters to outputs (using `Direction::Outgoing`)
2. The backward path from the loss to inputs (using `Direction::Incoming`)

The `valid_set` represents nodes that require gradient computation, as they both influence the loss and are influenced by the parameters.

### 3.2. Gradient Computation for Operations

Luminal implements specific gradient calculations for each operation type in a match-based approach:

```rust
// Example gradient computation for Add operation
if op == TypeId::of::<Add>() {
    // f(a, b) = a + b
    // df/da = 1
    if valid_set.contains(&inps[0].id) {
        add_grad(prev_grad, inps[0], graph, &mut grads);
    }
    // df/db = 1
    if valid_set.contains(&inps[1].id) {
        add_grad(prev_grad, inps[1], graph, &mut grads);
    }
} else if op == TypeId::of::<Mul>() {
    // f(a, b) = a * b
    // df/da = b
    if valid_set.contains(&inps[0].id) {
        add_grad(inps[1] * prev_grad, inps[0], graph, &mut grads);
    }
    // df/db = a
    if valid_set.contains(&inps[1].id) {
        add_grad(inps[0] * prev_grad, inps[1], graph, &mut grads);
    }
}
```

Each operation has its specific gradient rule, and the implementation traverses the graph in reverse topological order, applying these rules to compute gradients.

### 3.3. Gradient Accumulation

Gradients are accumulated using a `FxHashMap` to handle nodes that receive multiple gradients:

```rust
fn add_grad(
    mut grad: GraphTensor,
    fwd: GraphTensor,
    graph: &mut Graph,
    grad_map: &mut FxHashMap<NodeIndex, (NodeIndex, ShapeTracker)>,
) {
    // Reshape gradient to match the shape of the input
    // Undo permutes
    let mut new_indexes = ArrayVec::new();
    new_indexes.resize(fwd.shape.len(), 0);
    for i in 0..fwd.shape.len() {
        new_indexes[fwd.shape.indexes[i]] = grad.shape.indexes[i];
    }
    grad.shape.indexes = new_indexes;

    // Undo expands (sum reduce)
    for i in fwd.shape.indexes.into_iter().rev() {
        if fwd.shape.fake[i] {
            grad.id = graph
                .add_op(SumReduce(i))
                .input(grad.id, 0, grad.shape)
                .finish();
            grad.shape.remove_dim(i);
            grad.shape = grad.shape.contiguous();
        }
    }

    // Check reshapes and ensure contiguity
    // ...

    // Accumulate gradients
    if let Some((existing_grad_node, existing_grad_shape)) = grad_map.get(&fwd.id).copied() {
        let grad = GraphTensor::from_id(grad.id, grad.shape, graph);
        let existing_grad = GraphTensor::from_id(existing_grad_node, existing_grad_shape, graph);
        let new_grad = grad + existing_grad;
        grad_map.insert(fwd.id, (new_grad.id, new_grad.shape));
    } else {
        grad_map.insert(fwd.id, (grad.id, grad.shape));
    }
}
```

This accumulation is crucial for parameters that are used in multiple places in the network, as it ensures that all gradient contributions are properly summed.

## 4. Complex Operation Gradients

Luminal implements gradients for a comprehensive set of operations:

### 4.1. Reduction Operations

```rust
// SumReduce gradient implementation
else if let Some(op) = unsafe { graph_ref.as_ref().unwrap() }
    .try_get_op::<SumReduce>(fwd_node)
    .cloned()
{
    // f(x) = sum_reduce(x)
    // f'(x) = 1
    if valid_set.contains(&inps[0].id) {
        prev_grad
            .shape
            .expand(op.0, inps[0].shape.dims[inps[0].shape.indexes[op.0]]);
        add_grad(prev_grad, inps[0], graph, &mut grads);
    }
} 
// MaxReduce gradient implementation
else if let Some(op) = unsafe { graph_ref.as_ref().unwrap() }
    .try_get_op::<MaxReduce>(fwd_node)
    .cloned()
{
    // f(x) = max_reduce(x)
    // f'(x) = x == max_reduce(x)
    if valid_set.contains(&inps[0].id) {
        // fwd_node is already max_reduce(x)
        prev_grad
            .shape
            .expand(op.0, inps[0].shape.dims[inps[0].shape.indexes[op.0]]);
        let reduced = GraphTensor::from_id(fwd_node, prev_grad.shape, graph_ref);
        let grad = inps[0].equals(reduced) * prev_grad;
        add_grad(grad, inps[0], graph, &mut grads);
    }
}
```

The implementation handles reduction operations by properly expanding gradients along the reduced dimensions before propagating them.

### 4.2. Activation Function Gradients

```rust
// Unary operation gradients
else {
    if op == TypeId::of::<Log2>() {
        // f(x) = log2(x)
        // f'(x) = 1 / (x * ln(2))
        1.0 / (inps[0] * 2_f32.ln())
    } else if op == TypeId::of::<Exp2>() {
        // f(x) = exp2(x)
        // f'(x) = exp2(x) * ln(2)
        inps[0].exp2() * 2_f32.ln()
    } else if op == TypeId::of::<Sin>() {
        // f(x) = sin(x)
        // f'(x) = cos(x)
        inps[0].cos()
    } else if op == TypeId::of::<Sqrt>() {
        // f(x) = sqrt(x)
        // f'(x) = 1 / (2 * sqrt(x))
        1.0 / (2.0 * inps[0].sqrt())
    } else if op == TypeId::of::<Recip>() {
        // f(x) = 1 / x
        // f'(x) = -1 / x**2
        -1.0 / (inps[0] * inps[0])
    } else {
        unreachable!()
    }
    // multiply by upstream gradient
    add_grad(local_grad * prev_grad, inps[0], graph, &mut grads);
}

### 4.3. Reshape & Contiguity Gradients

```rust
// Identity gradient for contiguous reshape
else if op == TypeId::of::<Contiguous>() {
    // f(x) = contiguous(x)
    // f'(x) = 1
    add_grad(prev_grad, inps[0], graph, &mut grads);
}
```

## 5. Neural Network Training

With the autodiff system in place, Luminal enables neural network training through optimizer updates.

### 5.1. Training Loop Structure

A typical training loop in Luminal looks like this:

```rust
// Initialize model and dataset
let mut model = MyModel::new(input_dim, hidden_dim, output_dim, &mut cx);
let dataset = DataLoader::new(batch_size, shuffle);

// Training loop
for epoch in 0..num_epochs {
    for (batch_x, batch_y) in dataset.iter() {
        // Set input data
        cx.set_input_tensor(input_tensor_id, batch_x);
        
        // Forward pass
        let output = model.forward(input);
        let loss = loss_fn(output, target).retrieve();
        
        // Compute gradients
        let grads = cx.compile(Autograd::new(model_params, loss), ());
        
        // Update parameters using an optimizer
        // ...
        
        // Execute graph
        cx.execute();
    }
}
```

### 5.2. Optimizer Implementation

Luminal implements the SGD optimizer which takes gradients and produces update rules:

```rust
pub fn sgd(
    grads: &[(NodeIndex, ShapeTracker)],
) -> (
    Vec<NodeIndex>,
    Vec<NodeIndex>,
    Vec<NodeIndex>,
    Graph,
    GraphTensor,
) {
    let mut opt_graph = Graph::new();
    let (old_weights, gradients): (Vec<NodeIndex>, Vec<NodeIndex>) = grads
        .iter()
        .map(|_| (opt_graph.tensor(1).id, opt_graph.tensor(1).id))
        .unzip();

    let (new_weights, lr) = sgd_on_graph(
        &mut opt_graph,
        &old_weights,
        &gradients
            .iter()
            .zip(grads)
            .map(|(a, (_, b))| (*a, *b))
            .collect::<Vec<_>>(),
    );
    (old_weights, gradients, new_weights, opt_graph, lr)
}

pub fn sgd_on_graph(
    graph: &mut Graph,
    old_weights: impl ToIds,
    grads: &[(NodeIndex, ShapeTracker)],
) -> (Vec<NodeIndex>, GraphTensor) {
    let lr = graph.named_tensor("Learning Rate", 1).set(3e-4).keep(); // Karpathy constant
    let mut new_weights = vec![];
    for ((grad_id, grad_shape), old_weight_id) in grads.iter().copied().zip(old_weights.to_ids()) {
        let old_weight = GraphTensor::from_id(old_weight_id, grad_shape, graph);
        let gradient = GraphTensor::from_id(grad_id, grad_shape, graph);

        // SGD
        let new_weight = old_weight - (gradient * lr.expand_to(grad_shape));
        new_weight.keep();

        new_weights.push(new_weight.id);
    }

    (new_weights, lr)
}
```

The `sgd` function creates a new graph that takes old weights and gradients as inputs and returns updated weights. It applies the standard SGD update rule: `new_weight = old_weight - (gradient * learning_rate)`.

## 6. Testing and Verification

Luminal includes comprehensive tests to verify the correctness of its autodiff system:

```rust
#[test]
fn test_autograd_matmul() {
    let mut cx = Graph::new();
    let a = cx.named_tensor("A", (2, 2)).set([[2., 4.], [3., 1.]]);
    let input = cx.named_tensor("Input", 2).set([10., 5.]);
    let output = (input.matmul(a)).sum_reduce(0);

    let grads = cx.compile(Autograd::new(a, output), ());
    cx.keep_tensors(&grads);
    cx.execute();

    let dev = dfdx::prelude::Cpu::default();
    let w1 = dev.tensor([[2., 4.], [3., 1.]]);
    let inp = dev.tensor([10., 5.]);
    let out = inp.trace(Gradients::leaky()).matmul(w1.clone()).sum();
    let d_grads = out.backward();

    assert_exact(&get_vec(grads[0], &mut cx), &d_grads.get(&w1).as_vec());
}
```

These tests compare Luminal's gradient computation with reference implementations (using the `dfdx` crate) to ensure correctness. The tests cover a range of operations including matrix multiplication, layer normalization, softmax, and even transformer blocks.

## Conclusion

Luminal's autodiff system is built around the `Autograd` compiler, which computes gradients by traversing the computational graph in reverse topological order and applying operation-specific gradient rules. The system handles complex operations like reduction, reshaping, and various mathematical functions, making it capable of training sophisticated neural networks.

The implementation leverages Rust's type system and Luminal's graph representation to provide a robust and performant autodiff solution. Through careful gradient computation and accumulation, Luminal can handle complex neural network architectures while ensuring correct gradient propagation throughout the model. 