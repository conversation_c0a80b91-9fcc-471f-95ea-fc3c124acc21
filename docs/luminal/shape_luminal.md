# Luminal ShapeTracker & Symbolic Expression Documentation

## Introduction

In Luminal, the `ShapeTracker` manages the logical view of a tensor's shape, integrating tightly with the symbolic `Expression` type for dynamic dimensions, slicing, and padding.

## Core Concepts

### 1. Symbolic Expressions (`shape/symbolic.rs`)

Symbolic expressions support dynamic dimension logic via an RPN-based engine:

* **`Term` Enum:** Numeric (`Num`), variable (`Var`), arithmetic (`Add`, `Sub`, `Mul`, `Div`, `Mod`), min/max (`Min`, `Max`), logical (`And`, `Or`), comparisons (`Gte`, `Lt`).
* **`Expression` Struct:** Holds a `GenerationalBox<Vec<Term>>` in thread-local `UnsyncStorage`, enabling cheap `Copy`-able handles to shared RPN data.
* **Thread-Local Owner:** `EXPRESSION_OWNER` thread-local `Owner<UnsyncStorage>` manages term storage; freed via `expression_cleanup()`.
* **Construction & Overloads:** Operators and methods build new RPN sequences by concatenating term lists, returning new `Expression` handles without modifying existing ones.
* **Simplification:** Leverages the `egg` crate with custom rewrite rules (`make_rules()`). `simplify()` / `simplify_cache()` convert to `RecExpr`, run saturation, extract minimal form, and convert back to `Expression`.
* **Evaluation:** `exec_stack()` performs stack-based RPN evaluation via `Term::as_op` / `as_float_op`, returning `Option<usize>` / `Option<f64>` and `None` on invalid var or failure.
* **Utilities:** `substitute()`, `to_usize()`, `to_symbols()`, comparison builders (`min()`, `max()`, `gte()`, `lt()`).
* **Unit Tests:** Validate expression creation, simplification, and evaluation in `test_expressions`, `test_minimizations`, `test_substitution`, etc., at end of `symbolic.rs`.

### 2. `ShapeTracker` Struct (`shape/tracker.rs`)

Manages metadata for a potentially transformed tensor shape.

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct ShapeTracker {
    pub dims: ArrayVec<[Expression; 6]>,
    pub indexes: ArrayVec<[usize; 6]>,
    pub fake: ArrayVec<[bool; 6]>,
    pub mask: ArrayVec<[(Expression, Expression); 6]>,
    pub padding: ArrayVec<[(Expression, Expression); 6]>,
}
```

*   **Fields:**
    * `dims`: The original dimension sizes of the tensor.
    * `indexes`: Maps logical dimension order (permutation) to physical dimensions.
    * `fake`: Indicates if a dimension is "fake" (broadcasted, not stored in memory).
    * `mask`: Lower and upper bounds for slicing each dimension, stored as (start, end) tuples.
    * `padding`: Padding amounts before and after each dimension, stored as (before, after) tuples.
*   **Memory Management (`ArrayVec`)**:
    *   All fields use `ArrayVec<[T; 6]>`. `ArrayVec` is a fixed-capacity vector allocated on the stack.
    *   This capacity of 6 dimensions is fixed and part of the type.
    *   This aims for performance by avoiding heap allocations for common tensor ranks.
*   **Ownership/Borrowing (`Copy` Trait)**:
    *   `ShapeTracker` implements `Copy`. Assigning or passing it creates a full, independent copy of all its `ArrayVec` fields on the stack.
    *   Methods taking `&mut self` (e.g., `permute`, `slice`, `pad`, `add_dim`) modify the tracker's state *in-place*.
    *   Methods taking `self` (e.g., `new`, `fake`, `contiguous`, `realize`) consume the original tracker (if not already a copy) or operate on a copy, often returning a *new* `ShapeTracker` instance.

*   **Dimension Removal & Index Update:** `remove_dim` shifts entries in `indexes`, `dims`, `fake`, `mask`, and `padding`, maintaining correct mapping without heap allocation.
*   **Dedicated Fake Dimension (`expand`):** `expand` wraps `add_dim` then marks the inserted dimension as fake, optimizing broadcasting semantics.
*   **Permutation via Index Map:** `permute` reorders the internal `indexes` array, enabling O(n) axis reordering without touching `dims`.
*   **Stride Computation:** `unordered_strides` performs a reverse `scan` over `dims`, skipping fake dims, then `strides` reorders strides by `indexes`.
*   **Index Expression Generation:** `index_expression_no_simplify` builds a symbolic `Expression` by accumulating stride multipliers and applying `pad_mask_dim` on each dim; `index_expression` then simplifies it.
*   **Validity Expression:** `valid_expression_no_simplify` generates an expression that yields zero for out-of-mask or fake-padded indices, simplified by `valid_expression`.
*   **Dynamic Symbol Resolution:** `resolve_global_dyn_dims_stack` uses stack-based evaluation via `Expression::exec_stack`, updating `dims`, `mask`, and `padding` in-place.
*   **Helper Function `pad_mask_dim`:** Calculates effective dimension size by `(padding.0 + padding.1 + dim).min(mask.1) - mask.0`, used in expression building.
*   **In-Module Tests:** The `tests` section includes unit tests (`test_idx_expr`, `test_symbolic_idx`) that validate expression correctness and cleanup via `expression_cleanup()`

### 3. Dimensions (`shape/mod.rs`)
This module provides utilities for slicing, padding, and reshaping dimensions:
* **`ReshapeDim` Enum:** Used by `reshape` operations; `Const(usize)` for a fixed size, or `PrevDim(usize)` to reuse an existing axis size.
* **`SliceRange` Trait:** Converts Rust ranges (`Range`, `RangeFrom`, `RangeToInclusive`, `RangeFull`, tuples, etc.) into `(Expression, Expression)` bounds via `bounds()`. Internally uses `get_start_bound`/`get_end_bound` to handle inclusive/exclusive and unbounded cases.
* **`ToSlice` Trait:** Accepts single or multi-axis slice specs (range, tuple of ranges up to 5D, `Vec`, etc.) and produces `Vec<(Expression,Expression)>` for `ShapeTracker::slice`.
* **`ToPad` Trait:** Similar to `ToSlice`, converts padding specs (`(before, after)` or tuples) into `Vec<(Expression,Expression)>` for `ShapeTracker::pad`.
* **`get_start_bound` & `get_end_bound` Helpers:** Map `Bound<D>` into an `Expression` start or end bound, adjusting for inclusivity/exclusivity.

Luminal uses `Expression` directly for dimensions, with conversion handled by the `ToShape` trait.

## Key Operations and Design

### Initialization and Basic Methods

*   **Initialization (`ShapeTracker::new`)**: Creates tracker, populates all `ArrayVec` fields. Initializes masks to (0, MAX) and padding to (0, 0).
*   **Dimension Queries**:
    * `len()`: Returns the number of dimensions.
    * `dims()`: Returns the true logical dimensions with padding and masking applied.
    * `shape_usize()`: Returns dimensions as concrete `usize` values (requires all symbolic values to be resolved).
    * `n_elements()`: Calculates the total number of elements (the product of all logical dimensions).
    * `n_physical_elements()`: Calculates the product of all non-fake dimensions.

### Transformations:

*   **`permute(&mut self, axes: &[usize])`**: Modifies `indexes` in-place to reorder dimensions.
    ```rust
    // Example: permute a 3D tensor from [batch, height, width] to [batch, width, height]
    let mut st = ShapeTracker::new([32, 224, 224]); 
    st.permute(&[0, 2, 1]); // Now logically [32, 224, 224] but accessed as [32, 224, 224]
    ```

*   **`remove_dim(&mut self, axis: usize)`**: Removes a dimension, updating all `ArrayVec` fields and adjusting indexes.
*   **`add_dim(&mut self, axis: usize, dim: impl Into<Expression>)`**: Adds a dimension at the specified axis.
*   **`expand(&mut self, axis: usize, dim: impl Into<Expression>)`**: Adds a "fake" dimension for broadcasting.
    ```rust
    // Example: turn a vector [5] into a row of a 2D tensor [1, 5]
    let mut st = ShapeTracker::new([5]);
    st.expand(0, 1); // Now [1, 5] with the first dim marked as "fake"
    ```

*   **`slice(&mut self, mask: &[(Expression, Expression)])`**: Modifies `mask` in-place to limit dimension ranges.
    ```rust
    // Example: slice the middle section of an image
    let mut st = ShapeTracker::new([32, 224, 224]);
    st.slice(&[(0.into(), 32.into()), (56.into(), 168.into()), (56.into(), 168.into())]);
    ```

*   **`pad(&mut self, padding: &[(Expression, Expression)])`**: Modifies `padding` in-place to add padding.
    ```rust
    // Example: add padding to an image
    let mut st = ShapeTracker::new([32, 224, 224]);
    st.pad(&[(0.into(), 0.into()), (1.into(), 1.into()), (1.into(), 1.into())]);
    ```

*   **Reshaping**: Handled via `ShapeTracker::contiguous()` which returns a new contiguous `ShapeTracker`.
    ```rust
    // Example: reshape a tensor from [32, 224, 224] to [32, 50176]
    let st = ShapeTracker::new([32, 224, 224]);
    // After some operations that modify the tensor...
    let new_st = st.contiguous(); // Get a fresh ShapeTracker with no permute/slice/pad
    // This is typically followed by creating a new tensor with the desired shape
    ```

### Memory Indexing & Validity

*   **`index_expression(&self)`**: Generates a symbolic `Expression` that maps a flat logical index to a physical memory index.
    * Implementation: Constructs a formula that computes the physical index by:
      1. Utilizing the strides computed from the original dimensions
      2. Considering the logical permutation via `indexes`
      3. Accounting for masks (slicing) and padding
      4. Skipping fake dimensions entirely
      5. Finally simplifying the expression

    ```rust
    // Without permutation, mask, or padding, the expression is just 'z'
    // With transformations, it generates expressions like:
    // (z % 10) * 100 + ((z / 10) % 5) * 10 + (z / 50) - for a [10, 5, 2] tensor with no transformations
    // More complex for permuted/sliced/padded tensors
    ```

*   **`valid_expression(&self)`**: Generates a symbolic `Expression` that determines if a flat logical index maps to a valid element.
    * Implementation: Builds a logical formula that:
      1. Checks if the flat index falls within mask bounds
      2. Only considers non-fake dimensions
      3. Returns a non-zero value for valid indices, zero for invalid
    * Used by compilers to generate conditional access code

*   **`pad_mask_dim` Helper Function**: A utility that calculates the effective dimension size after applying padding and masking:
    ```rust
    fn pad_mask_dim(
        dim: impl Into<Expression>,
        padding: (Expression, Expression),
        mask: (Expression, Expression),
    ) -> Expression {
        (padding.0 + padding.1 + dim).min(mask.1) - mask.0
    }
    ```
    * This function:
      1. Adds the original dimension size to both padding amounts
      2. Takes the minimum of that result and the upper mask bound
      3. Subtracts the lower mask bound
    * Used by both `index_expression` and `valid_expression` to determine logical dimension sizes

### Stride Calculation

*   **`strides(&self)`**: Calculates the memory stride for each dimension, taking into account the permutation.
    * Implementation:
      1. First computes `unordered_strides()` - the strides of the original tensor layout
      2. Then reorders these strides according to the permutation stored in `indexes`
      3. Fake dimensions are skipped in stride calculations

*   **`unordered_strides(&self)`**: Core helper for calculating strides in the original dimension order.
    * Implements the standard row-major stride calculation (each stride is the product of all subsequent dimension sizes)
    * Uses a functional `scan` operation for efficiency, working backward through dimensions

### Symbolic Resolution

*   **`realize(self, dims: &[Expression]) -> Self`**: Creates a modified copy with the provided dimensions substituted.
    * Takes ownership of the ShapeTracker and returns an updated version
    * Preserves all transformations (permutations, mask, padding)
    * Used for binding concrete sizes to symbolic dimensions

*   **`resolve_global_dyn_dims(&mut self, dyn_dim_map: &FxHashMap<char, usize>)`**: In-place substitution of symbolic dimensions.
    * Walks through all symbolic expressions in `dims`, `mask`, and `padding`, replacing symbolic variables with concrete values
    * Uses a stack-based evaluation for efficiency
    * Interacts with the Graph's `dyn_map` during execution

    ```rust
    // Example: Resolving 'a' to 64 in expressions like 2*a+1
    let mut st = ShapeTracker::new(['a'.into(), ('a'.into() * 2).into()]);
    let mut dyn_map = FxHashMap::default();
    dyn_map.insert('a', 64);
    st.resolve_global_dyn_dims(&dyn_map);
    // Now dimensions are [64, 128]
    ```

### State Checking

*   **`is_contiguous(&self)`**: Returns true only if no permutation or fake dimensions exist.
*   **`is_reshaped(&self)`**: Returns true if any transformation (permute, slice, pad) has been applied.
*   **`is_sliced(&self)`**: Returns true if any dimension has been sliced (mask modified).
*   **`is_padded(&self)`**: Returns true if any dimension has padding applied.

## Broadcasting Implementation Details

Broadcasting in Luminal is primarily implemented through the "fake" dimension mechanism, which leverages several key components:

### 1. Core Implementation Approach

Broadcasting relies on two key properties:
* **"Fake" Dimension Marking:** The `fake` field in `ShapeTracker` tracks which dimensions are broadcasted (size 1) but don't physically exist in memory.
* **Special Index Expression Handling:** The `index_expression_no_simplify` method skips fake dimensions when generating index expressions.

```rust
// From ShapeTracker::index_expression_no_simplify
for i in self.indexes.into_iter().rev() {
    // Get logical dimension size with padding and mask
    let current_size = pad_mask_dim(self.dims[i], self.padding[i], self.mask[i]);
    // Don't include fake dimensions in the index expression
    if !self.fake[i] {
        let mut dim_ind = Expression::from('z');
        // ... index calculation logic ...
        ind_expr += dim_ind;
    }
    // Current element size tracking continues for all dims (fake or not)
    current_elem_size *= current_size;
}
```

### 2. The `expand` Operation

The high-level `expand` operation is implemented as a thin wrapper around `add_dim` that marks the newly added dimension as fake:

```rust
pub fn expand(&mut self, axis: usize, dim: impl Into<Expression>) {
    self.add_dim(axis, dim);
    self.fake[self.indexes[axis]] = true;
}
```

This enables an efficient implementation of broadcasting by:
1. Adding a new dimension with size 1 (or any size for general expansion)
2. Marking it as "fake" so it doesn't affect memory layout or indexing

### 3. Broadcasting Usage in Practice

In typical tensor operations, broadcasting is handled at a higher level by the operation implementations:

1. **Binary Operation Broadcasting:**
   * When performing operations between tensors of different ranks, the smaller tensor is expanded with fake dimensions
   * This is typically handled at the `Graph` or operator level, not directly in `ShapeTracker`

2. **Automatic Broadcasting:**
   * Luminal implements an N-way broadcasting algorithm that:
     - Left-pads dimensions with size 1 to match ranks
     - Ensures each aligned dimension is either identical or one of them is 1
     - Uses `expand` to create fake dimensions as needed

3. **Stride Calculation with Fake Dims:**
   * In `unordered_strides()`, fake dimensions are skipped during stride computation:
     ```rust
     let mut strides = (0..self.len())
         .rev()
         .scan(Expression::from(1), |state, i| {
             let ret = *state;
             if !self.fake[i] {
                 *state *= self.dims[i];
             }
             Some(ret)
         })
         .collect::<Vec<_>>();
     ```
   * This ensures that physical memory access patterns are correct despite the logical view having additional dimensions

### 4. Broadcasting Edge Cases

* **Broadcasting with Slicing/Padding:**
  * The system prevents adding padding to sliced dimensions to avoid conflicting transformations:
    ```rust
    // Validation in pad method
    if (e.to_usize().map(|n| n != 0).unwrap_or(true) && self.mask[ind].1.to_usize().map(|n| n as i32 != i32::MAX).unwrap_or(true))
        || (s.to_usize().map(|n| n != 0).unwrap_or(true) && self.mask[ind].0.to_usize().map(|n| n as i32 != 0).unwrap_or(true))
    {
        panic!("Adding padding to a masked shape isn't supported")
    }
    ```

* **Element Count Calculation:**
  * The `n_elements()` method includes all dimensions (including fake ones):
    ```rust
    pub fn n_elements(&self) -> Expression {
        self.dims().into_iter().product::<Expression>().max(1)
    }
    ```
  * While `n_physical_elements()` excludes fake dimensions:
    ```rust
    pub fn n_physical_elements(&self) -> Expression {
        self.indexes.into_iter().filter(|i| !self.fake[*i]).map(|i| self.dims[i]).product::<Expression>().max(1)
    }
    ```

## Graph / Compiler Interaction

*   **`GraphTensor` Integration**: Each tensor in the graph stores a `ShapeTracker` instance.
    ```rust
    // From graph_tensor.rs
    pub struct GraphTensor {
        pub id: NodeIndex,
        pub graph_ref: *mut Graph,
        pub shape: ShapeTracker,  // Shape tracking for this tensor
    }
    ```

*   **Propagation in Operations**: Operations receive `ShapeTracker`s from inputs and produce appropriate `ShapeTracker`s for outputs.
    ```rust
    // Example: slice operation creating a new GraphTensor with modified ShapeTracker
    pub fn slice(self, slice_spec: impl SliceInto) -> Self {
        let slice = slice_spec.into_slice(self.shape.len());
        let mut new_shape = self.shape;
        new_shape.slice(&slice);
        // Create new graph node with the modified shape tracker
        Self::from_id(
            self.graph().add_op(Slice::new(self.id, slice)),
            new_shape,
            self.graph_ref,
        )
    }
    ```

*   **Code Generation**: Compilers use `index_expression()` and `valid_expression()` to generate memory access code.
    * Example: When generating kernels, the compiler:
      1. Gets the `index_expression` from the ShapeTracker
      2. Converts it to code for computing memory offsets
      3. Uses the `valid_expression` to generate bounds-checking conditions
      4. Integrates these into kernel templates

*   **Optimization Decisions**: Compilers check properties like `is_contiguous()` to make optimized code generation decisions.
    * Contiguous tensors can use simple linear access patterns
    * Non-contiguous tensors require more complex indexing logic

## Design Choices & Rationale Summary

*   **Representation:** `Expression` for dims/masks/padding, `ArrayVec` fields, `Copy` struct.
*   **Memory:** Stack allocation focus via `ArrayVec` (rank <= 6).
*   **Ownership:** `Copy` semantics for the tracker state. In-place modification for some transforms (`slice`, `pad`, `permute`).
*   **Indexing:** Generates symbolic formulas for compiler.
*   **Error Handling:** Primarily `assert!` / panics for invalid setup/transformation args; `Option` for symbolic evaluation errors.

## Key Implementation Considerations for a Zig Port

When porting this design to Zig, several considerations should be made:

*   **Memory Management:** Luminal's `ArrayVec` and generational boxes would need Zig equivalents. Consider using `std.BoundedArray` or custom inline vectors.

*   **Expression Representation:** Decide between RPN-style expressions (like Luminal) or tree-based expressions (which may be more natural in Zig).

*   **E-Graph Integration:** Luminal has direct `egg` integration in Rust. A Zig port would need either:
    1. An FFI layer to communicate with `egg` (as currently planned)
    2. A pure Zig e-graph implementation (more work but avoids FFI complexity)

*   **Error Handling Philosophy:** Luminal uses panic/assert in many places, while Zig favors explicit error handling. Consider whether to adopt Zig's error union approach more broadly.

*   **Threading Model:** Thread-local storage in Zig works differently than in Rust. A direct port would need to account for these differences in the symbolic expression handling.

*   **Limited Chars for Variables:** Luminal uses single `char` values for variables, limiting the namespace. Consider a more flexible approach for Zig's symbolic variables.

*   **Fixed Dimension Limit:** Luminal's `ArrayVec<[T; 6]>` approach limits tensors to 6 dimensions. Consider if this is sufficient or if a different approach is needed.

The key strength of Luminal's design is its efficient handling of symbolic expressions and operations without sacrificing performance for common cases. Any Zig implementation should aim to preserve these performance characteristics while adapting to Zig's idioms and memory model. 