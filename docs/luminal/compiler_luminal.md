# Luminal Compiler Architecture

This document details the compiler architecture of Luminal, including the generic compiler infrastructure and backend-specific compilers. It explains how <PERSON><PERSON><PERSON> transforms the computational graph into optimized executable code for different hardware targets.

## 1. Overview of the Compiler Pipeline

Luminal uses a multi-stage compilation approach:

1. **High-level graph construction** - User builds a graph using GraphTensor operations
2. **Generic compiler passes** - Backend-agnostic optimizations
3. **Backend-specific compilers** - Hardware-specific optimizations for CPU, CUDA, or Metal
4. **Graph execution** - Running the optimized graph with memory management

```
┌───────────────┐    ┌───────────────┐    ┌───────────────┐    ┌───────────────┐
│  Raw Graph    │ -> │    Generic    │ -> │ Backend-      │ -> │   Optimized   │
│  Construction │    │   Compiler    │    │ Specific      │    │ Executable    │
│               │    │    Passes     │    │ Compilers     │    │ Graph         │
└───────────────┘    └───────────────┘    └───────────────┘    └───────────────┘
```

## 2. Generic Compiler Framework

### 2.1. The `Compiler` Trait

The foundation of Luminal's compilation system is the `Compiler` trait (found in `compiler_utils.rs`):

```rust
pub trait Compiler {
    type Output;
    /// Run a compilation pass
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, ids: T) -> Self::Output;
}
```

This trait allows Luminal to apply a sequence of compilation passes to a graph. Each compiler takes:
- A mutable reference to the graph
- A set of tensor/node IDs that might need to be updated if nodes are replaced

### 2.2. The Tuple Compiler Pattern

Luminal implements a technique for chaining compilers using tuples. This is defined through macro-generated implementations for tuples of different sizes, allowing multiple compilers to be composed together:

```rust
// Example of the tuple_impls! macro output for 2-element tuples
impl<M1: Compiler, M2: Compiler> Compiler for (M1, M2) {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, ids: T) {
        self.0.compile(graph, &ids);
        self.1.compile(graph, ids);
    }
}
```

This pattern allows arbitrary chaining of compiler passes:

```rust
// Example of chaining multiple compilers as seen in GenericCompiler
pub type GenericCompiler = (
    RemoveUnusedNodes,
    ArithmeticElimination,
    CSE,
);

// Usage
cx.compile(GenericCompiler::default(), &mut c);
```

### 2.3. Core Generic Compiler Passes

Luminal's generic compiler applies several backend-agnostic optimizations:

#### 2.3.1. Common Subexpression Elimination (CSE)

Identifies and eliminates redundant computations in the graph:

```rust
#[derive(Default, Debug)]
pub struct CSE;

impl Compiler for CSE {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, mut ids: T) {
        // Look for nodes that have the exact same sources
        let mut eliminated = true;
        while eliminated {
            eliminated = false;
            let mut srcs_set: HashMap<Vec<NodeIndex>, Vec<NodeIndex>> = HashMap::new();
            // Group nodes by their input sources
            // ...
            // Eliminate duplicates
            // ...
        }
    }
}
```

#### 2.3.2. Remove Unused Nodes

Removes nodes whose outputs are never used and that are not marked as `no_delete`.

```rust
#[derive(Default, Debug)]
pub struct RemoveUnusedNodes;

impl Compiler for RemoveUnusedNodes {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, _: T) {
        for node in toposort(&graph.graph, None).unwrap().into_iter().rev() {
            if graph.edges_directed(node, Direction::Outgoing).count() == 0
               && !graph.no_delete.contains(&node)
            {
                graph.remove_node(node);
            }
        }
    }
}
```

#### 2.3.3. Arithmetic Elimination

Reduces simple arithmetic patterns via graph rewriting:

```rust
/// Eliminates:
/// - x + 0 → x
/// - 0 + x → x
/// - exp2(log2(x)) → x
#[derive(Debug, Default)]
pub struct ArithmeticElimination;

impl Compiler for ArithmeticElimination {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, mut ids: T) {
        // -- x + 0 and 0 + x elimination --
        let zero = constant(0.);
        let x = node();
        let add_left = binary::<Add>(zero.clone(), x.clone());
        let add_right = binary::<Add>(x.clone(), zero.clone());
        let mut s1 = add_left.clone().search(graph);
        let mut s2 = add_right.clone().search(graph);
        while s1.next_match() || s2.next_match() {
            let (inp_node, zero_node, add_node) = if s1.matched {
                (s1.get(&x), s1.get(&zero), s1.get(&add_left))
            } else {
                (s2.get(&x), s2.get(&zero), s2.get(&add_right))
            };
            if graph.no_delete.contains(&zero_node) { continue; }
            // Bypass Add node
            move_outgoing_edge(add_node, inp_node, &mut graph.graph);
            remap(add_node, inp_node, &mut ids, graph);
            graph.graph.remove_node(add_node);
        }

        // -- exp2(log2(x)) fusion --
        let y = node();
        let exp_node = unary::<Exp2>(y.clone());
        let log_node = unary::<Log2>(exp_node.clone());
        let mut s3 = log_node.clone().search(graph);
        while s3.next_match() {
            let input = s3.get(&y);
            let exp_id = s3.get(&exp_node);
            let log_id = s3.get(&log_node);
            if graph.no_delete.contains(&exp_id) { continue; }
            // Bypass Exp2 and Log2
            move_outgoing_edge(log_id, input, &mut graph.graph);
            remap(log_id, input, &mut ids, graph);
            remap(exp_id, input, &mut ids, graph);
            graph.graph.remove_node(log_id);
            graph.graph.remove_node(exp_id);
        }
        
        // TODO: implement x * 1, x / x, x - x, and other eliminations
    }
}

## 3. Backend-Specific Compilers

Luminal provides specialized compilers for different hardware targets, defined in separate crates:

### 3.1. CPU Compiler (`luminal_cpu` crate)

```rust
pub type CPUCompiler = (
    matmul::MatMulCompiler,
    binary::SubtractionCompiler,
    binary::EqualCompiler,
    other::ARangeCompiler,
    binary::GatherCompiler,
    UnaryFusionCompiler,
);
```

The CPU compiler includes passes for:

1. **Specialized MatMul Implementation** - Optimizes matrix multiplication for CPU
2. **Binary Op Specialization** - Optimizes operations like subtraction and equality checks
3. **Unary Fusion** - Combines sequences of unary operations to avoid memory round-trips:

```rust
#[derive(Debug, Default)]
pub struct UnaryFusionCompiler;

impl Compiler for UnaryFusionCompiler {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, mut ids: T) {
        // Identify sequences of unary ops that can be fused
        // Replace them with a single FusedUnary operator
        // ...
    }
}
```

### 3.2. CUDA Compiler (`luminal_cuda` crate)

```rust
pub type CudaCompiler<T> = (
    prim::PrimitiveCompiler<T>,
    SpecialOpsCompiler<T>,
    other::CopyCompiler<T>,
    elementwise_fusion::ElementwiseFusionCompiler<T>,
);

pub type SpecialOpsCompiler<T> = (
    binary::SubtractionCompiler<T>,
    binary::EqualCompiler<T>,
    other::ARangeCompiler<T>,
    binary::GatherCompiler<T>,
    unary::CudaExpCompiler<T>,
    unary::CudaCosCompiler<T>,
    unary::MeanReduceCompiler<T>,
    unary::StdNormCompiler<T>,
    unary::SoftmaxCompiler<T>,
    matmul::MatMulCompiler<T>,
);
```

The CUDA compiler includes:

1. **Primitive Compiler** - Maps basic operations to CUDA implementations
2. **Special Ops Compiler** - Optimizes specific operations (MatMul, Softmax, etc.)
3. **Copy Compiler** - Optimizes memory transfers
4. **Elementwise Fusion Compiler** - Fuses elementwise operations into efficient CUDA kernels

#### 3.2.1. CUDA Kernel Generation

The CUDA compiler can generate optimized CUDA kernels for operations:

```rust
// Helpers for generating CUDA code
fn expr_to_cuda_string(expr: &Expression) -> String {
    // Convert a symbolic expression to CUDA code
    // ...
}

fn get_idx_valid_exps(shape: ShapeTracker) -> (String, String) {
    // Generate CUDA code for index and validity expressions
    // ...
}

fn compile_and_load_kernel(mut code: String, device: &Arc<CudaDevice>) -> CudaFunction {
    // Compile CUDA kernel using NVRTC
    // ...
}
```

### 3.3. Metal Compiler (`luminal_metal` crate)

The Metal compiler in the `luminal_metal` crate provides optimized compilation for Apple's Metal backend. It is structured in two phases, each wrapped in timing utilities:

```rust
// Pre-buffer compilers: wrap primitive, special ops, copy, and elementwise fusion compilers in timing
pub type MetalCompilerPreBuffer<T> = (
    Timed<prim::PrimitiveCompiler<T>>,
    Timed<SpecialOpsCompiler<T>>,
    Timed<other::CopyCompiler<T>>,
    Timed<elementwise_fusion::ElementwiseFusionCompiler<T>>,
);

// Buffer compilers: manage command and storage buffers
pub type BufferCompilers = (
    command_buffer::CommandBufferCompiler,
    storage_buffer::StorageBufferCompiler,
);

// Full Metal compiler pipeline
pub type MetalCompiler<T> = (
    Timed<MetalCompilerPreBuffer<T>>,
    Timed<BufferCompilers>,
);
```

The Metal compiler includes detailed implementations for pre-buffer and buffer compilers, matching the `luminal_metal` crate definitions:

```rust
// Pre-buffer compiler implementation
impl<T> Compiler for MetalCompilerPreBuffer<T> {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, ids: T) {
        // Compile primitive operations
        self.0.compile(graph, ids);
        // Compile special operations
        self.1.compile(graph, ids);
        // Compile copy operations
        self.2.compile(graph, ids);
        // Compile elementwise fusion operations
        self.3.compile(graph, ids);
    }
}

// Buffer compiler implementation
impl Compiler for BufferCompilers {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, ids: T) {
        // Compile command buffer operations
        self.0.compile(graph, ids);
        // Compile storage buffer operations
        self.1.compile(graph, ids);
    }
}
```

## 4. Graph Execution

After compilation, the graph is executed through the Graph's execute method:

```rust
// In Graph implementation
pub fn execute(&mut self) {
    // Topologically sort the graph if not already done
    if self.linearized_graph.is_none() {
        self.toposort();
    }
    
    // Clone consumer counts for tracking tensor lifetimes
    let mut consumers = self.consumers_map.as_ref().unwrap().clone();
    let mut dim_stack = Vec::new();

    // Execute nodes in topological order
    for (node, src_ids) in self.linearized_graph.as_ref().unwrap() {
        // Skip if already computed
        if self.tensors.contains_key(&(*node, 0)) {
            continue;
        }
        
        // Prepare inputs from source nodes
        let mut inputs = vec![];
        for (src, out_idx, mut shape) in src_ids.clone() {
            // Get input tensor
            // Resolve dynamic dimensions
            // Add to inputs
            // Update consumer count
        }
        
        // Process the node
        let op = self.graph.node_weight_mut(*node).unwrap();
        let outputs = op.process(...);
        
        // Store outputs
        for (i, output) in outputs.into_iter().enumerate() {
            self.tensors.insert((*node, i as u8), output);
        }
        
        // Clean up tensors that are no longer needed
        let mut to_remove = vec![];
        for (id, count) in consumers.iter() {
            if *count == 0 && !self.no_delete.contains(&id.0) {
                to_remove.push(*id);
            }
        }
        for id in to_remove {
            self.tensors.remove(&id);
        }
    }
}
```

## 5. Compiler Utilities and Extensions

### 5.1. Looped Compiler

Repeatedly applies a compiler until the graph no longer changes:

```rust
#[derive(Debug)]
pub struct Looped<C: Compiler + Debug>(C);

impl<C: Compiler + Debug> Compiler for Looped<C> {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, mut remap: T) {
        let mut linearized = None;
        loop {
            self.0.compile(graph, &mut remap);
            graph.toposort();
            if linearized == graph.linearized_graph {
                break;
            }
            linearized.clone_from(&graph.linearized_graph);
        }
    }
}
```

### 5.2. Timed Compiler

Measures compilation time for performance analysis:

```rust
pub struct Timed<C: Compiler + Debug>(pub C);

impl<C: Compiler + Debug> Compiler for Timed<C> {
    type Output = ();
    fn compile<T: ToIdsMut>(&self, graph: &mut Graph, remap: T) {
        let start = std::time::Instant::now();
        self.0.compile(graph, remap);
        println!("{:?} took {:?}", self.0, start.elapsed());
    }
}
```

## 6. Pattern Matching for Optimizations

Luminal includes a sophisticated pattern matching system for identifying subgraphs:

```rust
pub struct GraphSearch {
    current: FxHashMap<Uuid, NodeIndex>,
    selector: StableGraph<(Uuid, SelectOp), Option<u8>>,
    graph: *mut Graph,
    to_return: Vec<FxHashMap<NodeIndex, NodeIndex>>,
    returned_anchors: HashSet<NodeIndex>,
    anchor: NodeIndex,
    pub matched: bool,
}

// Pattern builders
pub fn op<T: Operator + 'static>() -> SelectGraph { /* ... */ }
pub fn node() -> SelectGraph { /* ... */ }
pub fn unary<T: Operator + 'static>(node: SelectGraph) -> SelectGraph { /* ... */ }
pub fn binary<T: Operator + 'static>(a: SelectGraph, b: SelectGraph) -> SelectGraph { /* ... */ }
```

These utilities allow compilers to find and replace patterns in the graph, such as identifying a sequence of operations that can be replaced with a more efficient implementation.

## 7. Compilation Process

To compile a graph, users typically:

1. Create a graph and add operations
2. Retrieve the tensors needed for output
3. Apply the appropriate compiler(s)
4. Execute the graph

```rust
// Example usage
let mut cx = Graph::new();
let a = cx.tensor((2, 3));
a.set(vec![1.0, 2.0, 3.0, 1.0, 2.0, 3.0]);
let b = cx.tensor((3, 4));
b.set(vec![1., 2., 3., 1., 2., 3., 1., 2., 3., 1., 2., 3.]);
let mut c = a.matmul(b).retrieve();

// First run with unoptimized graph
cx.execute();
let unoptimized_result = c.data();

// Apply compiler and run again
cx.compile(CPUCompiler::default(), &mut c);
cx.execute();
let optimized_result = c.data();
```

This approach allows for flexible compilation strategies targeting different hardware and optimization goals.