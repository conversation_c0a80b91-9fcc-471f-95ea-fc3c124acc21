# Luminal Computational Graph: Internal Implementation

This document details the internal structure and mechanisms of Luminal's computational graph, explaining how operations are represented, how shapes are tracked, and how the low-level graph structure works.

## 1. Core Design Principles

*   **Directed Acyclic Graph (DAG):** All computations are represented as a DAG where nodes are operations and edges represent data or control dependencies.
*   **Dynamic Dispatch:** Operations are stored as trait objects (`Box<dyn Operator>`), allowing different backend implementations (CPU, CUDA, Metal) and custom operations to coexist within the same graph structure.
*   **Explicit Shape Tracking:** Shape information, including symbolic expressions and transformations (padding, masks, strides), is explicitly stored on data dependency edges using `ShapeTracker`.
*   **Handles for Interaction:** Users primarily interact with the graph via `GraphTensor` handles, which trigger graph modifications when operations are applied.
*   **Decomposition:** High-level operations (like `MatMul`, `Conv2D`) are generally *not* single nodes in the core graph. Instead, they are implemented as functions or modules that build subgraphs composed of primitive operations.
*   **Compiler Passes:** The graph is designed to be mutated by compiler passes that optimize the structure (e.g., fusion, replacing patterns with optimized ops).
*   **Stable Node Indices:** Uses `StableGraph<Box<dyn Operator>, Dependency>` to maintain valid node indices after removals, enabling safe compiler passes.
*   **High-Performance Hash Maps:** Graph state (tensors, dyn_map, no_delete, to_retrieve) uses `FxHashMap`/`FxHashSet` from `rustc_hash` for speed and memory efficiency.
*   **Borrow vs. Own Tensor Data:** Execution uses `get_source_tensors` logic to transfer ownership (`InputTensor::Owned`) when a tensor’s remaining consumer count is one and not marked `no_delete`; otherwise, it borrows (`InputTensor::Borrowed`), minimizing data copies.
*   **Raw Pointer Handle:** `GraphTensor` contains a raw `*mut Graph` pointer; methods use `unsafe` internally for efficient graph mutation, simplifying lifetimes.
*   **Scheduling Dependencies:** `Dependency::Schedule` edges enforce operation ordering without data transfer.
*   **Dynamic Dimension Resolution:** `ShapeTracker` resolves symbolic dimensions via `resolve_global_dyn_dims_stack` on each edge using `dyn_map` before operator execution.
*   **Cleanup on Drop:** `Graph`'s `Drop` implementation calls `expression_cleanup()` to free residual resources upon destruction.

## 2. Key Data Structures

Luminal's graph relies on several core structs and traits:

### 2.1. `Graph` (`graph.rs`)

This is the central owner and manager of the computational graph.

```rust
#[derive(Debug, Default)]
pub struct Graph {
    /// The store of tensors in the graph. Indexed by node index and output index.
    pub tensors: FxHashMap<(NodeIndex, u8), Tensor>,
    /// A map of dynamic dimensions to concrete dimension sizes
    pub dyn_map: FxHashMap<char, usize>,
    /// Edge weights: (Input index, Output index, Input shape)
    pub graph: StorageGraph,
    /// Tensors marked in this set will not get deleted when the graph is ran
    pub no_delete: FxHashSet<NodeIndex>,
    /// Tensors marked in this set need to be retrieved later
    pub to_retrieve: FxHashMap<NodeIndex, (u8, ShapeTracker)>,
    /// A cached list of nodes to run, source nodes
    pub(crate) linearized_graph: Option<Vec<(NodeIndex, Vec<(NodeIndex, u8, ShapeTracker)>)>>,
    /// Cached consumers (for execution only)
    consumers_map: Option<FxHashMap<(NodeIndex, u8), usize>>,
}
```

*   **`graph: StorageGraph`**: The underlying graph representation, which is `StableGraph<Box<dyn Operator>, Dependency>` from the petgraph crate. `StableGraph` ensures node indices remain valid even after node removals, which is helpful for compiler passes.

*   **`tensors: FxHashMap<(NodeIndex, u8), Tensor>`**: A map storing the actual computed tensor data during/after execution. Keyed by (Node ID, Output Index).
    ```rust
    // Example lookup of tensor data:
    if let Some(tensor) = graph.tensors.get(&(node_id, 0)) {
        // Access the first output tensor of node_id
    }
    ```

*   **`dyn_map: FxHashMap<char, usize>`**: Stores the concrete values for symbolic dimension variables (e.g., `'a' -> 64`).
    ```rust
    // Example setting a dynamic dimension:
    graph.dyn_map.insert('a', 64);
    ```

*   **`no_delete: FxHashSet<NodeIndex>`**: A set of node IDs whose output tensors should not be deleted after execution.
    ```rust
    // Mark a tensor's node to keep its output
    graph.no_delete.insert(node_id);
    ```

*   **`to_retrieve: FxHashMap<NodeIndex, (u8, ShapeTracker)>`**: Marks tensors that need to be retrieved after execution (often used by optimizers).
    ```rust
    // Mark a tensor to be retrieved
    graph.to_retrieve.insert(node_id, (0, shape_tracker));
    ```

*   **`linearized_graph: Option<Vec<...>>`**: A cached topological sort of the graph used for efficient execution scheduling.

*   **`consumers_map: Option<FxHashMap<...>>`**: A cached map tracking how many consumers each tensor output has, used for memory management during execution.

#### Core Methods

*   **`fn new() -> Self`**: Creates a new empty graph.
    ```rust
    let mut graph = Graph::new();
    ```

*   **`fn add_op<O: Operator + 'static>(&mut self, op: O) -> NodeIndex`**: Adds an operation to the graph, returning its NodeIndex.
    ```rust
    let node_id = graph.add_op(AddOp::new()); 
    ```

*   **`fn connect(&mut self, src: NodeIndex, dst: NodeIndex, shape: ShapeTracker, out_idx: u8, in_idx: u8)`**: Connects two nodes with a data dependency, specifying the shape.
    ```rust
    // Connect output port 0 of src to input port 0 of dst with shape tracker
    graph.connect(src, dst, shape_tracker, 0, 0);
    ```

*   **`fn toposort(&mut self)`**: Performs a topological sorting of the graph nodes and caches the result.
    ```rust
    // Update the cached execution order
    graph.toposort();
    ```

*   **`fn execute(&mut self) -> Result<(), ExecutionError>`**: Executes the graph in topological order.

*   **`fn keep_tensors(&mut self, node: NodeIndex)`**: Marks a node's output tensors to be kept after execution.

*   **`fn drop_tensors(&mut self, node: NodeIndex)`**: Removes a node's tensors from the graph.

*   **`fn get_op<T: 'static>(&self, idx: NodeIndex) -> Option<&T>`**: Gets a reference to an operation if it matches the type `T`.
    ```rust
    // Get a reference to an operation if it's a specific type
    if let Some(add_op) = graph.get_op::<AddOp>(node_id) {
        // Use add_op
    }
    ```

*   **`fn get_op_mut<T: 'static>(&mut self, idx: NodeIndex) -> Option<&mut T>`**: Gets a mutable reference to an operation.

*   **`fn tensor_shape(&self, node: NodeIndex) -> Option<ShapeTracker>`**: Gets the shape of a node's output tensor.
    ```rust
    // Get the shape of node_id's output
    if let Some(shape) = graph.tensor_shape(node_id) {
        // Use shape
    }
    ```

*   **`fn get_tensor_ref(&self, node: NodeIndex, output_idx: u8) -> Option<&dyn Data>`**: Gets a reference to a tensor's data.

### 2.2. `NodeData` -> `Box<dyn Operator>` (`op.rs`)

Nodes in the `StorageGraph` hold trait objects implementing the `Operator` trait.

```rust
pub trait Operator: Debug + AsAny {
    fn process(
        &mut self,
        ctx: &mut dyn Context,
        inputs: Vec<(InputTensor, ShapeTracker)>,
    ) -> Result<Vec<Tensor>, ExecutionError>;
    
    fn custom(&mut self, key: &str, input: Box<dyn Any>) -> Option<Box<dyn Any>> {
        None
    }
}
```

*   **`Operator` Trait:**
    *   `process(&mut self, ctx: &mut dyn Context, inputs: Vec<(InputTensor, ShapeTracker)>) -> Result<Vec<Tensor>, ExecutionError>`: The core execution method. Takes inputs (tensor data + shape tracker) and returns output tensors.
    *   `custom(&mut self, key: &str, input: Box<dyn Any>) -> Option<Box<dyn Any>>`: An extension point used by compilers to query operator properties (e.g., asking if it's elementwise for fusion).
    *   Requires `Debug` and `as_any::AsAny` (for downcasting).

*   **Implementations:** Concrete structs implement `Operator`:
    ```rust
    // Example of a simple Add operator implementation
    pub struct AddOp {
        left: NodeIndex,
        right: NodeIndex,
    }
    
    impl Operator for AddOp {
        fn process(
            &mut self,
            ctx: &mut dyn Context,
            inputs: Vec<(InputTensor, ShapeTracker)>,
        ) -> Result<Vec<Tensor>, ExecutionError> {
            // Extract input tensors
            let left = &inputs[0].0;
            let right = &inputs[1].0;
            
            // Perform addition on CPU
            let left_data = left.as_ref().downcast_ref::<Vec<f32>>().unwrap();
            let right_data = right.as_ref().downcast_ref::<Vec<f32>>().unwrap();
            
            let mut result = Vec::with_capacity(left_data.len());
            for i in 0..left_data.len() {
                result.push(left_data[i] + right_data[i]);
            }
            
            // Return the result as a tensor
            Ok(vec![Tensor::new(result)])
        }
        
        fn custom(&mut self, key: &str, _input: Box<dyn Any>) -> Option<Box<dyn Any>> {
            if key == "is_elementwise" {
                return Some(Box::new(true));
            }
            None
        }
    }
    ```

### 2.3. `EdgeData` -> `Dependency` (`graph.rs`)

Edges in the `StorageGraph` represent dependencies.

```rust
#[derive(Debug, Clone, Copy)]
#[allow(clippy::large_enum_variant)]
pub enum Dependency {
    /// A data dependency (transferring a tensor from one node to the next)
    Data {
        input_order: u8,
        output_order: u8,
        shape: ShapeTracker,
    },
    /// Explicit dependency for ordering. No tensors are transferred through this dependency
    Schedule,
}
```

*   **`Dependency = union(enum)`**:
    *   **`Data: struct { input_order: u8, output_order: u8, shape: ShapeTracker }`**: Represents data flow. Critically, it stores the `ShapeTracker` instance describing the exact state (shape, strides, mask, padding, fake dims) of the tensor flowing along this edge. `input_order` and `output_order` specify which output/input ports are connected.
    
        ```rust
        // Example of creating a Data dependency
        let data_dep = Dependency::Data {
            input_order: 0,  // Use the first input port
            output_order: 0,   // Connect to the first output port
            shape: shape_tracker,  // ShapeTracker for this tensor
        };
        ```
        
    *   **`Schedule`**: Represents a simple ordering dependency without data transfer.
    
        ```rust
        // Example of creating a scheduling dependency
        let schedule_dep = Dependency::Schedule;
        // This simply ensures op1 runs before op2
        graph.graph.add_edge(op1_idx, op2_idx, schedule_dep);
        ```

### 2.4. `GraphTensor` (`graph_tensor.rs`)

A lightweight handle used for building the graph.

```rust
#[derive(Clone, Copy)]
pub struct GraphTensor {
    pub id: NodeIndex,
    pub graph_ref: *mut Graph,
    pub shape: ShapeTracker,
}
```

*   **`struct { id: NodeIndex, graph_ref: *mut Graph, shape: ShapeTracker }`**: It's `Copy`-able.
    *   `id`: Points to the graph node that *produces* this tensor.
    *   `graph_ref`: A raw pointer back to the `Graph` instance it belongs to. Allows methods on `GraphTensor` to modify the graph.
    *   `shape`: Stores the `ShapeTracker` representing the shape *of this specific tensor*. This tracker is computed when the `GraphTensor` is created (as the output of an operation).

*   **Key Methods:**
    ```rust
    impl GraphTensor {
        // Create from a node ID
        pub fn from_id(id: NodeIndex, shape: ShapeTracker, graph_ref: *mut Graph) -> Self {
            Self { id, graph_ref, shape }
        }
        
        // Get a reference to the containing graph
        pub fn graph(&self) -> &mut Graph {
            unsafe { self.graph_ref.as_mut().unwrap() }
        }
        
        // Mark for keeping after execution
        pub fn keep(self) -> Self {
            self.graph().keep_tensors(self.id);
            self
        }
        
        // Mark for retrieval after execution
        pub fn retrieve(self) -> Self {
            self.keep();
            self.graph().to_retrieve.insert(self.id, (0, self.shape));
            self
        }
        
        // Get the data of the tensor
        pub fn data(&self) -> Vec<f32> {
            // Implementation to access tensor data...
        }
    }
    ```

### 2.5. `Tensor` and `Data` (`op.rs`)

*   **`Data` Trait:** An interface (`Any + Debug + DynClone`) for actual tensor data buffers.
    ```rust
    pub trait Data: Any + Debug + DynClone {}
    
    // Implement for common types
    impl Data for Vec<f32> {}
    ```

*   **`Tensor` Struct:** A wrapper holding the polymorphic data buffer.
    ```rust
    pub struct Tensor {
        pub data: Box<dyn Data>,
    }
    
    impl Tensor {
        pub fn new<T: Data + 'static>(data: T) -> Self {
            Self { data: Box::new(data) }
        }
    }
    ```

*   **`InputTensor<'a>` Enum:** Used by `Operator::process` to accept either owned or borrowed tensors.
    ```rust
    pub enum InputTensor<'a> {
        Owned(Tensor),
        Borrowed(&'a Tensor),
    }
    
    impl<'a> InputTensor<'a> {
        pub fn as_ref(&self) -> &dyn Data {
            match self {
                InputTensor::Owned(tensor) => tensor.data.as_ref(),
                InputTensor::Borrowed(tensor) => tensor.data.as_ref(),
            }
        }
    }
    ```

## 3. Graph Construction: The Decomposition Strategy

Luminal primarily uses a **decomposition strategy** for constructing the graph:

1.  **Internal Graph Construction Methods:**
    ```rust
    impl Graph {
        // Add a node for a specific operation
        pub fn add_op<O: Operator + 'static>(&mut self, op: O) -> NodeIndex {
            self.graph.add_node(Box::new(op))
        }
        
        // Connect two nodes with a data dependency
        pub fn connect(
            &mut self,
            src: NodeIndex,
            dst: NodeIndex,
            shape: ShapeTracker,
            out_idx: u8,
            in_idx: u8,
        ) {
            let dependency = Dependency::Data {
                output_order: out_idx,
                input_order: in_idx,
                shape,
            };
            self.graph.add_edge(src, dst, dependency);
        }
        
        // Add a scheduling dependency (no data flow)
        pub fn schedule(&mut self, src: NodeIndex, dst: NodeIndex) {
            self.graph.add_edge(src, dst, Dependency::Schedule);
        }
    }
    ```

2.  **Node Creation Example:**
    ```rust
    // Example of how a primitive operation might be added internally
    fn add_log2_operation(graph: &mut Graph, input: NodeIndex, input_shape: ShapeTracker) -> NodeIndex {
        // Create the Log2 operation node
        let log2_op = Log2Op { input };
        let log2_node = graph.add_op(log2_op);
        
        // Connect the input to the Log2 node
        graph.connect(input, log2_node, input_shape, 0, 0);
        
        log2_node
    }
    ```

3.  **Adding Nodes/Edges:** When a new operation node is added to the graph:
    *   The operation object is wrapped in a `Box<dyn Operator>` and added to the graph storage.
    *   Input-output connections are created as `Dependency::Data` edges.
    *   The `ShapeTracker` for the tensor is stored on the edge.
    *   A unique `NodeIndex` is assigned by the graph storage.

4.  **Metadata Handling:**
    *   Shape information is tracked explicitly in the `ShapeTracker` instances stored on edges.
    *   Dynamic dimensions are registered in the graph's `dyn_map`.
    *   Nodes that need to persist their data are added to the `no_delete` set.

## 4. Internal Graph Traversal and Analysis

The graph provides several methods for traversal and analysis:

```rust
impl Graph {
    // Get a topological sort of the graph
    pub fn toposort(&mut self) {
        self.linearized_graph = Some(
            petgraph::algo::toposort(&self.graph, None)
                .unwrap()
                .into_iter()
                .map(|node| (node, self.get_sources(node)))
                .collect(),
        );
        
        // Refresh the internal remaining consumers map
        self.consumers_map = Some(
            self.graph
                .node_indices()
                .flat_map(|i| {
                    self.graph
                        .edges_directed(i, Direction::Outgoing)
                        .filter_map(|e| e.weight().as_data().map(|i| (e.source(), i)))
                        .group_by(|(_, (_, i, _))| *i)
                        .into_iter()
                        .map(|(ind, g)| ((i, ind), g.count()))
                        .collect::<Vec<_>>()
                })
                .collect(),
        );
    }
    
    // Find all source tensors for a node
    pub fn get_sources(&self, node: NodeIndex) -> Vec<(NodeIndex, u8, ShapeTracker)> {
        self.graph
            .edges_directed(node, Direction::Incoming)
            .filter_map(|e| {
                if let Dependency::Data { output_order, input_order, shape } = *e.weight() {
                    Some((e.source(), output_order, shape))
                } else {
                    None
                }
            })
            .collect()
    }
    
    // Get all consumers of a node's output
    pub fn get_consumers(&self, node: NodeIndex) -> Vec<(NodeIndex, u8, u8)> {
        self.graph
            .edges_directed(node, Direction::Outgoing)
            .filter_map(|e| {
                if let Dependency::Data { output_order, input_order, .. } = *e.weight() {
                    Some((e.target(), input_order, output_order))
                } else {
                    None
                }
            })
            .collect()
    }
}
```

## 5. Execution Flow

The execution of a graph involves several stages:

### 5.1. Topological Sorting

Before execution, the graph is topologically sorted to determine the processing order:

```rust
impl Graph {
    // Generate and cache a topological sort
    fn ensure_linearized(&mut self) {
        if self.linearized_graph.is_none() {
            self.toposort();
        }
    }
}
```

### 5.2. Consumer Count Calculation

Tensor lifetime tracking is done via consumer counts:

```rust
impl Graph {
    // Calculate how many times each tensor is used
    fn calculate_consumer_counts(&mut self) {
        if self.consumers_map.is_some() {
            return;
        }
        
        // Calculate consumers_map (implementation in toposort)
    }
}
```

### 5.3. Memory Management 

```rust
impl Graph {
    // Check if a tensor can be freed based on consumer count
    fn can_free_tensor(&self, node: NodeIndex, output_idx: u8) -> bool {
        if self.no_delete.contains(&node) {
            return false;
        }
        
        if let Some(ref consumers) = self.consumers_map {
            if let Some(count) = consumers.get(&(node, output_idx)) {
                return *count == 0;
            }
        }
        
        true
    }
    
    // Decrement consumer count after tensor is used
    fn decrement_consumer_count(&mut self, node: NodeIndex, output_idx: u8) {
        if let Some(ref mut consumers) = self.consumers_map {
            if let Some(count) = consumers.get_mut(&(node, output_idx)) {
                *count -= 1;
            }
        }
    }
}
```

### 5.4. Execution Loop

```rust
impl Graph {
    // Main execution method
    pub fn execute(&mut self) {
        // Track the number of views pointing to each tensor so we know when to clear
        if self.linearized_graph.is_none() {
            self.toposort();
        }
        let mut consumers = self.consumers_map.as_ref().unwrap().clone();
        let mut dim_stack = Vec::new();

        for (node, src_ids) in self.linearized_graph.as_ref().unwrap() {
            if self.tensors.contains_key(&(*node, 0)) {
                continue;
            }
            
            // Prepare inputs
            let mut inputs = vec![];
            for (src, out_idx, mut shape) in src_ids.clone() {
                // Get the input tensor
                let tensor = match self.tensors.get(&(src, out_idx)) {
                    Some(t) => t,
                    None => continue,
                };
                
                // Resolve dynamic dimensions
                shape.resolve_global_dyn_dims_stack(&self.dyn_map, &mut dim_stack);
                
                // Add to inputs
                inputs.push((InputTensor::Borrowed(tensor), shape));
                
                // Update consumer count
                consumers.get_mut(&(src, out_idx)).map(|count| *count -= 1);
            }
            
            // Process the node
            let op = self.graph.node_weight_mut(*node).unwrap();
            let outputs = op.process(&mut dyn_map_ctx(&self.dyn_map), inputs).unwrap();
            
            // Store outputs
            for (i, output) in outputs.into_iter().enumerate() {
                self.tensors.insert((*node, i as u8), output);
            }
            
            // Clean up tensors that are no longer needed
            let mut to_remove = vec![];
            for (id, count) in consumers.iter() {
                if *count == 0 && !self.no_delete.contains(&id.0) {
                    to_remove.push(*id);
                }
            }
            for id in to_remove {
                self.tensors.remove(&id);
            }
        }
    }
}
```

### 5.5 Retrieving Results

```rust
impl Graph {
    // Get a tensor by node ID and output index
    pub fn get_tensor_ref(&self, id: NodeIndex, ind: u8) -> Option<&Tensor> {
        self.tensors.get(&(id, ind))
    }
    
    // Get a tensor's data
    pub fn get_tensor(&mut self, id: NodeIndex, ind: u8) -> Option<Tensor> {
        self.tensors.remove(&(id, ind))
    }
}
```

## High-Level Operation Representation

An important aspect of Luminal's graph representation is how it handles high-level operations like matrix multiplication and convolution. This section explains the representation strategy and its implications.

### Decomposition of High-Level Operations

In Luminal, high-level operations are not represented as single nodes in the graph. Instead, they are decomposed into a sequence of more primitive operations:

1. **Matrix Multiplication Decomposition**:
   ```rust
   // For 2D tensors, matmul(A, B) becomes:
   let mul = A.expand(1, n) * B.permute((1, 0)).expand(0, m);
   let result = mul.sum_reduce(2);
   ```

2. **Convolution Decomposition**:
   ```rust
   // For a 2D convolution:
   let input_pooled = input
       .pool_last_dim(kernel.1, stride.1, dilation.1)
       .permute((0, 1, 3, 4, 2))
       .pool_last_dim(kernel.0, stride.0, dilation.0)
       .permute((0, 1, 5, 3, 4, 2))
       .reshape((batch, ch_in * kernel.0 * kernel.1, dim_out * dim_out));
       
   let output = weight.expand(0, batch)
       .matmul(input_pooled)
       .reshape((batch, ch_out, dim_out, dim_out));
   ```

This decomposition provides flexibility but introduces a "semantic gap" - the graph only contains primitive operations, temporarily losing the higher-level semantics.

### Implications for Graph Representation

This decomposition strategy has several important implications:

1. **Graph Size and Complexity**: High-level operations translate to multiple nodes in the graph, increasing graph size and complexity.

2. **Operation Identification**: The original high-level operation (e.g., matrix multiplication) is not directly identifiable in the graph structure.

3. **Optimization Challenge**: The compiler must reconstruct the semantic meaning through pattern matching to apply optimized implementations.

4. **Flexible Implementation**: Different tensor shapes/dimensions can use different decomposition strategies tailored to specific cases.

### Example: Tracing a Matrix Multiplication in the Graph

To illustrate how a matrix multiplication appears in the graph, consider this code:

```rust
let c = a.matmul(b);
```

This creates the following subgraph:
1. A **permute** node that transposes tensor b
2. **expand** nodes for both a and the transposed b
3. A **multiply** node for element-wise multiplication
4. A **sum_reduce** node to sum along the appropriate dimension

Visually, instead of a single MatMul node, the graph contains:

```
    a       b
    |       |
    |     permute
    |       |
  expand  expand
     \     /
      \   /
     multiply
        |
    sum_reduce
        |
        c
```

### Bridging the Semantic Gap During Compilation

During compilation, Luminal's pattern recognition system bridges this semantic gap:

1. **Pattern Detection**: The compiler scans the graph for specific patterns of operations.
2. **Pattern Matching**: When a known pattern is found (like the matmul pattern above), it's identified as a high-level operation.
3. **Optimized Replacement**: The pattern is replaced with an optimized implementation specific to the target hardware.

This approach allows Luminal to maintain a flexible graph representation while still achieving high performance through specialized implementations during compilation.