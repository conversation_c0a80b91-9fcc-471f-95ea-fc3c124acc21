# Luminal CUDA Backend: Compilation and Execution Tutorial

This document provides a detailed explanation of the internal mechanisms used by Luminal's CUDA backend to transform a high-level computational graph into executable CUDA code. It covers the compilation pipeline, kernel generation strategies, interaction with symbolic expressions, and runtime execution.

## Prerequisites: The Input Graph

Before the CUDA-specific compilers run, the input is assumed to be a Luminal `Graph` containing:

1.  **Core Operations:** Nodes representing fundamental operations defined in Luminal's core (e.g., `Add`, `Mul`, `Log2`, `SumReduce`, `Contiguous`, `MatMul`, etc.).
2.  **ShapeTrackers:** Edges annotated with `ShapeTracker` instances. These trackers describe the shape of the tensor data flowing along the edge and, crucially, contain symbolic `Expression` objects representing:
    *   `index_expression()`: A formula to calculate the physical memory index from a logical index.
    *   `valid_expression()`: A formula to determine if a logical index corresponds to a valid memory location (handling padding, slicing, etc.).
3.  **Simplified Expressions:** It's assumed that these `Expression` objects within the `ShapeTracker`s have already been processed and simplified by Luminal's core symbolic engine (which uses the external `egg` and `symbolic_expressions` crates). The CUDA backend consumes *these simplified results*.

## The CUDA Compiler Pipeline

Luminal employs a multi-stage compilation strategy for the CUDA backend. The main `CudaCompiler<T>` is a tuple of more specialized compilers, executed sequentially:

```rust
// From crates/luminal_cuda/src/lib.rs
pub type CudaCompiler<T> = (
    prim::PrimitiveCompiler<T>,
    SpecialOpsCompiler<T>,
    other::CopyCompiler<T>,
    elementwise_fusion::ElementwiseFusionCompiler<T>,
);
```

These stages progressively transform the graph:

### Stage 1: Primitive Compiler (`PrimitiveCompiler`)

*   **Goal:** Replace generic core Luminal operations with CUDA-specific operator implementations.
*   **Mechanism:** This compiler iterates through the graph, identifying nodes representing primitive operations (like `Log2`, `Exp2`, `Add`, `Mul`, `SumReduce`, `Contiguous`, `LessThan`, etc.). For each matched primitive op, it replaces the node with a corresponding CUDA-specific struct (e.g., `CudaLog2`, `CudaAdd`).
*   **Kernel Generation (for most primitives):**
    *   The `new` function for these CUDA ops (often generated by macros like `cuda_unary_op!`) takes the input `ShapeTracker`(s).
    *   It calls `get_idx_valid_exps(shape)` (defined in `lib.rs`) to retrieve the simplified index and validity `Expression`s from the `ShapeTracker`.
    *   It calls `expr_to_cuda_string(expr)` (defined in `lib.rs`) to convert these simplified symbolic expressions into CUDA C++ code snippets (e.g., `a * 10 + b`, `(idx < 100) != 0`).
    *   It formats a CUDA kernel string template, embedding the generated index/validity code snippets. Example for `CudaAdd`:
        ```c++
        // Simplified Example
        extern "C" __global__ void kernel(float *out, const float *inp_a, const float *inp_b, int numel, /* dynamic dims */) {
            int idx = blockIdx.x * blockDim.x + threadIdx.x;
            if (idx < numel) {
                // a_valid/b_valid and a_idx/b_idx are generated from ShapeTracker expressions
                out[idx] =
                    ((a_valid) == 0 ? 0.0 : inp_a[a_idx])
                    + ((b_valid) == 0 ? 0.0 : inp_b[b_idx]);
            }
        }
        ```
    *   It calls `compile_and_load_kernel(code, device)` which uses NVIDIA's Runtime Compilation (NVRTC) via the `cudarc` crate to compile the generated CUDA C++ string into PTX assembly and load it onto the GPU. The resulting `CudaFunction` is stored within the operator struct.
*   **Elementwise Fusion Support:** These primitive ops implement the `custom("elementwise", ...)` method, returning a string representation of their operation (e.g., `"log2(input0)"`, `"input0 + input1"`). This allows the fusion compiler (Stage 3) to identify them.

### Stage 2: Specialized Op Compilers (`SpecialOpsCompiler`)

*   **Goal:** Identify specific graph patterns that can be replaced by highly optimized library calls or custom kernels, rather than relying on generic primitives or fusion.
*   **Mechanism:** This stage (itself a tuple of compilers like `MatMulCompiler`, `MeanReduceCompiler`, etc.) searches for predefined graph structures.
    *   **Example: `MatMulCompiler`:** It looks for a pattern like `Mul` followed by `SumReduce` with specific shape/broadcast characteristics.
*   **Replacement:** If a pattern is found, the matched nodes are replaced by a single, specialized CUDA op node (e.g., `Matmul`).
*   **Execution:** These specialized ops typically **do not** generate custom CUDA code. Instead, their `process` method directly calls optimized libraries.
    *   **Example: `Matmul::process`:** Extracts matrix dimensions (M, K, N), batch size, and data layout (row/column major) from the input `ShapeTracker`s. It then calls the appropriate `cuBLAS` function (`sgemm_strided_batched` or `hgemm_strided_batched`) with the correctly configured parameters (transpose flags, strides, pointers).

### Stage 3: Elementwise Fusion Compiler (`ElementwiseFusionCompiler`)

*   **Goal:** Reduce kernel launch overhead and improve memory access by fusing sequences of compatible elementwise operations into a single CUDA kernel.
*   **Mechanism:**
    *   Identifies chains of adjacent nodes where the source node provides the `"elementwise"` custom property (meaning it's a primitive like `CudaAdd`, `CudaLog2`, or potentially another `FusedElementwiseOp`).
    *   Performs validity checks (e.g., ensuring an intermediate node isn't used elsewhere, checking for complex view stacking with `is_more_than_one_view`).
    *   Combines the inputs of the fused nodes.
    *   Merges the logic by taking the string subexpressions from the involved ops and rewriting them using complex regex replacements to correctly reference combined inputs and intermediate values (e.g., `input1` in the second op might become `intermediate0` referencing the output of the first op).
*   **Kernel Generation:**
    *   A new `FusedElementwiseOp` node replaces the fused sequence. It stores the combined list of rewritten subexpression strings and the original `ShapeTracker`s for the *external* inputs to the fused group.
    *   During execution, the `FusedElementwiseOp::process` method generates the final, large CUDA kernel string. It iterates through the stored subexpressions, embedding the necessary CUDA C++ code for each primitive operation. Crucially, for each *load* from an original input tensor required by the fused expression, it retrieves the corresponding `ShapeTracker`, calls `get_idx_valid_exps` -> `expr_to_cuda_string` to get the CUDA indexing/validity code for that specific input, and embeds it in the kernel string.
    *   It calls `compile_and_load_kernel` to compile the generated fused kernel using NVRTC.
*   **Result:** A single kernel performs the work of multiple original operations.

### Stage 4: Other Compilers (e.g., `CopyCompiler`)

*   Handles specific tasks like ensuring data is correctly copied to/from the device using `CudaCopyToDevice` and `CudaCopyFromDevice` ops where needed.

## Symbolic Expression Handling Recap

The CUDA backend doesn't *perform* symbolic simplification (that's assumed to be done earlier using `egg`), but it heavily *consumes* the results:

1.  **Input:** Simplified `Expression` objects within `ShapeTracker`s.
2.  **Retrieval:** `shape.index_expression()` and `shape.valid_expression()` are called.
3.  **Conversion:** `expr_to_cuda_string(expr)` translates the (likely RPN) `Expression` terms into CUDA C++ code snippets. This function handles mapping variables ('z' -> `(int)idx`), numbers, and operators (`Max` -> `max()`, `Min` -> `min()`, Add -> `+`, etc.) into valid CUDA syntax.
4.  **Embedding:** These generated CUDA strings representing indexing and validity checks are embedded directly into the C++ kernel code strings generated by the primitive ops and the elementwise fusion compiler.

## Runtime Execution

After compilation, the graph contains CUDA-specific ops (`CudaAdd`, `Matmul`, `FusedElementwiseOp`, `CudaCopyToDevice`, etc.). Execution proceeds by calling the `process` method of each node:

1.  **Data Allocation:** Ops allocate necessary output buffers on the GPU (`device.alloc_zeros::<T>(...)`).
2.  **Kernel Launch / Library Call:**
    *   Ops with compiled `CudaFunction`s (primitives, fused ops) construct kernel parameters (buffer pointers, element counts, dynamic dimension values using `input_dyn_dims`). They launch the kernel using `function.launch(config, params)`.
    *   Specialized ops (`Matmul`) call the relevant library functions (`cuBLAS`).
    *   Copy ops transfer data between host and device.
3.  **Output:** Ops return `Tensor` objects wrapping the `CudaData` (GPU buffer).

## Kernel Compilation and Caching (NVRTC)

*   Luminal uses NVRTC via the `cudarc` crate (`compile_ptx_with_opts`) to compile generated CUDA C++ kernel strings to PTX assembly *at runtime*.
*   The `compile_and_load_kernel` function calculates a hash of the generated code string.
*   It checks if a function corresponding to that hash already exists on the device (`device.has_func`).
*   If not, it compiles the code and loads it (`device.load_ptx`). If it exists, the existing `CudaFunction` is retrieved.
*   This caching prevents recompiling identical kernels repeatedly, which is crucial for performance, especially when the same fused operation or primitive with the same indexing pattern appears multiple times.

## Conclusion

Luminal's CUDA backend employs a sophisticated, multi-stage compilation process. It strategically combines:

*   Runtime CUDA code generation using NVRTC for primitive and fused elementwise operations, directly embedding simplified symbolic indexing logic derived from `ShapeTracker`s.
*   Pattern matching to replace common complex operations with direct calls to optimized libraries like `cuBLAS`.
*   Kernel caching to minimize runtime compilation overhead.

This hybrid approach allows Luminal to generate efficient, specialized CUDA code tailored to the specific computational graph while leveraging highly optimized vendor libraries where appropriate, ultimately aiming for high execution performance. The consumption of simplified symbolic expressions is a critical enabler for generating efficient indexing logic within the custom kernels. 