# Luminal Symbolic Engine Documentation (`shape/symbolic.rs`)

## Introduction

Luminal's symbolic engine provides the foundation for representing and manipulating dimensions and indices that may not be known at compile time. It allows the `ShapeTracker` and the compiler to work with dynamic shapes effectively. The core components are the `Expression` struct, the `Term` enum, and the use of the `egg` library for simplification.

*   **Term Storage & Cleanup:** Uses `EXPRESSION_OWNER` thread-local `Owner<UnsyncStorage>` to manage `Vec<Term>` data; `expression_cleanup()` clears storage.
*   **GenerationalBox Internals:** RPN `Vec<Term>` sequences live in a `GenerationalBox`, providing cheap `Copy`-able handles with generational versioning.
*   **Rewrite Rule Set:** `make_rules()` in `symbolic.rs` defines algebraic identities, constant folding, and cancel rewrites; `simplify()` runs an `egg::Runner` with these rules and extracts minimal form via `Extractor`.
*   **Evaluation Engine:** `exec_stack()` uses a stack-based RPN algorithm with `Term::as_op`/`as_float_op`, returning `Option` to indicate errors (overflow, div-zero, missing var).
*   **Performance & Thread Safety:** `UnsyncStorage` allows lock-free, per-thread term storage; cloning `Expression` is O(1), and thread-local storage avoids synchronization.

## Core Concepts

### 1. `Term` Enum

Defines the atomic units (operators, variables, numbers) of an expression. It's a simple `Copy` enum.

```rust
pub enum Term {
    Num(i32),         // Constant integer
    Var(char),        // Symbolic variable (single character)
    Add, Sub, Mul,    // Arithmetic operators
    Div, Mod,         // Integer division and remainder
    Min, Max,         // Minimum and maximum
    And, Or,          // Logical AND and OR (treating non-zero as true)
    Gte, Lt,          // Greater-than-or-equal, Less-than
}
```

### 2. `Expression` Struct

This struct represents a symbolic expression using Reverse Polish Notation (RPN).

```rust
#[derive(Clone, Copy)]
pub struct Expression {
    pub terms: GenerationalBox<Vec<Term>>,
}
```

*   **Representation:** Holds a `GenerationalBox<Vec<Term>>`. The `Vec<Term>` stores the RPN sequence.
*   **Memory Management (`GenerationalBox`)**:
    *   `GenerationalBox` is provided by the `generational_box` crate, providing a form of safely managed shared, mutable memory. It stores the actual `Vec<Term>` data in a separate, thread-local storage managed by an `Owner<UnsyncStorage>`.
    *   The `Expression` struct itself only stores a handle (including reference information) to the data within the `GenerationalBox`.
    *   This allows multiple `Expression` instances to refer to the *same* underlying `Vec<Term>` data without deep copies.
    *   Memory associated with an `Expression`'s `terms` is reclaimed when the `Owner` is dropped or goes out of scope (managed via `expression_cleanup()` and the `thread_local!` mechanism).
*   **Ownership/Borrowing (`Copy` Trait)**:
    *   `Expression` implements `Copy`. Assigning or passing an `Expression` copies the handle, *not* the `Vec<Term>`. This is very cheap.
    *   Reading the terms requires a temporary borrow via `self.terms.read()`.
    *   Operations that build new expressions (operator overloads, `min`, `max`, etc.) create a *new* `Vec<Term>`, insert it into the `GenerationalBox` storage via the owner, and return a *new* `Expression` handle. They generally don't modify existing expressions in place.

### 3. Expression Creation & Operations

*   Built by combining `Expression` instances via overloaded operators or methods (`min`, `max`, etc.).
*   Operations typically involve cloning the `terms` vectors of operands, concatenating them, appending the operator `Term`, and creating a new `Expression` handle pointing to this new vector in the `GenerationalBox`.

### 4. Simplification (`Expression::simplify`)

*   **`egg` Library:** Simplification relies on the `egg` crate, an e-graph (equality saturation) library.
*   **Rewrite Rules:** Luminal defines a set of algebraic rewrite rules (e.g., `x * 0 => 0`, `x + 0 => x`, `min(x, MAX) => x`, constant folding) used by `egg`.
*   **Process:** The `simplify()` method converts the `Expression`'s RPN `terms` into `egg`'s internal representation (`RecExpr<Math>`), runs the e-graph saturation process with the defined rules, extracts the simplest representation found, and converts it back into an `Expression` (RPN `terms`).
*   **Cache:** A `simplify_cache()` method exists to memoize simplification results.

### 5. Evaluation (`Expression::exec_stack`)

*   Uses a stack-based RPN evaluation algorithm.
*   **Error Handling:** Returns `Option<usize>` (or `Option<f64>`). `None` is returned if a variable lookup fails or if a checked arithmetic operation (like division by zero or overflow) fails during evaluation.

### 6. Other Operations

*   **`substitute(var: char, expr: Expression)`**: Replaces occurrences of a variable `Term` with the `terms` of another `Expression`.
*   **`to_usize()`**: Attempts to evaluate the expression assuming it contains no variables. Returns `Option<usize>`.
*   **`to_symbols()`**: Extracts all unique variable `char`s present in the expression.
*   **`min()`, `max()`, `gte()`, `lt()`**: Methods for creating comparison/min/max expressions.

## Performance Characteristics

*   **Memory Footprint:**
    *   Each `Expression` instance is small (typically 8 bytes) since it only holds a handle to the actual data.
    *   The actual `Vec<Term>` data is stored in thread-local storage, with each `Term` being 1-2 bytes.
    *   This design optimizes for the common case of many `Expression` copies with relatively few distinct term vectors.

*   **Operation Costs:**
    *   **Copy/Clone:** O(1) - Just copying a handle, making `Expression` extremely cheap to pass around.
    *   **Reading Terms:** O(1) for access, actual read performance depends on vector size.
    *   **Binary Operations (add, mul, etc.):** O(n) where n is the combined size of operand term vectors.
    *   **Simplification:** Potentially expensive - O(n log n) to O(n²) depending on expression complexity and rule applicability.
    *   **Evaluation:** O(n) where n is the number of terms.
    *   **Substitution:** O(n) where n is the number of terms, but creates a new term vector.

*   **Optimization Strategies:**
    *   Early returns for common cases in binary operations (e.g., `x + 0 = x`, `x * 1 = x`).
    *   Eagerly evaluating constant expressions during construction (e.g., `2 + 3` becomes `5` immediately).
    *   Maintaining expressions in a semi-canonical form by commutative reordering (e.g., constants to the right).

*   **Thread Safety Considerations:**
    *   Each thread maintains its own storage via `thread_local!`, avoiding synchronization overhead.
    *   Thread-local design means expressions cannot be shared across threads without serialization.
    *   `expression_cleanup()` must be called explicitly to release memory within a thread.

### Simplification Caching Strategy

The caching mechanism for simplification is implemented via the `simplify_cache` method:

```rust
#[allow(clippy::mutable_key_type)]
pub fn simplify_cache(self, cache: &mut FxHashMap<Expression, Expression>) -> Self {
    if let Some(s) = cache.get(&self) {
        *s
    } else {
        let simplified = self.simplify();
        cache.insert(self, simplified);
        simplified
    }
}
```

*   **Cache Structure:**
    *   Uses `FxHashMap` (a Rust fast-hasher variant optimized for small keys) for O(1) lookups.
    *   Maps original expressions to their simplified forms.
    *   Explicitly allows "mutable key type" clippy warning because `Expression` can technically be mutated via its `GenerationalBox`.

*   **Caching Strategy:**
    *   **On Cache Hit:** Return the previously simplified expression directly, avoiding re-simplification.
    *   **On Cache Miss:** Perform simplification, store the result in the cache, then return it.
    *   Note that the cache is passed as a mutable reference, allowing it to be reused across multiple simplifications.

*   **Implementation Notes:**
    *   Cache is an external structure passed by the caller, not maintained by `Expression` itself.
    *   This allows different parts of the system to maintain separate caches with different lifetimes.
    *   For example, the compiler might use a long-lived cache for common expression patterns, while a specific operation could use a short-lived cache.

*   **Potential Issues:**
    *   Mutable key semantics could cause issues if expression content is modified after insertion.
    *   No automatic cache size management - growth depends entirely on caller behavior.
    *   No explicit cache invalidation mechanism if a simplification rule changes.

## Key Design Aspects Summary

*   **Representation:** RPN `Vec<Term>`.
*   **Memory:** `GenerationalBox` with thread-local owner for shared term vectors. `Expression` itself is `Copy`.
*   **Ownership:** Handles are copied; underlying data is shared via `GenerationalBox`. Operations create new expressions.
*   **Simplification:** Powerful `egg` library with optional caching layer.
*   **Evaluation:** Stack-based RPN returning `Option`.
*   **Thread Safety:** Uses thread-local storage for the `GenerationalBox` owner.
*   **Performance Tradeoffs:** Optimizes for fast copying and simple operations at the cost of potentially expensive simplification.
