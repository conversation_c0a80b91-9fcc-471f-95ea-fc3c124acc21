# Luminal API: Building Deep Learning Models

This document explains the high-level API provided by Luminal for building deep learning models. It details the available operations, how they're implemented in terms of low-level operations, and how to use the neural network modules provided by the `luminal_nn` crate.

## 1. Core Concepts

Luminal's API is built around a few central concepts:

* **Graph**: The computational graph that represents your model
* **GraphTensor**: A handle to a tensor within the graph
* **Module**: A reusable component that can be composed to build complex models
* **Compiler**: Transforms the graph for optimal execution

### 1.1. Lazy Graph Construction Model

Luminal uses a **lazy graph construction** approach. When you call operations on tensors, they don't execute immediately. Instead, they add nodes to the computational graph:

```rust
use luminal::prelude::*;

// Create a new computational graph
let mut cx = Graph::new();

// Create input tensors - this doesn't allocate memory yet,
// it just creates placeholder nodes in the graph
let x = cx.tensor((batch_size, input_dim));
let y = cx.tensor((batch_size, input_dim));

// Add an operation to the graph - this doesn't perform the addition yet,
// it just creates a node in the graph that represents the addition
let z = x + y;

// The graph now contains three nodes (two inputs and one add operation)
// but no computation has happened yet

// Mark output for retrieval
let mut result = z.retrieve();

// This is when the actual computation happens
cx.execute();

// Now we can access the computed results
let output_data = result.data();
```

This lazy construction approach allows Luminal to:
1. Analyze the entire computation before execution
2. Optimize the graph as a whole rather than individual operations
3. Efficiently allocate memory for intermediate results
4. Enable automatic differentiation by constructing the backward graph

### 1.2. Graph Tensors and References

Graph tensors are symbolic references to tensors in the graph, not actual data:

```rust
// GraphTensor is a lightweight handle that references a node in the graph
// and holds a shape tracker for tensor dimensions
pub struct GraphTensor {
    id: NodeIndex,
    shape: ShapeTracker,
    graph_ref: *mut Graph,
}
```

Operations on `GraphTensor` objects create new nodes in the graph and return new `GraphTensor` handles:

```rust
// When you add two GraphTensors:
let c = a + b;

// What happens internally:
// 1. A new Add operation node is created in the graph
// 2. Edges are added from 'a' and 'b' nodes to this new Add node
// 3. A new GraphTensor referencing this Add node is returned
```

## 2. High-Level Operations: Implementation and Execution

### 2.1. Operation Implementation

High-level operations in Luminal are implemented as methods on the `GraphTensor` struct, organized in modules like `binary.rs`, `unary.rs`, `matmul.rs`, etc. These methods manipulate the computational graph by adding operation nodes and connecting them appropriately.

For example, examining the implementation of matrix multiplication in `hl_ops/matmul.rs`:

```rust
pub fn matmul(self, rhs: GraphTensor) -> GraphTensor {
    // Handle 1D and 2D tensor multiplication
    if (self.shape.len() == 1 || self.shape.len() == 2) && rhs.shape.len() == 2 {
        let vec = self.shape.len() == 1;
        // If vector, expand to matrix with 1 row
        let mut self_tensor = if vec {
            self.expand(0, 1)
        } else {
            self
        };
        
        let (m, _) = self_tensor.dims2();
        let (_, n) = rhs.dims2();
        
        // Broadcast and multiply element-wise
        let mul = self_tensor.expand(1, n) * rhs.permute((1, 0)).expand(0, m);

        // Sum reduce to get matrix multiplication
        let mut ret = mul.sum_reduce(2);
        
        // Convert back to vector if input was vector
        if vec {
            ret = ret.reshape(ret.dims().last().unwrap());
        }
        ret
    } else {
        // Additional handling for 3D, 4D, and 5D tensors...
    }
}
```

This shows how matrix multiplication is actually implemented by decomposing it into expansion, permutation, element-wise multiplication, and sum reduction operations.

#### 2.1.2 Binary Operations

Located in `src/hl_ops/binary.rs`, binary operations on `GraphTensor` implement `Add`, `Sub`, `Mul`, `Div`, `Rem` (and their assign variants). Each op emits a corresponding graph node:

```rust
impl Add for GraphTensor {
    type Output = GraphTensor;
    fn add(self, rhs: GraphTensor) -> Self::Output {
        let new_id = self.graph().add_op(op::Add)
            .input(self.id, 0, self.shape)
            .input(rhs.id, 0, rhs.shape)
            .finish();
        GraphTensor::from_id(new_id, self.shape.contiguous(), self.graph_ref)
    }
}
```

Scalar and `Into<Expression>` overloads enable adding constants or complex expressions.

#### 2.1.3 Movement and Reshaping

In `src/hl_ops/movement.rs`, these methods update the `ShapeTracker` to change tensor layout without heavy computation (except for ops like `contiguous`, `slice`, `pad`, and `pool_last_dim` which insert nodes):

- `permute(axes)`: reorder dimensions.
- `expand(axis, size)` / `expand_to(shape)`: broadcast tensor.
- `reshape(new_shape)`: inserts a `Contiguous` op and updates shape.
- `slice(...)`, `pad(...)`, `excise(...)`: modify data layout.
- `concat_along(axis)`: concatenate along an axis.
- `pool_last_dim(...)`: sliding-window pooling creating a new dimension.

```rust
pub fn reshape(mut self, new_shape: impl ToShape) -> GraphTensor {
    self = self.contiguous();
    self.shape = ShapeTracker::new(new_shape);
    self
}
```

#### 2.1.4 Reduction Operations

Implemented in `src/hl_ops/reduction.rs`, reduction methods chain `SumReduce`, `MaxReduce`, etc., across specified axes:

- `sum_reduce(axes)`, `max_reduce(axes)`
- `mean_reduce(axes)`: divides by element count then sums.
- `prod_reduce(axes)`: logs, sums, and exponentiates.

```rust
pub fn sum_reduce(self, axes: impl ToAxes) -> GraphTensor { /* ... */ }
```

#### 2.1.5 Other Utilities

`src/hl_ops/other.rs` provides:

- Cumulative operations: `cumsum_last_dim`, `cummax_last_dim`, `cumprod_last_dim`.
- Graph constructors: `Graph::constant`, `Graph::arange`, `Graph::tril`, `Graph::triu`.
- Misc: `gather`, `print`, `diff`.

```rust
pub fn arange(&mut self, to: impl Into<Expression>) -> GraphTensor { /* ... */ }
```

### 2.2. Tensor Execution Process

When `execute()` is called on a graph, the following steps occur:

1. **Topological Sort**: The graph is sorted in topological order to determine execution sequence
2. **Tensor Preparation**: For each node, input tensors are gathered
3. **Memory Management**: Input tensors are provided as either owned or borrowed based on their usage pattern
4. **Dynamic Dimension Resolution**: Shape dimensions are resolved
5. **Operator Execution**: Each operator's `process()` method is called with its input tensors
6. **Tensor Reference Tracking**: The graph tracks tensor usage to determine when memory can be freed
7. **Cleanup**: After execution, temporary tensors are removed unless marked to be kept

### 2.3. Memory Management During Execution

Luminal uses a memory management strategy that includes:

1. **Tensor Lifetime Tracking**: Tracking when tensors are no longer needed
2. **Owned vs Borrowed Access**: Determining whether a tensor is passed by value or reference
3. **Explicit Tensor Preservation**: Using `keep()` to prevent automatic cleanup
4. **Retrieval Marking**: Using `retrieve()` to access tensors after execution
5. **Automatic Cleanup**: Removing tensors after their last use

```rust
// Mark a tensor to be kept (not deleted during execution)
pub fn keep(self) -> Self {
    self.graph().keep_tensors(self.id);
    self
}

// Mark a tensor for retrieval after execution
pub fn retrieve(self) -> Self {
    self.keep();
    self.graph().to_retrieve.insert(self.id, (0, self.shape));
    self
}
```

### 2.4. Complex Operations Implementation

Complex operations like matrix multiplication and convolutions in Luminal are implemented by decomposing them into simpler primitive operations.

#### Matrix Multiplication

As shown in the actual code in `hl_ops/matmul.rs`, matrix multiplication is implemented differently depending on the tensor dimensions:

```rust
// For 1D/2D tensors
// Broadcast, multiply element-wise, then sum reduce
let mul = self.expand(1, n) * rhs.permute((1, 0)).expand(0, m);
let ret = mul.sum_reduce(2);

// For 3D tensors (batched)
// Reshape to 2D, perform matrix multiplication, reshape back
let (b, m, k) = self.dims3();
let (_, _, n) = rhs.dims3();
let lhs_flat = self.reshape((b * m, k));
let rhs_flat = rhs.reshape((b * k, n));
let ret_flat = lhs_flat.matmul(rhs_flat);
ret_flat.reshape((b, m, n))
```

#### Convolution Operations

Based on `luminal_nn/src/convolution.rs`, convolutions are implemented using pooling and matrix multiplication:

```rust
// From Conv2D::forward
// Pool input to extract windows for convolution
let input_pooled = input
    .pool_last_dim(self.kernel.1, self.stride.1, self.dilation.1)
    .permute((0, 1, 3, 4, 2))
    .pool_last_dim(self.kernel.0, self.stride.0, self.dilation.0)
    .permute((0, 1, 5, 3, 4, 2))
    .reshape((
        batch,
        self.ch_in * self.kernel.0 * self.kernel.1,
        dimx_out * dimy_out,
    ));

// Matrix multiply with weights
let mut output = self.weight.expand(0, batch).matmul(input_pooled).reshape((
    batch,
    self.ch_out,
    dimx_out,
    dimy_out,
));

// Add bias if present
if let Some(bias) = &self.bias {
    output = output + bias.expand(0, batch).expand(2, dimx_out).expand(3, dimy_out);
}
```

This implementation effectively converts convolution into a matrix multiplication problem after extracting windows of the input through pooling operations.

## 3. Neural Network Building Blocks

Luminal's neural network modules are built on top of these high-level operations, implementing the `Module` trait.

### 3.1. The Module Trait

All neural network components implement the `Module` trait:

```rust
pub trait Module<Input> {
    type Output;
    
    fn forward(&self, input: Input) -> Self::Output;
}
```

### 3.2. Module Implementation Details

Looking at the actual implementation of `Linear` in `luminal_nn/src/linear.rs`:

```rust
pub struct Linear {
    pub weight: GraphTensor,
    pub bias: Option<GraphTensor>,
    permute: bool,
}

impl Linear {
    pub fn new(inp: usize, out: usize, bias: bool, cx: &mut Graph) -> Self {
        Self {
            weight: cx.named_tensor("Weight", (inp, out)),
            bias: if bias {
                Some(cx.named_tensor("Bias", out))
            } else {
                None
            },
            permute: false,
        }
    }
}

impl Module<GraphTensor> for Linear {
    type Output = GraphTensor;

    fn forward(&self, input: GraphTensor) -> Self::Output {
        let mut output = input.matmul(if self.permute {
            self.weight.permute((1, 0))
        } else {
            self.weight
        });
        if let Some(bias) = self.bias {
            output += bias.expand_to(output.shape);
        }
        output
    }
}
```

The implementation is straightforward, using matrix multiplication and addition operations to implement a linear layer.

### 3.3. Module Registration and Serialization

Modules implement the `SerializeModule` trait for serialization:

```rust
impl SerializeModule for Linear {
    fn serialize(&self, s: &mut luminal::module::Serializer) {
        s.tensor("weight", self.weight);
        if let Some(bias) = self.bias {
            s.tensor("bias", bias);
        }
    }
}
```

## 4. Available Neural Network Components

**Note:** All neural network components below are provided by the separate `luminal_nn` crate (see `crates/luminal_nn`).

### 4.1. Basic Layers

- **Linear**: Fully connected layer with optional bias
- **Embedding**: Lookup table for embeddings

### 4.2. Activation Functions

Based on `luminal_nn/src/activation.rs` and `src/hl_ops/unary.rs`, the following activation functions are implemented:

- **ReLU**: Rectified Linear Unit
- **Sigmoid**: Sigmoid activation
- **Tanh**: Hyperbolic tangent
- **GELU**: Gaussian Error Linear Unit
- **Softmax**: Softmax normalization
- **Leaky ReLU**: ReLU with a small gradient for negative inputs
- **Swish**: Self-gated activation function

### 4.3. Convolution Layers

Based on `luminal_nn/src/convolution.rs`, Luminal implements:

- **Conv1D**: 1D convolution with configurable stride, dilation, and padding
- **Conv2D**: 2D convolution with configurable stride, dilation, and padding
- **Conv3D**: 3D convolution with configurable stride, dilation, and padding

### 4.4. Normalization Layers

Based on `luminal_nn/src/norm.rs` and `src/hl_ops/unary.rs`:

- **LayerNorm**: Layer normalization

### 4.5. Transformer Components

Based on the `luminal_nn/src/transformer` directory:

- **TransformerEncoder**: Transformer encoder
- **TransformerDecoder**: Transformer decoder
- **Transformer**: Complete transformer with encoder and decoder
- **MultiHeadAttention**: Multi-head attention mechanism

## 5. Building Complex Models

Luminal's design allows building sophisticated models by combining modules:

```rust
struct TransformerLM {
    embedding: Embedding,
    transformer: TransformerEncoder,
    lm_head: Linear,
}

impl Module<GraphTensor> for TransformerLM {
    type Output = GraphTensor;
    
    fn forward(&self, input: GraphTensor) -> Self::Output {
        // Get embeddings for input tokens
        let embedded = self.embedding.forward(input);
        
        // Apply transformer encoder
        let transformed = self.transformer.forward(embedded);
        
        // Project to vocabulary size for language modeling
        self.lm_head.forward(transformed)
    }
}
```

## 6. Execution and Compilation

After defining your model and graph, you can execute it:

```rust
// Mark the output for retrieval
let mut output = model.forward(input).retrieve();

// Execute the graph (unoptimized)
cx.execute();

// Compile the graph with a specific compiler
cx.compile(GenericCompiler::default(), &mut output);

// Execute the optimized graph
cx.execute();

// Access the results
let result = output.data();
```

## Conclusion

Luminal provides a flexible API for building deep learning models that balances high-level abstractions with fine-grained control. The API is designed to be intuitive while leveraging Rust's type system for additional safety and clarity.

The underlying implementation efficiently translates high-level operations into a computational graph that can be optimized and executed through the compiler system. 