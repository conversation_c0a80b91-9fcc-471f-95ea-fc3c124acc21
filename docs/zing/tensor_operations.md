# Tensor Operations Documentation (v2)

## Overview

The tensor operations module in `src/tensor/` provides a comprehensive, high-level tensor computation API built on top of the core computation graph engine. It implements a dual-interface design pattern with both functional and object-oriented APIs, following Luminal-style design principles where view operations are pure metadata transformations.

**Key Features:**
- **Full Symbolic Dimension Support**: Create and manipulate tensors with unknown dimensions
- **Type-Safe ID System**: Uses strongly-typed `NodeId`, `ViewId`, `ShapeId`, and `ExprId`
- **Luminal Compatibility**: Direct decomposition to Luminal primitive operations
- **Flexible APIs**: Both functional and object-oriented interfaces

## Architecture

### Core Components

```
tensor/
├── types.zig         - Type definitions and aliases
├── tensor.zig        - Core Tensor struct and OO interface
├── creation.zig      - Tensor creation from data and patterns
├── manipulation.zig  - Shape manipulation and view operations
├── pointwise.zig     - Element-wise operations and activations
├── linalg.zig        - Linear algebra operations
├── reduction.zig     - Reduction operations (sum, mean, etc.)
├── utils.zig         - Utility functions and helpers
├── errors.zig        - Tensor-specific error definitions
└── mod.zig          - Module interface and re-exports
```

### Design Principles

- **Luminal-Style Views**: View operations modify metadata only, no data copying
- **Primitive Decomposition**: Complex operations decomposed into graph primitives
- **Dual Interface**: Both functional and object-oriented APIs
- **Type Safety**: Strong typing with enum-based IDs
- **Arena Memory**: All allocations use core's arena allocator

## Core Data Structures

### Type Aliases (types.zig)

```zig
// Re-exported core types for consistency
pub const NodeId = core_types.NodeId;      // Computation graph node
pub const ViewId = core_types.ViewId;      // Shape view reference  
pub const ShapeId = core_types.ShapeId;    // Shape definition
pub const ExprId = core_types.ExprId;      // Symbolic expression

// Tensor is just a node ID alias
pub const TensorId = NodeId;

// Unified error handling
pub const ZingError = core.errors.ZingError || TensorError;
```

### Tensor Struct (tensor.zig)

Main object-oriented interface:

```zig
pub const Tensor = struct {
    ctx: *core.Core,    // Reference to computation context
    id: NodeId,         // Node identifier in the graph
    
    // Factory methods
    pub fn constant(ctx: *core.Core, data: anytype) !Tensor
    pub fn zeros(ctx: *core.Core, shape: []const *types.Expr) !Tensor
    pub fn ones(ctx: *core.Core, shape: []const *types.Expr) !Tensor
    
    // Shape operations (return same tensor, modified view)
    pub fn reshape(self: Tensor, new_shape: []const *types.Expr) !Tensor
    pub fn transpose(self: Tensor, dim0: ?i64, dim1: ?i64) !Tensor
    pub fn permute(self: Tensor, dims: []const i32) !Tensor
    pub fn squeeze(self: Tensor, dims: ?[]const i64) !Tensor
    pub fn unsqueeze(self: Tensor, dim: i32) !Tensor
    
    // Computation operations (create new graph nodes)
    pub fn add(self: Tensor, other: Tensor) !Tensor
    pub fn multiply(self: Tensor, other: Tensor) !Tensor
    pub fn matmul(self: Tensor, other: Tensor) !Tensor
    pub fn relu(self: Tensor) !Tensor
    pub fn softmax(self: Tensor, dim: i64) !Tensor
    
    // Utilities
    pub fn shape(self: Tensor) ![]const *types.Expr
    pub fn dtype(self: Tensor) DataType
    pub fn ndim(self: Tensor) !usize
};
```

### TensorBuilder (tensor.zig)

Provides error-safe method chaining:

```zig
pub const TensorBuilder = struct {
    tensor: ?Tensor,
    
    pub fn from(tensor: Tensor) TensorBuilder
    pub fn add(self: *TensorBuilder, other: Tensor) *TensorBuilder
    pub fn multiply(self: *TensorBuilder, other: Tensor) *TensorBuilder
    pub fn relu(self: *TensorBuilder) *TensorBuilder
    pub fn build(self: TensorBuilder) !Tensor
};
```

### Error Types (errors.zig)

```zig
pub const TensorError = error{
    // Validation errors
    InvalidTensor,
    InvalidDimensions,
    InvalidShape,
    IncompatibleShapes,
    
    // Operation errors  
    UnsupportedDataType,
    UnsupportedOperation,
    ShapeMismatch,
    BroadcastFailure,
    IncompatibleShapesForExpand,
    CanOnlyExpandDimensionsOfSize1,
    
    // Linear algebra errors
    MatmulRequires2DTensors,
    BmmRequires3DTensors,
    IncompatibleDimensionsForMatmul,
    
    // Implementation gaps
    SymbolicDimsNotYetSupported,
    OperationNotImplemented,
};
```

## Public API Reference

### Creation Operations (creation.zig)

#### Tensor Creation from Data

```zig
// Create tensor from compile-time known data with automatic shape inference
pub fn constant(c: *Core, data: anytype) !NodeId

// Usage examples:
const scalar = try constant(ctx, 42.0);           // Shape: []
const vector = try constant(ctx, [_]f32{1,2,3});  // Shape: [3]  
const matrix = try constant(ctx, [_][_]f32{       // Shape: [2,3]
    [_]f32{1,2,3},
    [_]f32{4,5,6}
});
const tensor_3d = try constant(ctx, [_][_][_]f32{ // Shape: [2,2,2]
    [_][_]f32{
        [_]f32{1,2}, [_]f32{3,4}
    },
    [_][_]f32{
        [_]f32{5,6}, [_]f32{7,8}
    }
});
```

#### Shape-Based Creation

```zig
// Create zeros/ones tensors with given shape
pub fn zeros(c: *Core, shape: []const *types.Expr) !NodeId {
    const shape_id_raw = try c.shape.newShape(shape);
    const view_id_raw = try c.shape.newDefaultView(shape_id_raw);
    
    const dtype = DataType.f32;
    const view_id = @as(core_types.ViewId, @enumFromInt(view_id_raw));
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store zeros pattern for this node
    try c.data.setConstantPattern(@intFromEnum(node_id), data_module.DataStore.Pattern.zeros);
    
    return node_id;
}

pub fn ones(c: *Core, shape: []const *types.Expr) !NodeId  // Similar with .ones pattern
pub fn full(c: *Core, shape: []const *types.Expr, value: f32) !NodeId  // Uses setConstantFull

// Utility tensors
pub fn eye(c: *Core, n: usize, dtype: DataType) !NodeId  // Uses .identity pattern
pub fn linspace(c: *Core, start: f32, stop: f32, num: usize) !NodeId  // Calculates values
pub fn arange(c: *Core, start: f32, stop: f32, step: f32) !NodeId  // Calculates range

// Also exported from mod.zig: randn, identity (alias for eye)

// Usage examples:
const batch = try ctx.symbolic.newIntegerExpr(32);
const seq_len = try ctx.symbolic.newIntegerExpr(128);
const hidden = try ctx.symbolic.newIntegerExpr(768);

const zeros_tensor = try zeros(ctx, &[_]*types.Expr{batch, seq_len, hidden});
const identity = try eye(ctx, 768, .f32);
const range_tensor = try arange(ctx, 0.0, 100.0, 1.0);  // [0, 1, 2, ..., 99]
```

#### Symbolic Tensor Creation

Create tensors with dimensions unknown at compile time:

```zig
// Create fully symbolic tensor
pub fn placeholder(c: *Core, dtype: DataType, shape: []const []const u8) !NodeId {
    var dims = try c.arena.allocator().alloc(*shape_engine_types.Expr, shape.len);
    for (shape, 0..) |dim_name, i| {
        dims[i] = try c.symbolic.newSymbolExpr(dim_name);
    }
    
    const shape_id = try c.shape.newShape(dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    return c.graph.newNodeInput(view_id);
}

// Create with mixed symbolic/concrete dimensions
pub fn placeholderMixed(c: *Core, dtype: DataType, dims: []const *types.Expr) !NodeId {
    const shape_id = try c.shape.newShape(dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    return c.graph.newNodeInput(view_id);
}

// Usage examples:
// Fully symbolic shape
const input = try placeholder(ctx, .f32, &[_][]const u8{"batch", "seq_len", "hidden"});

// Mixed symbolic/concrete shape
const batch_sym = try ctx.symbolic.newSymbolExpr("batch");
const mixed = try placeholderMixed(ctx, .f32, &[_]*types.Expr{
    batch_sym,
    try ctx.symbolic.newIntegerExpr(512),
    try ctx.symbolic.newIntegerExpr(768),
});
```

#### Variable and Placeholder Creation

```zig
// Trainable parameters
pub fn variable(c: *Core, shape: []const *core_types.Expr, dtype: DataType) !NodeId
pub fn variableWithData(c: *Core, data: anytype) !NodeId

// Input placeholders
pub fn placeholder(c: *Core, shape: []const *core_types.Expr, dtype: DataType) !NodeId

// Symbolic dimension specification
pub const DimSpec = struct { 
    size: ?usize,        // null means symbolic
    symbol: ?[]const u8  // optional symbol name
};
pub fn placeholderSymbolic(c: *Core, shape_spec: []const DimSpec, dtype: DataType) !NodeId

// Usage examples:
const weight = try variable(ctx, &[_]*types.Expr{hidden, hidden}, .f32);
const bias = try variableWithData(ctx, [_]f32{0.1, 0.2, 0.3});

const input_placeholder = try placeholderSymbolic(ctx, &[_]DimSpec{
    .{ .size = null, .symbol = "batch" },     // Symbolic batch dimension
    .{ .size = 128, .symbol = null },         // Fixed sequence length
    .{ .size = 768, .symbol = null },         // Fixed hidden dimension
}, .f32);
```

### Manipulation Operations (manipulation.zig)

#### Pure View Operations (No Graph Nodes Created)

Following Luminal principles, these operations only modify view metadata:

```zig
// Shape transformations
pub fn reshape(ctx: *Core, a: NodeId, shape: []const i64) !NodeId
pub fn flatten(ctx: *Core, a: NodeId) !NodeId
pub fn squeeze(ctx: *Core, a: NodeId, dims: ?[]const i64) !NodeId
pub fn unsqueeze(ctx: *Core, a: NodeId, dim: i32) !NodeId

// Dimension reordering
pub fn transpose(ctx: *Core, a: NodeId, dim0: ?i64, dim1: ?i64) !NodeId
pub fn permute(ctx: *Core, a: NodeId, dims: []const i32) !NodeId

// Broadcasting and slicing
pub fn expand(ctx: *Core, a: NodeId, shape: []const i64) !NodeId
pub fn broadcastTo(ctx: *Core, a: NodeId, shape: []const i64) !NodeId
pub fn slice(ctx: *Core, a: NodeId, start: []const i64, end: []const i64) !NodeId
pub fn view(ctx: *Core, a: NodeId, shape: []const i64) !NodeId

// Low-level view manipulation
pub fn as_strided(ctx: *Core, a: NodeId, size: []const i64, 
                  strides: []const i64, offset: i64) !NodeId
```

**Key Characteristic**: These operations return the **same NodeId** with modified view metadata:

```zig
pub fn reshape(ctx: *Core, a: NodeId, shape: []const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a_raw);
    const current_view_id = @intFromEnum(node.output_view_id);
    
    // Create new view with different shape
    const new_view_id = try ctx.shape.newReshapedView(current_view_id, new_shape_id);
    
    // Update node's view - NO NEW NODE CREATED
    node.output_view_id = viewIdFromU32(new_view_id);
    
    return a; // Return same NodeId
}
```

#### Computational Operations (Create Graph Nodes)

```zig
// Multi-tensor operations
pub fn concat(ctx: *Core, tensors: []const NodeId, dim: i64) !NodeId
pub fn stack(ctx: *Core, tensors: []const NodeId, dim: i64) !NodeId

// Splitting operations  
pub fn split(ctx: *Core, a: NodeId, split_size: i64, dim: i64) ![]NodeId
pub fn splitWithSizes(ctx: *Core, a: NodeId, split_sizes: []const i64, dim: i64) ![]NodeId
pub fn unbind(ctx: *Core, a: NodeId, dim: i64) ![]NodeId
pub fn chunk(ctx: *Core, a: NodeId, chunks: i64, dim: i64) ![]NodeId

// Indexing and selection
pub fn select(ctx: *Core, a: NodeId, dim: i64, index: i64) !NodeId
pub fn indexSelect(ctx: *Core, a: NodeId, dim: i64, indices: NodeId) !NodeId
pub fn gather(ctx: *Core, a: NodeId, dim: i64, indices: NodeId) !NodeId
pub fn scatter(ctx: *Core, a: NodeId, dim: i64, indices: NodeId, src: NodeId) !NodeId

// Materialization (the only view -> computation conversion)
pub fn contiguous(ctx: *Core, a: NodeId) !NodeId
```

#### Usage Examples

```zig
// Pure view operations (no computation)
const reshaped = try reshape(ctx, input, &[_]i64{32, -1});  // Flatten last dims
const transposed = try transpose(ctx, matrix, 0, 1);        // Swap first two dims
const expanded = try expand(ctx, vector, &[_]i64{10, -1});  // Broadcast to [10, N]
const sliced = try slice(ctx, tensor, &[_]i64{0, 5}, &[_]i64{10, 15}); // [0:10, 5:15]

// Computational operations (create nodes)
const concatenated = try concat(ctx, &[_]NodeId{a, b, c}, 1);  // Concat along dim 1
const chunks = try chunk(ctx, tensor, 4, 0);                  // Split into 4 chunks
const selected = try select(ctx, tensor, 1, 5);               // Select index 5 on dim 1
```

#### Symbolic Dimension Support in Shape Operations

All shape operations work seamlessly with symbolic dimensions:

```zig
// Create tensor with symbolic shape
const batch_sym = try ctx.symbolic.newSymbolExpr("batch");
const input = try zeros(ctx, &[_]*types.Expr{
    batch_sym,
    try ctx.symbolic.newIntegerExpr(128),
    try ctx.symbolic.newIntegerExpr(768),
});

// Reshape with symbolic dimensions
pub fn reshapeSymbolic(ctx: *Core, a: NodeId, new_dims: []const *types.Expr) !NodeId {
    const shape_id = try ctx.shape.newShape(new_dims);
    const node = try ctx.graph.getNodeMut(a);
    const current_view_id = node.output_view_id;
    const new_view_id = try ctx.shape.newReshapedView(current_view_id, shape_id);
    node.output_view_id = new_view_id;
    return a;
}

// Example: Reshape [batch, 128, 768] to [batch, -1]
const flattened = try reshapeSymbolic(ctx, input, &[_]*types.Expr{
    batch_sym,
    try ctx.symbolic.newIntegerExpr(128 * 768),
});

// Broadcasting with symbolic dimensions
const expanded = try expandSymbolic(ctx, input, &[_]*types.Expr{
    batch_sym,
    try ctx.symbolic.newIntegerExpr(10),
    try ctx.symbolic.newIntegerExpr(128),
    try ctx.symbolic.newIntegerExpr(768),
});
```

### Pointwise Operations (pointwise.zig)

#### Unary Operations

```zig
// Arithmetic
pub fn neg(ctx: *Core, a: NodeId) !NodeId        // -a
pub fn abs(ctx: *Core, a: NodeId) !NodeId        // |a|
pub fn sqrt(ctx: *Core, a: NodeId) !NodeId       // √a
pub fn recip(ctx: *Core, a: NodeId) !NodeId      // 1/a

// Exponential and logarithmic
pub fn exp(ctx: *Core, a: NodeId) !NodeId        // e^a
pub fn log(ctx: *Core, a: NodeId) !NodeId        // ln(a)
pub fn exp2(ctx: *Core, a: NodeId) !NodeId       // 2^a
pub fn log2(ctx: *Core, a: NodeId) !NodeId       // log₂(a)

// Trigonometric
pub fn sin(ctx: *Core, a: NodeId) !NodeId        // sin(a)
pub fn cos(ctx: *Core, a: NodeId) !NodeId        // cos(a)
pub fn tan(ctx: *Core, a: NodeId) !NodeId        // tan(a)
```

#### Binary Operations

```zig
// Basic arithmetic
pub fn add(ctx: *Core, a: NodeId, b: NodeId) !NodeId     // a + b
pub fn sub(ctx: *Core, a: NodeId, b: NodeId) !NodeId     // a - b
pub fn mul(ctx: *Core, a: NodeId, b: NodeId) !NodeId     // a * b
pub fn div(ctx: *Core, a: NodeId, b: NodeId) !NodeId     // a / b
pub fn mod(ctx: *Core, a: NodeId, b: NodeId) !NodeId     // a % b
pub fn pow(ctx: *Core, a: NodeId, b: NodeId) !NodeId     // a^b

// Comparison operations
pub fn less_than(ctx: *Core, a: NodeId, b: NodeId) !NodeId      // a < b
pub fn greater_than(ctx: *Core, a: NodeId, b: NodeId) !NodeId   // a > b
pub fn equals(ctx: *Core, a: NodeId, b: NodeId) !NodeId         // a == b

// Element-wise min/max
pub fn minimum(ctx: *Core, a: NodeId, b: NodeId) !NodeId        // min(a, b)
pub fn maximum(ctx: *Core, a: NodeId, b: NodeId) !NodeId        // max(a, b)
```

#### Activation Functions

```zig
// Standard activations
pub fn sigmoid(ctx: *Core, a: NodeId) !NodeId                   // 1/(1+e^(-a))
pub fn tanh(ctx: *Core, a: NodeId) !NodeId                      // (e^(2a)-1)/(e^(2a)+1)
pub fn relu(ctx: *Core, a: NodeId) !NodeId                      // max(0, a)
pub fn leaky_relu(ctx: *Core, a: NodeId, negative_slope: f32) !NodeId  // max(α*a, a)

// Advanced activations
pub fn gelu(ctx: *Core, a: NodeId) !NodeId                      // GELU activation
pub fn fast_gelu(ctx: *Core, a: NodeId) !NodeId                 // Fast GELU approximation
pub fn swish(ctx: *Core, a: NodeId) !NodeId                     // a * sigmoid(a)
pub fn silu(ctx: *Core, a: NodeId) !NodeId                      // Same as swish
pub fn softplus(ctx: *Core, a: NodeId) !NodeId                  // ln(1 + e^a)
```

#### Normalization Operations

```zig
// Layer normalization
pub fn layer_norm(ctx: *Core, a: NodeId, normalized_shape: []const usize, eps: f32) !NodeId

// RMS normalization  
pub fn rms_norm(ctx: *Core, a: NodeId, weight: NodeId, eps: f32) !NodeId

// Softmax operations
pub fn softmax(ctx: *Core, a: NodeId, dim: i64) !NodeId
pub fn log_softmax(ctx: *Core, a: NodeId, dim: i64) !NodeId
```

**Decomposition Examples:**

```zig
// Sigmoid: 1 / (1 + exp(-x))
pub fn sigmoid(ctx: *Core, a: NodeId) !NodeId {
    const neg_a = try neg(ctx, a);
    const exp_neg_a = try exp(ctx, neg_a);
    const one_plus_exp = try add(ctx, one_constant, exp_neg_a);
    return try recip(ctx, one_plus_exp);
}

// GELU: 0.5 * x * (1 + tanh(√(2/π) * (x + 0.044715 * x³)))
pub fn gelu(ctx: *Core, a: NodeId) !NodeId {
    const x_cubed = try pow(ctx, a, three_constant);
    const scaled_x_cubed = try mul(ctx, coeff_constant, x_cubed);
    const inner = try add(ctx, a, scaled_x_cubed);
    const scaled_inner = try mul(ctx, sqrt_2_pi_constant, inner);
    const tanh_result = try tanh(ctx, scaled_inner);
    const one_plus_tanh = try add(ctx, one_constant, tanh_result);
    const half_x = try mul(ctx, half_constant, a);
    return try mul(ctx, half_x, one_plus_tanh);
}
```

### Linear Algebra Operations (linalg.zig)

```zig
// Matrix multiplication (2D tensors: [M,K] × [K,N] -> [M,N])
pub fn matmul(ctx: *Core, a: NodeId, b: NodeId) !NodeId

// Batch matrix multiplication (3D tensors: [B,M,K] × [B,K,N] -> [B,M,N])  
pub fn bmm(ctx: *Core, a: NodeId, b: NodeId) !NodeId

// Dot product (1D tensors: [N] · [N] -> scalar)
pub fn dot(ctx: *Core, a: NodeId, b: NodeId) !NodeId

// Matrix transpose (2D only: [M,N] -> [N,M])
pub fn transpose(ctx: *Core, a: NodeId) !NodeId
```

**Matrix Multiplication Decomposition:**

```zig
// matmul(A[M,K], B[K,N]) -> [M,N] using broadcasting and reduction
pub fn matmul(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // 1. Transpose B: [K,N] -> [N,K]
    const b_t = try manipulation.transpose(ctx, b, 0, 1);
    
    // 2. Expand A: [M,K] -> [M,1,K]
    const a_exp = try manipulation.unsqueeze(ctx, a, 1);
    
    // 3. Expand B_t: [N,K] -> [1,N,K]  
    const b_t_exp = try manipulation.unsqueeze(ctx, b_t, 0);
    
    // 4. Element-wise multiply: [M,1,K] * [1,N,K] -> [M,N,K]
    const mul = try pointwise.mul(ctx, a_exp, b_t_exp);
    
    // 5. Sum along K dimension: [M,N,K] -> [M,N]
    return try reduction.sum(ctx, mul, &[_]i32{-1}, false);
}
```

### Reduction Operations (reduction.zig)

```zig
// Statistical reductions
pub fn sum(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId
pub fn mean(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId
pub fn prod(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId

// Min/max operations
pub fn max(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId
pub fn min(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId
pub fn argmax(ctx: *Core, a: NodeId, dim: i32, keepdim: bool) !NodeId
pub fn argmin(ctx: *Core, a: NodeId, dim: i32, keepdim: bool) !NodeId

// Statistical measures
pub fn var(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool, unbiased: bool) !NodeId
pub fn std(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool, unbiased: bool) !NodeId
```

**Usage Examples:**

```zig
// Sum all elements: [M,N] -> scalar
const total_sum = try sum(ctx, tensor, null, false);

// Sum along dimension 1, keep dimensions: [M,N] -> [M,1]
const row_sums = try sum(ctx, tensor, &[_]i32{1}, true);

// Mean along multiple dimensions: [B,H,W,C] -> [B,C]
const spatial_mean = try mean(ctx, tensor, &[_]i32{1, 2}, false);

// Global max: [M,N,K] -> scalar
const global_max = try max(ctx, tensor, null, false);
```

### Object-Oriented Interface

#### Method Chaining

```zig
// Functional style
const result = try pointwise.add(ctx,
    try pointwise.mul(ctx, a, b),
    try pointwise.relu(ctx, c)
);

// Object-oriented style
const tensor_a = try Tensor.from(ctx, a);
const tensor_b = try Tensor.from(ctx, b);
const tensor_c = try Tensor.from(ctx, c);

const result = try tensor_a
    .multiply(tensor_b)
    .add(tensor_c.relu());

// Error-safe builder pattern
const result = try TensorBuilder.from(tensor_a)
    .multiply(tensor_b)
    .add(tensor_c)
    .relu()
    .build();
```

#### Complex Neural Network Operations

```zig
// Multi-head attention block
pub fn multihead_attention(
    ctx: *Core,
    input: NodeId,
    query_weight: NodeId,
    key_weight: NodeId,
    value_weight: NodeId,
    output_weight: NodeId,
    num_heads: usize,
    head_dim: usize
) !NodeId {
    // Linear projections
    const query = try linalg.matmul(ctx, input, query_weight);
    const key = try linalg.matmul(ctx, input, key_weight);  
    const value = try linalg.matmul(ctx, input, value_weight);
    
    // Reshape for multi-head: [B,S,H*D] -> [B,S,H,D]
    const query_heads = try reshape(ctx, query, &[_]i64{-1, -1, @intCast(num_heads), @intCast(head_dim)});
    const key_heads = try reshape(ctx, key, &[_]i64{-1, -1, @intCast(num_heads), @intCast(head_dim)});
    const value_heads = try reshape(ctx, value, &[_]i64{-1, -1, @intCast(num_heads), @intCast(head_dim)});
    
    // Transpose for attention: [B,S,H,D] -> [B,H,S,D]
    const query_t = try permute(ctx, query_heads, &[_]i32{0, 2, 1, 3});
    const key_t = try permute(ctx, key_heads, &[_]i32{0, 2, 1, 3});
    const value_t = try permute(ctx, value_heads, &[_]i32{0, 2, 1, 3});
    
    // Attention scores: Q @ K^T / √d
    const key_transposed = try transpose(ctx, key_t, -2, -1);
    const scores = try linalg.matmul(ctx, query_t, key_transposed);
    const scale = try pointwise.div(ctx, scores, scale_constant);
    const attention_weights = try pointwise.softmax(ctx, scale, -1);
    
    // Apply attention: weights @ V
    const attention_output = try linalg.matmul(ctx, attention_weights, value_t);
    
    // Transpose back and reshape: [B,H,S,D] -> [B,S,H*D]
    const output_transposed = try permute(ctx, attention_output, &[_]i32{0, 2, 1, 3});
    const concatenated = try reshape(ctx, output_transposed, &[_]i64{-1, -1, @intCast(num_heads * head_dim)});
    
    // Final linear projection
    return try linalg.matmul(ctx, concatenated, output_weight);
}
```

## Memory Management

### Arena-Based Allocation

All tensor operations use arena allocation:

```zig
// Shape arrays allocated from arena
var dims = try ctx.arena.allocator().alloc(*core_types.Expr, shape.len);

// Constant data stored in data manager
try ctx.data.setConstantData(f32, node_id_raw, data_slice);
```

### View-Based Memory Optimization

Following Luminal principles:
- **View operations** only modify metadata (strides, offsets, shape)
- **No data copying** until explicit `contiguous()` call
- **Lazy evaluation** of view transformations
- **Memory-efficient** for reshape/transpose/slice chains

### Data Store Integration

```zig
// Constant data patterns
try ctx.data.setConstantPattern(node_id_raw, .zeros);
try ctx.data.setConstantPattern(node_id_raw, .ones);

// Parameter and input registration
try ctx.data.registerParameter(node_id_raw);
try ctx.data.registerInput(node_id_raw);
```

## Error Handling

### Error Propagation

```zig
pub fn matmul(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // Validate tensor existence
    const a_node = ctx.graph.getNode(nodeIdToU32(a)) orelse return error.InvalidTensor;
    const b_node = ctx.graph.getNode(nodeIdToU32(b)) orelse return error.InvalidTensor;
    
    // Validate shapes for matrix multiplication
    const a_shape = ctx.shape.getShape(a_node.output_view_id);
    const b_shape = ctx.shape.getShape(b_node.output_view_id);
    
    if (a_shape.dims.len != 2 or b_shape.dims.len != 2) {
        return error.MatmulRequires2DTensors;
    }
    
    // Continue with operation...
}
```

### Shape Validation

```zig
pub fn validateBroadcastCompatibility(ctx: *Core, a: NodeId, b: NodeId) !void {
    const a_view = getNodeView(ctx, a);
    const b_view = getNodeView(ctx, b);
    
    if (!ctx.shape.canBroadcastShapes(a_view.shape_id, b_view.shape_id)) {
        return error.IncompatibleShapes;
    }
}
```

## Issues and Improvement Opportunities

### Current Issues

1. **Incomplete Comparison Operations:**
```zig
// Placeholder implementations
pub fn equals(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    return try less_than(ctx, a, b); // Wrong implementation!
}
```

2. **Missing Min/Max Implementations:**
```zig
pub fn minimum(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    _ = ctx; _ = b; return a; // Not implemented
}
```

3. **Type Conversion Inconsistencies:**
   - Mixed i32/i64/u32 usage for dimension indices
   - Some operations expect i32, others i64

4. **Limited Broadcasting Validation:**
   - Could fail at execution time rather than creation time

### Recommended Improvements

1. **Complete Missing Implementations:**
```zig
// Proper element-wise minimum using conditional logic
pub fn minimum(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    const mask = try less_than(ctx, a, b);
    return try select_where(ctx, mask, a, b);
}

// Proper equality comparison
pub fn equals(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    const diff = try sub(ctx, a, b);
    const abs_diff = try abs(ctx, diff);
    return try less_than(ctx, abs_diff, epsilon_constant);
}
```

2. **Standardize Dimension Types:**
```zig
// Use consistent i64 for all dimension indices
pub fn permute(ctx: *Core, a: NodeId, dims: []const i64) !NodeId
pub fn transpose(ctx: *Core, a: NodeId, dim0: i64, dim1: i64) !NodeId
```

3. **Enhanced Shape Validation:**
```zig
pub fn validateMatmulShapes(ctx: *Core, a: NodeId, b: NodeId) !struct { m: i64, k: i64, n: i64 } {
    // Extract and validate matrix dimensions
    // Return dimensions for use in result shape calculation
}
```

4. **Performance Optimizations:**
```zig
// View chain optimization
pub fn optimizeViewChain(ctx: *Core, tensor: NodeId) !NodeId {
    // Collapse consecutive view operations into single view
}

// Constant folding
pub fn foldConstantExpressions(ctx: *Core, expr: NodeId) !NodeId {
    // Evaluate constant sub-expressions at compile time
}
```

## Integration with Core System

The tensor module integrates seamlessly with core components:

- **Graph Engine**: Creates computation nodes for operations
- **Shape Engine**: Manages tensor shapes and view transformations  
- **Symbolic Engine**: Handles symbolic dimensions in shapes
- **Memory System**: Uses arena allocation and data store

## Summary

The tensor operations module provides a comprehensive, well-designed API for tensor computations with full symbolic dimension support:

**Key Strengths:**
- **Full Symbolic Dimension Support**: Create and manipulate tensors with runtime-determined shapes
- **Type-Safe ID System**: Uses strongly-typed `NodeId`, `ViewId`, `ShapeId`, and `ExprId`
- **Clear Separation**: View operations (metadata-only) vs computational operations
- **Comprehensive Coverage**: Following PyTorch/NumPy conventions
- **Dual Interface**: Both functional and object-oriented programming styles
- **Luminal Integration**: Direct decomposition to graph primitives
- **Memory Efficiency**: View system with lazy evaluation and arena allocation

**Recent Improvements:**
- ✅ Implemented full symbolic tensor creation (`placeholder`, `placeholderSymbolic`)
- ✅ All shape operations now support symbolic dimensions
- ✅ Type-safe ID migration completed
- ✅ Enhanced integration with symbolic shape engine

**Future Enhancements:**
- Complete missing operation implementations (min/max, comparisons)
- Add symbolic dimension support to reduction operations
- Implement shape inference for complex symbolic operations
- Add more neural network building blocks with symbolic support
- Optimize view chain performance

The tensor operations module now provides a robust foundation for both static and dynamic tensor computations, enabling sophisticated deep learning models with variable-sized inputs while maintaining type safety and memory efficiency.