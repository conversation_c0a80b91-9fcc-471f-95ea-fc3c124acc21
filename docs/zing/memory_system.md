# Zing Memory Management System

## Overview

The memory management system in `src/core/memory.zig` provides high-performance, specialized data structures for object pooling and content-addressable caching. It's designed to optimize allocation patterns common in computational graph systems while maintaining type safety and comprehensive statistics tracking.

**Critical Safety Improvements ✅ Implemented:**
- **Arena-based symbolic caching**: Eliminates memory leaks in expression management
- **Automatic cleanup**: Arena deallocation prevents double-free errors
- **Buffer safety**: Explicit size tracking prevents buffer overflows

## Core Components

### 1. Object Pool (`ObjectPool(T)`)

A generic object pool for efficient allocation and deallocation of fixed-size objects with memory reuse.

#### Data Structure
```zig
pub fn ObjectPool(comptime T: type) type {
    return struct {
        allocator: std.mem.Allocator,
        items: std.ArrayList(T),          // Actual storage
        free_list: std.ArrayList(usize),  // Indices of available items
        stats: PoolStats,                 // Usage statistics
    };
}
```

#### Key Features
- **Memory Reuse**: Released objects are added to a free list for reuse rather than deallocation
- **Statistics Tracking**: Comprehensive metrics including acquire/release counts and peak usage
- **Pointer Validation**: Safe release operations with bounds checking
- **Reset Capability**: Can reset the pool without deallocating memory

#### API Methods
- `init(allocator)` - Initialize with allocator
- `deinit()` - Clean up all resources
- `acquire() !*T` - Get an object (reused or newly allocated)
- `release(item: *T) !void` - Return object to pool with validation
- `getStats() PoolStats` - Get usage statistics
- `reset()` - Mark all items as available for reuse

#### Memory Pattern
```
Initial:     items=[], free_list=[]
After adds:  items=[A,B,C], free_list=[]
After release B: items=[A,B,C], free_list=[1]
Next acquire reuses: items=[A,D,C], free_list=[] (D overwrites B)
```

### 2. ID Pool (`IdPool(T)`)

An ID-based object pool that provides stable identifiers for stored objects, essential for graph node management.

#### Data Structure
```zig
pub fn IdPool(comptime T: type) type {
    return struct {
        allocator: std.mem.Allocator,
        items: std.AutoHashMapUnmanaged(u32, T),    // ID -> Object mapping
        next_id: u32,                               // Next available ID
        free_ids: std.ArrayListUnmanaged(u32),      // Recycled IDs
        stats: IdPoolStats,                         // Usage tracking
    };
}
```

#### Key Features
- **Stable IDs**: Objects have persistent identifiers that remain valid until explicitly removed
- **ID Recycling**: Freed IDs are reused to prevent ID space exhaustion
- **HashMap Storage**: O(1) access by ID using hash map
- **Statistics**: Tracks additions, removals, and peak count

#### API Methods
- `add(item: T) !u32` - Add item and return its ID
- `get(id: u32) ?*const T` - Get immutable reference by ID
- `getMut(id: u32) ?*T` - Get mutable reference by ID
- `remove(id: u32) bool` - Remove item and recycle ID
- `getStats() IdPoolStats` - Get usage statistics

#### ID Lifecycle
```
add(A) -> ID 0, items={0: A}, free_ids=[]
add(B) -> ID 1, items={0: A, 1: B}, free_ids=[]
remove(0) -> items={1: B}, free_ids=[0]
add(C) -> ID 0 (recycled), items={0: C, 1: B}, free_ids=[]
```

### 3. Content Cache (`ContentCache(K, V)`)

A content-addressable cache that deduplicates objects based on their content using precomputed hash values.

#### Data Structure
```zig
pub fn ContentCache(comptime K: type, comptime V: type) type {
    return struct {
        allocator: std.mem.Allocator,
        map: std.HashMapUnmanaged(CacheKey(K), V, Key.HashContext, 80),
        stats: CacheStats,
    };
}

pub fn CacheKey(comptime T: type) type {
    return struct {
        hash_value: u64,    // Precomputed hash
        data: T,           // Original data
    };
}
```

#### Key Features
- **Content Deduplication**: Identical content maps to the same cached value
- **Precomputed Hashing**: Hash calculated once during key creation
- **Deep Equality**: Structural comparison for complex types
- **Generic Hashing**: Supports structs, arrays, unions, enums, and primitives
- **Hit/Miss Statistics**: Comprehensive cache performance metrics

#### API Methods
- `getOrPut(key_data: K, create_fn) !V` - Get cached value or create new one
- `get(key_data: K) ?V` - Retrieve cached value
- `put(key_data: K, value: V) !void` - Store value directly
- `clear()` - Clear cache while retaining capacity
- `getStats() CacheStats` - Get cache performance statistics

#### Hashing System

The cache implements deep structural hashing for any type:

```zig
// Supports complex nested structures
const MyStruct = struct {
    values: []i32,
    nested: struct { x: f32, y: f32 },
    tag: enum { A, B, C },
};

// Hash includes all fields recursively
hashType(hasher, MyStruct, my_struct);
```

**Supported Types:**
- **Structs**: Hashes all fields recursively
- **Arrays/Slices**: Hashes length and all elements
- **Unions**: Hashes active tag and payload
- **Enums**: Hashes enum value
- **Optionals**: Distinguishes null vs non-null
- **Primitives**: Direct byte representation

### 4. Specialized Instances

#### Expression Pool
```zig
pub const ExprPool = ObjectPool(types.Expr);
```

Specialized pool for symbolic expressions, likely used heavily in the symbolic engine for expression reuse.

## Memory Management Patterns

### 1. Pool-Based Allocation
- Objects are allocated from pools rather than general allocator
- Reduces fragmentation and improves cache locality
- Enables bulk operations (reset entire pool)

### 2. Content Deduplication
- Identical structures share the same cached instance
- Reduces memory usage for repeated patterns
- Critical for expression simplification systems

### 3. ID-Based References
- Objects referenced by stable IDs rather than pointers
- Enables safe serialization and cross-module references
- Prevents use-after-free when objects are moved

### 4. Statistics-Driven Optimization
- All structures track usage patterns
- Enables performance tuning and memory leak detection
- Peak usage tracking helps size pools appropriately

## Error Handling

### Pool Errors
- `error.InvalidPointer` - Attempting to release pointer not from pool
- Standard allocation errors propagated from underlying allocator

### Cache Errors
- Allocation failures during cache growth
- Hash computation errors for unsupported types (compile-time)

### Safety Features
- **Bounds Checking**: Pool release validates pointer ranges
- **Type Safety**: Generic implementations prevent type mismatches
- **Memory Leak Detection**: Statistics help identify unreleased objects

## Performance Characteristics

### Object Pool
- **Acquire**: O(1) from free list, O(1) amortized for new allocation
- **Release**: O(1) with pointer arithmetic validation
- **Memory**: Linear growth, no deallocation until deinit

### ID Pool
- **Add**: O(1) average (hash map insertion)
- **Get**: O(1) average (hash map lookup)
- **Remove**: O(1) average (hash map removal)
- **Memory**: Sparse storage, only used IDs consume space

### Content Cache
- **Hit**: O(1) hash lookup
- **Miss**: O(hash calculation) + O(1) insertion
- **Memory**: Proportional to unique content, not total requests

## Integration with Core Systems

### Shape Engine Integration
- Likely uses `ContentCache` for `ViewDesc` deduplication
- Expression pool for symbolic shape expressions
- ID pools for stable shape/view identifiers

### Graph Engine Integration
- ID pools for node management with stable `NodeId`
- Content caching for operation deduplication
- Object pools for temporary computation objects

### Symbolic Engine Integration
- `ExprPool` for expression object reuse
- Content cache for expression canonicalization
- Deep hashing for structural expression equality

## Issues and Limitations

### 1. Hash Function Coverage
- Custom `hashType` may not handle all edge cases
- No validation for hash collision handling
- Compile-time errors for unsupported types

### 2. Memory Growth
- Pools never shrink, only grow
- Long-running processes may accumulate unused capacity
- No automatic garbage collection or compaction

### 3. ID Space Management
- 32-bit ID space may overflow in very large graphs
- No protection against ID wraparound
- Free ID list grows indefinitely

### 4. Thread Safety
- No synchronization primitives
- Not safe for concurrent access
- Would require external synchronization

## Recommended Improvements

### 1. Memory Compaction
```zig
pub fn compact(self: *Self) !void {
    // Rebuild free list and compact storage
}
```

### 2. Configurable Pool Sizes
```zig
pub const PoolConfig = struct {
    initial_capacity: usize = 64,
    max_capacity: ?usize = null,
    shrink_threshold: f32 = 0.25,
};
```

### 3. Advanced Hash Functions
- Consider using faster hash functions for primitives
- Add bloom filters for negative cache lookups
- Implement hash distribution analysis

### 4. Memory Diagnostics
```zig
pub fn getMemoryUsage(self: *const Self) MemoryInfo {
    return .{
        .allocated_bytes = self.items.capacity * @sizeOf(T),
        .used_bytes = (self.items.len - self.free_list.len) * @sizeOf(T),
        .fragmentation_ratio = ...,
    };
}
```

## Usage Examples

### Object Pool Pattern
```zig
var expr_pool = ExprPool.init(allocator);
defer expr_pool.deinit();

// Acquire and use
const expr = try expr_pool.acquire();
expr.* = .{ .symbol = "x" };

// Release for reuse
try expr_pool.release(expr);

// Check statistics
const stats = expr_pool.getStats();
std.log.info("Peak usage: {}", .{stats.peak_usage});
```

### Content Cache Pattern
```zig
var shape_cache = ContentCache(ViewDesc, ShapeId).init(allocator);
defer shape_cache.deinit();

// Deduplicate shapes
const shape_id = try shape_cache.getOrPut(view_desc, createShape);

// Direct lookup
if (shape_cache.get(view_desc)) |existing_id| {
    return existing_id;
}
```

The memory system provides the foundational infrastructure for efficient, type-safe memory management across all Zing components, with particular emphasis on the allocation patterns common in computational graph systems.