# Graph Engine Documentation (v2)

## Overview

The graph engine in `src/core/graph/` implements a computational graph system following the Luminal primitive-based architecture. It manages directed acyclic graphs (DAGs) of computational operations where nodes represent either data sources or primitive operations, and edges represent data dependencies.

**Key Features:**
- **Type-Safe ID System**: Uses strongly-typed `NodeId`, `ViewId`, `ShapeId`, and `ExprId` enums
- **Symbolic Dimension Support**: Full support for symbolic tensor dimensions
- **Arena-Based Memory**: Efficient memory management with automatic cleanup
- **Luminal Compatibility**: Direct mapping to Luminal's primitive operation set

## Architecture

### Core Components

```
graph/
├── types.zig          - Core data structures and type definitions
├── engine.zig         - Main GraphEngine implementation
├── errors.zig         - Graph-specific error definitions  
├── topology.zig       - Graph topology analysis (sorting, cycles)
├── memory.zig         - Memory management with object pools
├── operator.zig       - Operator execution traits and implementations
├── operator_types.zig - Operator I/O type definitions
└── test.zig          - Comprehensive test suite
```

### Design Principles

- **Luminal Primitive Focus**: Only implements core Luminal primitives (`add`, `multiply`, `reciprocal`, `sqrt`, etc.)
- **Type Safety**: Uses <PERSON><PERSON>'s type-safe enum IDs (`NodeId`, `ViewId`, `ShapeId`, `ExprId`)
- **Arena-based Memory**: Leverages arena allocators for efficient memory handling
- **Shape Integration**: Tight coupling with shape engine for tensor dimension management
- **State Management**: Clear separation between building, compiled, and executed states
- **Symbolic Support**: Handles tensors with unknown dimensions at compile time

## Type-Safe ID System

The graph engine uses strongly-typed enum IDs to prevent type confusion and improve API clarity:

```zig
// Defined in src/core/types.zig
pub const NodeId = enum(u32) { _ };
pub const ViewId = enum(u32) { _ };
pub const ShapeId = enum(u32) { _ };
pub const ExprId = enum(u32) { _ };
```

**Benefits:**
- **Compile-Time Safety**: Cannot accidentally pass a `ViewId` where a `NodeId` is expected
- **API Clarity**: Function signatures clearly indicate what type of ID is expected
- **Easy Conversion**: Use `@intFromEnum` and `@enumFromInt` for conversions when needed

**Usage Example:**
```zig
// Type-safe API
const node_id: NodeId = try graph.newNodeAdd(&[_]NodeId{a, b}, output_view);

// Convert to u32 when needed (e.g., for backward compatibility)
const node_u32: u32 = @intFromEnum(node_id);

// Convert back from u32
const node_id_again: NodeId = @enumFromInt(node_u32);
```

## Core Data Structures

### OpType (types.zig)

Defines the set of supported operations following Luminal primitives:

```zig
pub const OpType = enum {
    // Data nodes
    constant,    // Immutable data
    variable,    // Trainable parameters  
    input,       // Placeholder for runtime data
    
    // Binary operations (ONLY Luminal primitives)
    add,
    multiply,
    mod,         // Modulo operation
    less_than,   // The only comparison primitive in Luminal
    
    // Unary operations (ONLY Luminal primitives)
    reciprocal,  // 1/x
    sqrt,
    sin,
    log2,        // Base-2 logarithm
    exp2,        // Base-2 exponential
    
    // Memory layout operations
    contiguous,  // Force contiguous memory layout
    
    // Reduction operations (ONLY Luminal primitives)
    reduce_sum,
    reduce_max,
};
```

**Key Characteristics:**
- **Primitive-Only**: Complex operations like `tanh`, `sigmoid` must be decomposed at tensor layer
- **Consistent**: All operations follow the same interface patterns
- **Luminal Compatible**: Direct mapping to Luminal's operation set

### Node (types.zig)

Core computational unit in the graph:

```zig
pub const Node = struct {
    id: NodeId,                    // Type-safe unique identifier
    op: OpType,                    // Operation type
    inputs: []const NodeId,        // Input node IDs (arena-allocated)
    output_view_id: ViewId,        // Output shape view ID
    dtype: DataType,               // Data type of output
    metadata: ?*NodeMetadata,      // Optional operation metadata
    consumer_count: u32 = 0,       // Number of nodes consuming this node's output
    consumers: []NodeId,           // Consumer node IDs (arena-allocated)
    
    // Helper methods for node classification
    pub fn isConstant(self: Node) bool
    pub fn isUnary(self: Node) bool
    pub fn isBinary(self: Node) bool
    pub fn isReduction(self: Node) bool
};
```

**Features:**
- **Type-Safe IDs**: `NodeId` and `ViewId` prevent ID type confusion
- **Dependency Tracking**: Consumer lists enable efficient graph traversal
- **Metadata Support**: Optional metadata for complex operations
- **Arena Memory**: All arrays allocated from core's arena

### NodeMetadata (types.zig)

Optional metadata for operations requiring additional parameters:

```zig
pub const NodeMetadata = union(enum) {
    constant: ConstantMetadata,
    reduction: ReductionMetadata,
    slice: SliceMetadata,
    transpose: TransposeMetadata,
};

pub const ReductionMetadata = struct {
    axes: []const i32,
    keep_dims: bool = false,
};

pub const ConstantMetadata = struct {
    source: ConstantSource,
};

pub const ConstantSource = enum {
    pattern,
    literal,
    external,
};
```

### Type-Safe Identifiers

Uses Zig 0.14's type-safe enum pattern:

```zig
// From ../types.zig
pub const NodeId = enum(u32) { 
    invalid = 0,
    _,
    pub fn isValid(self: NodeId) bool
    pub fn eql(self: NodeId, other: NodeId) bool
};

pub const ViewId = enum(u32) { 
    invalid = 0,
    _,
    pub fn isValid(self: ViewId) bool
};
```

**Benefits:**
- **Compile-Time Safety**: Cannot mix node IDs with view IDs
- **Invalid State Handling**: Explicit invalid values
- **Zero-Cost**: No runtime overhead compared to raw integers

## Public API Reference

### GraphEngine (engine.zig)

Central orchestrator for all graph operations:

```zig
pub const GraphEngine = struct {
    core: *Core,
    nodes: std.ArrayList(Node),
    node_map: std.AutoHashMap(parent_types.NodeId, usize), // node_id -> array index
    topology_manager: TopologyManager,
    memory_manager: MemoryManager,
    state: GraphState = .building,
    next_node_id: u32 = 1, // Start at 1, since 0 is reserved for invalid
    stats: types.GraphStats,
    
    pub fn init(core: *Core) !GraphEngine
    pub fn deinit(self: *Self) void
    pub fn reset(self: *Self) void
    
    // Node Creation - Data Nodes
    pub fn newNodeConstant(self: *Self, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeConstantTyped(self: *Self, output_view_id: parent_types.ViewId, dtype: DataType) !parent_types.NodeId
    pub fn newNodeVariable(self: *Self, output_view_id: parent_types.ViewId, dtype: DataType) !parent_types.NodeId
    pub fn newNodeInput(self: *Self, output_view_id: parent_types.ViewId, dtype: DataType) !parent_types.NodeId
    
    // Node Creation - Binary Operations
    pub fn newNodeAdd(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeMultiply(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeMod(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeLessThan(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    
    // Node Creation - Unary Operations
    pub fn newNodeSqrt(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeSin(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeReciprocal(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeLog2(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeExp2(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeContiguous(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    
    // Node Creation - Reduction Operations
    pub fn newNodeReduceSum(self: *Self, input_id: parent_types.NodeId, axes: []const i32, output_view_id: parent_types.ViewId) !parent_types.NodeId
    pub fn newNodeReduceMax(self: *Self, input_id: parent_types.NodeId, axes: []const i32, output_view_id: parent_types.ViewId) !parent_types.NodeId
    
    // High-Level Convenience APIs (still use u32 for backward compatibility)
    pub fn add(self: *Self, a: u32, b: u32) !u32
    pub fn multiply(self: *Self, a: u32, b: u32) !u32
    pub fn sqrt(self: *Self, a: u32) !u32
    pub fn reciprocal(self: *Self, a: u32) !u32
    
    // Node Query and Access
    pub fn getNode(self: *Self, node_id: parent_types.NodeId) ?*const Node
    pub fn getNodeMut(self: *Self, node_id: parent_types.NodeId) ?*Node
    pub fn getNodeOrError(self: *const Self, node_id: parent_types.NodeId) !*const Node
    pub fn getNodeConsumers(self: *Self, node_id: parent_types.NodeId) []const u32
    pub fn isNodeUsed(self: *Self, node_id: parent_types.NodeId) bool
    
    // Graph Operations
    pub fn topologicalSort(self: *Self) ![]u32
    
    // Internal helper functions
    fn createBinaryOp(self: *Self, op_type: OpType, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    fn createUnaryOp(self: *Self, op_type: OpType, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId
    fn createReductionOp(self: *Self, op_type: OpType, input_id: parent_types.NodeId, axes: []const i32, output_view_id: parent_types.ViewId, keep_dims: bool) !parent_types.NodeId
    fn createNode(self: *Self, node_init: Node) !parent_types.NodeId
};
```

#### Usage Examples

**Basic Graph Construction:**
```zig
var core = try Core.init(allocator);
defer core.deinit();

// Create shapes using symbolic engine
const dim_2 = try core.symbolic.newIntegerExpr(2);
const dim_3 = try core.symbolic.newIntegerExpr(3);
const shape_id = try core.shape.newShape(&[_]*Expr{ dim_2, dim_3 });

// Create views for nodes
const view1_id = try core.shape.newDefaultView(shape_id);
const view2_id = try core.shape.newDefaultView(shape_id);
const output_view = try core.shape.newDefaultView(shape_id);

// Create data nodes using low-level API (returns NodeId)
const a = try core.graph.newNodeConstant(view1_id);
const b = try core.graph.newNodeConstant(view2_id);

// Create operation nodes
// Low-level API (uses NodeId)
const sum_node = try core.graph.newNodeAdd(&[_]NodeId{a, b}, output_view);

// High-level convenience API (uses u32)
const a_u32 = @intFromEnum(a);
const b_u32 = @intFromEnum(b);
const product = try core.graph.multiply(a_u32, b_u32);  // Shape inference in high-level API
```

**Complex Operations via Decomposition:**
```zig
// Create scalar shape for constants
const scalar_shape = try core.shape.newShape(&[_]*Expr{});

// Subtraction: a - b = a + (-1 * b)
const neg_one_view = try core.shape.newDefaultView(scalar_shape);
const neg_one = try core.graph.newNodeConstantTyped(neg_one_view, .f32); // Set -1 value separately
const neg_b_u32 = try core.graph.multiply(@intFromEnum(neg_one), @intFromEnum(b));
const difference = try core.graph.add(@intFromEnum(a), neg_b_u32);

// Division: a / b = a * reciprocal(b)  
const recip_b = try core.graph.reciprocal(@intFromEnum(b));
const quotient = try core.graph.multiply(@intFromEnum(a), recip_b);

// Tanh decomposition (simplified for clarity)
// Note: Full implementation would require more intermediate steps
// tanh(x) ≈ (exp(2x) - 1) / (exp(2x) + 1)
// But exp2 expects base-2, so need to convert: exp(2x) = exp2(2x / ln(2))
```

**Reduction Operations:**
```zig
// Sum along axis 1, keeping dimensions
const sum_axis1 = try core.graph.newNodeReduceSum(
    input_node,
    &[_]i32{1},  // axes to reduce
    output_view
);

// Global max (reduce all dimensions)
const global_max = try core.graph.newNodeReduceMax(
    input_node,
    &[_]i32{0, 1, 2},  // all axes
    scalar_view
);
```

### GraphStats (types.zig)

Performance and usage statistics:

```zig
pub const GraphStats = struct {
    node_count: usize = 0,
    constant_count: usize = 0,
    binary_op_count: usize = 0,
    unary_op_count: usize = 0,
    reduction_count: usize = 0,
    
    pub fn getOperationRatio(self: GraphStats) f32
    pub fn getComplexity(self: GraphStats) usize
};

// Usage
const stats = core.graph.getStats();
std.debug.print("Nodes: {}, Operations: {}\n", .{stats.node_count, stats.getComplexity()});
```

## Memory Management

### Arena-Based Allocation

All long-lived graph data uses arena allocation:

```zig
pub fn newNodeAdd(self: *Self, input_ids: []const NodeId, output_view_id: ViewId) !NodeId {
    // Validation
    if (input_ids.len != 2) return error.InvalidArgumentCount;
    
    // Use internal helper for binary operations
    return self.createBinaryOp(.add, input_ids, output_view_id);
}

// High-level convenience API (for backward compatibility)
pub fn add(self: *Self, a: u32, b: u32) !u32 {
    // Convert u32 to NodeId
    const node_a = @as(NodeId, @enumFromInt(a));
    const node_b = @as(NodeId, @enumFromInt(b));
    
    // Infer output shape (implementation detail)
    const output_view = try self.inferBinaryOpShape(node_a, node_b);
    
    // Create node and return as u32
    const result = try self.newNodeAdd(&[_]NodeId{node_a, node_b}, output_view);
    return @intFromEnum(result);
}
```

**Benefits:**
- **Bulk Deallocation**: Arena cleanup frees all graph memory
- **Cache Locality**: Related allocations are memory-adjacent
- **No Fragmentation**: Large contiguous allocations
- **Simple Lifetime**: All graph data has same lifetime

### Object Pooling (memory.zig)

For frequently allocated small objects:

```zig
pub const MemoryManager = struct {
    node_pool: ObjectPool(Node),           // Pool for node structures
    input_pool: ObjectPool([8]u32),        // Pool for small input arrays
    consumer_pool: ObjectPool([16]u32),    // Pool for consumer arrays
    arena: std.heap.ArenaAllocator,        // Arena for large arrays
    
    pub fn allocateInputArray(self: *Self, count: usize) ![]u32 {
        if (count <= 8) {
            return self.input_pool.acquire();  // Use pool for small arrays
        } else {
            return self.arena.allocator().alloc(u32, count);  // Use arena for large
        }
    }
    
    pub fn deallocateInputArray(self: *Self, array: []u32) void {
        if (array.len <= 8) {
            self.input_pool.release(array[0..8]);
        }
        // Arena arrays automatically freed on arena deinit
    }
};
```

### Memory Usage Patterns

1. **Node Storage**: `ArrayList(Node)` with `AutoHashMap(NodeId, usize)` for O(1) lookup
2. **Input Arrays**: Arena-allocated, immutable after creation
3. **Consumer Arrays**: Dynamically grown as new consumers are added
4. **Metadata**: Arena-allocated when needed (optional for most nodes)

**Memory Characteristics:**
- **Space Complexity**: O(V + E) where V = nodes, E = edges
- **Access Time**: O(1) node lookup via hash map
- **Memory Overhead**: ~40 bytes per node + variable array sizes

## Component Architecture

### TopologyManager Component

The `TopologyManager` handles graph analysis, sorting, and cycle detection:

```zig
pub const TopologyManager = struct {
    visited: std.AutoHashMap(u32, void),
    currently_visiting: std.AutoHashMap(u32, void),
    sorted_nodes: std.ArrayList(u32),
    allocator: std.mem.Allocator,
    
    // Main operations
    pub fn topologicalSort(self: *Self, nodes: []const Node) ![]u32 {
        // Returns nodes in execution order (as raw u32 values)
        // Uses DFS-based algorithm for efficiency
    }
    
    pub fn hasCycles(self: *Self, nodes: []const Node) !bool {
        // Three-color DFS algorithm for cycle detection
        // Returns true if graph contains cycles
    }
};
```

**Key Features:**
- Uses DFS (Depth-First Search) for topological sorting
- Three-color marking algorithm for cycle detection
- Returns raw u32 values instead of NodeId (inconsistency)
- Efficient O(V + E) complexity for both operations

### MemoryManager Component

The `MemoryManager` provides object pooling and efficient allocation:

```zig
pub const MemoryManager = struct {
    allocator: std.mem.Allocator,
    node_metadata_pool: ObjectPool(NodeMetadata, 64),
    small_array_pool: ObjectPool([8]parent_types.NodeId, 128),
    
    pub fn init(allocator: std.mem.Allocator) MemoryManager {
        return .{
            .allocator = allocator,
            .node_metadata_pool = ObjectPool(NodeMetadata, 64).init(allocator),
            .small_array_pool = ObjectPool([8]parent_types.NodeId, 128).init(allocator),
        };
    }
    
    pub fn allocateNodeMetadata(self: *Self) !*NodeMetadata {
        return self.node_metadata_pool.acquire();
    }
    
    pub fn deallocateNodeMetadata(self: *Self, metadata: *NodeMetadata) void {
        self.node_metadata_pool.release(metadata);
    }
};
```

**Object Pool Implementation:**
```zig
pub fn ObjectPool(comptime T: type, comptime pool_size: usize) type {
    return struct {
        items: [pool_size]T,
        free_list: std.ArrayList(usize),
        allocator: std.mem.Allocator,
        
        pub fn acquire(self: *Self) !*T {
            if (self.free_list.items.len > 0) {
                const index = self.free_list.pop();
                return &self.items[index];
            }
            // Fall back to regular allocation if pool exhausted
            return self.allocator.create(T);
        }
        
        pub fn release(self: *Self, item: *T) void {
            // Return to pool if it's a pooled item
            if (self.isPooled(item)) {
                const index = self.getPoolIndex(item);
                self.free_list.append(index) catch {};
            } else {
                self.allocator.destroy(item);
            }
        }
    };
}
```

**Benefits:**
- Reduces allocation overhead for frequently created/destroyed objects
- Improves cache locality for small objects
- Falls back to regular allocation when pool is exhausted
- Zero-overhead for pool misses

## Error Handling

### Error Types (errors.zig)

```zig
pub const GraphError = error{
    // Node errors
    InvalidNode,
    InvalidNodeId,
    InvalidNodeType,
    NodeNotFound,
    DuplicateNode,
    
    // Graph structure errors  
    CyclicDependency,
    InvalidTopology,
    GraphHasCycles,
    
    // Tensor errors
    InvalidTensor,
    InvalidTensorContext,
    IncompatibleShapes,
    
    // Compilation and execution errors
    NotCompiled,
    CompilationFailed,
    ExecutionFailed,
    
    // Input/output errors
    InvalidInput,
    InvalidOutput,
    InvalidArgumentCount,
    
    // State errors
    InvalidState,
    InvalidGraphState,
    
    // Implementation errors
    NotImplemented,
};
```

### Error Propagation Patterns

```zig
pub fn newNodeAdd(self: *Self, input_ids: []const u32, output_view_id: u32) !u32 {
    // Early validation
    if (input_ids.len != 2) return error.InvalidArgumentCount;
    
    // Node existence validation
    for (input_ids) |input_id| {
        _ = self.getNode(input_id) orelse return error.InvalidNodeId;
    }
    
    // Shape compatibility validation (when available)
    const shapes = try self.getInputShapes(input_ids);
    if (!self.areShapesCompatibleForElementwise(shapes)) {
        return error.IncompatibleShapes;
    }
    
    // State validation
    if (self.state != .building) return error.InvalidGraphState;
    
    // Proceed with operation...
}
```

**Error Handling Patterns:**
- **Early Validation**: Check preconditions before expensive operations
- **Null Safety**: Optional return types with explicit error alternatives
- **State Validation**: Ensure operations are valid for current graph state
- **Error Propagation**: Uses Zig's `try` for automatic error propagation

## Topology Management

### TopologyManager (topology.zig)

Handles graph analysis and ordering:

```zig
pub const TopologyManager = struct {
    allocator: std.mem.Allocator,
    sorted_nodes: std.ArrayList(u32),
    visited: std.AutoHashMap(u32, void),
    
    pub fn init(allocator: std.mem.Allocator) TopologyManager
    pub fn deinit(self: *Self) void
    pub fn reset(self: *Self) void
    
    // Core algorithms
    pub fn topologicalSort(self: *Self, nodes: []const Node) ![]u32
    pub fn hasCycles(self: *Self, nodes: []const Node) !bool
    
    // Internal helpers  
    fn dfsVisit(self: *Self, node_id: NodeId, nodes: []const Node) !void
    fn hasCycleFromNode(self: *Self, node_id: NodeId, nodes: []const Node, currently_visiting: *std.AutoHashMap(u32, void)) !bool
};
```

#### Topological Sort (DFS-based)

```zig
pub fn topologicalSort(self: *Self, nodes: []const Node) ![]u32 {
    self.visited.clearRetainingCapacity();
    self.sorted_nodes.clearRetainingCapacity();
    
    // Visit all nodes in DFS order
    for (nodes) |node| {
        const id_raw = @intFromEnum(node.id);
        if (!self.visited.contains(id_raw)) {
            try self.dfsVisit(node.id, nodes);
        }
    }
    
    // Return reverse post-order (topological order)
    std.mem.reverse(u32, self.sorted_nodes.items);
    return self.sorted_nodes.items;
}

fn dfsVisit(self: *Self, node_id: NodeId, nodes: []const Node) !void {
    const id_raw = @intFromEnum(node_id);
    try self.visited.put(id_raw, {});
    
    // Find node in array
    const node = findNodeById(nodes, node_id) orelse return error.NodeNotFound;
    
    // Visit all dependencies first
    for (node.inputs) |input_id| {
        const input_raw = @intFromEnum(input_id);
        if (!self.visited.contains(input_raw)) {
            try self.dfsVisit(input_id, nodes);
        }
    }
    
    // Add to sorted list after visiting dependencies
    try self.sorted_nodes.append(id_raw);
}
```

#### Cycle Detection (Three-Color DFS)

```zig
pub fn hasCycles(self: *Self, nodes: []const Node) !bool {
    self.visited.clearRetainingCapacity();
    var currently_visiting = std.AutoHashMap(u32, void).init(self.allocator);
    defer currently_visiting.deinit();
    
    for (nodes) |node| {
        const id_raw = @intFromEnum(node.id);
        if (!self.visited.contains(id_raw)) {
            if (try self.hasCycleFromNode(node.id, nodes, &currently_visiting)) {
                return true;
            }
        }
    }
    return false;
}

fn hasCycleFromNode(self: *Self, node_id: NodeId, nodes: []const Node, currently_visiting: *std.AutoHashMap(u32, void)) !bool {
    const id_raw = @intFromEnum(node_id);
    
    // Mark as currently visiting (gray)
    try currently_visiting.put(id_raw, {});
    
    const node = findNodeById(nodes, node_id) orelse return error.NodeNotFound;
    
    for (node.inputs) |input_id| {
        const input_raw = @intFromEnum(input_id);
        
        // If we encounter a currently visiting node, we have a cycle
        if (currently_visiting.contains(input_raw)) {
            return true;
        }
        
        // If not visited, recurse
        if (!self.visited.contains(input_raw)) {
            if (try self.hasCycleFromNode(input_id, nodes, currently_visiting)) {
                return true;
            }
        }
    }
    
    // Mark as visited (black) and remove from currently visiting
    try self.visited.put(id_raw, {});
    _ = currently_visiting.remove(id_raw);
    
    return false;
}
```

## Symbolic Dimension Support

The graph engine fully supports tensors with symbolic dimensions, enabling dynamic shapes that are resolved at runtime:

### Key Features

1. **Seamless Integration with Shape Engine:**
```zig
// Create symbolic dimensions
const batch = try core.symbolic.newSymbolExpr("batch");
const seq_len = try core.symbolic.newSymbolExpr("seq_len");

// Create shape with symbolic dimensions
const shape_id = try core.shape.newShape(&[_]*Expr{ batch, seq_len, hidden_dim });
const view_id = try core.shape.newDefaultView(shape_id);

// Create nodes with symbolic shapes
const input_node = try core.graph.newNodeInput(view_id);
```

2. **Operations on Symbolic Tensors:**
```zig
// All operations work with symbolic dimensions
const a = try core.graph.newNodeInput(symbolic_view);
const b = try core.graph.newNodeVariable(symbolic_view);

// Binary operations handle symbolic shapes
const sum = try core.graph.newNodeAdd(&[_]NodeId{a, b}, output_view);

// Reductions work with symbolic dimensions
const reduced = try core.graph.newNodeReduceSum(sum, &[_]i32{1}, reduced_view);
```

3. **Runtime Shape Resolution:**
- Symbolic dimensions are resolved when concrete values are provided
- Graph execution binds symbolic values to actual dimensions
- Shape inference propagates concrete values through the graph

### Example: Dynamic Batch Processing
```zig
// Define model with symbolic batch size
const batch_sym = try core.symbolic.newSymbolExpr("batch");
const input_shape = try core.shape.newShape(&[_]*Expr{
    batch_sym,
    try core.symbolic.newIntegerExpr(784), // MNIST image size
});

// Build graph with symbolic batch
const input = try core.graph.newNodeInput(input_view);
const weights = try core.graph.newNodeVariable(weight_view);
const output = try core.graph.multiply(@intFromEnum(input), @intFromEnum(weights));

// At runtime, bind batch size
// Note: Actual execution would require a backend implementation
// The graph engine provides the structure, execution is backend-specific
const bindings = std.StringHashMap(i64).init(allocator);
try bindings.put("batch", 32); // or 64, 128, etc.
```

## Issues and Improvement Opportunities

### Critical Issues

1. **Memory Leak in getNodeConsumers:**
```zig
pub fn getNodeConsumers(self: *Self, node_id: parent_types.NodeId) []const u32 {
    const node = self.getNode(node_id) orelse return &[_]u32{};
    const result = self.core.allocator.alloc(u32, node.consumers.len) catch unreachable;
    for (node.consumers, 0..) |consumer, i| {
        result[i] = @intFromEnum(consumer);
    }
    return result; // LEAK: Caller has no way to know this needs freeing!
}
```

2. **API Inconsistency - Mixed Type Usage:**
```zig
// Low-level API uses type-safe IDs
pub fn newNodeAdd(self: *Self, input_ids: []const NodeId, output_view_id: ViewId) !NodeId

// High-level convenience API uses u32 for backward compatibility
pub fn add(self: *Self, a: u32, b: u32) !u32

// All node access methods now use NodeId consistently
pub fn getNode(self: *Self, node_id: NodeId) ?*const Node
pub fn getNodeConsumers(self: *Self, node_id: NodeId) []const u32  // Returns u32 array though
pub fn isNodeUsed(self: *Self, node_id: NodeId) bool
```

3. **✅ FIXED: Buffer Safety in operator_types.zig:**
```zig
// BEFORE: Missing size information for borrowed data
.borrowed => |ptr| @as([*]const f32, ptr)[0..], // Need size info

// AFTER: Safe TensorDataRef with explicit size tracking
pub const TensorDataRef = union(enum) {
    owned: []f32,
    borrowed: struct {
        data: [*]const f32,
        len: usize,
        
        pub fn asSlice(self: @This()) []const f32 {
            return self.data[0..self.len];
        }
    },
    
    pub fn createBorrowed(data: [*]const f32, len: usize) TensorDataRef {
        return .{ .borrowed = .{ .data = data, .len = len } };
    }
};
```

3. **Incomplete Operator Implementations:**
```zig
// Many tensor operations return NotImplemented
pub fn matmul(self: *Self, _: u32, _: u32) !u32 {
    return error.NotImplemented;
}

pub fn reshape(self: *Self, _: u32, _: []const i64) !u32 {
    return error.NotImplemented;
}

pub fn concat(self: *Self, _: []const u32, _: i64) !u32 {
    return error.NotImplemented;
}
```

4. **TopologyManager Return Type Inconsistency:**
```zig
// TopologyManager properly handles NodeId conversions internally
// but still returns raw u32 arrays instead of NodeId arrays
pub const TopologyManager = struct {
    visited: std.AutoHashMap(u32, void),  // Uses u32 for efficiency 
    sorted_nodes: std.ArrayList(u32),     // Returns u32 array
    
    // But properly converts NodeId internally:
    const id_raw = @intFromEnum(node.id);
};
```

5. **Shape Validation Gaps:**
- No shape compatibility checking during node creation
- Shape inference logic is missing or incomplete
- Broadcasting rules are not implemented

### Design Issues

1. **API Inconsistency:**
   - Some methods take `u32` IDs, others expect `NodeId`
   - Mixed error handling patterns across similar operations
   - Inconsistent naming conventions

2. **Missing Features:**
   - No graph serialization/deserialization
   - No memory usage estimation
   - No execution planning or optimization
   - Limited debugging and introspection tools

3. **Performance Concerns:**
   - Linear search for nodes in topology operations
   - No caching of expensive computations
   - No parallel execution support

### Recommended Improvements

1. **Fix Memory Leak in getNodeConsumers:**
```zig
// Option 1: Return slice that doesn't need freeing
pub fn getNodeConsumers(self: *const Self, node_id: NodeId) []const NodeId {
    const node = self.getNode(node_id) orelse return &[_]NodeId{};
    return node.consumers;
}

// Option 2: Make caller responsible with clear ownership
pub fn getNodeConsumersAlloc(self: *const Self, allocator: Allocator, node_id: NodeId) ![]u32 {
    const node = self.getNode(node_id) orelse return error.NodeNotFound;
    const result = try allocator.alloc(u32, node.consumers.len);
    for (node.consumers, 0..) |consumer, i| {
        result[i] = @intFromEnum(consumer);
    }
    return result; // Caller must free
}
```

2. **Standardize API on Type-Safe IDs:**
```zig
// Convert all APIs to use NodeId/ViewId consistently
pub fn add(self: *Self, a: NodeId, b: NodeId) !NodeId
pub fn getNodeConsumers(self: *const Self, node_id: NodeId) []const NodeId
pub fn isNodeUsed(self: *const Self, node_id: NodeId) bool
pub fn topologicalSort(self: *Self) ![]NodeId  // Not []u32

// Provide conversion helpers if needed for compatibility
pub fn nodeIdFromU32(id: u32) NodeId { return @enumFromInt(id); }
pub fn nodeIdToU32(id: NodeId) u32 { return @intFromEnum(id); }
```

2. **Performance Optimizations:**
```zig
// Add node indexing for faster topology operations
pub const NodeIndex = struct {
    by_id: HashMap(NodeId, *Node),
    by_type: HashMap(OpType, []NodeId),
    
    pub fn findByType(self: *Self, op_type: OpType) []NodeId;
    pub fn findConsumers(self: *Self, node_id: NodeId) []NodeId;
};
```

3. **Advanced Features:**
```zig
// Graph optimization passes
pub const GraphOptimizer = struct {
    pub fn eliminateDeadCode(graph: *GraphEngine) !void;
    pub fn fuseOperations(graph: *GraphEngine) !void;
    pub fn constantFolding(graph: *GraphEngine) !void;
};

// Memory estimation
pub fn estimateMemoryUsage(self: *Self) !MemoryEstimate;

// Execution planning
pub fn createExecutionPlan(self: *Self) !ExecutionPlan;
```

4. **Better Debugging:**
```zig
// Graph visualization
pub fn exportDot(self: *Self, writer: anytype) !void;
pub fn printStatistics(self: *Self) void;

// Node tracing
pub fn enableTracing(self: *Self, node_id: NodeId) void;
pub fn getExecutionTrace(self: *Self) []TraceEvent;
```

## Integration with Core System

The graph engine integrates tightly with other core components:

- **Shape Engine**: Uses view IDs for tensor shape management
- **Symbolic Engine**: Coordinates with symbolic expressions in shapes
- **Memory System**: Uses core's arena allocator for all allocations
- **Error System**: Follows core's error propagation patterns

## Testing Coverage

Current test coverage includes:

- **Node Creation**: All operation types with various input configurations
- **Graph Validation**: Cycle detection, topology sorting
- **Error Conditions**: Invalid inputs, incompatible shapes, state violations
- **Integration**: Cross-module interaction with shape and symbolic engines

**Recommended Additional Tests:**
1. **Performance**: Large graph construction and traversal
2. **Memory**: Memory usage patterns and leak detection
3. **Edge Cases**: Empty graphs, single nodes, complex topologies
4. **Optimization**: Graph transformation correctness

## Summary

The graph engine provides a robust foundation for computational graph management with full symbolic dimension support:

**Key Strengths:**
- Type-safe ID system with `NodeId`, `ViewId`, `ShapeId`, and `ExprId`
- Full support for symbolic dimensions with runtime shape resolution
- Clear separation between graph structure and tensor operations
- Comprehensive topology management with cycle detection
- Memory-efficient arena-based allocation with object pooling
- Strong integration with shape and symbolic engines
- Focus on Luminal primitives ensures consistency
- Well-structured component architecture

**Recent Improvements:**
- ✅ Migrated to type-safe ID system throughout most of the codebase
- ✅ Full symbolic dimension support for dynamic tensor shapes
- ✅ Enhanced buffer safety with explicit size tracking
- ✅ Improved memory safety with arena-based allocation
- ✅ Better integration with shape engine's symbolic capabilities

**Remaining Issues:**
1. **Memory leak** in `getNodeConsumers()` - allocates memory with no way to free it
2. **API inconsistency** - high-level convenience APIs still use u32 for backward compatibility
3. **Missing implementations** - complex tensor operations (concat, split, etc.) return `NotImplemented`
4. **Return type inconsistency** - TopologyManager returns raw u32 arrays instead of NodeId arrays

**Future Enhancements:**
- Complete type-safe ID migration in remaining methods
- Fix the memory leak in `getNodeConsumers()`
- Implement missing operator decompositions
- Add graph optimization passes
- Improve error handling with better context
- Add serialization/deserialization support

The graph engine now provides a solid foundation for both static and dynamic computational graphs, enabling sophisticated machine learning models with variable-sized inputs while maintaining type safety and memory efficiency.