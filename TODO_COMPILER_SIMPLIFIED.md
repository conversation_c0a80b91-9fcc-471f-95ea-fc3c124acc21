# Idiomatic Zig Compiler Implementation Plan

This document provides a pragmatic, idiomatic Zig approach to implementing a compiler system that matches or exceeds Luminal's capabilities while following Zig best practices from CLAUDE.md and integrating seamlessly with the existing core architecture.

## Core Design Principles

1. **Explicit Memory Management** - All allocators passed explicitly, no hidden allocations
2. **Comptime Polymorphism** - Use Zig's comptime features instead of vtables
3. **Error Handling** - Explicit error unions and proper error propagation
4. **Unmanaged Containers** - Follow Zig 0.14 patterns with explicit allocator passing
5. **Progressive Enhancement** - Start concrete, add abstractions only when needed
6. **Integration First** - Build on existing Core infrastructure, don't replace it

## Key Features from Luminal to Implement

Based on analysis of Luminal's codebase, we need:

1. **Compiler Passes**:
   - Common Subexpression Elimination (CSE)
   - Arithmetic Elimination
   - Dead Code Elimination
   - Operation Fusion
   - Constant Folding
   - Memory Layout Optimization
   - Autograd/Autodiff (for training)

2. **Backend Support**:
   - CPU with SIMD vectorization
   - CUDA (future)
   - Metal (future)
   - Storage buffer management
   - Command buffer optimization

3. **Advanced Features**:
   - Dynamic shapes with symbolic dimensions
   - Automatic memory management via consumer tracking
   - Kernel fusion for elementwise operations
   - Optimized matrix multiplication with tiling
   - Training support with automatic differentiation

## Phase 1: Core Integration & Infrastructure (Week 1)

### Integrate CompilerEngine with existing Core

```zig
// src/core/compiler/engine.zig
const std = @import("std");
const Core = @import("../core.zig").Core;
const graph_mod = @import("../graph/engine.zig");
const shape_mod = @import("../shape/engine.zig");
const symbolic_mod = @import("../symbolic/engine.zig");
const types = @import("../types.zig");
const operator_types = @import("../graph/operator_types.zig");

const Allocator = std.mem.Allocator;
const NodeId = types.NodeId;
const ShapeId = types.ShapeId;

/// Compiler engine that integrates with existing Core architecture
pub const CompilerEngine = struct {
    const Self = @This();

    /// Reference to parent Core - uses existing infrastructure
    core: *Core,
    
    /// Optimization passes in execution order
    passes: std.ArrayListUnmanaged(OptimizationPass) = .{},
    
    /// Consumer tracking for memory optimization
    consumer_tracker: ConsumerTracker,
    
    /// Compiler diagnostics
    diagnostics: std.ArrayListUnmanaged(CompilerDiagnostic) = .{},
    
    pub fn init(core: *Core) !Self {
        const allocator = core.arena.allocator();
        
        var self = Self{
            .core = core,
            .consumer_tracker = ConsumerTracker.init(allocator),
        };
        
        // Register default passes matching Luminal's GenericCompiler
        try self.registerDefaultPasses();
        
        return self;
    }
    
    pub fn deinit(self: *Self) void {
        const allocator = self.core.arena.allocator();
        self.passes.deinit(allocator);
        self.consumer_tracker.deinit();
        self.diagnostics.deinit(allocator);
    }
    
    /// Main compilation entry point
    pub fn compile(self: *Self, backend_name: []const u8) !CompiledGraph {
        // Get backend from Core's registry
        const backend = self.core.backends.get(backend_name) orelse 
            return error.BackendNotFound;
        
        // Phase 1: Run optimization passes
        try self.runOptimizationPasses();
        
        // Phase 2: Analyze consumer counts
        try self.consumer_tracker.analyze(&self.core.graph);
        
        // Phase 3: Create execution plan
        const plan = try self.createExecutionPlan();
        
        // Phase 4: Backend-specific compilation
        return self.compileWithBackend(backend, plan);
    }
    
    fn registerDefaultPasses(self: *Self) !void {
        const allocator = self.core.arena.allocator();
        
        // Direct function pointers - no abstraction needed
        const passes = [_]OptimizationPass{
            .{ .name = "remove_unused", .apply = removeUnusedNodes },
            .{ .name = "arithmetic_elimination", .apply = eliminateArithmetic },
            .{ .name = "cse", .apply = CommonSubexpressionElimination.apply },
            .{ .name = "constant_folding", .apply = foldConstants },
            .{ .name = "fusion", .apply = ElementwiseFusion.apply },
        };
        
        try self.passes.appendSlice(allocator, &passes);
    }
    
    fn runOptimizationPasses(self: *Self) !void {
        var changed = true;
        var iteration: u32 = 0;
        
        while (changed and iteration < 10) : (iteration += 1) {
            changed = false;
            
            for (self.passes.items) |pass| {
                const pass_changed = try pass.apply(self);
                changed = changed or pass_changed;
                
                if (pass_changed) {
                    std.log.debug("Pass '{s}' made changes in iteration {}", .{ pass.name, iteration });
                }
            }
        }
    }
    
    fn createExecutionPlan(self: *Self) !ExecutionPlan {
        const allocator = self.core.arena.allocator();
        const graph = &self.core.graph;
        
        // Get topological order
        const topo_order = try graph.topology_manager.topologicalSort();
        defer allocator.free(topo_order);
        
        var operations = std.ArrayList(Operation).init(allocator);
        var memory_layout = try MemoryLayout.init(allocator, self.consumer_tracker);
        
        for (topo_order) |node_id| {
            const node = graph.getNode(node_id) orelse continue;
            
            // Get shape information from shape engine
            const shape_info = try self.core.shape.getShapeInfo(node.shape_id);
            
            try operations.append(.{
                .node_id = node_id,
                .op_type = node.op,
                .inputs = try allocator.dupe(NodeId, node.inputs.items),
                .shape = shape_info,
                .memory_slot = try memory_layout.allocateSlot(node_id, shape_info.size_bytes),
            });
        }
        
        return ExecutionPlan{
            .operations = try operations.toOwnedSlice(),
            .memory_layout = memory_layout,
            .allocator = allocator,
        };
    }
};

/// Simple optimization pass structure - no vtables needed
pub const OptimizationPass = struct {
    name: []const u8,
    apply: *const fn (engine: *CompilerEngine) anyerror!bool,
};

/// Diagnostic information for compiler errors
pub const CompilerDiagnostic = struct {
    node_id: ?NodeId = null,
    pass_name: []const u8,
    message: []const u8,
    suggestion: ?[]const u8 = null,
    
    pub fn format(
        self: CompilerDiagnostic,
        comptime fmt: []const u8,
        options: std.fmt.FormatOptions,
        writer: anytype,
    ) !void {
        _ = fmt;
        _ = options;
        try writer.print("Compiler error in pass '{s}': {s}", .{ self.pass_name, self.message });
        if (self.node_id) |id| {
            try writer.print(" (node {})", .{@intFromEnum(id)});
        }
        if (self.suggestion) |s| {
            try writer.print("\n  Suggestion: {s}", .{s});
        }
    }
};
```

### Enhanced Backend Interface

```zig
// Update src/core/types.zig to add enhanced backend support
pub const BackendCapability = packed struct {
    compile: bool = true,
    execute: bool = true,
    optimize: bool = false,
    profile: bool = false,
    distributed: bool = false,
};

/// Enhanced backend interface that maintains compatibility
pub const BackendV2 = struct {
    // Keep reference to legacy backend if needed
    legacy: ?*Backend = null,
    
    // Basic info
    name: []const u8,
    capabilities: BackendCapability,
    
    // Lifecycle
    initWithCore: *const fn (core: *Core) anyerror!*BackendV2,
    deinit: *const fn (self: *BackendV2) void,
    
    // Compilation - returns compiled graph
    compileGraph: *const fn (
        self: *BackendV2,
        engine: *CompilerEngine,
        plan: ExecutionPlan,
    ) anyerror!CompiledGraph,
    
    // Execution using existing operator types
    executeGraph: *const fn (
        self: *BackendV2,
        compiled: *CompiledGraph,
        inputs: []const operator_types.OperatorInput,
    ) anyerror![]operator_types.OperatorOutput,
    
    // Optional: Memory planning
    planMemory: ?*const fn (
        self: *BackendV2,
        plan: *ExecutionPlan,
    ) anyerror!void = null,
};

/// Update Core to support compiler
// In core.zig, add to Core struct:
pub const Core = struct {
    // ... existing fields ...
    
    // Compiler engine (lazy initialization)
    compiler: ?*CompilerEngine = null,
    
    pub fn getCompiler(self: *Core) !*CompilerEngine {
        if (self.compiler == null) {
            self.compiler = try self.arena.allocator().create(CompilerEngine);
            self.compiler.?.* = try CompilerEngine.init(self);
        }
        return self.compiler.?;
    }
    
    // In deinit(), add:
    // if (self.compiler) |compiler| {
    //     compiler.deinit();
    // }
};
```

### Common Subexpression Elimination

```zig
// src/core/compiler/passes/cse.zig
pub const CommonSubexpressionElimination = struct {
    pub fn apply(engine: *CompilerEngine) !bool {
        const allocator = engine.core.arena.allocator();
        const graph = &engine.core.graph;
        
        var expr_map = std.StringHashMapUnmanaged(NodeId){};
        defer expr_map.deinit(allocator);
        
        var changed = false;
        
        // Process nodes in topological order
        const topo_order = try graph.topology_manager.topologicalSort();
        defer allocator.free(topo_order);
        
        for (topo_order) |node_id| {
            const node = graph.getNode(node_id) orelse continue;
            
            // Skip certain node types (matching Luminal's Function check)
            if (node.op == .variable or node.op == .constant) continue;
            
            // Create signature for this operation
            const signature = try createSignature(allocator, engine, node);
            defer allocator.free(signature);
            
            if (expr_map.get(signature)) |existing_id| {
                // Found duplicate - merge nodes
                try mergeNodes(graph, node_id, existing_id);
                changed = true;
            } else {
                // New expression - add to map
                const key = try allocator.dupe(u8, signature);
                try expr_map.put(allocator, key, node_id);
            }
        }
        
        return changed;
    }
    
    fn createSignature(
        allocator: Allocator,
        engine: *CompilerEngine,
        node: *const graph_mod.Node,
    ) ![]const u8 {
        var sig = std.ArrayList(u8).init(allocator);
        errdefer sig.deinit();
        
        // Include operation type
        try sig.writer().print("{s}", .{@tagName(node.op)});
        
        // Include sorted input node IDs
        var sorted_inputs = try allocator.dupe(NodeId, node.inputs.items);
        defer allocator.free(sorted_inputs);
        std.sort.heap(NodeId, sorted_inputs, {}, struct {
            fn lessThan(_: void, a: NodeId, b: NodeId) bool {
                return @intFromEnum(a) < @intFromEnum(b);
            }
        }.lessThan);
        
        for (sorted_inputs) |input_id| {
            try sig.writer().print(",{}", .{@intFromEnum(input_id)});
        }
        
        // Include shape information
        if (engine.core.shape.getShape(node.shape_id)) |shape| {
            try sig.writer().print(";dims=");
            for (shape.dims, 0..) |dim, i| {
                if (i > 0) try sig.writer().print(",");
                // Handle symbolic dimensions
                try sig.writer().print("{}", .{dim});
            }
        }
        
        return sig.toOwnedSlice();
    }
};
```

## Phase 2: CPU Backend with Existing Infrastructure (Week 2)

### CPU Backend using Operator System

```zig
// src/core/backends/cpu/backend.zig
const std = @import("std");
const Core = @import("../../core.zig").Core;
const compiler_mod = @import("../../compiler/engine.zig");
const operator_mod = @import("../../graph/operator.zig");
const operator_types = @import("../../graph/operator_types.zig");
const types = @import("../../types.zig");

pub const CpuBackend = struct {
    core: *Core,
    thread_pool: ThreadPool,
    kernel_registry: KernelRegistry,
    
    pub fn initWithCore(core: *Core) !*CpuBackend {
        const allocator = core.arena.allocator();
        const self = try allocator.create(CpuBackend);
        
        self.* = .{
            .core = core,
            .thread_pool = try ThreadPool.init(allocator, null),
            .kernel_registry = KernelRegistry.init(allocator),
        };
        
        try self.registerKernels();
        return self;
    }
    
    pub fn deinit(self: *CpuBackend) void {
        self.thread_pool.deinit();
        self.kernel_registry.deinit();
    }
    
    fn registerKernels(self: *CpuBackend) !void {
        // Register optimized kernels for each operation type
        try self.kernel_registry.register(.add, CpuKernels.createAddKernel);
        try self.kernel_registry.register(.multiply, CpuKernels.createMultiplyKernel);
        try self.kernel_registry.register(.matmul, CpuKernels.createMatMulKernel);
        try self.kernel_registry.register(.relu, CpuKernels.createReluKernel);
        // ... more kernels
    }
    
    pub fn compileGraph(
        self: *CpuBackend,
        engine: *compiler_mod.CompilerEngine,
        plan: compiler_mod.ExecutionPlan,
    ) !compiler_mod.CompiledGraph {
        const allocator = self.core.arena.allocator();
        
        // Create CPU-specific execution plan
        var cpu_plan = try allocator.create(CpuExecutionPlan);
        cpu_plan.* = .{
            .base_plan = plan,
            .operators = std.AutoArrayHashMapUnmanaged(types.NodeId, *operator_mod.Operator){},
            .memory_buffers = std.AutoArrayHashMapUnmanaged(types.NodeId, []u8){},
        };
        
        // Create operators for each operation
        for (plan.operations) |op| {
            const kernel_fn = self.kernel_registry.get(op.op_type) orelse 
                return error.UnsupportedOperation;
            
            const operator = try kernel_fn(self, op);
            try cpu_plan.operators.put(allocator, op.node_id, operator);
        }
        
        // Allocate memory buffers based on memory layout
        try self.allocateBuffers(cpu_plan, plan.memory_layout);
        
        return compiler_mod.CompiledGraph{
            .backend_data = cpu_plan,
            .backend = .{ .v2 = self },
            .allocator = allocator,
        };
    }
    
    pub fn executeGraph(
        self: *CpuBackend,
        compiled: *compiler_mod.CompiledGraph,
        inputs: []const operator_types.OperatorInput,
    ) ![]operator_types.OperatorOutput {
        const cpu_plan = @as(*CpuExecutionPlan, @ptrCast(@alignCast(compiled.backend_data)));
        const allocator = self.core.arena.allocator();
        
        var outputs = std.ArrayList(operator_types.OperatorOutput).init(allocator);
        
        // Execute operations in topological order
        for (cpu_plan.base_plan.operations) |op| {
            const operator = cpu_plan.operators.get(op.node_id) orelse continue;
            
            // Gather inputs
            var op_inputs = try self.gatherInputs(cpu_plan, op, inputs);
            defer allocator.free(op_inputs);
            
            // Execute operator
            const op_outputs = try operator.process(op_inputs, allocator);
            
            // Store outputs
            try self.storeOutputs(cpu_plan, op.node_id, op_outputs);
            
            // Handle memory management based on consumer tracking
            try self.updateConsumerCounts(cpu_plan, op);
        }
        
        // Extract final outputs
        return self.extractOutputs(cpu_plan, &outputs);
    }
};

/// CPU kernels that build on existing Operator interface
pub const CpuKernels = struct {
    pub fn createAddKernel(backend: *CpuBackend, op: Operation) !*operator_mod.Operator {
        const allocator = backend.core.arena.allocator();
        const kernel = try allocator.create(AddKernel);
        
        kernel.* = .{
            .op = op,
            .vectorized = op.shape.n_elements > 1000,
            .parallel = op.shape.n_elements > 10000,
        };
        
        const operator = try allocator.create(operator_mod.Operator);
        operator.* = .{
            .processFn = AddKernel.process,
            .data = kernel,
        };
        
        return operator;
    }
    
    const AddKernel = struct {
        op: Operation,
        vectorized: bool,
        parallel: bool,
        
        pub fn process(
            operator: *operator_mod.Operator,
            inputs: []const operator_types.OperatorInput,
            allocator: Allocator,
        ) ![]operator_types.OperatorOutput {
            const self = @as(*AddKernel, @ptrCast(@alignCast(operator.data.?)));
            
            if (inputs.len != 2) return error.InvalidInputCount;
            
            const a = inputs[0].data.asSlice();
            const b = inputs[1].data.asSlice();
            
            // Allocate output
            const output = try allocator.alloc(f32, a.len);
            
            if (self.parallel and a.len > 10000) {
                try executeParallel(a, b, output);
            } else if (self.vectorized) {
                executeVectorized(a, b, output);
            } else {
                executeScalar(a, b, output);
            }
            
            // Return as OperatorOutput
            var result = try allocator.alloc(operator_types.OperatorOutput, 1);
            result[0] = .{
                .data = operator_types.TensorDataRef.fromOwned(output),
                .view = inputs[0].view, // Preserve view descriptor
            };
            
            return result;
        }
        
        fn executeVectorized(a: []const f32, b: []const f32, out: []f32) void {
            const vec_size = 8; // AVX width
            var i: usize = 0;
            
            // Main vectorized loop
            while (i + vec_size <= a.len) : (i += vec_size) {
                const a_vec: @Vector(vec_size, f32) = a[i..][0..vec_size].*;
                const b_vec: @Vector(vec_size, f32) = b[i..][0..vec_size].*;
                out[i..][0..vec_size].* = a_vec + b_vec;
            }
            
            // Scalar remainder
            while (i < a.len) : (i += 1) {
                out[i] = a[i] + b[i];
            }
        }
    };
};
```

## Phase 3: Memory Management & Consumer Tracking (Week 3)

### Consumer Tracking for Automatic Memory Management

```zig
/// Consumer tracking that integrates with existing memory manager
pub const ConsumerTracker = struct {
    allocator: Allocator,
    /// Map from node ID to number of consumers
    consumer_counts: std.AutoHashMapUnmanaged(NodeId, u32) = .{},
    /// Nodes that must persist (outputs, parameters)
    persistent_nodes: std.AutoHashMapUnmanaged(NodeId, void) = .{},
    /// Integration with graph's memory manager
    memory_manager: *memory.MemoryManager,
    
    pub fn init(allocator: Allocator) ConsumerTracker {
        return .{ 
            .allocator = allocator,
            .memory_manager = undefined, // Set during analyze
        };
    }
    
    pub fn deinit(self: *ConsumerTracker) void {
        self.consumer_counts.deinit(self.allocator);
        self.persistent_nodes.deinit(self.allocator);
    }
    
    pub fn analyze(self: *ConsumerTracker, graph: *graph_mod.GraphEngine) !void {
        self.consumer_counts.clearRetainingCapacity();
        self.persistent_nodes.clearRetainingCapacity();
        self.memory_manager = &graph.memory_manager;
        
        // Use existing node iteration
        for (graph.nodes.items, 0..) |node, idx| {
            const node_id = graph.node_map.keys()[idx];
            
            // Mark outputs and parameters as persistent
            if (node.consumers.items.len == 0 or node.op == .variable) {
                try self.persistent_nodes.put(self.allocator, node_id, {});
            }
            
            // Count how many times each input is consumed
            for (node.inputs.items) |input_id| {
                const count = self.consumer_counts.get(input_id) orelse 0;
                try self.consumer_counts.put(self.allocator, input_id, count + 1);
            }
        }
    }
    
    pub fn shouldFree(self: *ConsumerTracker, node_id: NodeId) bool {
        if (self.persistent_nodes.contains(node_id)) return false;
        const count = self.consumer_counts.get(node_id) orelse 0;
        return count == 0;
    }
    
    pub fn consumeNode(self: *ConsumerTracker, node_id: NodeId) !void {
        if (self.consumer_counts.getPtr(node_id)) |count_ptr| {
            if (count_ptr.* > 0) {
                count_ptr.* -= 1;
                
                // Integrate with existing memory manager
                if (count_ptr.* == 0 and !self.persistent_nodes.contains(node_id)) {
                    try self.memory_manager.markForReuse(node_id);
                }
            }
        }
    }
};

/// Memory layout that uses existing memory pools
pub const MemoryLayout = struct {
    allocator: Allocator,
    consumer_tracker: ConsumerTracker,
    /// Reuse existing buffer pool from Core
    buffer_pool: *std.heap.MemoryPool(types.TensorBuffer),
    /// Memory slots for each node
    slots: std.AutoHashMapUnmanaged(NodeId, MemorySlot) = .{},
    
    pub const MemorySlot = struct {
        offset: usize,
        size: usize,
        buffer: ?*types.TensorBuffer = null,
    };
    
    pub fn init(allocator: Allocator, consumer_tracker: ConsumerTracker) !MemoryLayout {
        return .{
            .allocator = allocator,
            .consumer_tracker = consumer_tracker,
            .buffer_pool = undefined, // Set from Core
        };
    }
    
    pub fn allocateSlot(self: *MemoryLayout, node_id: NodeId, size: usize) !MemorySlot {
        // Try to reuse a freed slot first
        if (self.findReusableSlot(size)) |slot| {
            try self.slots.put(self.allocator, node_id, slot);
            return slot;
        }
        
        // Allocate new slot from pool
        const buffer = try self.buffer_pool.create();
        buffer.* = .{
            .data = try self.allocator.alignedAlloc(u8, 32, size), // AVX alignment
            .dtype = .f32, // Will be set properly later
        };
        
        const slot = MemorySlot{
            .offset = 0,
            .size = size,
            .buffer = buffer,
        };
        
        try self.slots.put(self.allocator, node_id, slot);
        return slot;
    }
};
```

## Phase 4: Advanced Optimizations (Week 4)

### Operation Fusion

```zig
// src/core/compiler/passes/fusion.zig
pub const ElementwiseFusion = struct {
    pub fn apply(engine: *CompilerEngine) !bool {
        const allocator = engine.core.arena.allocator();
        const graph = &engine.core.graph;
        
        var changed = false;
        
        // Find fusable chains
        const chains = try findFusableChains(allocator, graph);
        defer allocator.free(chains);
        
        for (chains) |chain| {
            if (chain.len < 2) continue;
            
            // Create fused operation
            const fused_node = try createFusedNode(graph, chain);
            
            // Redirect edges
            try redirectEdges(graph, chain, fused_node);
            
            // Mark old nodes for removal
            for (chain) |node_id| {
                if (node_id != fused_node) {
                    try graph.markNodeForRemoval(node_id);
                }
            }
            
            changed = true;
        }
        
        return changed;
    }
    
    fn findFusableChains(allocator: Allocator, graph: *graph_mod.GraphEngine) ![][]const NodeId {
        var chains = std.ArrayList([]const NodeId).init(allocator);
        var visited = std.AutoHashMap(NodeId, void).init(allocator);
        defer visited.deinit();
        
        const topo_order = try graph.topology_manager.topologicalSort();
        defer allocator.free(topo_order);
        
        for (topo_order) |node_id| {
            if (visited.contains(node_id)) continue;
            
            if (try buildElementwiseChain(allocator, graph, node_id, &visited)) |chain| {
                try chains.append(chain);
            }
        }
        
        return chains.toOwnedSlice();
    }
    
    fn isElementwiseOp(op: graph_mod.OpType) bool {
        return switch (op) {
            .add, .multiply, .subtract, .divide,
            .neg, .exp, .log, .sqrt, .abs, .sign,
            .sin, .cos, .relu, .gelu => true,
            else => false,
        };
    }
};
```

### Autograd Implementation

```zig
// src/core/compiler/passes/autograd.zig
pub const Autograd = struct {
    pub fn buildGradientGraph(engine: *CompilerEngine, loss_node: NodeId) !void {
        const allocator = engine.core.arena.allocator();
        const graph = &engine.core.graph;
        
        var grad_map = std.AutoHashMapUnmanaged(NodeId, NodeId){};
        defer grad_map.deinit(allocator);
        
        // Initialize gradient of loss to 1
        const ones = try graph.constant(&[_]f32{1.0});
        try grad_map.put(allocator, loss_node, ones);
        
        // Traverse backwards through graph
        const topo_order = try graph.topology_manager.topologicalSort();
        defer allocator.free(topo_order);
        
        // Process in reverse topological order
        var i = topo_order.len;
        while (i > 0) : (i -= 1) {
            const node_id = topo_order[i - 1];
            const grad_output = grad_map.get(node_id) orelse continue;
            
            const node = graph.getNode(node_id) orelse continue;
            
            // Compute gradients for inputs
            for (node.inputs.items, 0..) |input_id, idx| {
                const grad_input = try computeGradient(
                    engine,
                    node,
                    idx,
                    grad_output,
                );
                
                // Accumulate gradients
                if (grad_map.get(input_id)) |existing_grad| {
                    const sum = try graph.add(&[_]NodeId{ existing_grad, grad_input });
                    try grad_map.put(allocator, input_id, sum);
                } else {
                    try grad_map.put(allocator, input_id, grad_input);
                }
            }
        }
    }
    
    fn computeGradient(
        engine: *CompilerEngine,
        node: *graph_mod.Node,
        input_idx: usize,
        grad_output: NodeId,
    ) !NodeId {
        const graph = &engine.core.graph;
        
        return switch (node.op) {
            .add => grad_output, // Gradient passes through unchanged
            .multiply => {
                // Gradient is other input * grad_output
                const other_idx = 1 - input_idx;
                const other_input = node.inputs.items[other_idx];
                return try graph.multiply(&[_]NodeId{ other_input, grad_output });
            },
            .relu => {
                // Gradient is grad_output * (input > 0)
                const input = node.inputs.items[0];
                const mask = try graph.greater_than(&[_]NodeId{ input, try graph.constant(&[_]f32{0.0}) });
                return try graph.multiply(&[_]NodeId{ mask, grad_output });
            },
            else => return error.UnsupportedGradient,
        };
    }
};
```

## Key Implementation Details

### Memory Management
- Uses existing arena allocators from Core
- Leverages existing memory pools for objects
- Buffer pool integration for tensor data
- Explicit allocator passing throughout

### Error Handling
- Explicit error unions on all fallible operations
- Proper cleanup with `errdefer`
- Diagnostic system for detailed compiler errors
- Integration with existing error types

### Performance Features
- SIMD vectorization using Zig's `@Vector`
- Cache-friendly tiling for matrix operations
- Thread pool for parallel execution
- Reuses existing operator system for kernels

### Integration Points
- CompilerEngine lives within Core structure
- Uses existing GraphEngine, ShapeEngine, SymbolicEngine
- Leverages existing memory pools and managers
- Builds on operator interface for execution
- Compatible with existing backend registry

### Extensibility
- Simple function pointers for passes (no abstraction needed)
- Tagged unions only where beneficial
- Clean separation of passes and backends
- Easy to add new operations and optimizations

## Implementation Timeline

**Week 1**: 
- Integrate CompilerEngine with Core
- Basic passes (CSE, constant folding, dead code elimination)
- Enhanced backend interface

**Week 2**: 
- CPU backend using existing operator system
- Vectorized kernels for common operations
- Integration with existing memory manager

**Week 3**: 
- Consumer tracking and automatic memory management
- Operation fusion for elementwise chains
- Memory layout optimization

**Week 4**: 
- Autograd/automatic differentiation
- Performance tuning and benchmarking
- Testing with existing test infrastructure

This approach provides all of Luminal's power while:
- Following idiomatic Zig patterns from CLAUDE.md
- Integrating seamlessly with existing architecture
- Reusing existing infrastructure where possible
- Maintaining explicit control and zero-cost abstractions