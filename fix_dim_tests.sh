#!/bin/bash
# Script to fix Dim references in test files

# Step 1: Find files referencing types.Dim or shape_types.Dim
FILES=$(find src examples -type f -name "*.zig" -exec grep -l "types\.Dim\|shape_types\.Dim" {} \;)

echo "Found $(echo "$FILES" | wc -l) files to fix"

# Step 2: Fix common pattern - array initialization with Dim
for file in $FILES; do
    echo "Processing $file"
    
    # Replace array initialization patterns
    # [_]types.Dim{ .{ .concrete = N } } -> [_]*types.Expr{ try types.Expr.testDim(allocator, N) }
    sed -i '' 's/\[[_]\]types\.Dim{/[_]*types.Expr{/g' "$file"
    sed -i '' 's/\[[_]\]shape_types\.Dim{/[_]*shape_types.Expr{/g' "$file"

    # Fix direct concrete and symbolic access pattern matching
    sed -i '' 's/shape\.dims\[\([^]]*\)\]\.concrete/self.core.symbolic.evaluate(shape.dims[\1], null) catch @as(i64, 1)/g' "$file"
    sed -i '' 's/shape\.dims\[\([^]]*\)\] == \.concrete/shape.dims[\1].tag == .integer/g' "$file"
    sed -i '' 's/shape\.dims\[\([^]]*\)\] == \.symbolic/shape.dims[\1].tag == .symbol/g' "$file"
    
    # Replace concrete dimension initializations with testDim
    # TODO: This pattern is complex and might need manual fixing
    grep -n "\.concrete = " "$file" || echo "No concrete initializations found"
done

echo "Script completed. Manual verification needed for each file."