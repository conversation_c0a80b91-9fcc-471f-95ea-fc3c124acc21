pub const POSITIVE_FIXINT_MIN = 0x00;
pub const POSITIVE_FIXINT_MAX = 0x7f;
pub const FIXMAP_MIN = 0x80;
pub const FIXMAP_MAX = 0x8f;
pub const FIXARRAY_MIN = 0x90;
pub const FIXARRAY_MAX = 0x9f;
pub const FIXSTR_MIN = 0xa0;
pub const FIXSTR_MAX = 0xbf;
pub const NIL = 0xc0;
pub const FALSE = 0xc2;
pub const TRUE = 0xc3;
pub const BIN8 = 0xc4;
pub const BIN16 = 0xc5;
pub const BIN32 = 0xc6;
pub const EXT8 = 0xc7;
pub const EXT16 = 0xc8;
pub const EXT32 = 0xc9;
pub const FLOAT32 = 0xca;
pub const FLOAT64 = 0xcb;
pub const UINT8 = 0xcc;
pub const UINT16 = 0xcd;
pub const UINT32 = 0xce;
pub const UINT64 = 0xcf;
pub const INT8 = 0xd0;
pub const INT16 = 0xd1;
pub const INT32 = 0xd2;
pub const INT64 = 0xd3;
pub const FIXEXT1 = 0xd4;
pub const FIXEXT2 = 0xd5;
pub const FIXEXT4 = 0xd6;
pub const FIXEXT8 = 0xd7;
pub const FIXEXT16 = 0xd8;
pub const STR8 = 0xd9;
pub const STR16 = 0xda;
pub const STR32 = 0xdb;
pub const ARRAY16 = 0xdc;
pub const ARRAY32 = 0xdd;
pub const MAP16 = 0xde;
pub const MAP32 = 0xdf;
pub const NEGATIVE_FIXINT_MIN = 0xe0;
pub const NEGATIVE_FIXINT_MAX = 0xff;
