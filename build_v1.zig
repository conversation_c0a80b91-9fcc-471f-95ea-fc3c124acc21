//===-- build.zig - Zing Build System -----------------------------------===//
//
// Zing build system configuration.
//
//===----------------------------------------------------------------------===//

const std = @import("std");

pub fn build(b: *std.Build) void {
    const target = b.standardTargetOptions(.{});
    const optimize = b.standardOptimizeOption(.{});

    //--------------------------------------------------------------------------
    // Configuration options
    //--------------------------------------------------------------------------
    
    // FFI option (disabled by default)
    const enable_ffi = b.option(
        bool,
        "enable_ffi",
        "Enable FFI integration with external libraries (default: false)",
    ) orelse false;
    
    // Debug assert option for shape module (default: true in Debug mode, false in Release mode)
    const shape_debug_assert = b.option(
        bool,
        "shape_debug_assert",
        "Enable debug assertions and warnings in shape module (default: true in Debug, false in Release)",
    ) orelse (@import("builtin").mode == .Debug);

    // Verbose output option (default: true in Debug mode, false in Release mode)
    const verbose_output = b.option(
        bool,
        "verbose",
        "Enable verbose debug output in tests and examples (default: true in Debug, false in Release)",
    ) orelse (@import("builtin").mode == .Debug);

    //--------------------------------------------------------------------------
    // Module definitions
    //--------------------------------------------------------------------------
    
    //--------------------------------------------------------------------------
    // Shape module feature flags
    //--------------------------------------------------------------------------
    
    // Enable memory pooling for dimension arrays
    const shape_enable_memory_pool = b.option(
        bool,
        "shape-enable-memory-pool",
        "Enable memory pooling for dimension arrays (default: false)",
    ) orelse false;
    
    // Enable content-addressable caching for shape operations
    const shape_enable_caching = b.option(
        bool,
        "shape-enable-caching", 
        "Enable content-addressable caching for shape operations (default: false)",
    ) orelse false;
    
    // Enable balanced traversal for deep operation chains
    const shape_enable_balanced_traversal = b.option(
        bool,
        "shape-enable-balanced-traversal",
        "Enable balanced traversal for deep operation chains (default: false)",
    ) orelse false;
    
    // Enable detailed error context reporting
    const shape_enable_error_context = b.option(
        bool,
        "shape-enable-error-context",
        "Enable detailed error context reporting (default: false)",
    ) orelse false;
    
    // Enable advanced resource usage statistics tracking
    const shape_enable_advanced_stats = b.option(
        bool,
        "shape-enable-advanced-stats",
        "Enable advanced resource usage statistics tracking (default: false)",
    ) orelse false;
    
    // Define options module
    const options = b.addOptions();
    options.addOption(bool, "symbolic_enable_ffi", enable_ffi);
    options.addOption(bool, "verbose", verbose_output); // Add verbose flag to options module
    
    // Add shape feature flags to options module
    options.addOption(bool, "shape_enable_memory_pool", shape_enable_memory_pool);
    options.addOption(bool, "shape_enable_caching", shape_enable_caching);
    options.addOption(bool, "shape_enable_balanced_traversal", shape_enable_balanced_traversal);
    options.addOption(bool, "shape_enable_error_context", shape_enable_error_context);
    options.addOption(bool, "shape_enable_advanced_stats", shape_enable_advanced_stats);
    
    const options_module = options.createModule();
    
    // Create shape options module
    const shape_options = b.addOptions();
    shape_options.addOption(bool, "ENABLE_SYMBOLIC_SHAPES", true); // Re-enable symbolic shapes
    shape_options.addOption(bool, "SHAPE_DEBUG_ASSERT", shape_debug_assert); // Add debug assert option
    const shape_options_module = shape_options.createModule();

    // Create root options module
    const root_options = b.addOptions();
    root_options.addOption(bool, "shape_verbose", verbose_output); // Add verbose output option to root
    
    // Create the common types module
    const common_types_module = b.addModule("common_types", .{
        .root_source_file = b.path("src/common/symbolic_types.zig"),
    });
    
    // Create the symbolic module
    const symbolic_module = b.addModule("symbolic", .{
        .root_source_file = b.path("src/symbolic/symbolic.zig"),
    });
    symbolic_module.addImport("options", options_module);
    symbolic_module.addImport("common_types", common_types_module);
    
    // Create shape module
    const shape_module = b.addModule("shape", .{
        .root_source_file = b.path("src/shape/shape.zig"),
    });
    shape_module.addImport("symbolic", symbolic_module);
    shape_module.addImport("shape_options", shape_options_module);
    shape_module.addImport("options", options_module);
    shape_module.addImport("common_types", common_types_module);
    
    // Create graph types module
    const graph_types_module = b.addModule("graph_types", .{
        .root_source_file = b.path("src/graph/types.zig"),
    });
    graph_types_module.addImport("symbolic", symbolic_module);
    graph_types_module.addImport("shape", shape_module);
    
    // Create graph memory pool module
    const memory_pool_module = b.addModule("memory_pool", .{
        .root_source_file = b.path("src/graph/memory_pool.zig"),
    });
    memory_pool_module.addImport("types", graph_types_module);
    
    // Create graph module first
    const graph_module = b.addModule("graph", .{
        .root_source_file = b.path("src/graph/module.zig"),
    });
    graph_module.addImport("symbolic", symbolic_module);
    graph_module.addImport("shape", shape_module);
    graph_module.addImport("graph_types", graph_types_module);
    graph_module.addImport("memory_pool", memory_pool_module);
    
    // Create tensor module
    const tensor_module = b.addModule("tensor", .{
        .root_source_file = b.path("src/graph/tensor.zig"),
    });
    tensor_module.addImport("symbolic", symbolic_module);
    tensor_module.addImport("shape", shape_module);
    tensor_module.addImport("graph_types", graph_types_module);
    
    // Create integration module last (after graph and tensor modules are defined)
    const integration_module = b.addModule("integration", .{
        .root_source_file = b.path("src/graph/integration.zig"),
    });
    integration_module.addImport("symbolic", symbolic_module);
    integration_module.addImport("shape", shape_module);
    integration_module.addImport("types", graph_types_module);
    integration_module.addImport("graph", graph_module);
    integration_module.addImport("tensor", tensor_module);
    
    // Create tensor_ops module (higher-level operations on top of graph)
    const tensor_ops_module = b.addModule("tensor_ops", .{
        .root_source_file = b.path("src/tensor_ops/module.zig"),
    });
    tensor_ops_module.addImport("symbolic", symbolic_module);
    tensor_ops_module.addImport("shape", shape_module);
    tensor_ops_module.addImport("graph", graph_module);

    //--------------------------------------------------------------------------
    // Test steps creation
    //--------------------------------------------------------------------------
    
    // Create the main test step - includes symbolic tests followed by shape module tests
    const test_step = b.step("test", "Run both symbolic and shape module tests in the correct order");
    
    // Create a step specifically for symbolic tests
    const symbolic_test_step = b.step("test-symbolic", "Run symbolic module tests");
    
    // Create a step for advanced symbolic tests
    const symbolic_advanced_test_step = b.step("test-symbolic-advanced", "Run advanced symbolic module tests");
    
    // Create a step for performance tests
    const symbolic_perf_test_step = b.step("test-symbolic-performance", "Run symbolic module performance tests");
    
    // Create a step for all tests (symbolic + shape + graph)
    const all_tests_step = b.step("test-all", "Run all unit tests");
    
    // Shape module test steps
    const shape_only_test_step = b.step("test-shape", "Run shape module tests only");
    const shape_comprehensive_test_step = b.step("test-shape-comprehensive", "Run comprehensive shape module tests");
    const shape_simple_test_step = b.step("test-shape-simple", "Run simple shape module tests");
    const shape_minimal_test_step = b.step("test-shape-minimal", "Run minimal shape module tests");
    const operations_test_step = b.step("test-shape-operations", "Run shape operations tests");
    const integration_test_step = b.step("test-shape-integration", "Run shape-symbolic integration tests");
    const minimal_integration_test_step = b.step("test-shape-minimal-integration", "Run minimal shape-symbolic integration tests");
    const symbolic_shape_test_step = b.step("test-symbolic-shape", "Run symbolic-shape integration tests");
    const minimal_tracker_test_step = b.step("test-shape-minimal-tracker", "Run minimal shape tracker integration tests");
    
    // Consolidated shape module test steps
    const consolidated_test_step = b.step("test-shape-consolidated", "Run all shape tests from consolidated test modules");
    const core_test_step = b.step("test-shape-core", "Run shape core tests from consolidated test module");
    const memory_management_test_step = b.step("test-shape-memory", "Run shape memory management tests from consolidated test module");
    const operations_consolidated_test_step = b.step("test-shape-ops-consolidated", "Run shape operations tests from consolidated test module");
    
    // TensorOps test steps
    const tensor_ops_test_step = b.step("test-tensor-ops", "Run tensor operations module tests");
    const tensor_ops_math_test_step = b.step("test-tensor-ops-math", "Run tensor operations math tests (matmul)");
    const tensor_ops_graph_test_step = b.step("test-tensor-ops-graph", "Run tensor operations graph structure verification tests");
    const integration_consolidated_test_step = b.step("test-shape-integration-consolidated", "Run shape-symbolic integration tests from consolidated test module");
    const feature_flags_test_step = b.step("test-shape-features", "Run shape feature flags tests from consolidated test module");
    
    // Graph module test steps
    const graph_index_test_step = b.step("test-graph-index", "Run graph index operations tests");
    const tensor_test_step = b.step("test-tensor", "Run tensor module tests");
    const minimal_tensor_test_step = b.step("test-tensor-minimal", "Run minimal tensor tests that avoid circular dependencies");
    const graph_test_step = b.step("test-graph", "Run graph module tests");
    const mock_graph_test_step = b.step("test-mock-graph", "Run mock graph implementation tests");
    const memory_test_step = b.step("test-memory", "Run memory management tests");
    const comprehensive_graph_test_step = b.step("test-graph-comprehensive", "Run comprehensive graph tests");
    const comprehensive_ops_test_step = b.step("test-graph-ops", "Run comprehensive graph operations tests");
    
    //--------------------------------------------------------------------------
    // 1. SYMBOLIC MODULE TESTS
    //--------------------------------------------------------------------------
    
    // Core symbolic module tests (basic functionality)
    const symbolic_core_tests = b.addTest(.{
        .name = "symbolic-core-tests",
        .root_source_file = b.path("src/symbolic/symbolic.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_core_tests.root_module.addImport("options", options_module);
    symbolic_core_tests.root_module.addImport("root_options", root_options.createModule());
    
    // Add build options to disable features that require zlib
    const test_options = b.addOptions();
    test_options.addOption(bool, "enable_zlib", false);
    symbolic_core_tests.root_module.addImport("test_options", test_options.createModule());
    
    // Link only LibC (no zlib)
    symbolic_core_tests.linkLibC();
    
    // Add symbolic_core_tests to the test steps
    const run_symbolic_core_tests = b.addRunArtifact(symbolic_core_tests);
    test_step.dependOn(&run_symbolic_core_tests.step);
    all_tests_step.dependOn(&run_symbolic_core_tests.step);
    symbolic_test_step.dependOn(&run_symbolic_core_tests.step);
    
    // Create symbolic native (non-FFI) tests - can be run without zlib
    const symbolic_native_tests = b.addTest(.{
        .name = "symbolic-native-tests",
        .root_source_file = b.path("src/symbolic/tests/native_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_native_tests.root_module.addImport("symbolic", symbolic_module);
    symbolic_native_tests.root_module.addImport("options", options_module);
    symbolic_native_tests.linkLibC();
    
    const run_symbolic_native_tests = b.addRunArtifact(symbolic_native_tests);
    test_step.dependOn(&run_symbolic_native_tests.step);
    all_tests_step.dependOn(&run_symbolic_native_tests.step);
    symbolic_test_step.dependOn(&run_symbolic_native_tests.step);
    
    // Add utils tests
    const symbolic_utils_tests = b.addTest(.{
        .name = "symbolic-utils-tests",
        .root_source_file = b.path("src/symbolic/tests/utils_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_utils_tests.root_module.addImport("symbolic", symbolic_module);
    symbolic_utils_tests.root_module.addImport("options", options_module);
    symbolic_utils_tests.linkLibC();
    
    const run_symbolic_utils_tests = b.addRunArtifact(symbolic_utils_tests);
    test_step.dependOn(&run_symbolic_utils_tests.step);
    all_tests_step.dependOn(&run_symbolic_utils_tests.step);
    symbolic_test_step.dependOn(&run_symbolic_utils_tests.step);
    
    // Add cross context tests
    const cross_context_file_tests = b.addTest(.{
        .name = "symbolic-cross-context-file-tests",
        .root_source_file = b.path("src/symbolic/tests/cross_context_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    cross_context_file_tests.root_module.addImport("symbolic", symbolic_module);
    cross_context_file_tests.root_module.addImport("options", options_module);
    cross_context_file_tests.linkLibC();
    
    const run_cross_context_file_tests = b.addRunArtifact(cross_context_file_tests);
    test_step.dependOn(&run_cross_context_file_tests.step);
    all_tests_step.dependOn(&run_cross_context_file_tests.step);
    symbolic_test_step.dependOn(&run_cross_context_file_tests.step);
    
    // Add deep copy stress tests
    const deep_copy_stress_tests = b.addTest(.{
        .name = "symbolic-deep-copy-stress-tests",
        .root_source_file = b.path("src/symbolic/tests/deep_copy_stress_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    deep_copy_stress_tests.root_module.addImport("options", options_module);
    deep_copy_stress_tests.root_module.addImport("symbolic", symbolic_module);
    deep_copy_stress_tests.linkLibC();
    
    const run_deep_copy_stress_tests = b.addRunArtifact(deep_copy_stress_tests);
    test_step.dependOn(&run_deep_copy_stress_tests.step);
    all_tests_step.dependOn(&run_deep_copy_stress_tests.step);
    symbolic_test_step.dependOn(&run_deep_copy_stress_tests.step);
    
    // Add property-based tests
    const property_based_tests = b.addTest(.{
        .name = "symbolic-property-based-tests",
        .root_source_file = b.path("src/symbolic/tests/property_based_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    property_based_tests.root_module.addImport("options", options_module);
    property_based_tests.root_module.addImport("symbolic", symbolic_module);
    property_based_tests.linkLibC();
    
    const run_property_based_tests = b.addRunArtifact(property_based_tests);
    test_step.dependOn(&run_property_based_tests.step);
    all_tests_step.dependOn(&run_property_based_tests.step);
    symbolic_test_step.dependOn(&run_property_based_tests.step);
    
    // Add advanced symbolic tests (but don't run in main test step)
    const symbolic_advanced_tests = b.addTest(.{
        .name = "symbolic-advanced-tests",
        .root_source_file = b.path("src/symbolic/tests/advanced_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_advanced_tests.root_module.addImport("symbolic", symbolic_module);
    symbolic_advanced_tests.root_module.addImport("options", options_module);
    symbolic_advanced_tests.linkLibC();
    
    const run_symbolic_advanced_tests = b.addRunArtifact(symbolic_advanced_tests);
    symbolic_advanced_test_step.dependOn(&run_symbolic_advanced_tests.step);
    // Don't add to main test step
    
    // Add performance symbolic tests (but don't run in main test step)
    const symbolic_perf_tests = b.addTest(.{
        .name = "symbolic-performance-tests",
        .root_source_file = b.path("src/symbolic/tests/performance_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_perf_tests.root_module.addImport("symbolic", symbolic_module);
    symbolic_perf_tests.root_module.addImport("options", options_module);
    symbolic_perf_tests.linkLibC();
    
    const run_symbolic_perf_tests = b.addRunArtifact(symbolic_perf_tests);
    symbolic_perf_test_step.dependOn(&run_symbolic_perf_tests.step);
    // Don't add to main test step
    
    //--------------------------------------------------------------------------
    // 2. SHAPE MODULE TESTS (after all symbolic tests)
    //--------------------------------------------------------------------------
    
    // Shape module core tests
    const shape_tests = b.addTest(.{
        .name = "shape-core-tests",
        .root_source_file = b.path("src/shape/shape.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_tests.root_module.addImport("options", options_module);
    shape_tests.root_module.addImport("symbolic", symbolic_module);
    shape_tests.root_module.addImport("shape_options", shape_options_module);
    shape_tests.linkLibC();
    
    const run_shape_tests = b.addRunArtifact(shape_tests);
    test_step.dependOn(&run_shape_tests.step);
    all_tests_step.dependOn(&run_shape_tests.step);
    shape_only_test_step.dependOn(&run_shape_tests.step);
    
    // Shape integration tests
    const shape_integration_tests = b.addTest(.{
        .name = "shape-integration-tests",
        .root_source_file = b.path("src/shape/tests/integration_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_integration_tests.root_module.addImport("options", options_module);
    shape_integration_tests.root_module.addImport("symbolic", symbolic_module);
    shape_integration_tests.root_module.addImport("shape", shape_module);
    shape_integration_tests.root_module.addImport("shape_options", shape_options_module);
    shape_integration_tests.root_module.addImport("root_options", root_options.createModule());
    shape_integration_tests.linkLibC();
    
    const run_integration_tests = b.addRunArtifact(shape_integration_tests);
    test_step.dependOn(&run_integration_tests.step);
    all_tests_step.dependOn(&run_integration_tests.step);
    shape_only_test_step.dependOn(&run_integration_tests.step);
    integration_test_step.dependOn(&run_integration_tests.step);
    
    // Shape operations tests
    const shape_operations_tests = b.addTest(.{
        .name = "shape-operations-tests",
        .root_source_file = b.path("src/shape/tests/operations_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_operations_tests.root_module.addImport("options", options_module);
    shape_operations_tests.root_module.addImport("symbolic", symbolic_module);
    shape_operations_tests.root_module.addImport("shape", shape_module);
    shape_operations_tests.root_module.addImport("shape_options", shape_options_module);
    shape_operations_tests.linkLibC();
    
    const run_operations_tests = b.addRunArtifact(shape_operations_tests);
    test_step.dependOn(&run_operations_tests.step);
    all_tests_step.dependOn(&run_operations_tests.step);
    shape_only_test_step.dependOn(&run_operations_tests.step);
    operations_test_step.dependOn(&run_operations_tests.step);
    
    // Shape test helpers
    const shape_test_helpers = b.addTest(.{
        .name = "shape-test-helpers",
        .root_source_file = b.path("src/shape/tests/test_helpers.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_test_helpers.root_module.addImport("options", options_module);
    shape_test_helpers.root_module.addImport("symbolic", symbolic_module);
    shape_test_helpers.root_module.addImport("shape", shape_module);
    shape_test_helpers.root_module.addImport("shape_options", shape_options_module);
    shape_test_helpers.linkLibC();
    
    const run_test_helpers = b.addRunArtifact(shape_test_helpers);
    test_step.dependOn(&run_test_helpers.step);
    all_tests_step.dependOn(&run_test_helpers.step);
    shape_only_test_step.dependOn(&run_test_helpers.step);
    
    // Minimal integration tests
    const minimal_integration_tests = b.addTest(.{
        .name = "shape-minimal-integration-tests",
        .root_source_file = b.path("src/shape/tests/minimal_integration_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    minimal_integration_tests.root_module.addImport("options", options_module);
    minimal_integration_tests.root_module.addImport("symbolic", symbolic_module);
    minimal_integration_tests.root_module.addImport("shape", shape_module);
    minimal_integration_tests.root_module.addImport("shape_options", shape_options_module);
    minimal_integration_tests.linkLibC();
    
    const run_minimal_integration_tests = b.addRunArtifact(minimal_integration_tests);
    test_step.dependOn(&run_minimal_integration_tests.step);
    all_tests_step.dependOn(&run_minimal_integration_tests.step);
    shape_only_test_step.dependOn(&run_minimal_integration_tests.step);
    minimal_integration_test_step.dependOn(&run_minimal_integration_tests.step);
    
    // Symbolic shape tests
    const symbolic_shape_tests = b.addTest(.{
        .name = "symbolic-shape-tests",
        .root_source_file = b.path("src/shape/tests/symbolic_shape_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_shape_tests.root_module.addImport("options", options_module);
    symbolic_shape_tests.root_module.addImport("symbolic", symbolic_module);
    symbolic_shape_tests.root_module.addImport("shape", shape_module);
    symbolic_shape_tests.root_module.addImport("shape_options", shape_options_module);
    symbolic_shape_tests.linkLibC();
    
    const run_symbolic_shape_tests = b.addRunArtifact(symbolic_shape_tests);
    test_step.dependOn(&run_symbolic_shape_tests.step);
    all_tests_step.dependOn(&run_symbolic_shape_tests.step);
    shape_only_test_step.dependOn(&run_symbolic_shape_tests.step);
    symbolic_shape_test_step.dependOn(&run_symbolic_shape_tests.step);
    
    // Minimal tracker tests
    const minimal_tracker_tests = b.addTest(.{
        .name = "shape-minimal-tracker-tests",
        .root_source_file = b.path("src/shape/tests/minimal_tracker_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    minimal_tracker_tests.root_module.addImport("options", options_module);
    minimal_tracker_tests.root_module.addImport("symbolic", symbolic_module);
    minimal_tracker_tests.root_module.addImport("shape", shape_module);
    minimal_tracker_tests.root_module.addImport("shape_options", shape_options_module);
    minimal_tracker_tests.linkLibC();
    
    const run_minimal_tracker_tests = b.addRunArtifact(minimal_tracker_tests);
    test_step.dependOn(&run_minimal_tracker_tests.step);
    all_tests_step.dependOn(&run_minimal_tracker_tests.step);
    shape_only_test_step.dependOn(&run_minimal_tracker_tests.step);
    minimal_tracker_test_step.dependOn(&run_minimal_tracker_tests.step);
    
    // Comprehensive shape tracker tests
    const shape_tracker_comprehensive_exe = b.addExecutable(.{
        .name = "shape-tracker-comprehensive-tests",
        .root_source_file = b.path("src/shape/tests/shape_tracker_comprehensive_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_tracker_comprehensive_exe.root_module.addImport("symbolic", symbolic_module);
    shape_tracker_comprehensive_exe.root_module.addImport("shape_options", shape_options_module);
    shape_tracker_comprehensive_exe.root_module.addImport("shape", shape_module);
    shape_tracker_comprehensive_exe.root_module.addImport("root_options", root_options.createModule());
    shape_tracker_comprehensive_exe.linkLibC();
    
    const run_shape_tracker_comprehensive = b.addRunArtifact(shape_tracker_comprehensive_exe);
    const run_shape_tracker_comprehensive_step = b.step(
        "shape-tracker-comprehensive", 
        "Run comprehensive shape tracker test with all features"
    );
    run_shape_tracker_comprehensive_step.dependOn(&run_shape_tracker_comprehensive.step);
    test_step.dependOn(&run_shape_tracker_comprehensive.step);
    all_tests_step.dependOn(&run_shape_tracker_comprehensive.step);
    
    // These tests are optional and not included in the main test step
    
    const shape_comprehensive_tests = b.addTest(.{
        .name = "shape-comprehensive-tests",
        .root_source_file = b.path("src/shape/tests/shape_comprehensive_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_comprehensive_tests.root_module.addImport("options", options_module);
    shape_comprehensive_tests.root_module.addImport("symbolic", symbolic_module);
    shape_comprehensive_tests.root_module.addImport("shape", shape_module);
    shape_comprehensive_tests.root_module.addImport("shape_options", shape_options_module);
    shape_comprehensive_tests.linkLibC();
    
    const run_shape_comprehensive_tests = b.addRunArtifact(shape_comprehensive_tests);
    shape_comprehensive_test_step.dependOn(&run_shape_comprehensive_tests.step);
    all_tests_step.dependOn(&run_shape_comprehensive_tests.step);
    shape_only_test_step.dependOn(&run_shape_comprehensive_tests.step);
    
    const shape_simple_tests = b.addTest(.{
        .name = "shape-simple-tests",
        .root_source_file = b.path("src/shape/tests/simple_shape_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_simple_tests.root_module.addImport("options", options_module);
    shape_simple_tests.root_module.addImport("symbolic", symbolic_module);
    shape_simple_tests.root_module.addImport("shape", shape_module);
    shape_simple_tests.root_module.addImport("shape_options", shape_options_module);
    shape_simple_tests.linkLibC();
    
    const run_shape_simple_tests = b.addRunArtifact(shape_simple_tests);
    shape_simple_test_step.dependOn(&run_shape_simple_tests.step);
    all_tests_step.dependOn(&run_shape_simple_tests.step);
    shape_only_test_step.dependOn(&run_shape_simple_tests.step);
    
    const shape_minimal_tests = b.addTest(.{
        .name = "shape-minimal-tests",
        .root_source_file = b.path("src/shape/tests/minimal_shape_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_minimal_tests.root_module.addImport("options", options_module);
    shape_minimal_tests.root_module.addImport("symbolic", symbolic_module);
    shape_minimal_tests.root_module.addImport("shape", shape_module);
    shape_minimal_tests.root_module.addImport("shape_options", shape_options_module);
    shape_minimal_tests.linkLibC();
    
    const run_shape_minimal_tests = b.addRunArtifact(shape_minimal_tests);
    shape_minimal_test_step.dependOn(&run_shape_minimal_tests.step);
    all_tests_step.dependOn(&run_shape_minimal_tests.step);
    shape_only_test_step.dependOn(&run_shape_minimal_tests.step);
    
    // Bus error debug test
    const bus_error_debug_test = b.addTest(.{
        .name = "bus-error-debug",
        .root_source_file = b.path("src/shape/tests/bus_error_debug.zig"),
        .target = target,
        .optimize = optimize,
    });
    bus_error_debug_test.root_module.addImport("shape", shape_module);
    const run_bus_error_debug = b.addRunArtifact(bus_error_debug_test);
    const bus_error_step = b.step("test-bus-error", "Debug bus error");
    bus_error_step.dependOn(&run_bus_error_debug.step);
    
    // Tracker debug test
    const tracker_debug_test = b.addTest(.{
        .name = "tracker-debug",
        .root_source_file = b.path("src/shape/tests/tracker_debug.zig"),
        .target = target,
        .optimize = optimize,
    });
    tracker_debug_test.root_module.addImport("shape", shape_module);
    const run_tracker_debug = b.addRunArtifact(tracker_debug_test);
    const tracker_debug_step = b.step("test-tracker-debug", "Debug tracker creation");
    tracker_debug_step.dependOn(&run_tracker_debug.step);
    
    const shape_operations_additional_tests = b.addTest(.{
        .name = "shape-operations-additional-tests",
        .root_source_file = b.path("src/shape/tests/operations_test_additional.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_operations_additional_tests.root_module.addImport("options", options_module);
    shape_operations_additional_tests.root_module.addImport("symbolic", symbolic_module);
    shape_operations_additional_tests.root_module.addImport("shape", shape_module);
    shape_operations_additional_tests.root_module.addImport("shape_options", shape_options_module);
    shape_operations_additional_tests.linkLibC();
    
    const run_operations_additional_tests = b.addRunArtifact(shape_operations_additional_tests);
    operations_test_step.dependOn(&run_operations_additional_tests.step);
    all_tests_step.dependOn(&run_operations_additional_tests.step);
    shape_only_test_step.dependOn(&run_operations_additional_tests.step);
    
    const shape_operations_comprehensive_tests = b.addTest(.{
        .name = "shape-operations-comprehensive-tests",
        .root_source_file = b.path("src/shape/tests/shape_operations_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_operations_comprehensive_tests.root_module.addImport("options", options_module);
    shape_operations_comprehensive_tests.root_module.addImport("symbolic", symbolic_module);
    shape_operations_comprehensive_tests.root_module.addImport("shape", shape_module);
    shape_operations_comprehensive_tests.root_module.addImport("shape_options", shape_options_module);
    shape_operations_comprehensive_tests.linkLibC();
    
    const run_operations_comprehensive_tests = b.addRunArtifact(shape_operations_comprehensive_tests);
    operations_test_step.dependOn(&run_operations_comprehensive_tests.step);
    all_tests_step.dependOn(&run_operations_comprehensive_tests.step);
    shape_only_test_step.dependOn(&run_operations_comprehensive_tests.step);
    
    const shape_api_tests = b.addTest(.{
        .name = "shape-api-tests",
        .root_source_file = b.path("src/shape/tests/shape_api_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_api_tests.root_module.addImport("options", options_module);
    shape_api_tests.root_module.addImport("symbolic", symbolic_module);
    shape_api_tests.root_module.addImport("shape", shape_module);
    shape_api_tests.root_module.addImport("shape_options", shape_options_module);
    shape_api_tests.linkLibC();
    
    const run_shape_api_tests = b.addRunArtifact(shape_api_tests);
    all_tests_step.dependOn(&run_shape_api_tests.step);
    shape_only_test_step.dependOn(&run_shape_api_tests.step);
    
    //--------------------------------------------------------------------------
    // 2.1 CONSOLIDATED SHAPE MODULE TESTS
    //--------------------------------------------------------------------------
    
    // Test main for debugging consolidated tests
    const test_main = b.addTest(.{
        .name = "shape-test-main",
        .root_source_file = b.path("src/shape/tests/consolidated/test_main.zig"),
        .target = target,
        .optimize = optimize,
    });
    test_main.root_module.addImport("options", options_module);
    test_main.root_module.addImport("symbolic", symbolic_module);
    test_main.root_module.addImport("shape", shape_module);
    test_main.root_module.addImport("shape_options", shape_options_module);
    test_main.root_module.addImport("common_types", common_types_module);
    test_main.linkLibC();
    
    const run_test_main = b.addRunArtifact(test_main);
    const test_main_step = b.step("test-shape-main", "Run test_main for debugging consolidated tests");
    test_main_step.dependOn(&run_test_main.step);
    
    // All consolidated tests
    const all_consolidated_tests = b.addTest(.{
        .name = "shape-all-consolidated-tests",
        .root_source_file = b.path("src/shape/tests/consolidated/all_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    all_consolidated_tests.root_module.addImport("options", options_module);
    all_consolidated_tests.root_module.addImport("symbolic", symbolic_module);
    all_consolidated_tests.root_module.addImport("shape", shape_module);
    all_consolidated_tests.root_module.addImport("shape_options", shape_options_module);
    all_consolidated_tests.root_module.addImport("common_types", common_types_module);
    all_consolidated_tests.linkLibC();
    
    const run_all_consolidated_tests = b.addRunArtifact(all_consolidated_tests);
    consolidated_test_step.dependOn(&run_all_consolidated_tests.step);
    shape_only_test_step.dependOn(&run_all_consolidated_tests.step);
    test_step.dependOn(&run_all_consolidated_tests.step);
    all_tests_step.dependOn(&run_all_consolidated_tests.step);
    
    // Core tests
    const core_consolidated_tests = b.addTest(.{
        .name = "shape-core-consolidated-tests",
        .root_source_file = b.path("src/shape/tests/consolidated/core_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    core_consolidated_tests.root_module.addImport("options", options_module);
    core_consolidated_tests.root_module.addImport("symbolic", symbolic_module);
    core_consolidated_tests.root_module.addImport("shape", shape_module);
    core_consolidated_tests.root_module.addImport("shape_options", shape_options_module);
    core_consolidated_tests.linkLibC();
    
    const run_core_consolidated_tests = b.addRunArtifact(core_consolidated_tests);
    core_test_step.dependOn(&run_core_consolidated_tests.step);
    
    // Memory management tests
    const memory_consolidated_tests = b.addTest(.{
        .name = "shape-memory-consolidated-tests",
        .root_source_file = b.path("src/shape/tests/consolidated/memory_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    memory_consolidated_tests.root_module.addImport("options", options_module);
    memory_consolidated_tests.root_module.addImport("symbolic", symbolic_module);
    memory_consolidated_tests.root_module.addImport("shape", shape_module);
    memory_consolidated_tests.root_module.addImport("shape_options", shape_options_module);
    memory_consolidated_tests.linkLibC();
    
    const run_memory_consolidated_tests = b.addRunArtifact(memory_consolidated_tests);
    memory_management_test_step.dependOn(&run_memory_consolidated_tests.step);
    
    // Operations tests
    const operations_consolidated_tests = b.addTest(.{
        .name = "shape-operations-consolidated-tests",
        .root_source_file = b.path("src/shape/tests/consolidated/operations_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    operations_consolidated_tests.root_module.addImport("options", options_module);
    operations_consolidated_tests.root_module.addImport("symbolic", symbolic_module);
    operations_consolidated_tests.root_module.addImport("shape", shape_module);
    operations_consolidated_tests.root_module.addImport("shape_options", shape_options_module);
    operations_consolidated_tests.linkLibC();
    
    const run_operations_consolidated_tests = b.addRunArtifact(operations_consolidated_tests);
    operations_consolidated_test_step.dependOn(&run_operations_consolidated_tests.step);
    
    // Integration tests
    const integration_consolidated_tests = b.addTest(.{
        .name = "shape-integration-consolidated-tests",
        .root_source_file = b.path("src/shape/tests/consolidated/integration_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    integration_consolidated_tests.root_module.addImport("options", options_module);
    integration_consolidated_tests.root_module.addImport("symbolic", symbolic_module);
    integration_consolidated_tests.root_module.addImport("shape", shape_module);
    integration_consolidated_tests.root_module.addImport("shape_options", shape_options_module);
    integration_consolidated_tests.linkLibC();
    
    const run_integration_consolidated_tests = b.addRunArtifact(integration_consolidated_tests);
    integration_consolidated_test_step.dependOn(&run_integration_consolidated_tests.step);
    
    // Feature flags tests
    const feature_flags_tests = b.addTest(.{
        .name = "shape-feature-flags-tests",
        .root_source_file = b.path("src/shape/tests/consolidated/feature_flags_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    feature_flags_tests.root_module.addImport("options", options_module);
    feature_flags_tests.root_module.addImport("symbolic", symbolic_module);
    feature_flags_tests.root_module.addImport("shape", shape_module);
    feature_flags_tests.root_module.addImport("shape_options", shape_options_module);
    feature_flags_tests.linkLibC();
    
    const run_feature_flags_tests = b.addRunArtifact(feature_flags_tests);
    feature_flags_test_step.dependOn(&run_feature_flags_tests.step);
    
    //--------------------------------------------------------------------------
    // 3. GRAPH MODULE TESTS (only included in all_tests_step)
    //--------------------------------------------------------------------------
    
    // The graph tests are not included in the main test step,
    // but are available through specialized test steps
    
    const graph_index_tests = b.addTest(.{
        .name = "graph-index-tests",
        .root_source_file = b.path("src/graph/tests/index_ops_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    graph_index_tests.root_module.addImport("options", options_module);
    graph_index_tests.root_module.addImport("symbolic", symbolic_module);
    graph_index_tests.root_module.addImport("shape", shape_module);
    graph_index_tests.linkLibC();
    
    const run_graph_index_tests = b.addRunArtifact(graph_index_tests);
    all_tests_step.dependOn(&run_graph_index_tests.step);
    graph_index_test_step.dependOn(&run_graph_index_tests.step);
    
    const tensor_tests = b.addTest(.{
        .name = "tensor-core-tests",
        .root_source_file = b.path("src/graph/tests/tensor_tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    tensor_tests.root_module.addImport("options", options_module);
    tensor_tests.root_module.addImport("symbolic", symbolic_module);
    tensor_tests.root_module.addImport("shape", shape_module);
    tensor_tests.root_module.addImport("graph_types", graph_types_module);
    tensor_tests.root_module.addImport("graph", graph_module);
    tensor_tests.root_module.addImport("tensor", tensor_module);
    tensor_tests.linkLibC();
    
    const run_tensor_tests = b.addRunArtifact(tensor_tests);
    all_tests_step.dependOn(&run_tensor_tests.step);
    tensor_test_step.dependOn(&run_tensor_tests.step);
    
    const tensor_minimal_tests = b.addTest(.{
        .name = "tensor-minimal-tests",
        .root_source_file = b.path("src/graph/tests/tensor_minimal_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    tensor_minimal_tests.root_module.addImport("options", options_module);
    tensor_minimal_tests.root_module.addImport("symbolic", symbolic_module);
    tensor_minimal_tests.root_module.addImport("shape", shape_module);
    tensor_minimal_tests.root_module.addImport("graph_types", graph_types_module);
    tensor_minimal_tests.root_module.addImport("tensor", tensor_module);
    tensor_minimal_tests.linkLibC();
    
    const run_tensor_minimal_tests = b.addRunArtifact(tensor_minimal_tests);
    all_tests_step.dependOn(&run_tensor_minimal_tests.step);
    minimal_tensor_test_step.dependOn(&run_tensor_minimal_tests.step);
    
    const graph_tests = b.addTest(.{
        .name = "graph-core-tests",
        .root_source_file = b.path("src/graph/tests/graph_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    graph_tests.root_module.addImport("options", options_module);
    graph_tests.root_module.addImport("symbolic", symbolic_module);
    graph_tests.root_module.addImport("shape", shape_module);
    graph_tests.root_module.addImport("graph_types", graph_types_module);
    graph_tests.root_module.addImport("graph", graph_module);
    graph_tests.root_module.addImport("tensor", tensor_module);
    graph_tests.linkLibC();
    
    const run_graph_tests = b.addRunArtifact(graph_tests);
    all_tests_step.dependOn(&run_graph_tests.step);
    graph_test_step.dependOn(&run_graph_tests.step);
    
    const graph_minimal_tests = b.addTest(.{
        .name = "graph-minimal-tests",
        .root_source_file = b.path("src/graph/tests/graph_minimal_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    graph_minimal_tests.root_module.addImport("options", options_module);
    graph_minimal_tests.root_module.addImport("symbolic", symbolic_module);
    graph_minimal_tests.root_module.addImport("shape", shape_module);
    graph_minimal_tests.root_module.addImport("graph_types", graph_types_module);
    graph_minimal_tests.root_module.addImport("graph", graph_module);
    graph_minimal_tests.root_module.addImport("tensor", tensor_module);
    graph_minimal_tests.linkLibC();
    
    const run_minimal_graph_tests = b.addRunArtifact(graph_minimal_tests);
    all_tests_step.dependOn(&run_minimal_graph_tests.step);
    const minimal_graph_test_step = b.step("test-graph-minimal", "Run minimal graph tests");
    minimal_graph_test_step.dependOn(&run_minimal_graph_tests.step);
    
    const minimal_mock_tests = b.addTest(.{
        .name = "graph-mock-tests",
        .root_source_file = b.path("src/graph/tests/minimal_mock_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    minimal_mock_tests.root_module.addImport("graph_types", graph_types_module);
    minimal_mock_tests.linkLibC();
    
    const run_minimal_mock_tests = b.addRunArtifact(minimal_mock_tests);
    all_tests_step.dependOn(&run_minimal_mock_tests.step);
    mock_graph_test_step.dependOn(&run_minimal_mock_tests.step);
    
    // We already have separate tests for each aspect, so reuse the main test entry point
    const comprehensive_graph_tests = b.addTest(.{
        .name = "graph-comprehensive-tests",
        .root_source_file = b.path("src/graph/tests/graph_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    comprehensive_graph_tests.root_module.addImport("symbolic", symbolic_module);
    comprehensive_graph_tests.root_module.addImport("shape", shape_module);
    comprehensive_graph_tests.root_module.addImport("graph_types", graph_types_module);
    comprehensive_graph_tests.root_module.addImport("graph", graph_module);
    comprehensive_graph_tests.root_module.addImport("tensor", tensor_module);
    comprehensive_graph_tests.root_module.addImport("memory_pool", memory_pool_module);
    comprehensive_graph_tests.root_module.addImport("integration", integration_module);
    comprehensive_graph_tests.linkLibC();
    
    const run_comprehensive_graph_tests = b.addRunArtifact(comprehensive_graph_tests);
    comprehensive_graph_test_step.dependOn(&run_comprehensive_graph_tests.step);
    all_tests_step.dependOn(&run_comprehensive_graph_tests.step);
    
    const minimal_ops_tests = b.addTest(.{
        .name = "graph-ops-minimal-tests",
        .root_source_file = b.path("src/graph/tests/basic_operations.zig"),
        .target = target,
        .optimize = optimize,
    });
    minimal_ops_tests.root_module.addImport("graph_types", graph_types_module);
    minimal_ops_tests.linkLibC();
    
    const run_minimal_ops_tests = b.addRunArtifact(minimal_ops_tests);
    comprehensive_ops_test_step.dependOn(&run_minimal_ops_tests.step);
    all_tests_step.dependOn(&run_minimal_ops_tests.step);
    graph_test_step.dependOn(&run_minimal_ops_tests.step);
    
    const ops_summary = b.addTest(.{
        .name = "ops-summary",
        .root_source_file = b.path("src/graph/tests/advanced_operations.zig"),
        .target = target,
        .optimize = optimize,
    });
    ops_summary.root_module.addImport("shape", shape_module);
    ops_summary.linkLibC();
    
    const run_ops_summary = b.addRunArtifact(ops_summary);
    comprehensive_ops_test_step.dependOn(&run_ops_summary.step);
    all_tests_step.dependOn(&run_ops_summary.step);
    graph_test_step.dependOn(&run_ops_summary.step);
    
    const comprehensive_tensor_tests = b.addTest(.{
        .name = "tensor-comprehensive-tests",
        .root_source_file = b.path("src/graph/tests/tensor_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    comprehensive_tensor_tests.root_module.addImport("symbolic", symbolic_module);
    comprehensive_tensor_tests.root_module.addImport("shape", shape_module);
    comprehensive_tensor_tests.root_module.addImport("graph_types", graph_types_module);
    comprehensive_tensor_tests.root_module.addImport("graph", graph_module);
    comprehensive_tensor_tests.root_module.addImport("tensor", tensor_module);
    comprehensive_tensor_tests.root_module.addImport("integration", integration_module);
    comprehensive_tensor_tests.linkLibC();
    
    const run_comprehensive_tensor_tests = b.addRunArtifact(comprehensive_tensor_tests);
    tensor_test_step.dependOn(&run_comprehensive_tensor_tests.step);
    all_tests_step.dependOn(&run_comprehensive_tensor_tests.step);
    
    const memory_tests = b.addTest(.{
        .name = "memory-management-tests",
        .root_source_file = b.path("src/graph/tests/memory_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    memory_tests.root_module.addImport("graph_types", graph_types_module);
    memory_tests.root_module.addImport("memory_pool", memory_pool_module);
    memory_tests.linkLibC();
    
    const run_memory_tests = b.addRunArtifact(memory_tests);
    memory_test_step.dependOn(&run_memory_tests.step);
    all_tests_step.dependOn(&run_memory_tests.step);
    
    // TensorOps Module Tests
    const tensor_ops_tests = b.addTest(.{
        .root_source_file = b.path("src/tensor_ops/tests/tests.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    // Add dependencies
    tensor_ops_tests.root_module.addImport("symbolic", symbolic_module);
    tensor_ops_tests.root_module.addImport("shape", shape_module);
    tensor_ops_tests.root_module.addImport("graph", graph_module);
    tensor_ops_tests.root_module.addImport("tensor_ops", tensor_ops_module);
    
    const run_tensor_ops_tests = b.addRunArtifact(tensor_ops_tests);
    
    all_tests_step.dependOn(&run_tensor_ops_tests.step);
    tensor_ops_test_step.dependOn(&run_tensor_ops_tests.step);
    
    // Also add a specific matmul test
    const tensor_ops_math_tests = b.addTest(.{
        .root_source_file = b.path("src/tensor_ops/tests/math_ops_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    // Add dependencies
    tensor_ops_math_tests.root_module.addImport("symbolic", symbolic_module);
    tensor_ops_math_tests.root_module.addImport("shape", shape_module);
    tensor_ops_math_tests.root_module.addImport("graph", graph_module);
    tensor_ops_math_tests.root_module.addImport("tensor_ops", tensor_ops_module);
    
    const run_tensor_ops_math_tests = b.addRunArtifact(tensor_ops_math_tests);
    
    all_tests_step.dependOn(&run_tensor_ops_math_tests.step);
    tensor_ops_math_test_step.dependOn(&run_tensor_ops_math_tests.step);
    
    // Add graph structure verification tests
    const tensor_ops_graph_tests = b.addTest(.{
        .name = "tensor-ops-graph-tests",
        .root_source_file = b.path("src/tensor_ops/tests/graph_verification_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    // Add dependencies
    tensor_ops_graph_tests.root_module.addImport("symbolic", symbolic_module);
    tensor_ops_graph_tests.root_module.addImport("shape", shape_module);
    tensor_ops_graph_tests.root_module.addImport("graph", graph_module);
    tensor_ops_graph_tests.root_module.addImport("tensor_ops", tensor_ops_module);
    
    const run_tensor_ops_graph_tests = b.addRunArtifact(tensor_ops_graph_tests);
    
    all_tests_step.dependOn(&run_tensor_ops_graph_tests.step);
    tensor_ops_test_step.dependOn(&run_tensor_ops_graph_tests.step);
    tensor_ops_graph_test_step.dependOn(&run_tensor_ops_graph_tests.step);
    
    // Add matmul graph structure test
    const matmul_graph_tests = b.addTest(.{
        .name = "matmul-graph-structure-tests",
        .root_source_file = b.path("src/tensor_ops/tests/matmul_graph_structure_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    const run_matmul_graph_tests = b.addRunArtifact(matmul_graph_tests);
    
    all_tests_step.dependOn(&run_matmul_graph_tests.step);
    tensor_ops_test_step.dependOn(&run_matmul_graph_tests.step);
    tensor_ops_graph_test_step.dependOn(&run_matmul_graph_tests.step);
    
    // Add comprehensive tensor ops test
    const tensor_ops_comprehensive_tests = b.addTest(.{
        .name = "tensor-ops-comprehensive-tests",
        .root_source_file = b.path("src/tensor_ops/tests/comprehensive_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    // Add dependencies
    tensor_ops_comprehensive_tests.root_module.addImport("symbolic", symbolic_module);
    tensor_ops_comprehensive_tests.root_module.addImport("shape", shape_module);
    tensor_ops_comprehensive_tests.root_module.addImport("graph", graph_module);
    tensor_ops_comprehensive_tests.root_module.addImport("tensor_ops", tensor_ops_module);
    
    const run_tensor_ops_comprehensive_tests = b.addRunArtifact(tensor_ops_comprehensive_tests);
    
    all_tests_step.dependOn(&run_tensor_ops_comprehensive_tests.step);
    tensor_ops_test_step.dependOn(&run_tensor_ops_comprehensive_tests.step);
    
    // Add a simple test runner for debugging
    const tensor_ops_simple_tests = b.addTest(.{
        .name = "tensor-ops-simple-tests",
        .root_source_file = b.path("src/tensor_ops/tests/test_simple.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    const run_tensor_ops_simple_tests = b.addRunArtifact(tensor_ops_simple_tests);
    
    const tensor_ops_simple_test_step = b.step("test-tensor-ops-simple", "Run simple tensor ops tests");
    tensor_ops_simple_test_step.dependOn(&run_tensor_ops_simple_tests.step);
    
    //--------------------------------------------------------------------------
    // 4. EXAMPLE APPLICATIONS
    //--------------------------------------------------------------------------
    
    // Index operations example
    const index_ops_example_exe = b.addExecutable(.{
        .name = "index-ops-example",
        .root_source_file = b.path("examples/index_ops_example.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    const run_index_ops_example = b.addRunArtifact(index_ops_example_exe);
    const run_index_ops_example_step = b.step(
        "run-index-ops-example", 
        "Run index operations example"
    );
    run_index_ops_example_step.dependOn(&run_index_ops_example.step);
    
    // Comprehensive index operations example
    const index_ops_comprehensive_exe = b.addExecutable(.{
        .name = "index-ops-comprehensive",
        .root_source_file = b.path("examples/index_ops_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    
    const run_index_ops_comprehensive = b.addRunArtifact(index_ops_comprehensive_exe);
    const run_index_ops_comprehensive_step = b.step(
        "run-index-ops-comprehensive", 
        "Run comprehensive index operations example"
    );
    run_index_ops_comprehensive_step.dependOn(&run_index_ops_comprehensive.step);
    
    // Native engine example
    const native_engine_exe = b.addExecutable(.{
        .name = "engine-direct-tests",
        .root_source_file = b.path("examples/engine_direct_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    native_engine_exe.root_module.addImport("symbolic", symbolic_module);
    
    const run_native_engine = b.addRunArtifact(native_engine_exe);
    const run_native_engine_test_step = b.step(
        "run-native-engine-test", 
        "Run native symbolic engine test"
    );
    run_native_engine_test_step.dependOn(&run_native_engine.step);
    
    // Shape tracker example
    const shape_tracker_exe = b.addExecutable(.{
        .name = "shape-tracker-tests",
        .root_source_file = b.path("examples/shape_tracker_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_tracker_exe.root_module.addImport("symbolic", symbolic_module);
    shape_tracker_exe.root_module.addImport("shape_options", shape_options_module);
    shape_tracker_exe.root_module.addImport("shape", shape_module);
    shape_tracker_exe.linkLibC();
    
    const run_shape_tracker = b.addRunArtifact(shape_tracker_exe);
    const run_shape_tracker_test_step = b.step(
        "shape-tracker-test", 
        "Run shape tracker test"
    );
    run_shape_tracker_test_step.dependOn(&run_shape_tracker.step);
    
    // Shape-symbolic integration example
    const shape_symbolic_integration_exe = b.addExecutable(.{
        .name = "shape-symbolic-integration",
        .root_source_file = b.path("examples/shape_symbolic_integration.zig"),
        .target = target,
        .optimize = optimize,
    });
    shape_symbolic_integration_exe.root_module.addImport("symbolic", symbolic_module);
    shape_symbolic_integration_exe.root_module.addImport("shape_options", shape_options_module);
    shape_symbolic_integration_exe.root_module.addImport("shape", shape_module);
    shape_symbolic_integration_exe.linkLibC();
    
    const run_shape_symbolic_integration = b.addRunArtifact(shape_symbolic_integration_exe);
    const run_shape_symbolic_integration_step = b.step(
        "run-shape-symbolic-integration",
        "Run the shape symbolic integration example"
    );
    run_shape_symbolic_integration_step.dependOn(&run_shape_symbolic_integration.step);
    
    // Symbolic builder demo
    const symbolic_builder_demo_exe = b.addExecutable(.{
        .name = "symbolic-builder-demo",
        .root_source_file = b.path("examples/symbolic_builder_demo.zig"),
        .target = target,
        .optimize = optimize,
    });
    symbolic_builder_demo_exe.root_module.addImport("symbolic", symbolic_module);
    symbolic_builder_demo_exe.linkLibC();
    
    const run_symbolic_builder_demo = b.addRunArtifact(symbolic_builder_demo_exe);
    const run_symbolic_builder_demo_step = b.step(
        "run-symbolic-builder-demo",
        "Run the symbolic builder pattern demo"
    );
    run_symbolic_builder_demo_step.dependOn(&run_symbolic_builder_demo.step);
    
    // Deep copy test
    const deep_copy_test_exe = b.addExecutable(.{
        .name = "deep-copy-test",
        .root_source_file = b.path("examples/deep_copy_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    deep_copy_test_exe.root_module.addImport("symbolic", symbolic_module);
    deep_copy_test_exe.linkLibC();
    
    const run_deep_copy_test = b.addRunArtifact(deep_copy_test_exe);
    const run_deep_copy_test_step = b.step(
        "run-deep-copy-test",
        "Run the deep copy functionality test"
    );
    run_deep_copy_test_step.dependOn(&run_deep_copy_test.step);
    
    //--------------------------------------------------------------------------
    // V2 REFACTORED TESTS
    //--------------------------------------------------------------------------
    
    // Create V2 test step
    const v2_test_step = b.step("test-v2", "Run V2 refactored tests");
    
    // V2 Core module
    const core_v2_module = b.addModule("core_v2", .{
        .root_source_file = b.path("src/core/core.zig"),
    });
    core_v2_module.addImport("options", options_module);
    
    // V2 Core tests - all tests from core directory
    
    // 1. Symbolic Engine Tests
    const v2_symbolic_tests = b.addTest(.{
        .name = "v2-symbolic-tests",
        .root_source_file = b.path("src/core/tests/test_symbolic.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_tests.root_module.addImport("core", core_v2_module);
    v2_symbolic_tests.linkLibC();
    
    const run_v2_symbolic_tests = b.addRunArtifact(v2_symbolic_tests);
    v2_test_step.dependOn(&run_v2_symbolic_tests.step);
    test_step.dependOn(&run_v2_symbolic_tests.step);
    all_tests_step.dependOn(&run_v2_symbolic_tests.step);
    
    // 2. Shape Engine Tests
    const v2_shape_tests = b.addTest(.{
        .name = "v2-shape-tests",
        .root_source_file = b.path("src/core/tests/test_shape.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_tests.root_module.addImport("core", core_v2_module);
    v2_shape_tests.linkLibC();
    
    const run_v2_shape_tests = b.addRunArtifact(v2_shape_tests);
    v2_test_step.dependOn(&run_v2_shape_tests.step);
    test_step.dependOn(&run_v2_shape_tests.step);
    all_tests_step.dependOn(&run_v2_shape_tests.step);
    
    // 3. Graph Engine Tests
    const v2_graph_tests = b.addTest(.{
        .name = "v2-graph-tests",
        .root_source_file = b.path("src/core/tests/test_graph.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_graph_tests.root_module.addImport("core", core_v2_module);
    v2_graph_tests.linkLibC();
    
    const run_v2_graph_tests = b.addRunArtifact(v2_graph_tests);
    v2_test_step.dependOn(&run_v2_graph_tests.step);
    test_step.dependOn(&run_v2_graph_tests.step);
    all_tests_step.dependOn(&run_v2_graph_tests.step);
    
    // 4. Core Integration Tests
    const v2_integration_tests = b.addTest(.{
        .name = "v2-integration-tests",
        .root_source_file = b.path("src/core/tests/test_integration.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_integration_tests.root_module.addImport("core", core_v2_module);
    v2_integration_tests.linkLibC();
    
    const run_v2_integration_tests = b.addRunArtifact(v2_integration_tests);
    v2_test_step.dependOn(&run_v2_integration_tests.step);
    test_step.dependOn(&run_v2_integration_tests.step);
    all_tests_step.dependOn(&run_v2_integration_tests.step);
    
    // 5. Additional Integration Tests
    const v2_additional_integration_tests = b.addTest(.{
        .name = "v2-additional-integration-tests",
        .root_source_file = b.path("src/core/tests/integration_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_additional_integration_tests.root_module.addImport("core", core_v2_module);
    v2_additional_integration_tests.linkLibC();
    
    const run_v2_additional_integration_tests = b.addRunArtifact(v2_additional_integration_tests);
    v2_test_step.dependOn(&run_v2_additional_integration_tests.step);
    test_step.dependOn(&run_v2_additional_integration_tests.step);
    all_tests_step.dependOn(&run_v2_additional_integration_tests.step);
    
    // 6. Core Tests
    const v2_core_tests = b.addTest(.{
        .name = "v2-core-tests",
        .root_source_file = b.path("src/core/tests/test.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_core_tests.root_module.addImport("core", core_v2_module);
    v2_core_tests.linkLibC();
    
    const run_v2_core_tests = b.addRunArtifact(v2_core_tests);
    v2_test_step.dependOn(&run_v2_core_tests.step);
    test_step.dependOn(&run_v2_core_tests.step);
    all_tests_step.dependOn(&run_v2_core_tests.step);
    
    // 7. Graph Module Tests
    const v2_graph_module_tests = b.addTest(.{
        .name = "v2-graph-module-tests",
        .root_source_file = b.path("src/core/graph/test.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_graph_module_tests.root_module.addImport("core", core_v2_module);
    v2_graph_module_tests.linkLibC();
    
    const run_v2_graph_module_tests = b.addRunArtifact(v2_graph_module_tests);
    v2_test_step.dependOn(&run_v2_graph_module_tests.step);
    test_step.dependOn(&run_v2_graph_module_tests.step);
    all_tests_step.dependOn(&run_v2_graph_module_tests.step);
    
    // 8. Enhanced Symbolic Tests
    const v2_symbolic_enhanced_tests = b.addTest(.{
        .name = "v2-symbolic-enhanced-tests",
        .root_source_file = b.path("src/core/tests/test_symbolic_enhanced.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_enhanced_tests.root_module.addImport("core", core_v2_module);
    v2_symbolic_enhanced_tests.linkLibC();
    
    const run_v2_symbolic_enhanced_tests = b.addRunArtifact(v2_symbolic_enhanced_tests);
    v2_test_step.dependOn(&run_v2_symbolic_enhanced_tests.step);
    test_step.dependOn(&run_v2_symbolic_enhanced_tests.step);
    all_tests_step.dependOn(&run_v2_symbolic_enhanced_tests.step);
    
    // 9. Constraint Solver Tests
    const v2_constraint_solver_tests = b.addTest(.{
        .name = "v2-constraint-solver-tests",
        .root_source_file = b.path("src/core/tests/test_constraint_solver.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_constraint_solver_tests.root_module.addImport("core", core_v2_module);
    v2_constraint_solver_tests.linkLibC();
    
    const run_v2_constraint_solver_tests = b.addRunArtifact(v2_constraint_solver_tests);
    v2_test_step.dependOn(&run_v2_constraint_solver_tests.step);
    test_step.dependOn(&run_v2_constraint_solver_tests.step);
    all_tests_step.dependOn(&run_v2_constraint_solver_tests.step);
    
    // 10. Comprehensive Shape Engine Tests
    const v2_shape_comprehensive_tests = b.addTest(.{
        .name = "v2-shape-comprehensive-tests",
        .root_source_file = b.path("src/core/tests/test_shape_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_comprehensive_tests.root_module.addImport("core", core_v2_module);
    v2_shape_comprehensive_tests.linkLibC();
    
    const run_v2_shape_comprehensive_tests = b.addRunArtifact(v2_shape_comprehensive_tests);
    v2_test_step.dependOn(&run_v2_shape_comprehensive_tests.step);
    test_step.dependOn(&run_v2_shape_comprehensive_tests.step);
    all_tests_step.dependOn(&run_v2_shape_comprehensive_tests.step);
    
    // 11. Comprehensive Symbolic Engine Tests
    const v2_symbolic_comprehensive_tests = b.addTest(.{
        .name = "v2-symbolic-comprehensive-tests",
        .root_source_file = b.path("src/core/tests/test_symbolic_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_comprehensive_tests.root_module.addImport("core", core_v2_module);
    v2_symbolic_comprehensive_tests.linkLibC();
    
    const run_v2_symbolic_comprehensive_tests = b.addRunArtifact(v2_symbolic_comprehensive_tests);
    v2_test_step.dependOn(&run_v2_symbolic_comprehensive_tests.step);
    test_step.dependOn(&run_v2_symbolic_comprehensive_tests.step);
    all_tests_step.dependOn(&run_v2_symbolic_comprehensive_tests.step);
    
    //--------------------------------------------------------------------------
    // 5. FFI SUPPORT (ONLY WHEN ENABLED)
    //--------------------------------------------------------------------------
    
    if (enable_ffi) {
        // FFI tests step
        const ffi_test_step = b.step("test-ffi", "Run symbolic FFI tests (requires Rust FFI)");
        
        // Create and configure FFI test executable
        const ffi_tests = b.addTest(.{
            .name = "symbolic-ffi-tests",
            .root_source_file = b.path("src/symbolic/tests/ffi_tests.zig"),
            .target = target,
            .optimize = optimize,
        });
        
        // Add dependencies
        ffi_tests.root_module.addImport("options", options_module);
        ffi_tests.root_module.addImport("symbolic", symbolic_module);
        
        // Add FFI-specific configuration
        ffi_tests.root_module.link_libc = true;
        ffi_tests.addIncludePath(b.path("src/symbolic/egraph"));
        ffi_tests.addLibraryPath(b.path("egg_ffi/target/release"));
        
        // Platform-specific linkage
        if (target.result.os.tag == .macos) {
            ffi_tests.linkSystemLibrary("egg_ffi");
            ffi_tests.addRPath(b.path("egg_ffi/target/release"));
        } else {
            ffi_tests.linkSystemLibrary("egg_ffi");
        }
        
        const run_ffi_tests = b.addRunArtifact(ffi_tests);
        ffi_test_step.dependOn(&run_ffi_tests.step);
        
        // Create directories for FFI if needed
        const mkdir_steps = b.addSystemCommand(&.{
            "mkdir", "-p", "src/symbolic/egraph"
        });
        
        // Build the Rust FFI library
        const cargo_build = b.addSystemCommand(&.{
            "cargo",
            "build",
            "--release",
            "--manifest-path",
            "Cargo.toml",
        });
        cargo_build.cwd = b.path("egg_ffi");
        cargo_build.step.dependOn(&mkdir_steps.step);
        
        // Copy the FFI header file to the expected location
        const copy_header = b.addSystemCommand(&.{
            "cp", 
            "egg_ffi/target/release/egg_ffi.h", 
            "src/symbolic/egraph/egg_ffi.h",
        });
        copy_header.step.dependOn(&cargo_build.step);
        
        // Run FFI demo step
        const run_ffi_demo_step = b.step("run-ffi-demo", "Run FFI demo from examples directory");
        
        const ffi_demo_exe = b.addExecutable(.{
            .name = "egg-ffi-demo",
            .root_source_file = b.path("examples/egg_ffi_demo.zig"),
            .target = target,
            .optimize = optimize,
        });
        
        // Link against libc and FFI library
        ffi_demo_exe.root_module.link_libc = true;
        ffi_demo_exe.addIncludePath(b.path("src/symbolic/egraph"));
        ffi_demo_exe.addLibraryPath(b.path("egg_ffi/target/release"));
        
        if (target.result.os.tag == .macos) {
            ffi_demo_exe.linkSystemLibrary("egg_ffi");
            ffi_demo_exe.addRPath(b.path("egg_ffi/target/release"));
        } else {
            ffi_demo_exe.linkSystemLibrary("egg_ffi");
        }
        
        const run_ffi_demo = b.addRunArtifact(ffi_demo_exe);
        run_ffi_demo_step.dependOn(&run_ffi_demo.step);
        
        // FFI Benchmark step
        const run_ffi_benchmark_step = b.step(
            "run-ffi-benchmark", 
            "Run FFI batch vs non-batch benchmark"
        );
        
        const ffi_benchmark_exe = b.addExecutable(.{
            .name = "ffi-benchmark",
            .root_source_file = b.path("benchmarks/egg_ffi_batch_vs_nonbatch.zig"),
            .target = target,
            .optimize = optimize,
        });
        
        // Link against libc and FFI library
        ffi_benchmark_exe.root_module.link_libc = true;
        ffi_benchmark_exe.addIncludePath(b.path("src/symbolic/egraph"));
        ffi_benchmark_exe.addLibraryPath(b.path("egg_ffi/target/release"));
        
        if (target.result.os.tag == .macos) {
            ffi_benchmark_exe.linkSystemLibrary("egg_ffi");
            ffi_benchmark_exe.addRPath(b.path("egg_ffi/target/release"));
        } else {
            ffi_benchmark_exe.linkSystemLibrary("egg_ffi");
        }
        
        const run_ffi_benchmark = b.addRunArtifact(ffi_benchmark_exe);
        run_ffi_benchmark_step.dependOn(&run_ffi_benchmark.step);
    }
}