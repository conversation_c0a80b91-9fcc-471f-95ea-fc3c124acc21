const std = @import("std");

pub fn main() !void {
    // Create an allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Create some data
    const a = try allocator.alloc(f32, 4);
    defer allocator.free(a);
    
    a[0] = 1;
    a[1] = 2;
    a[2] = 3;
    a[3] = 4;
    
    const b = try allocator.alloc(f32, 4);
    defer allocator.free(b);
    
    b[0] = 5;
    b[1] = 6;
    b[2] = 7;
    b[3] = 8;
    
    // Perform a simple operation (element-wise addition)
    const c = try allocator.alloc(f32, 4);
    defer allocator.free(c);
    
    for (0..4) |i| {
        c[i] = a[i] + b[i];
    }
    
    // Print the result
    std.debug.print("Result of A + B:\n", .{});
    for (c) |val| {
        std.debug.print("{d} ", .{val});
    }
    std.debug.print("\n", .{});
    
    // Verify the result
    std.debug.assert(c[0] == 6);
    std.debug.assert(c[1] == 8);
    std.debug.assert(c[2] == 10);
    std.debug.assert(c[3] == 12);
    
    std.debug.print("Basic test passed!\n", .{});
}