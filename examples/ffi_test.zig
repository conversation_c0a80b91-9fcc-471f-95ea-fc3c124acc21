const std = @import("std");
const symbolic = @import("symbolic");

pub fn main() !void {
    // Create a context with an allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();

    var ctx = try symbolic.init(gpa.allocator());
    defer symbolic.deinit(ctx);

    // Set the default engine to Egg
    ctx.simplification.default_engine = .Egg;

    // Build a simple expression: (x + 0) * 1
    const x = try symbolic.symbol(ctx, "x");
    const zero = try symbolic.integer(ctx, 0);
    const one = try symbolic.integer(ctx, 1);

    const x_plus_0 = try symbolic.add(ctx, x, zero);
    const expr = try symbolic.mul(ctx, x_plus_0, one);

    // Simplify using the egg engine
    const simplified = try ctx.simplify(expr);

    // Print the original and simplified expressions
    std.debug.print("Original: ", .{});
    try symbolic.formatToWriter(std.io.getStdOut().writer(), ctx, expr);
    std.debug.print("\n", .{});

    std.debug.print("Simplified: ", .{});
    try symbolic.formatToWriter(std.io.getStdOut().writer(), ctx, simplified);
    std.debug.print("\n", .{});

    // Verify the result
    const equal = symbolic.nodesEqual(ctx, simplified, x);
    std.debug.print("Is equal to 'x': {}\n", .{equal});
}