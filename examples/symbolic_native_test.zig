const std = @import("std");
const symbolic = @import("../src/symbolic/symbolic.zig");
const context = @import("../src/symbolic/context.zig");

pub fn main() !void {
    // Initialize memory and context
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const allocator = gpa.allocator();
    const ctx = try context.init(allocator);
    defer context.deinit(ctx);
    
    // Create example symbolic expressions
    const a = try context.symbol(ctx, "a");
    const b = try context.symbol(ctx, "b");
    const zero = try context.integer(ctx, 0);
    const one = try context.integer(ctx, 1);
    
    // Test simplifications
    std.debug.print("\n--- Testing Native Symbolic Engine ---\n", .{});
    
    // Test 1: Identity operations (x + 0 = x)
    {
        std.debug.print("\nTest 1: a + 0\n", .{});
        
        // Create the expression a + 0
        const expr = try context.add(ctx, a, zero);
        
        // Format and print the original expression
        var buffer: [100]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original: {s}\n", .{formatted});
        
        // Simplify and print the result (using direct function approach)
        const simplified = try symbolic.simplify(ctx, expr);
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified: {s}\n", .{formatted_simp});
        
        // Assert that the result is as expected
        std.debug.print("Result matches expected: {}\n", .{context.nodesEqual(ctx, simplified, a)});
    }
    
    // Test 2: Multiplication identity (x * 1 = x)
    {
        std.debug.print("\nTest 2: a * 1\n", .{});
        
        // Create the expression a * 1
        const expr = try context.mul(ctx, a, one);
        
        // Format and print the original expression
        var buffer: [100]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original: {s}\n", .{formatted});
        
        // Simplify and print the result (using direct function approach)
        const simplified = try symbolic.simplify(ctx, expr);
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified: {s}\n", .{formatted_simp});
        
        // Assert that the result is as expected
        std.debug.print("Result matches expected: {}\n", .{context.nodesEqual(ctx, simplified, a)});
    }
    
    // Test 3: Complex expression ((a + 0) * 1) + ((b + 0) * 1) = a + b
    {
        std.debug.print("\nTest 3: ((a + 0) * 1) + ((b + 0) * 1) = a + b\n", .{});
        
        // Create the complex expression
        const a_plus_0 = try context.add(ctx, a, zero);
        const a_term = try context.mul(ctx, a_plus_0, one);
        
        const b_plus_0 = try context.add(ctx, b, zero);
        const b_term = try context.mul(ctx, b_plus_0, one);
        
        const expr = try context.add(ctx, a_term, b_term);
        
        // Format and print the original expression
        var buffer: [200]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original: {s}\n", .{formatted});
        
        // Simplify and print the result (using direct function approach)
        const simplified = try symbolic.simplify(ctx, expr);
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified: {s}\n", .{formatted_simp});
        
        // Expected result: a + b
        const expected = try context.add(ctx, a, b);
        std.debug.print("Result matches expected: {}\n", .{context.nodesEqual(ctx, simplified, expected)});
    }
    
    std.debug.print("\nAll tests completed.\n", .{});
}