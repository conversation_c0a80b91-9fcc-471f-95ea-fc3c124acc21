/// Example demonstrating symbolic expression manipulation
/// Shows how to use the idiomatic Zig direct function call approach

const std = @import("std");
const symbolic = @import("symbolic");

pub fn main() !void {
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Create a symbolic context
    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);
    
    std.debug.print("=== Symbolic Expression Direct Function Call Demo ===\n", .{});
    
    // Create symbolic variables
    const a = try symbolic.symbol(ctx, "a");
    const b = try symbolic.symbol(ctx, "b");
    const c = try symbolic.symbol(ctx, "c");
    const five = try symbolic.integer(ctx, 5);
    
    std.debug.print("Created symbols a, b, c and constant 5\n", .{});
    
    // Use direct function calls to create a complex expression
    const a_plus_b = try symbolic.add(ctx, a, b);
    const times_five = try symbolic.mul(ctx, a_plus_b, five);
    const minus_c = try symbolic.sub(ctx, times_five, c);
    const expr = try symbolic.simplify(ctx, minus_c);
    
    std.debug.print("Built expression: (a + b) * 5 - c\n", .{});
    
    // Create some values for evaluation
    var values = std.StringHashMap(i64).init(allocator);
    defer values.deinit();
    
    try values.put("a", 3);
    try values.put("b", 2);
    try values.put("c", 10);
    
    // Evaluate the expression
    const result = try symbolic.eval(ctx, expr, &values);
    std.debug.print("With a=3, b=2, c=10: Result = {d}\n", .{result});
    
    // Test logical operations
    const x = try symbolic.symbol(ctx, "x");
    const zero = try symbolic.integer(ctx, 0);
    const ten = try symbolic.integer(ctx, 10);
    
    // Create x >= 0 && x < 10 using direct function calls
    const gte_zero = try symbolic.greaterEqual(ctx, x, zero);
    const lt_ten = try symbolic.lessThan(ctx, x, ten);
    const in_range = try symbolic.logicalAnd(ctx, gte_zero, lt_ten);
    
    std.debug.print("\nBuilt range check: (x >= 0) && (x < 10)\n", .{});
    
    // Test with different values of x
    for ([_]i64{-5, 0, 5, 9, 10, 15}) |val| {
        try values.put("x", val);
        const is_in_range = try symbolic.eval(ctx, in_range, &values) != 0;
        std.debug.print("x = {d}, in range? {}\n", .{val, is_in_range});
    }
    
    // Use shape helpers with direct function calls
    // Create symbolic dimensions
    const dim1 = try symbolic.symbol(ctx, "dim1");
    const dim2 = try symbolic.symbol(ctx, "dim2");
    
    // Calculate product of dimensions
    const dims = [_]symbolic.SymExpr{dim1, dim2};
    const product = try symbolic.shape.buildProduct(ctx, &dims);
    
    std.debug.print("\nBuilt dimension product: dim1 * dim2\n", .{});
    
    // Evaluate with different values
    try values.put("dim1", 640);
    try values.put("dim2", 480);
    const pixels = try symbolic.eval(ctx, product, &values);
    std.debug.print("With dim1=640, dim2=480: {d} pixels\n", .{pixels});
    
    std.debug.print("=== Demo completed successfully ===\n", .{});
}