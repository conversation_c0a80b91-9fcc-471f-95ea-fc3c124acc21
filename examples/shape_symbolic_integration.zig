/// Example demonstrating the improved integration between the shape and symbolic modules
/// Shows how to use direct function calls to manipulate shapes symbolically

const std = @import("std");
const symbolic = @import("symbolic");
const shape = @import("shape");

pub fn main() !void {
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Create a symbolic context
    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);
    
    std.debug.print("=== Shape Symbolic Integration Example ===\n", .{});
    
    // Create a shape tracker with symbolic dimensions
    // [batch_size, 28, 28, channels]
    var tracker = try shape.ShapeTracker.init(
        ctx,
        allocator,
        &[_]shape.Dim{
            shape.Dim{ .Symbolic = try symbolic.symbol(ctx, "batch_size") },
            shape.Dim{ .Concrete = 28 },
            shape.Dim{ .Concrete = 28 },
            shape.Dim{ .Symbolic = try symbolic.symbol(ctx, "channels") },
        }
    );
    defer tracker.deinit();
    
    // Print the initial shape
    std.debug.print("Initial shape: {any}\n", .{tracker.dims()});
    
    // Use direct function calls for creating expressions
    const batch_size = try symbolic.symbol(ctx, "batch_size");
    const channels = try symbolic.symbol(ctx, "channels");
    
    // Print the total number of elements (symbolically)
    const int28 = try symbolic.integer(ctx, 28);
    
    // batch_size * 28 * 28 * channels
    const batchTimes28 = try symbolic.mul(ctx, batch_size, int28);
    const batchTimes28Squared = try symbolic.mul(ctx, batchTimes28, int28);
    const total_elements = try symbolic.mul(ctx, batchTimes28Squared, channels);
    const simplified_total = try symbolic.simplify(ctx, total_elements);
    
    std.debug.print("Total elements: {any}\n", .{simplified_total});
    
    // Now use the shape operators to transform the shape
    
    // 1. Pad height and width
    // Create padding information
    const padding_info = [_]shape.PaddingInfo{
        .{ .before = try symbolic.integer(ctx, 2), .after = try symbolic.integer(ctx, 2) },
        .{ .before = try symbolic.integer(ctx, 2), .after = try symbolic.integer(ctx, 2) },
    };
    
    // Note: The ViewOps.pad function is implemented as a static function in the module
    try shape.ViewOps.pad(&tracker, &padding_info);
    
    std.debug.print("After padding: {any}\n", .{tracker.dims()});
    
    // 2. Reshape to [batch_size, 32*32, channels]
    const new_shape = [_]shape.Dim{
        shape.Dim{ .Symbolic = batch_size },
        shape.Dim{ .Concrete = 32 * 32 },
        shape.Dim{ .Symbolic = channels },
    };
    // Use the CoreOps module for reshape
    try shape.CoreOps.reshape(&tracker, &new_shape);
    
    std.debug.print("After reshape: {any}\n", .{tracker.dims()});
    
    // 3. Use shape helper functions to build complex expressions
    
    // Calculate memory required for the tensor (assuming float32)
    const bytes_per_element = try symbolic.integer(ctx, 4);
    // Calculate memory required (total_elements * bytes_per_element)
    const memory_required = try symbolic.mul(ctx, simplified_total, bytes_per_element);
    const simplified_memory = try symbolic.simplify(ctx, memory_required);
    
    std.debug.print("Memory required (bytes): {any}\n", .{simplified_memory});
    
    // Evaluate with concrete values
    var values = std.StringHashMap(i64).init(allocator);
    defer values.deinit();
    
    try values.put("batch_size", 32);
    try values.put("channels", 3);
    
    const concrete_memory = try symbolic.eval(ctx, simplified_memory, &values);
    std.debug.print("With batch_size=32, channels=3: {d} bytes\n", .{concrete_memory});
    
    // Check if two dimensions are broadcastable
    const dim1 = try symbolic.integer(ctx, 32);
    const dim2 = try symbolic.symbol(ctx, "batch_size");
    
    // Use the shape_helpers module for broadcast checking
    const is_broadcastable = try symbolic.shape.areDimsBroadcastable(ctx, dim1, dim2);
    std.debug.print("Dimensions {any} and {any} broadcastable? {any}\n", 
        .{dim1, dim2, is_broadcastable});
    
    // Use range mask to check if an index is valid using shape helpers
    const idx = try symbolic.symbol(ctx, "idx");
    const zero = try symbolic.integer(ctx, 0);
    const thirtytwo = try symbolic.integer(ctx, 32);
    const valid_range = try symbolic.shape.buildRangeMask(ctx, idx, zero, thirtytwo);
    
    // Check if idx=16 is in range
    try values.put("idx", 16);
    const is_valid = try symbolic.eval(ctx, valid_range, &values);
    std.debug.print("Index 16 in range [0, 32)? {any}\n", .{is_valid != 0});
    
    // Check if idx=42 is in range
    try values.put("idx", 42);
    const is_valid2 = try symbolic.eval(ctx, valid_range, &values);
    std.debug.print("Index 42 in range [0, 32)? {any}\n", .{is_valid2 != 0});
    
    std.debug.print("=== Example completed successfully ===\n", .{});
}