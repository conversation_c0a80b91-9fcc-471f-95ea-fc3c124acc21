const std = @import("std");
const zing = @import("../src/zing.zig");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Initialize core
    var ctx = try zing.Core.init(allocator);
    defer ctx.deinit();
    
    // Example showing how high-level operations decompose into primitives
    // Let's trace through a simple computation: sigmoid(2 * x)
    
    // Create input constant x = 0.5
    const x_shape_id = try ctx.shape.newShape(&[_]zing.types.Dim{});
    const x_view_id = try ctx.shape.newDefaultView(x_shape_id);
    const x = try ctx.graph.newNodeConstantTyped(x_view_id, .f32);
    
    // Create constant 2.0
    const two_shape_id = try ctx.shape.newShape(&[_]zing.types.Dim{});
    const two_view_id = try ctx.shape.newDefaultView(two_shape_id);
    const two = try ctx.graph.newNodeConstantTyped(two_view_id, .f32);
    
    // Multiply: 2 * x (uses primitive multiply)
    const node_x = ctx.graph.getNode(x).?;
    const node_two = ctx.graph.getNode(two).?;
    const view_x = ctx.shape.getView(node_x.output_view_id);
    const view_two = ctx.shape.getView(node_two.output_view_id);
    const output_shape_id = try ctx.shape.broadcast(view_x.shape_id, view_two.shape_id);
    const output_view_id = try ctx.shape.newDefaultView(output_shape_id);
    const two_x = try ctx.graph.newNodeMultiply(&[_]u32{two, x}, output_view_id);
    
    // Now apply sigmoid using the tensor layer (will decompose)
    const tensor_ops = @import("../src/tensor/pointwise.zig");
    const sigmoid_result = try tensor_ops.sigmoid(&ctx, two_x);
    
    // Print the graph to see the decomposition
    std.debug.print("Created graph with {} nodes\\n", .{ctx.graph.nodes.items.len});
    
    // The sigmoid operation should have been decomposed into:
    // 1. neg(2x) -> multiply(-1, 2x)
    // 2. exp(neg) -> exp2(scaled)
    // 3. add(1, exp_result) 
    // 4. reciprocal(add_result)
    // 5. multiply(1, recip) for type consistency
    
    for (ctx.graph.nodes.items) |node| {
        std.debug.print("Node {}: op = {}\\n", .{ node.id, node.op });
    }
}