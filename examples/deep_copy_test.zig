const std = @import("std");
const symbolic = @import("symbolic");

pub fn main() !void {
    const stderr = std.io.getStdErr().writer();
    const stdout = std.io.getStdOut().writer();
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    // Create source and target contexts
    const source_ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(source_ctx);

    const target_ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(target_ctx);

    try stdout.print("Testing deep copy functionality...\n", .{});

    // Create a simple expression in the source context
    const integer_expr = try symbolic.integer(source_ctx, 42);

    // Create a complex expression (x + y) * (x - y)
    const x = try symbolic.symbol(source_ctx, "x");
    const y = try symbolic.symbol(source_ctx, "y");
    const x_plus_y = try symbolic.add(source_ctx, x, y);
    const x_minus_y = try symbolic.sub(source_ctx, x, y);
    const complex_expr = try symbolic.mul(source_ctx, x_plus_y, x_minus_y);

    // Deep copy to target context
    const copied_integer = try symbolic.deepCopyToContext(source_ctx, target_ctx, integer_expr);
    const copied_complex = try symbolic.deepCopyToContext(source_ctx, target_ctx, complex_expr);

    // Verify the copy is correct
    if (copied_integer.tag != .integer) {
        try stderr.print("Error: Expected integer tag but got something else\n", .{});
        return error.TestFailed;
    }
    
    if (copied_integer.payload.integer != 42) {
        try stderr.print("Error: Expected value 42 but got {}\n", .{copied_integer.payload.integer});
        return error.TestFailed;
    }

    // Verify complex expression structure
    if (copied_complex.tag != .mul) {
        try stderr.print("Error: Expected mul tag but got something else\n", .{});
        return error.TestFailed;
    }
    
    if (copied_complex.payload.mul.left.tag != .add) {
        try stderr.print("Error: Expected add tag in left branch but got something else\n", .{});
        return error.TestFailed;
    }
    
    if (copied_complex.payload.mul.right.tag != .sub) {
        try stderr.print("Error: Expected sub tag in right branch but got something else\n", .{});
        return error.TestFailed;
    }

    try stdout.print("All deep copy tests passed!\n", .{});
}