const std = @import("std");
const Core = @import("core").Core;

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create core
    const core = try Core.init(allocator);
    defer core.deinit();

    // Get compiler - just test that it can be created
    const compiler = try core.getCompiler();
    _ = compiler;
    
    std.debug.print("Compiler integration test passed!\n", .{});
}