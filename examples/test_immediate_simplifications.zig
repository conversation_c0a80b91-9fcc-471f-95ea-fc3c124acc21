const std = @import("std");

// Import the symbolic module directly
const symbolic = @import("symbolic");
const context = symbolic.context;
const SymbolicContext = context.SymbolicContext;

// Import the init and builder modules directly
const initMod = @import("symbolic").init;
const builderMod = @import("symbolic").builder;

pub fn main() !void {
    // Create allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Setup context
    const ctx = try initMod.init(allocator);
    defer initMod.deinit(ctx);
    
    // Run tests and report results
    try testMultiplication(ctx);
    try testAddition(ctx);
    try testSubtraction(ctx);
    try testDivision(ctx);
    try testLogical(ctx);
    try testMinMax(ctx);
    try testComplex(ctx);
    
    std.debug.print("All immediate simplification tests passed!\n", .{});
}

fn testMultiplication(ctx: *SymbolicContext) !void {
    std.debug.print("Testing multiplication identities...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const one = try builderMod.integer(ctx, 1);
    const zero = try builderMod.integer(ctx, 0);

    // Test x * 1 = x
    const x_mul_one = try builderMod.mul(ctx, x, one);
    try expectEqual(x, x_mul_one);
    std.debug.print("  ✓ x * 1 = x\n", .{});

    // Test 1 * x = x
    const one_mul_x = try builderMod.mul(ctx, one, x);
    try expectEqual(x, one_mul_x);
    std.debug.print("  ✓ 1 * x = x\n", .{});

    // Test x * 0 = 0
    const x_mul_zero = try builderMod.mul(ctx, x, zero);
    try expectEqual(zero, x_mul_zero);
    std.debug.print("  ✓ x * 0 = 0\n", .{});

    // Test 0 * x = 0
    const zero_mul_x = try builderMod.mul(ctx, zero, x);
    try expectEqual(zero, zero_mul_x);
    std.debug.print("  ✓ 0 * x = 0\n", .{});
}

fn testAddition(ctx: *SymbolicContext) !void {
    std.debug.print("Testing addition identities...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const zero = try builderMod.integer(ctx, 0);

    // Test x + 0 = x
    const x_add_zero = try builderMod.add(ctx, x, zero);
    try expectEqual(x, x_add_zero);
    std.debug.print("  ✓ x + 0 = x\n", .{});

    // Test 0 + x = x
    const zero_add_x = try builderMod.add(ctx, zero, x);
    try expectEqual(x, zero_add_x);
    std.debug.print("  ✓ 0 + x = x\n", .{});
}

fn testSubtraction(ctx: *SymbolicContext) !void {
    std.debug.print("Testing subtraction identities...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const zero = try builderMod.integer(ctx, 0);

    // Test x - 0 = x
    const x_sub_zero = try builderMod.sub(ctx, x, zero);
    try expectEqual(x, x_sub_zero);
    std.debug.print("  ✓ x - 0 = x\n", .{});

    // Test x - x = 0
    const x_sub_x = try builderMod.sub(ctx, x, x);
    const result_zero = try builderMod.integer(ctx, 0);
    
    // Need to check integer value since this creates a new zero node
    try expectEqual(result_zero.tag, x_sub_x.tag);
    try expectEqual(result_zero.payload.Integer, x_sub_x.payload.Integer);
    std.debug.print("  ✓ x - x = 0\n", .{});
}

fn testDivision(ctx: *SymbolicContext) !void {
    std.debug.print("Testing division identities...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const one = try builderMod.integer(ctx, 1);
    const zero = try builderMod.integer(ctx, 0);

    // Test x / 1 = x
    const x_div_one = try builderMod.div(ctx, x, one);
    try expectEqual(x, x_div_one);
    std.debug.print("  ✓ x / 1 = x\n", .{});

    // Test 0 / x = 0
    const zero_div_x = try builderMod.div(ctx, zero, x);
    try expectEqual(zero, zero_div_x);
    std.debug.print("  ✓ 0 / x = 0\n", .{});
}

fn testLogical(ctx: *SymbolicContext) !void {
    std.debug.print("Testing logical operation identities...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const true_val = try builderMod.integer(ctx, 1);
    const false_val = try builderMod.integer(ctx, 0);

    // Test true AND x = x
    const true_and_x = try builderMod.logicalAnd(ctx, true_val, x);
    try expectEqual(x, true_and_x);
    std.debug.print("  ✓ true AND x = x\n", .{});

    // Test x AND true = x
    const x_and_true = try builderMod.logicalAnd(ctx, x, true_val);
    try expectEqual(x, x_and_true);
    std.debug.print("  ✓ x AND true = x\n", .{});

    // Test false OR x = x
    const false_or_x = try builderMod.logicalOr(ctx, false_val, x);
    try expectEqual(x, false_or_x);
    std.debug.print("  ✓ false OR x = x\n", .{});

    // Test x OR false = x
    const x_or_false = try builderMod.logicalOr(ctx, x, false_val);
    try expectEqual(x, x_or_false);
    std.debug.print("  ✓ x OR false = x\n", .{});

    // Test NOT NOT x = x
    const not_x = try builderMod.logicalNot(ctx, x);
    const not_not_x = try builderMod.logicalNot(ctx, not_x);
    try expectEqual(x, not_not_x);
    std.debug.print("  ✓ NOT NOT x = x\n", .{});
}

fn testMinMax(ctx: *SymbolicContext) !void {
    std.debug.print("Testing min/max identities...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const three = try builderMod.integer(ctx, 3);
    const five = try builderMod.integer(ctx, 5);

    // Test min(x, x) = x
    const min_x_x = try builderMod.min(ctx, x, x);
    try expectEqual(x, min_x_x);
    std.debug.print("  ✓ min(x, x) = x\n", .{});

    // Test max(x, x) = x
    const max_x_x = try builderMod.max(ctx, x, x);
    try expectEqual(x, max_x_x);
    std.debug.print("  ✓ max(x, x) = x\n", .{});

    // Test min(3, 5) = 3
    const min_3_5 = try builderMod.min(ctx, three, five);
    try expectEqual(three, min_3_5);
    std.debug.print("  ✓ min(3, 5) = 3\n", .{});

    // Test max(3, 5) = 5
    const max_3_5 = try builderMod.max(ctx, three, five);
    try expectEqual(five, max_3_5);
    std.debug.print("  ✓ max(3, 5) = 5\n", .{});
}

fn testComplex(ctx: *SymbolicContext) !void {
    std.debug.print("Testing complex expression simplifications...\n", .{});
    
    // Create symbols
    const x = try builderMod.symbol(ctx, "x");
    const zero = try builderMod.integer(ctx, 0);
    const one = try builderMod.integer(ctx, 1);

    // Test (x * 1) + 0 = x
    const x_mul_one = try builderMod.mul(ctx, x, one);
    const complex_expr = try builderMod.add(ctx, x_mul_one, zero);
    try expectEqual(x, complex_expr);
    std.debug.print("  ✓ (x * 1) + 0 = x\n", .{});

    // Test more complex: (x + 0) * 1 / 1 = x
    const x_add_zero = try builderMod.add(ctx, x, zero);
    const mul_one = try builderMod.mul(ctx, x_add_zero, one);
    const div_one = try builderMod.div(ctx, mul_one, one);
    try expectEqual(x, div_one);
    std.debug.print("  ✓ (x + 0) * 1 / 1 = x\n", .{});
}

// Simple test utility
fn expectEqual(expected: anytype, actual: @TypeOf(expected)) !void {
    if (expected != actual) {
        std.debug.print("Expected {any}, got {any}\n", .{ expected, actual });
        return error.TestFailed;
    }
}