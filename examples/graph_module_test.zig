const std = @import("std");
const shape = @import("src").shape;
const symbolic = @import("src").symbolic;

// Import the graph module
const graph = @import("src").graph;

pub fn main() !void {
    // Create an allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Create a graph context
    const ctx = try graph.createContext(allocator, .{});
    defer ctx.destroyContext();
    
    // Create constant tensors
    const a = try graph.constant(ctx, &[_]f32{1, 2, 3, 4}, &[_]shape.Dim{
        shape.Dim.fromValue(2),
        shape.Dim.fromValue(2),
    });
    
    const b = try graph.constant(ctx, &[_]f32{5, 6, 7, 8}, &[_]shape.Dim{
        shape.Dim.fromValue(2),
        shape.Dim.fromValue(2),
    });
    
    // Add tensors
    const c = try a.add(&b);
    
    // Execute graph
    try graph.execute(ctx);
    
    // Get results
    const result = try graph.getData(c);
    
    // Print results
    std.debug.print("Result: [", .{});
    for (result, 0..) |val, i| {
        if (i > 0) std.debug.print(", ", .{});
        std.debug.print("{d}", .{val});
    }
    std.debug.print("]\n", .{});
    
    // Expected result: [6, 8, 10, 12]
    const expected = [_]f32{6, 8, 10, 12};
    for (result, expected) |val, exp| {
        if (val != exp) {
            std.debug.print("Test failed: {d} != {d}\n", .{val, exp});
            return error.TestFailed;
        }
    }
    
    std.debug.print("Test passed!\n", .{});
}