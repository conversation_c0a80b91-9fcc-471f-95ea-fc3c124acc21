// Minimal V2 test with detailed debugging
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("Starting minimal test...\n", .{});
    
    const core = try zing.Core.init(gpa.allocator());
    defer core.deinit();
    
    std.debug.print("Core initialized!\n", .{});
    std.debug.print("Core address: {*}\n", .{core});
    std.debug.print("Core.symbolic address: {*}\n", .{&core.symbolic});
    std.debug.print("Core.arena allocator: {any}\n", .{core.arena.allocator()});
    
    // Test the arena allocator directly
    std.debug.print("Testing arena allocator...\n", .{});
    const test_alloc = try core.arena.allocator().create(u32);
    test_alloc.* = 42;
    std.debug.print("Arena allocation works, value: {}\n", .{test_alloc.*});
    
    // Test symbolic first
    std.debug.print("Creating symbol...\n", .{});
    const sym = try core.symbolic.newSymbolExpr("test_symbol");
    std.debug.print("Symbol created!\n", .{});
    
    // Just create the shape engine and test it
    const dims = [_]zing.Dim{
        .{ .concrete = 10 },
        .{ .symbolic = sym },
    };
    
    std.debug.print("Creating shape...\n", .{});
    const shape_id = try core.shape.newShape(&dims);
    
    std.debug.print("Shape created with id: {}\n", .{shape_id});
    
    const shape = core.shape.getShape(shape_id);
    std.debug.print("Shape has {} dimensions\n", .{shape.dims.len});
    
    std.debug.print("Test complete!\n", .{});
}