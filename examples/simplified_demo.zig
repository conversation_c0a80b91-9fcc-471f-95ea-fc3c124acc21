//! Simplified Egg FFI Demonstration
//!
//! This program demonstrates the new simplified FFI interface for the egg library.
//! It creates a simple expression, simplifies it, and prints the result.
//!
//! Key advantages over the previous approach:
//! 1. Single FFI call for simplification (no need to create EGraphs, add nodes, etc.)
//! 2. Simplified memory management (clear ownership boundaries)
//! 3. More intuitive expression building API
//! 4. Backend abstraction for future native implementation
//!
//! Compile with: `zig build-exe simplified_demo.zig -lc -legg_ffi -I../src`

const std = @import("std");
const symbolic = @import("../src/symbolic/symbolic.zig");
const context = @import("../src/symbolic/context.zig");

pub fn main() !void {
    // Initialize the general purpose allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Initialize a symbolic context
    const ctx = try context.init(allocator);
    defer context.deinit(ctx);
    
    // ===== EXPRESSION 1: (x + 0) * 1 =====
    {
        std.debug.print("\n=== Demonstration 1: (x + 0) * 1 ===\n", .{});
        
        // Create the expression: (x + 0) * 1
        const x = try context.symbol(ctx, "x");
        const zero = try context.integer(ctx, 0);
        const one = try context.integer(ctx, 1);
        
        const x_plus_0 = try context.add(ctx, x, zero);
        const expr = try context.mul(ctx, x_plus_0, one);
        
        // Print the original expression
        var buffer: [100]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original expression: {s}\n", .{formatted});
        
        // Simplify the expression using direct function (FFI)
        const simplified = try symbolic.simplifyWithEgg(ctx, expr);
        
        // Print the simplified expression
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified expression: {s}\n", .{formatted_simp});
    }
    
    // ===== EXPRESSION 2: a * 0 =====
    {
        std.debug.print("\n=== Demonstration 2: a * 0 ===\n", .{});
        
        // Create the expression: a * 0
        const a = try context.symbol(ctx, "a");
        const zero = try context.integer(ctx, 0);
        
        const expr = try context.mul(ctx, a, zero);
        
        // Print the original expression
        var buffer: [100]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original expression: {s}\n", .{formatted});
        
        // Simplify the expression using direct function (FFI)
        const simplified = try symbolic.simplifyWithEgg(ctx, expr);
        
        // Print the simplified expression
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified expression: {s}\n", .{formatted_simp});
    }
    
    // ===== EXPRESSION 3: (a + b) * (c + d) =====
    {
        std.debug.print("\n=== Demonstration 3: (a + b) * (c + d) ===\n", .{});
        
        // Create the expression: (a + b) * (c + d)
        const a = try context.symbol(ctx, "a");
        const b = try context.symbol(ctx, "b");
        const c = try context.symbol(ctx, "c");
        const d = try context.symbol(ctx, "d");
        
        const a_plus_b = try context.add(ctx, a, b);
        const c_plus_d = try context.add(ctx, c, d);
        
        const expr = try context.mul(ctx, a_plus_b, c_plus_d);
        
        // Print the original expression
        var buffer: [100]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original expression: {s}\n", .{formatted});
        
        // Simplify the expression using direct function (FFI)
        const simplified = try symbolic.simplifyWithEgg(ctx, expr);
        
        // Print the simplified expression
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified expression: {s}\n", .{formatted_simp});
    }
    
    // ===== EXPRESSION 4: 1 + 2 + 3 + 4 =====
    {
        std.debug.print("\n=== Demonstration 4: 1 + 2 + 3 + 4 ===\n", .{});
        
        // Create the expression: 1 + 2 + 3 + 4
        const one = try context.integer(ctx, 1);
        const two = try context.integer(ctx, 2);
        const three = try context.integer(ctx, 3);
        const four = try context.integer(ctx, 4);
        
        const one_plus_two = try context.add(ctx, one, two);
        const three_plus_four = try context.add(ctx, three, four);
        
        const expr = try context.add(ctx, one_plus_two, three_plus_four);
        
        // Print the original expression
        var buffer: [100]u8 = undefined;
        const formatted = try std.fmt.bufPrint(&buffer, "{}", .{expr});
        std.debug.print("Original expression: {s}\n", .{formatted});
        
        // Simplify the expression using direct function (FFI)
        const simplified = try symbolic.simplifyWithEgg(ctx, expr);
        
        // Print the simplified expression
        const formatted_simp = try std.fmt.bufPrint(&buffer, "{}", .{simplified});
        std.debug.print("Simplified expression: {s}\n", .{formatted_simp});
    }
    
    std.debug.print("\nSimplified FFI demonstration completed successfully!\n", .{});
}