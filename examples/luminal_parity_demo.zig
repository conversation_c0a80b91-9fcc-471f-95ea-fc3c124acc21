const std = @import("std");
const Core = @import("core").Core;
const tensor = @import("tensor");

// Demonstrates Luminal parity features implemented in the tensor module
pub fn main() !void {
    // Initialize core context
    var arena = std.heap.ArenaAllocator.init(std.heap.c_allocator);
    defer arena.deinit();
    var core_ctx = try Core.init(arena.allocator());
    defer core_ctx.deinit();
    
    // Print header
    std.debug.print("\n=== Luminal Parity Features Demo ===\n\n", .{});
    
    // Create a 2x3 tensor with values 0-5
    std.debug.print("Creating a 2x3 tensor...\n", .{});
    const data = [_]f32{ 0, 1, 2, 3, 4, 5 };
    const a = try tensor.constant(&core_ctx, &data, &[_]i64{2, 3});
    
    // Demonstrate as_strided (low-level view manipulation)
    std.debug.print("\nDemonstrating as_strided to transpose the tensor using custom strides...\n", .{});
    const b = try tensor.manipulation.as_strided(&core_ctx, a, &[_]i64{3, 2}, &[_]i64{1, 2}, 0);
    
    // Verify that b is a view of a (same node ID)
    std.debug.print("Verifying b is a view of a (no new data)...\n", .{});
    const a_id = @intFromEnum(tensor.utils.nodeIdToU32(a));
    const b_id = @intFromEnum(tensor.utils.nodeIdToU32(b));
    std.debug.print("  a node ID: {d}\n", .{a_id});
    std.debug.print("  b node ID: {d}\n", .{b_id});
    std.debug.print("  Same node? {}\n", .{a_id == b_id});
    
    // Materialize with contiguous to see the transposed data
    std.debug.print("\nMaterializing the transposed view with contiguous()...\n", .{});
    const b_materialized = try tensor.manipulation.contiguous(&core_ctx, b);
    const b_id_materialized = @intFromEnum(tensor.utils.nodeIdToU32(b_materialized));
    std.debug.print("  materialized node ID: {d}\n", .{b_id_materialized});
    std.debug.print("  New node created? {}\n", .{b_id_materialized != b_id});
    
    // Demonstrate expand with multiple dimensions
    std.debug.print("\nDemonstrating expand with multiple dimensions...\n", .{});
    const small = try tensor.constant(&core_ctx, &[_]f32{1, 2, 3}, &[_]i64{1, 1, 3});
    const expanded = try tensor.manipulation.expand(&core_ctx, small, &[_]i64{2, 3, 3});
    
    // Verify expanded is a view (same node ID)
    const small_id = @intFromEnum(tensor.utils.nodeIdToU32(small));
    const expanded_id = @intFromEnum(tensor.utils.nodeIdToU32(expanded));
    std.debug.print("  Expanded shape: 1x1x3 -> 2x3x3\n", .{});
    std.debug.print("  Same node? {}\n", .{small_id == expanded_id});
    
    // Demonstrate broadcast as a view operation
    std.debug.print("\nDemonstrating broadcastTo as a view operation...\n", .{});
    const vec = try tensor.constant(&core_ctx, &[_]f32{1, 2, 3}, &[_]i64{1, 3});
    const broadcasted = try tensor.manipulation.broadcastTo(&core_ctx, vec, &[_]i64{4, 3});
    
    // Verify broadcasted is a view (same node ID)
    const vec_id = @intFromEnum(tensor.utils.nodeIdToU32(vec));
    const broadcast_id = @intFromEnum(tensor.utils.nodeIdToU32(broadcasted));
    std.debug.print("  Broadcast shape: 1x3 -> 4x3\n", .{});
    std.debug.print("  Same node? {}\n", .{vec_id == broadcast_id});
    
    // Demonstrate view chain optimization
    std.debug.print("\nDemonstrating view chain optimization...\n", .{});
    const original = try tensor.constant(&core_ctx, &[_]f32{1, 2, 3, 4, 5, 6}, &[_]i64{2, 3});
    
    // Apply a chain of view operations
    std.debug.print("  Applying chain: reshape -> transpose -> reshape\n", .{});
    const reshape1 = try tensor.manipulation.reshape(&core_ctx, original, &[_]i64{6, 1});
    const transpose = try tensor.manipulation.transpose(&core_ctx, reshape1, null, null);
    const reshape2 = try tensor.manipulation.reshape(&core_ctx, transpose, &[_]i64{2, 3});
    
    // Optimize the view chain
    std.debug.print("  Optimizing view chain...\n", .{});
    const reshape2_node = core_ctx.graph.getNode(tensor.utils.nodeIdToU32(reshape2)) orelse unreachable;
    const pre_optimize_view_id = @intFromEnum(reshape2_node.output_view_id);
    const optimized = try tensor.manipulation.optimizeViews(&core_ctx, reshape2);
    const optimized_node = core_ctx.graph.getNode(tensor.utils.nodeIdToU32(optimized)) orelse unreachable;
    const post_optimize_view_id = @intFromEnum(optimized_node.output_view_id);
    
    std.debug.print("  Pre-optimization view ID: {d}\n", .{pre_optimize_view_id});
    std.debug.print("  Post-optimization view ID: {d}\n", .{post_optimize_view_id});
    std.debug.print("  Views changed? {}\n", .{pre_optimize_view_id != post_optimize_view_id});
    
    // Verify matmul uses composition
    std.debug.print("\nDemonstrating matmul using composition...\n", .{});
    const m1 = try tensor.constant(&core_ctx, &[_]f32{1, 2, 3, 4}, &[_]i64{2, 2});
    const m2 = try tensor.constant(&core_ctx, &[_]f32{5, 6, 7, 8}, &[_]i64{2, 2});
    
    // Perform matrix multiplication
    const matmul_result = try tensor.linalg.matmul(&core_ctx, m1, m2);
    
    // Verify it's a compute operation (creates new node)
    const m1_id = @intFromEnum(tensor.utils.nodeIdToU32(m1));
    const matmul_id = @intFromEnum(tensor.utils.nodeIdToU32(matmul_result));
    std.debug.print("  m1 node ID: {d}\n", .{m1_id});
    std.debug.print("  matmul result node ID: {d}\n", .{matmul_id});
    std.debug.print("  New node created? {}\n", .{m1_id != matmul_id});
    
    std.debug.print("\n=== Demo Complete ===\n", .{});
}