// Test memory pool behavior
const std = @import("std");

const TestStruct = struct {
    tag: u8,
    data: union {
        value: i64,
        ptr: struct { left: *TestStruct, right: *TestStruct },
    },
};

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    var arena = std.heap.ArenaAllocator.init(gpa.allocator());
    defer arena.deinit();
    
    std.debug.print("Testing MemoryPool behavior...\n", .{});
    std.debug.print("Size of TestStruct: {}\n", .{@sizeOf(TestStruct)});
    std.debug.print("Alignment of TestStruct: {}\n", .{@alignOf(TestStruct)});
    
    // Test direct pool allocation
    var pool = std.heap.MemoryPool(TestStruct).init(arena.allocator());
    
    std.debug.print("Pool initialized\n", .{});
    
    const item1 = try pool.create();
    std.debug.print("Created item1 at: {*}\n", .{item1});
    
    const item2 = try pool.create();
    std.debug.print("Created item2 at: {*}\n", .{item2});
    
    // Test with nested pool
    var pool2 = std.heap.MemoryPool(TestStruct).init(arena.allocator());
    const item3 = try pool2.create();
    std.debug.print("Created item3 at: {*}\n", .{item3});
    
    std.debug.print("Test complete!\n", .{});
}