const std = @import("std");
const zing = @import("zing");
const ops = zing.ops;

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Initialize Zing core
    const core = try zing.core.Core.init(allocator);
    defer core.deinit();
    
    // Test creating constants with patterns
    const zeros = try ops.creation.zeros(core, &[_]zing.core.types.Dim{
        .{ .concrete = 3 },
        .{ .concrete = 4 },
    });
    
    const ones = try ops.creation.ones(core, &[_]zing.core.types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 2 },
    });
    
    const full = try ops.creation.full(core, &[_]zing.core.types.Dim{
        .{ .concrete = 5 },
    }, 3.14);
    
    const eye = try ops.creation.eye(core, 3, .f32);
    
    // Test creating constants with actual data
    const scalar = try ops.creation.constant(core, @as(f32, 42.0));
    
    const array_data = [_]f32{ 1.0, 2.0, 3.0, 4.0 };
    const array = try ops.creation.constant(core, array_data);
    
    const slice_data = &[_]f32{ 5.0, 6.0, 7.0 };
    const slice = try ops.creation.constant(core, slice_data);
    
    // Test linspace and arange
    const linspace = try ops.creation.linspace(core, 0.0, 10.0, 5);
    const arange = try ops.creation.arange(core, 0.0, 10.0, 2.0);
    
    // Test creating variables
    const weights = try ops.creation.variable(core, &[_]zing.core.types.Dim{
        .{ .concrete = 3 },
        .{ .concrete = 3 },
    }, .f32);
    
    const init_data = [_]f32{ 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0 };
    const weights_init = try ops.creation.variableWithData(core, init_data);
    
    // Test creating placeholders
    const input = try ops.creation.placeholder(core, &[_]zing.core.types.Dim{
        .{ .concrete = 10 },
        .{ .concrete = 784 },
    }, .f32);
    
    // Test creating symbolic placeholder (batch dimension is symbolic)
    const batch_input = try ops.creation.placeholderSymbolic(core, &.{
        .{ .size = null, .symbol = "batch" },
        .{ .size = 784, .symbol = null },
    }, .f32);
    
    // Verify we created the right node types
    const graph = &core.graph;
    
    // Check zeros node
    if (graph.getNode(zeros)) |node| {
        std.debug.assert(node.op == .constant);
        std.debug.assert(node.isConstant());
    }
    
    // Check weights node
    if (graph.getNode(weights)) |node| {
        std.debug.assert(node.op == .variable);
        std.debug.assert(node.isVariable());
    }
    
    // Check input node
    if (graph.getNode(input)) |node| {
        std.debug.assert(node.op == .input);
        std.debug.assert(node.isInput());
    }
    
    // Check data store
    const data_store = &core.data;
    
    // Constants should be stored
    std.debug.assert(data_store.hasConstantData(zeros));
    std.debug.assert(data_store.hasConstantData(ones));
    std.debug.assert(data_store.hasConstantData(full));
    std.debug.assert(data_store.hasConstantData(eye));
    std.debug.assert(data_store.hasConstantData(scalar));
    std.debug.assert(data_store.hasConstantData(array));
    
    // Parameters should be registered
    std.debug.assert(data_store.isParameter(weights));
    std.debug.assert(data_store.isParameter(weights_init));
    
    // Inputs should be registered
    std.debug.assert(data_store.isInput(input));
    std.debug.assert(data_store.isInput(batch_input));
    
    std.debug.print("All data store tests passed!\n", .{});
    
    // Test getting constant patterns
    const zeros_pattern = try data_store.getConstantPattern(zeros);
    std.debug.assert(zeros_pattern == .zeros);
    
    const ones_pattern = try data_store.getConstantPattern(ones);
    std.debug.assert(ones_pattern == .ones);
    
    const eye_pattern = try data_store.getConstantPattern(eye);
    std.debug.assert(eye_pattern == .identity);
    
    // Test getting actual data
    const scalar_data = try data_store.getConstantData(f32, scalar);
    std.debug.assert(scalar_data.len == 1);
    std.debug.assert(scalar_data[0] == 42.0);
    
    const array_const_data = try data_store.getConstantData(f32, array);
    std.debug.assert(array_const_data.len == 4);
    for (array_const_data, 0..) |val, i| {
        std.debug.assert(val == array_data[i]);
    }
    
    std.debug.print("Data retrieval tests passed!\n", .{});
}