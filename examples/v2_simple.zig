// Simple example demonstrating the V2 API
const std = @import("std");
// Use proper module imports
const Core = @import("core").Core;
const Expr = @import("core").types.Expr;

pub fn main() !void {
    // Initialize allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    // Initialize core
    const core = try Core.init(gpa.allocator());
    defer core.deinit();
    
    std.debug.print("=== Zing V2 Example ===\n", .{});
    
    // Create symbolic dimensions for dynamic batch size
    const batch_sym = try core.symbolic.newSymbolExpr("batch_size");
    const seq_sym = try core.symbolic.newSymbolExpr("seq_len");
    
    // Create shapes with mixed concrete and symbolic dimensions
    const embed_dim_expr = try core.symbolic.newIntegerExpr(128);
    const input_shape_id = try core.shape.newShape(&.{
        batch_sym,      // Dynamic batch size
        seq_sym,        // Dynamic sequence length  
        embed_dim_expr, // Fixed embedding dimension
    });
    
    // Create view for input shape
    const input_view_id = try core.shape.newDefaultView(input_shape_id);
    
    // Create input node
    const input_node = try core.graph.newNodeConstant(input_view_id);
    
    // Get shape of the input to verify
    const input_view = core.shape.getView(core.graph.getNode(input_node).?.output_view_id);
    const input_shape = core.shape.getShape(input_view.shape_id);
    
    std.debug.print("Input shape: [batch_size, seq_len, 128]\n", .{});
    std.debug.print("Weight shape: [128, 256]\n", .{});
    std.debug.print("Input shape has {} dimensions\n", .{input_shape.dims.len});
    
    // Print input dimensions
    for (input_shape.dims, 0..) |expr, i| {
        // Try to evaluate as concrete value, fallback to expression description
        if (core.symbolic.evaluate(expr, null)) |val| {
            std.debug.print("  Dim {}: {}\n", .{ i, val });
        } else |_| {
            if (expr.tag == .symbol) {
                std.debug.print("  Dim {}: {s}\n", .{ i, expr.data.symbol.name });
            } else {
                std.debug.print("  Dim {}: <expression>\n", .{i});
            }
        }
    }
    
    // Demonstrate broadcasting
    std.debug.print("\n=== Broadcasting Example ===\n", .{});
    
    const one_expr = try Expr.testDim(gpa.allocator(), 1);
    const five_expr = try Expr.testDim(gpa.allocator(), 5);
    const three_expr = try Expr.testDim(gpa.allocator(), 3);
    const one_expr_b = try Expr.testDim(gpa.allocator(), 1);
    
    const shape_a = try core.shape.newShape(&.{
        one_expr,
        five_expr,
    });
    
    const shape_b = try core.shape.newShape(&.{
        three_expr,
        one_expr_b,
    });
    
    const broadcast_shape_id = try core.shape.inferBroadcastShape(shape_a, shape_b);
    const broadcast_shape = core.shape.getShape(broadcast_shape_id);
    
    std.debug.print("Shape A: [1, 5]\n", .{});
    std.debug.print("Shape B: [3, 1]\n", .{});
    std.debug.print("Broadcast result: [", .{});
    for (broadcast_shape.dims, 0..) |expr, i| {
        if (i > 0) std.debug.print(", ", .{});
        if (core.symbolic.evaluate(expr, null)) |val| {
            std.debug.print("{}", .{val});
        } else |_| {
            std.debug.print("?", .{});
        }
    }
    std.debug.print("]\n", .{});
    
    std.debug.print("\n=== V2 Example Complete ===\n", .{});
}

