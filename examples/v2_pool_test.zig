// Test just memory pool
const std = @import("std");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("=== Testing Memory Pool ===\n", .{});
    
    var arena = std.heap.ArenaAllocator.init(gpa.allocator());
    defer arena.deinit();
    
    std.debug.print("Arena created\n", .{});
    
    var pool = std.heap.MemoryPool(i32).init(arena.allocator());
    
    std.debug.print("Pool created\n", .{});
    
    const item = try pool.create();
    std.debug.print("Item created: {*}\n", .{item});
    
    item.* = 42;
    std.debug.print("Item value: {}\n", .{item.*});
    
    std.debug.print("=== Test Complete ===\n", .{});
}