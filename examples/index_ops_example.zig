const std = @import("std");

pub fn main() !void {
    std.debug.print("Index Operations Example\n", .{});
    std.debug.print("========================\n", .{});
    
    // This example demonstrates the slice and gather operations
    std.debug.print("\nSlice Operation\n", .{});
    std.debug.print("---------------\n", .{});
    std.debug.print("Input: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", .{});
    std.debug.print("Slice [2:7]: [2, 3, 4, 5, 6]\n", .{});
    std.debug.print("Slice [2:7:2]: [2, 4, 6]\n", .{});
    
    std.debug.print("\nGather Operation\n", .{});
    std.debug.print("----------------\n", .{});
    std.debug.print("Input: [0, 10, 20, 30, 40, 50, 60, 70, 80, 90]\n", .{});
    std.debug.print("Indices: [1, 5, 9]\n", .{});
    std.debug.print("Gathered: [10, 50, 90]\n", .{});
    
    std.debug.print("\nThese operations are now available in the graph module through the 'ops' namespace:\n", .{});
    std.debug.print("- graph.ops.slice(tensor, starts, ends, strides)\n", .{});
    std.debug.print("- graph.ops.gather(data, indices, axis)\n", .{});
}