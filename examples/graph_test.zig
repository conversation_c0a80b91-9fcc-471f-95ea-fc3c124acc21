const std = @import("std");

// This is a workaround for direct file imports
const shape_types = @import("../src/shape/types.zig");
const shape_context = @import("../src/shape/context/init.zig");
const shape_utils = @import("../src/shape/utils.zig");
const shape = @import("../src/shape/shape.zig");

// Directly import all needed graph files
const graph_types = @import("../src/graph/types.zig");
const graph_context = @import("../src/graph/graph_context.zig");
const graph_node = @import("../src/graph/graph_node.zig");
const tensor = @import("../src/graph/tensor.zig");
const graph = @import("../src/graph/module.zig");

pub fn main() !void {
    // Create an arena allocator
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    // Create a graph context
    const ctx = try graph.createContext(allocator, .{});
    defer ctx.destroyContext();
    
    // Create constant tensors
    const a = try graph.constant(ctx, &[_]f32{1, 2, 3, 4}, &[_]shape.Dim{
        shape.Dim.fromValue(2),
        shape.Dim.fromValue(2),
    });
    
    const b = try graph.constant(ctx, &[_]f32{5, 6, 7, 8}, &[_]shape.Dim{
        shape.Dim.fromValue(2),
        shape.Dim.fromValue(2),
    });
    
    // Add tensors
    const c = try graph.add(a, b);
    
    // Execute graph
    try graph.execute(ctx);
    
    // Get results
    const result = try graph.getData(c);
    
    // Print results
    std.debug.print("Result: {any}\n", .{result});
    
    // Verify results
    std.debug.assert(result[0] == 6);
    std.debug.assert(result[1] == 8);
    std.debug.assert(result[2] == 10);
    std.debug.assert(result[3] == 12);
    
    std.debug.print("Basic graph operations test passed!\n", .{});
}