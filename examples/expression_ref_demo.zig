/// This example demonstrates how to use the ExpressionRef utility struct
/// to safely work with symbolic expressions with automatic validation and error handling.

const std = @import("std");
const symbolic = @import("symbolic");
const ExpressionRef = symbolic.ExpressionRef;

pub fn main() !void {
    // Create a context 
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();
    
    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);
    
    // Enable debug mode for verbose validation
    ctx.setDebugMode(true);
    
    // Create expressions using the ExpressionRef utility
    const a = try ExpressionRef.initSymbol(ctx, "a");
    const b = try ExpressionRef.initSymbol(ctx, "b");
    const two = try ExpressionRef.initInteger(ctx, 2);
    const three = try ExpressionRef.initInteger(ctx, 3);
    
    // Use the fluent API to build an expression: 2*a + 3*b
    const two_a = try two.mul(a);
    const three_b = try three.mul(b);
    const expr = try two_a.add(three_b);
    
    // Print the created expression
    var output = std.ArrayList(u8).init(allocator);
    defer output.deinit();
    const writer = output.writer();
    
    try symbolic.format(ctx, expr.get(), writer);
    const expr_str = try output.toOwnedSlice();
    defer allocator.free(expr_str);
    
    std.debug.print("Created expression: {s}\n", .{expr_str});
    
    // Define a map of values for evaluation
    var bindings = std.StringHashMap(i64).init(allocator);
    defer bindings.deinit();
    try bindings.put("a", 5);
    try bindings.put("b", 7);
    
    // Evaluate the expression with the given bindings
    const result = try expr.evaluate(&bindings.unmanaged);
    
    std.debug.print("Evaluation result (with a=5, b=7): {d}\n", .{result});
    std.debug.print("Expected value: {d}\n", .{2 * 5 + 3 * 7});
    
    // Demonstrate the safety features - validation and error handling
    std.debug.print("\nDemonstrating safety features:\n", .{});
    
    // Check the type of expressions
    std.debug.print("a is a symbol: {}\n", .{a.isSymbol()});
    std.debug.print("two is a constant: {}\n", .{two.isConstant()});
    
    // Simplify the expression
    const simplified = try expr.simplify();
    
    // The expression is already in simplified form, but we can still call simplify()
    std.debug.print("Simplified expression is equal to original: {}\n", 
        .{try simplified.isEqual(expr)});
        
    // Create and demonstrate logical operations
    const true_expr = try ExpressionRef.initBoolean(ctx, true);
    const false_expr = try ExpressionRef.initBoolean(ctx, false);
    
    // Create a more complex logical expression: (a > b) || (true && false)
    const a_gt_b = try a.greaterThan(b);
    const true_and_false = try true_expr.logicalAnd(false_expr);
    const logical_expr = try a_gt_b.logicalOr(true_and_false);
    
    // Clear the output buffer
    output = std.ArrayList(u8).init(allocator);
    try symbolic.format(ctx, logical_expr.get(), output.writer());
    const logical_str = try output.toOwnedSlice();
    defer allocator.free(logical_str);
    
    std.debug.print("\nLogical expression: {s}\n", .{logical_str});
    
    // Evaluate the logical expression
    // With a=5, b=7, (a > b) is false, and (true && false) is false
    // So the result should be false
    const logical_result = try logical_expr.evaluate(&bindings.unmanaged);
    std.debug.print("Logical expression result: {}\n", .{logical_result != 0});
    
    std.debug.print("\nExpressionRef demonstration completed successfully!\n", .{});
}