//! Debug program to investigate the FFI issue with constant extraction

const std = @import("std");

/// FFI interface to the egg library
pub const c_api = @cImport({
    @cInclude("egg_ffi.h");
});

/// ID offset used for FFI conversions
/// This matches the ID_OFFSET defined in egg_ffi.h
pub const ID_OFFSET: usize = 1;

/// Main function to debug the FFI issue
pub fn main() !void {
    // Initialize allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create an EGraph
    const egraph_id = c_api.egg_create_egraph();
    if (egraph_id == 0) {
        std.debug.print("Failed to create EGraph\n", .{});
        return error.EGraphCreationFailed;
    }
    defer _ = c_api.egg_free_egraph(egraph_id);

    // Add constants 0 and 1
    const zero_id = c_api.egg_add_constant(egraph_id, 0);
    if (zero_id == 0) {
        std.debug.print("Failed to add constant 0\n", .{});
        return error.NodeCreationFailed;
    }
    std.debug.print("Added constant 0 with ID: {d}\n", .{zero_id});

    const one_id = c_api.egg_add_constant(egraph_id, 1);
    if (one_id == 0) {
        std.debug.print("Failed to add constant 1\n", .{});
        return error.NodeCreationFailed;
    }
    std.debug.print("Added constant 1 with ID: {d}\n", .{one_id});

    // Extract the constants
    const zero_str = c_api.egg_extract_best(egraph_id, zero_id);
    if (zero_str == null) {
        std.debug.print("Failed to extract constant 0\n", .{});
        return error.ExtractionFailed;
    }
    defer c_api.egg_free_string(zero_str);
    const zero_result = std.mem.span(zero_str);
    std.debug.print("Extracted constant 0: {s}\n", .{zero_result});

    const one_str = c_api.egg_extract_best(egraph_id, one_id);
    if (one_str == null) {
        std.debug.print("Failed to extract constant 1\n", .{});
        return error.ExtractionFailed;
    }
    defer c_api.egg_free_string(one_str);
    const one_result = std.mem.span(one_str);
    std.debug.print("Extracted constant 1: {s}\n", .{one_result});

    // Add a symbol
    const symbol_name = "x";
    const symbol_c_str = try allocator.dupeZ(u8, symbol_name);
    defer allocator.free(symbol_c_str);
    const x_id = c_api.egg_add_symbol(egraph_id, symbol_c_str.ptr);
    if (x_id == 0) {
        std.debug.print("Failed to add symbol x\n", .{});
        return error.NodeCreationFailed;
    }
    std.debug.print("Added symbol x with ID: {d}\n", .{x_id});

    // Extract the symbol
    const x_str = c_api.egg_extract_best(egraph_id, x_id);
    if (x_str == null) {
        std.debug.print("Failed to extract symbol x\n", .{});
        return error.ExtractionFailed;
    }
    defer c_api.egg_free_string(x_str);
    const x_result = std.mem.span(x_str);
    std.debug.print("Extracted symbol x: {s}\n", .{x_result});

    // Add binary operations
    const add_id = c_api.egg_add_binary_op(egraph_id, 0, x_id, zero_id); // x + 0
    if (add_id == 0) {
        std.debug.print("Failed to add binary op x + 0\n", .{});
        return error.NodeCreationFailed;
    }
    std.debug.print("Added binary op x + 0 with ID: {d}\n", .{add_id});

    // Extract the binary operation
    const add_str = c_api.egg_extract_best(egraph_id, add_id);
    if (add_str == null) {
        std.debug.print("Failed to extract binary op x + 0\n", .{});
        return error.ExtractionFailed;
    }
    defer c_api.egg_free_string(add_str);
    const add_result = std.mem.span(add_str);
    std.debug.print("Extracted binary op x + 0: {s}\n", .{add_result});

    // Run rules
    const result = c_api.egg_run_rules_with_mask(egraph_id, 10, 16); // Identity rules
    if (result == 0) {
        std.debug.print("Failed to run rules\n", .{});
        return error.RuleApplicationFailed;
    }
    std.debug.print("Rules applied successfully\n", .{});

    // Extract the binary operation again after rules
    const add_str2 = c_api.egg_extract_best(egraph_id, add_id);
    if (add_str2 == null) {
        std.debug.print("Failed to extract binary op x + 0 after rules\n", .{});
        return error.ExtractionFailed;
    }
    defer c_api.egg_free_string(add_str2);
    const add_result2 = std.mem.span(add_str2);
    std.debug.print("Extracted binary op x + 0 after rules: {s}\n", .{add_result2});
}
