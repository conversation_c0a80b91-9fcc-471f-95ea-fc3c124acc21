const std = @import("std");
const zing = @import("../src/zing.zig");

pub fn main() !void {
    // Initialize core
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    var ctx = try zing.Core.init(allocator);
    defer ctx.deinit();
    
    // Create test tensor
    const creation = @import("../src/tensor/creation.zig");
    const pointwise = @import("../src/tensor/pointwise.zig");
    
    // Create a test tensor with shape [2, 4]
    const a = try creation.constant(&ctx, @as(f32, 1.0));
    
    std.debug.print("Testing normalization functions\\n", .{});
    
    // Test layer_norm
    const normalized_shape = [_]usize{ 4 };
    const layer_normed = try pointwise.layer_norm(&ctx, a, &normalized_shape, 1e-5);
    std.debug.print("Layer norm created: {any}\\n", .{layer_normed});
    
    // Test RMS norm (need a weight tensor)
    const weight = try creation.constant(&ctx, @as(f32, 1.0));
    const rms_normed = try pointwise.rms_norm(&ctx, a, weight, 1e-5);
    std.debug.print("RMS norm created: {any}\\n", .{rms_normed});
    
    // Test mean norm
    const mean_normed = try pointwise.mean_norm(&ctx, a, 1e-5);
    std.debug.print("Mean norm created: {any}\\n", .{mean_normed});
    
    // Test std norm
    const std_normed = try pointwise.std_norm(&ctx, a, 0, 1e-5);
    std.debug.print("Std norm created: {any}\\n", .{std_normed});
    
    // Test softmax
    const softmaxed = try pointwise.softmax(&ctx, a, -1);
    std.debug.print("Softmax created: {any}\\n", .{softmaxed});
    
    // Test log_softmax
    const log_softmaxed = try pointwise.log_softmax(&ctx, a, -1);
    std.debug.print("Log softmax created: {any}\\n", .{log_softmaxed});
    
    std.debug.print("All normalization tests passed!\\n", .{});
}