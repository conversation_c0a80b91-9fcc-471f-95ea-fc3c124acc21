// Test just symbolic engine
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("=== Testing Symbolic Engine ===\n", .{});
    
    const core = try zing.Core.init(gpa.allocator());
    defer core.deinit();
    
    std.debug.print("Core initialized\n", .{});
    
    // Test integer expression first
    std.debug.print("Creating integer expression...\n", .{});
    const int_expr = try core.symbolic.newIntegerExpr(42);
    std.debug.print("Integer expr created: tag={}\n", .{int_expr.tag});
    
    // Now test symbol expression
    std.debug.print("Creating symbol expression...\n", .{});
    const sym_expr = try core.symbolic.newSymbolExpr("test");
    std.debug.print("Symbol expr created: tag={}\n", .{sym_expr.tag});
    
    std.debug.print("=== Test Complete ===\n", .{});
}