/// Shape System Enhancements Demo
/// 
/// This example demonstrates the enhanced shape system features:
/// - Dimension optimization (collapsing)
/// - Fake dimension tracking
/// - Expression generation for validity and memory mapping
const std = @import("std");
const core = @import("core");
const Core = core.Core;
const ViewDesc = core.shape.view_desc.ViewDesc;
const ShapeId = u32; 
const ViewId = u32;

pub fn main() !void {
    // Initialize allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Initialize core
    var ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const stdout = std.io.getStdOut().writer();
    
    try stdout.print("\n=== Shape System Enhancements Demo ===\n\n", .{});
    
    // 1. Dimension optimization
    try stdout.print("=== Dimension Optimization Demo ===\n\n", .{});
    try demonstrateDimensionOptimization(&ctx, stdout);
    
    // 2. Fake dimension tracking
    try stdout.print("\n=== Fake Dimension Tracking Demo ===\n\n", .{});
    try demonstrateFakeDimensions(&ctx, stdout);
    
    // 3. Expression generation
    try stdout.print("\n=== Expression Generation Demo ===\n\n", .{});
    try demonstrateExpressionGeneration(&ctx, stdout);
    
    try stdout.print("\n=== Demo Complete ===\n\n", .{});
}

/// Demonstrates dimension collapsing and optimization
fn demonstrateDimensionOptimization(ctx: *Core, writer: anytype) !void {
    // Create a shape with dimensions that can be optimized
    const dims = [_]core.shape.types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
        .{ .concrete = 4 },
        .{ .concrete = 5 },
    };
    
    const shape_id = try ctx.shape.newShape(&dims);
    
    try writer.print("Original shape: [2, 3, 4, 5]\n", .{});
    
    // Create a view with default strides (contiguous in memory)
    const view_id = try ctx.shape.newDefaultView(shape_id);
    const view = ctx.shape.getView(view_id);
    
    try writer.print("Original strides: ", .{});
    try printStrides(writer, view.strides);
    
    // Check which dimensions can be collapsed
    const can_collapse_0_1 = view.canCollapseDims(0, 1, &ctx.shape);
    const can_collapse_1_2 = view.canCollapseDims(1, 2, &ctx.shape);
    const can_collapse_2_3 = view.canCollapseDims(2, 3, &ctx.shape);
    
    try writer.print("Dimensions 0,1 can be collapsed: {}\n", .{can_collapse_0_1});
    try writer.print("Dimensions 1,2 can be collapsed: {}\n", .{can_collapse_1_2});
    try writer.print("Dimensions 2,3 can be collapsed: {}\n", .{can_collapse_2_3});
    
    // Collapse dimensions 1 and 2
    try writer.print("\nCollapsing dimensions 1 and 2...\n", .{});
    const collapsed_view_id = try ctx.shape.collapseDimensions(view_id, 1, 2);
    const collapsed_view = ctx.shape.getView(collapsed_view_id);
    const collapsed_shape = ctx.shape.getShape(collapsed_view.shape_id);
    
    try writer.print("New shape: ", .{});
    try printShape(writer, collapsed_shape);
    try writer.print("New strides: ", .{});
    try printStrides(writer, collapsed_view.strides);
    
    // Optimize the view (automatically collapse all possible dimensions)
    try writer.print("\nAutomatically optimizing the view...\n", .{});
    const optimized_view_id = try ctx.shape.optimizeView(view_id);
    const optimized_view = ctx.shape.getView(optimized_view_id);
    const optimized_shape = ctx.shape.getShape(optimized_view.shape_id);
    
    try writer.print("Optimized shape: ", .{});
    try printShape(writer, optimized_shape);
    try writer.print("Optimized strides: ", .{});
    try printStrides(writer, optimized_view.strides);
}

/// Demonstrates fake dimension tracking for broadcasting
fn demonstrateFakeDimensions(ctx: *Core, writer: anytype) !void {
    // Create a shape with a fake dimension (size 1 for broadcasting)
    const a_dims = [_]core.shape.types.Dim{
        .{ .concrete = 1 }, // Will be broadcasted
        .{ .concrete = 5 },
    };
    
    // Target shape
    const b_dims = [_]core.shape.types.Dim{
        .{ .concrete = 3 },
        .{ .concrete = 5 },
    };
    
    const shape_a_id = try ctx.shape.newShape(&a_dims);
    const shape_b_id = try ctx.shape.newShape(&b_dims);
    
    try writer.print("Shape A: [1, 5]\n", .{});
    try writer.print("Shape B: [3, 5]\n", .{});
    
    // Create views
    const view_a_id = try ctx.shape.newDefaultView(shape_a_id);
    const view_a = ctx.shape.getView(view_a_id);
    
    try writer.print("\nOriginal view A strides: ", .{});
    try printStrides(writer, view_a.strides);
    
    // Create a broadcast view from A to B's shape
    try writer.print("\nBroadcasting view A to shape B...\n", .{});
    const broadcast_view_id = try ctx.shape.newBroadcastView(view_a_id, shape_b_id);
    const broadcast_view = ctx.shape.getView(broadcast_view_id);
    const broadcast_shape = ctx.shape.getShape(broadcast_view.shape_id);
    
    try writer.print("Broadcast view shape: ", .{});
    try printShape(writer, broadcast_shape);
    try writer.print("Broadcast view strides: ", .{});
    try printStrides(writer, broadcast_view.strides);
    
    // Check fake dimensions
    try writer.print("\nFake dimension tracking:\n", .{});
    try writer.print("Dimension 0 is fake: {}\n", .{broadcast_view.isFakeDim(0)});
    try writer.print("Dimension 1 is fake: {}\n", .{broadcast_view.isFakeDim(1)});
    
    // Try to collapse dimensions
    const can_collapse = broadcast_view.canCollapseDims(0, 1, &ctx.shape);
    try writer.print("\nCan collapse dimensions 0 and 1: {}\n", .{can_collapse});
    try writer.print("Note: Fake dimensions cannot be collapsed with other dimensions\n", .{});
}

/// Demonstrates expression generation for validity and memory mapping
fn demonstrateExpressionGeneration(ctx: *Core, writer: anytype) !void {
    // Create a shape for demonstration
    const dims = [_]core.shape.types.Dim{
        .{ .concrete = 3 },
        .{ .concrete = 4 },
    };
    
    const shape_id = try ctx.shape.newShape(&dims);
    
    try writer.print("Shape: [3, 4]\n", .{});
    
    // Create a view with expressions for validity and index mapping
    try writer.print("\nCreating view with automatic expression generation...\n", .{});
    const view_id = try ctx.shape.createView(shape_id, &[_]i64{4, 1}, 0);
    const view = ctx.shape.getView(view_id);
    
    try writer.print("View strides: ", .{});
    try printStrides(writer, view.strides);
    try writer.print("Validity expression generated: {}\n", .{view.validity_expr != null});
    try writer.print("Index expression generated: {}\n", .{view.mask_expr != null});
    
    // Test expression evaluation
    try writer.print("\nEvaluating expressions with different indices:\n", .{});
    
    const test_indices = [_][3]usize{
        // i, j, expected_index
        .{0, 0, 0},
        .{1, 2, 6},
        .{2, 3, 11},
    };
    
    for (test_indices) |test_case| {
        const indices = [_]usize{test_case[0], test_case[1]};
        const expected_index = test_case[2];
        
        // Evaluate index expression to get memory location
        const idx = try ctx.shape.evaluateExpressionWithIndex(view.mask_expr.?, &indices);
        try writer.print("Indices [{}, {}] -> Memory Index {}\n", .{
            indices[0], indices[1], idx
        });
        
        // Verify the result matches our expectation
        if (idx == expected_index) {
            try writer.print("  ✓ Matches expected value ({})\n", .{expected_index});
        } else {
            try writer.print("  ✗ Does not match expected value ({})\n", .{expected_index});
        }
        
        // Evaluate validity expression
        const validity = try ctx.shape.evaluateExpressionWithIndex(view.validity_expr.?, &indices);
        try writer.print("  Validity: {}\n", .{validity});
    }
    
    // Try with invalid indices
    const invalid_indices = [_]usize{3, 4}; // Out of bounds
    const validity = try ctx.shape.evaluateExpressionWithIndex(view.validity_expr.?, &invalid_indices);
    try writer.print("\nInvalid indices [3, 4] -> Validity: {}\n", .{validity});
}

// Helper function to print shape dimensions
fn printShape(writer: anytype, shape: *const core.shape.types.Shape) !void {
    try writer.print("[", .{});
    for (shape.dims, 0..) |dim, i| {
        if (i > 0) try writer.print(", ", .{});
        switch (dim) {
            .concrete => |size| try writer.print("{}", .{size}),
            .symbolic => |expr| try writer.print("sym", .{}),
        }
    }
    try writer.print("]\n", .{});
}

// Helper function to print strides
fn printStrides(writer: anytype, strides: []const i64) !void {
    try writer.print("[", .{});
    for (strides, 0..) |stride, i| {
        if (i > 0) try writer.print(", ", .{});
        try writer.print("{}", .{stride});
    }
    try writer.print("]\n", .{});
}