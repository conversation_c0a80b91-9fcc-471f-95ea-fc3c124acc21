const std = @import("std");
// Import from the symbolic module that we defined in build.zig
const symbolic = @import("symbolic");
// Import the Expr type which is now re-exported in symbolic.zig
const Expr = symbolic.Expr;

pub fn main() !void {
    std.debug.print("=== Testing simplified FFI implementation ===\n\n", .{});

    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    try testBasicOperations(allocator);
    try testComprehensiveExpression(allocator);
}

fn testBasicOperations(allocator: std.mem.Allocator) !void {
    std.debug.print("Test: Basic operations\n", .{});

    // Create a simple expression: x + 0
    var expr = Expr.init(allocator);
    defer expr.deinit();

    const x = try expr.symbol("x");
    const zero = try expr.integer(0);
    const add_expr = try expr.add(x, zero);

    const expr_str = try expr.toString(allocator, add_expr);
    defer allocator.free(expr_str);
    std.debug.print("Expression: {s}\n", .{expr_str});

    // Simplify the expression
    var simplified = try expr.simplify(allocator);
    defer simplified.deinit();

    const simplified_str = try simplified.toString(allocator, @as(u32, @intCast(simplified.nodes.items.len - 1)));
    defer allocator.free(simplified_str);
    std.debug.print("Simplified: {s}\n", .{simplified_str});

    // Verify the simplification (should be just 'x')
    try std.testing.expectEqualStrings("x", simplified_str);
    std.debug.print("✓ Basic simplification works\n", .{});
    std.debug.print("\n", .{});
}

fn testComprehensiveExpression(allocator: std.mem.Allocator) !void {
    std.debug.print("Test: Comprehensive expression\n", .{});

    // Create a more complex expression: ((x + 0) * 1) + ((y - 0) / 1)
    var expr = Expr.init(allocator);
    defer expr.deinit();

    const x = try expr.symbol("x");
    const y = try expr.symbol("y");
    const zero = try expr.integer(0);
    const one = try expr.integer(1);

    // Build left part: (x + 0) * 1
    const x_plus_0 = try expr.add(x, zero);
    const left = try expr.mul(x_plus_0, one);

    // Build right part: (y - 0) / 1
    const y_minus_0 = try expr.sub(y, zero);
    const right = try expr.div(y_minus_0, one);

    // Combine: left + right
    const combined = try expr.add(left, right);

    const expr_str = try expr.toString(allocator, combined);
    defer allocator.free(expr_str);
    std.debug.print("Expression: {s}\n", .{expr_str});

    // Simplify the expression
    var simplified = try expr.simplify(allocator);
    defer simplified.deinit();

    const simplified_str = try simplified.toString(allocator, @as(u32, @intCast(simplified.nodes.items.len - 1)));
    defer allocator.free(simplified_str);
    std.debug.print("Simplified: {s}\n", .{simplified_str});

    // Expected result: (x + y) or similar form
    const expected_contains = "x";
    const expected_contains2 = "y";
    const contains = std.mem.indexOf(u8, simplified_str, expected_contains) != null and
                    std.mem.indexOf(u8, simplified_str, expected_contains2) != null;
    
    if (contains) {
        std.debug.print("✓ Complex simplification works\n", .{});
    } else {
        std.debug.print("✗ Complex simplification unexpected result\n", .{});
        std.debug.print("Expected to contain: {s}\n", .{expected_contains});
    }
    std.debug.print("\n", .{});
}