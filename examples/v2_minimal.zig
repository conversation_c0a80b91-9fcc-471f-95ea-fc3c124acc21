// Minimal V2 test
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("Starting minimal test...\n", .{});
    
    const core = try zing.Core.init(gpa.allocator());
    defer core.deinit();
    
    std.debug.print("Core initialized!\n", .{});
    
    // Test symbolic first
    std.debug.print("Creating symbol...\n", .{});
    const sym = try core.symbolic.newSymbolExpr("test_symbol");
    std.debug.print("Symbol created!\n", .{});
    
    // Just create the shape engine and test it
    const dims = [_]zing.Dim{
        .{ .concrete = 10 },
        .{ .symbolic = sym },
    };
    
    std.debug.print("Creating shape...\n", .{});
    const shape_id = try core.shape.newShape(&dims);
    
    std.debug.print("Shape created with id: {}\n", .{shape_id});
    
    const shape = core.shape.getShape(shape_id);
    std.debug.print("Shape has {} dimensions\n", .{shape.dims.len});
    
    std.debug.print("Test complete!\n", .{});
}