// Test expr type directly
const std = @import("std");
const zing = @import("zing");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("=== Testing Expr Type ===\n", .{});
    
    var arena = std.heap.ArenaAllocator.init(gpa.allocator());
    defer arena.deinit();
    
    std.debug.print("Arena created\n", .{});
    
    var pool = std.heap.MemoryPool(zing.Expr).init(arena.allocator());
    
    std.debug.print("Pool created\n", .{});
    
    const expr = try pool.create();
    std.debug.print("Expr created: {*}\n", .{expr});
    
    const name = try arena.allocator().dupe(u8, "test");
    std.debug.print("Name copied: {s}\n", .{name});
    
    expr.* = .{
        .tag = .symbol,
        .data = .{ .symbol = .{ .name = name } },
    };
    
    std.debug.print("Expr data set: tag={}, name={s}\n", .{expr.tag, expr.data.symbol.name});
    
    std.debug.print("=== Test Complete ===\n", .{});
}