const std = @import("std");
const core = @import("core");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    var zing_core = try core.Core.init(allocator);
    defer zing_core.deinit();
    
    std.debug.print("Testing Luminal parity operations...\n", .{});
    
    // Test excise view operation
    {
        std.debug.print("\nTest 1: Excise view operation\n", .{});
        
        // Create shape [10]
        const dim_10 = try zing_core.symbolic.newIntegerExpr(10);
        const shape_id = try zing_core.shape.newShape(&[_]*core.types.Expr{dim_10});
        const view_id = try zing_core.shape.newDefaultView(shape_id);
        
        // Apply excise: cut out 2 elements every 4 elements
        const excised_view = try zing_core.shape.newExciseView(view_id, 0, 2, 4);
        const excised_shape = zing_core.shape.getView(excised_view).shape_id;
        const excised_dims = zing_core.shape.getShape(excised_shape).dims;
        
        const excised_size = try zing_core.symbolic.evaluate(excised_dims[0], null);
        std.debug.print("  Original: 10 elements\n", .{});
        std.debug.print("  Excised: {} elements (expected 6)\n", .{excised_size});
        std.debug.print("  Result: {s}\n", .{if (excised_size == 6) "PASS" else "FAIL"});
    }
    
    // Test pool last dim operation
    {
        std.debug.print("\nTest 2: Pool last dim view operation\n", .{});
        
        // Create shape [5, 10]
        const dim_5 = try zing_core.symbolic.newIntegerExpr(5);
        const dim_10 = try zing_core.symbolic.newIntegerExpr(10);
        const shape_id = try zing_core.shape.newShape(&[_]*core.types.Expr{dim_5, dim_10});
        const view_id = try zing_core.shape.newDefaultView(shape_id);
        
        // Apply pool_last_dim with kernel=3, stride=2, dilation=0
        const pooled_view = try zing_core.shape.newPoolLastDimView(view_id, 3, 2, 0);
        const pooled_shape = zing_core.shape.getView(pooled_view).shape_id;
        const pooled_dims = zing_core.shape.getShape(pooled_shape).dims;
        
        std.debug.print("  Original shape: [5, 10]\n", .{});
        std.debug.print("  Pool params: kernel=3, stride=2, dilation=0\n", .{});
        
        const dim0 = try zing_core.symbolic.evaluate(pooled_dims[0], null);
        const dim1 = try zing_core.symbolic.evaluate(pooled_dims[1], null);
        const dim2 = try zing_core.symbolic.evaluate(pooled_dims[2], null);
        
        std.debug.print("  Pooled shape: [{}, {}, {}] (expected [5, 4, 3])\n", .{dim0, dim1, dim2});
        const pass = (dim0 == 5 and dim1 == 4 and dim2 == 3);
        std.debug.print("  Result: {s}\n", .{if (pass) "PASS" else "FAIL"});
    }
    
    // Test pool shape inference
    {
        std.debug.print("\nTest 3: Pool shape inference\n", .{});
        
        // Create shape [10, 20, 30]
        const dims = [_]*core.types.Expr{
            try zing_core.symbolic.newIntegerExpr(10),
            try zing_core.symbolic.newIntegerExpr(20),
            try zing_core.symbolic.newIntegerExpr(30),
        };
        const shape_id = try zing_core.shape.newShape(&dims);
        
        // Infer pool shape on axis 1 with kernel=4, stride=2, dilation=1
        const pooled_shape_id = try zing_core.shape.inferPoolShape(shape_id, 4, 2, 1, 1);
        const pooled_dims = zing_core.shape.getShape(pooled_shape_id).dims;
        
        std.debug.print("  Original shape: [10, 20, 30]\n", .{});
        std.debug.print("  Pool params: kernel=4, stride=2, dilation=1, axis=1\n", .{});
        std.debug.print("  Effective kernel: 4 + 3*1 = 7\n", .{});
        std.debug.print("  Num windows: (20 - 7) / 2 + 1 = 7\n", .{});
        
        const dim0 = try zing_core.symbolic.evaluate(pooled_dims[0], null);
        const dim1 = try zing_core.symbolic.evaluate(pooled_dims[1], null);
        const dim2 = try zing_core.symbolic.evaluate(pooled_dims[2], null);
        const dim3 = try zing_core.symbolic.evaluate(pooled_dims[3], null);
        
        std.debug.print("  Inferred shape: [{}, {}, {}, {}] (expected [10, 7, 4, 30])\n", .{dim0, dim1, dim2, dim3});
        const pass = (dim0 == 10 and dim1 == 7 and dim2 == 4 and dim3 == 30);
        std.debug.print("  Result: {s}\n", .{if (pass) "PASS" else "FAIL"});
    }
    
    // Test gather shape inference
    {
        std.debug.print("\nTest 4: Gather shape inference\n", .{});
        
        // Create input shape [5, 10, 15]
        const input_dims = [_]*core.types.Expr{
            try zing_core.symbolic.newIntegerExpr(5),
            try zing_core.symbolic.newIntegerExpr(10),
            try zing_core.symbolic.newIntegerExpr(15),
        };
        const input_shape = try zing_core.shape.newShape(&input_dims);
        
        // Create indices shape [3, 7]
        const indices_dims = [_]*core.types.Expr{
            try zing_core.symbolic.newIntegerExpr(3),
            try zing_core.symbolic.newIntegerExpr(7),
        };
        const indices_shape = try zing_core.shape.newShape(&indices_dims);
        
        // Gather along axis 1
        const gathered_shape_id = try zing_core.shape.inferGatherShape(input_shape, indices_shape, 1);
        const gathered_dims = zing_core.shape.getShape(gathered_shape_id).dims;
        
        std.debug.print("  Input shape: [5, 10, 15]\n", .{});
        std.debug.print("  Indices shape: [3, 7]\n", .{});
        std.debug.print("  Gather axis: 1\n", .{});
        
        const dim0 = try zing_core.symbolic.evaluate(gathered_dims[0], null);
        const dim1 = try zing_core.symbolic.evaluate(gathered_dims[1], null);
        const dim2 = try zing_core.symbolic.evaluate(gathered_dims[2], null);
        const dim3 = try zing_core.symbolic.evaluate(gathered_dims[3], null);
        
        std.debug.print("  Gathered shape: [{}, {}, {}, {}] (expected [5, 3, 7, 15])\n", .{dim0, dim1, dim2, dim3});
        const pass = (dim0 == 5 and dim1 == 3 and dim2 == 7 and dim3 == 15);
        std.debug.print("  Result: {s}\n", .{if (pass) "PASS" else "FAIL"});
    }
    
    std.debug.print("\nAll tests completed!\n", .{});
}