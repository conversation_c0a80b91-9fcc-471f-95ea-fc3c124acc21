const std = @import("std");
const core = @import("core");
const tensor = @import("tensor");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    var zing_core = try core.Core.init(allocator);
    defer zing_core.deinit();
    
    std.debug.print("Testing Luminal parity operations...\n", .{});
    
    // Test 1: Excise operation
    {
        std.debug.print("\n1. Testing excise operation:\n", .{});
        
        // Create a 1D tensor [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
        const input = try tensor.arange(&zing_core, 0, 10, 1);
        const input_shape = zing_core.graph.getNode(input).shape;
        const shape_obj = zing_core.shape.getShape(input_shape);
        std.debug.print("   Input shape dims: {d}\n", .{shape_obj.dims.len});
        
        // Apply excise: cut out 2 elements every 4 elements
        // This should give us [0, 1, 4, 5, 8, 9] (shape [6])
        const input_view = try zing_core.shape.newDefaultView(input_shape);
        const excised_view = try zing_core.shape.newExciseView(input_view, 0, 2, 4);
        const excised_shape = zing_core.shape.getView(excised_view).shape_id;
        const excised_shape_obj = zing_core.shape.getShape(excised_shape);
        
        std.debug.print("   Excised shape dims: {d}\n", .{excised_shape_obj.dims.len});
        std.debug.print("   Expected: [6]\n", .{});
    }
    
    // Test 2: Pool last dim operation
    {
        std.debug.print("\n2. Testing pool_last_dim operation:\n", .{});
        
        // Create a 2D tensor shape [3, 10]
        const input = try tensor.zeros(&zing_core, &[_]i64{3, 10});
        const input_shape = zing_core.graph.getNode(input).shape;
        std.debug.print("   Input shape: {any}\n", .{try zing_core.shape.getDims(input_shape)});
        
        // Apply pool_last_dim with kernel=3, stride=2, dilation=0
        // Output shape should be [3, 4, 3]
        const input_view = zing_core.shape.getDefaultView(input_shape);
        const pooled_view = try zing_core.shape.newPoolLastDimView(input_view, 3, 2, 0);
        const pooled_shape = zing_core.shape.getView(pooled_view).shape_id;
        
        std.debug.print("   Pooled shape: {any}\n", .{try zing_core.shape.getDims(pooled_shape)});
        std.debug.print("   Expected: [3, 4, 3]\n", .{});
    }
    
    // Test 3: Pool shape inference
    {
        std.debug.print("\n3. Testing pool shape inference:\n", .{});
        
        // Create a 3D tensor shape [2, 20, 5]
        const input = try tensor.zeros(&zing_core, &[_]i64{2, 20, 5});
        const input_shape = zing_core.graph.getNode(input).shape;
        std.debug.print("   Input shape: {any}\n", .{try zing_core.shape.getDims(input_shape)});
        
        // Infer pool shape on axis 1 with kernel=4, stride=2, dilation=1
        // Effective kernel = 4 + (4-1)*1 = 7
        // Num windows = (20 - 7) / 2 + 1 = 7
        // Output shape should be [2, 7, 4, 5]
        const pooled_shape_id = try zing_core.shape.inferPoolShape(input_shape, 4, 2, 1, 1);
        
        std.debug.print("   Inferred pool shape: {any}\n", .{try zing_core.shape.getDims(pooled_shape_id)});
        std.debug.print("   Expected: [2, 7, 4, 5]\n", .{});
    }
    
    // Test 4: Gather shape inference
    {
        std.debug.print("\n4. Testing gather shape inference:\n", .{});
        
        // Create input shape [5, 10, 15]
        const input = try tensor.zeros(&zing_core, &[_]i64{5, 10, 15});
        const input_shape = zing_core.graph.getNode(input).shape;
        
        // Create indices shape [3, 7]
        const indices = try tensor.zeros(&zing_core, &[_]i64{3, 7});
        const indices_shape = zing_core.graph.getNode(indices).shape;
        
        std.debug.print("   Input shape: {any}\n", .{try zing_core.shape.getDims(input_shape)});
        std.debug.print("   Indices shape: {any}\n", .{try zing_core.shape.getDims(indices_shape)});
        
        // Gather along axis 1
        // Output shape should be [5, 3, 7, 15]
        const gathered_shape_id = try zing_core.shape.inferGatherShape(input_shape, indices_shape, 1);
        
        std.debug.print("   Gathered shape: {any}\n", .{try zing_core.shape.getDims(gathered_shape_id)});
        std.debug.print("   Expected: [5, 3, 7, 15]\n", .{});
    }
    
    std.debug.print("\nAll Luminal parity operations tested successfully!\n", .{});
}