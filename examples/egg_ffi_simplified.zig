//! Simplified Egg FFI Interface
//!
//! This file implements a more streamlined approach to using the egg library
//! for expression simplification via FFI. The key improvements are:
//! 
//! 1. Single-call simplification - build the expression in Zig, then make a single FFI call
//! 2. Simplified memory management - clear ownership boundaries between Zig and Rust
//! 3. Reduced abstraction layers - no need to track node IDs across multiple calls
//! 4. Consistent serialization format - expressions are represented as simple arrays of nodes
//!
//! Compile with: `zig build-exe egg_ffi_simplified.zig -lc -legg_ffi`

const std = @import("std");

/// Node tag for different operation types
pub const NodeTag = enum(u8) {
    Integer = 0,
    Symbol = 1,
    Add = 2,
    Sub = 3,
    Mul = 4,
    Div = 5,
    Mod = 6,
    Pow = 7,
    Min = 8,
    Max = 9,
    Eq = 10,
    Ne = 11,
    Lt = 12,
    Le = 13,
    _,
};

/// Invalid index constant used for leaf nodes
pub const INVALID_IDX: u32 = 0xFFFFFFFF;

/// FFI node structure for passing expressions to and from Rust
pub const FFINode = extern struct {
    tag: u8,
    value: i64,
    left: u32,
    right: u32,
};

/// FFI symbol table entry structure
pub const FFISymbol = extern struct {
    id: u32,
    name_ptr: [*:0]const u8,
    name_len: usize,
};

/// FFI function declarations
extern fn egg_simplify(
    nodes: [*]const FFINode,
    node_count: usize,
    symbols: [*]const FFISymbol,
    symbol_count: usize,
    result_ptr: *[*]FFINode,
    result_len: *usize,
) bool;

extern fn egg_free_result(
    ptr: [*]FFINode, 
    len: usize
) void;

// Error type for expression operations
pub const ExprError = error{
    OutOfMemory,
    InvalidExpression,
    FFIError,
    SymbolNotFound,
};

/// Expression builder for constructing symbolic expressions
pub const Expr = struct {
    /// The allocator used for memory management
    allocator: std.mem.Allocator,
    
    /// The nodes in the expression
    nodes: std.ArrayList(FFINode),
    
    /// The symbol table
    symbols: std.ArrayList(FFISymbol),
    
    /// Map from symbol names to symbol IDs
    symbol_map: std.StringHashMap(u32),
    
    /// Initialize a new expression builder
    pub fn init(allocator: std.mem.Allocator) Expr {
        return .{
            .allocator = allocator,
            .nodes = std.ArrayList(FFINode).init(allocator),
            .symbols = std.ArrayList(FFISymbol).init(allocator),
            .symbol_map = std.StringHashMap(u32).init(allocator),
        };
    }
    
    /// Free all resources used by the expression builder
    pub fn deinit(self: *Expr) void {
        self.nodes.deinit();
        
        // Free any symbol strings
        for (self.symbols.items) |sym| {
            // Safely free the string without using sentinel slice
            const ptr = @as([*]const u8, @ptrCast(sym.name_ptr));
            self.allocator.free(ptr[0..sym.name_len]);
        }
        
        self.symbols.deinit();
        self.symbol_map.deinit();
    }
    
    /// Add an integer node to the expression
    pub fn integer(self: *Expr, value: i64) !u32 {
        const idx = @as(u32, @intCast(self.nodes.items.len));
        
        try self.nodes.append(.{
            .tag = @intFromEnum(NodeTag.Integer),
            .value = value,
            .left = INVALID_IDX,
            .right = INVALID_IDX,
        });
        
        return idx;
    }
    
    /// Add a symbol node to the expression
    pub fn symbol(self: *Expr, name: []const u8) !u32 {
        // Check if we've already seen this symbol
        if (self.symbol_map.get(name)) |id| {
            // We already have this symbol, reuse the ID
            const idx = @as(u32, @intCast(self.nodes.items.len));
            
            try self.nodes.append(.{
                .tag = @intFromEnum(NodeTag.Symbol),
                .value = @as(i64, @intCast(id)),
                .left = INVALID_IDX,
                .right = INVALID_IDX,
            });
            
            return idx;
        }
        
        // Create a new symbol
        const symbol_id = @as(u32, @intCast(self.symbols.items.len));
        
        // Allocate a null-terminated string for the symbol name
        const symbol_name = try self.allocator.dupeZ(u8, name);
        
        // Add to the symbol table
        try self.symbols.append(.{
            .id = symbol_id,
            .name_ptr = symbol_name,
            .name_len = name.len,
        });
        
        // Add to the symbol map
        try self.symbol_map.put(name, symbol_id);
        
        // Create the node
        const idx = @as(u32, @intCast(self.nodes.items.len));
        
        try self.nodes.append(.{
            .tag = @intFromEnum(NodeTag.Symbol),
            .value = @as(i64, @intCast(symbol_id)),
            .left = INVALID_IDX,
            .right = INVALID_IDX,
        });
        
        return idx;
    }
    
    /// Add a binary operation node for addition
    pub fn add(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Add, left, right);
    }
    
    /// Add a binary operation node for subtraction
    pub fn sub(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Sub, left, right);
    }
    
    /// Add a binary operation node for multiplication
    pub fn mul(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Mul, left, right);
    }
    
    /// Add a binary operation node for division
    pub fn div(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Div, left, right);
    }
    
    /// Add a binary operation node for modulo
    pub fn mod(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Mod, left, right);
    }
    
    /// Add a binary operation node for exponentiation
    pub fn pow(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Pow, left, right);
    }
    
    /// Add a binary operation node for minimum
    pub fn min(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Min, left, right);
    }
    
    /// Add a binary operation node for maximum
    pub fn max(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Max, left, right);
    }
    
    /// Add a binary operation node for equality comparison
    pub fn eq(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Eq, left, right);
    }
    
    /// Add a binary operation node for inequality comparison
    pub fn ne(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Ne, left, right);
    }
    
    /// Add a binary operation node for less than comparison
    pub fn lt(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Lt, left, right);
    }
    
    /// Add a binary operation node for less than or equal comparison
    pub fn le(self: *Expr, left: u32, right: u32) !u32 {
        return self.binaryOp(NodeTag.Le, left, right);
    }
    
    /// Helper function to add any binary operation
    fn binaryOp(self: *Expr, op: NodeTag, left: u32, right: u32) !u32 {
        // Validate indices
        if (left >= self.nodes.items.len) {
            return ExprError.InvalidExpression;
        }
        
        if (right >= self.nodes.items.len) {
            return ExprError.InvalidExpression;
        }
        
        // Create the node
        const idx = @as(u32, @intCast(self.nodes.items.len));
        
        try self.nodes.append(.{
            .tag = @intFromEnum(op),
            .value = 0, // Not used for binary operations
            .left = left,
            .right = right,
        });
        
        return idx;
    }
    
    /// Return a string representation of the expression
    pub fn toString(self: *const Expr, allocator: std.mem.Allocator, root: u32) ![]const u8 {
        if (root >= self.nodes.items.len) {
            return ExprError.InvalidExpression;
        }
        
        // For complex expressions, we need to build a string recursively
        var result = std.ArrayList(u8).init(allocator);
        defer result.deinit();
        
        try self.appendNodeString(&result, root);
        
        return result.toOwnedSlice();
    }
    
    /// Helper function to recursively build a string representation of a node
    fn appendNodeString(self: *const Expr, result: *std.ArrayList(u8), idx: u32) !void {
        const node = self.nodes.items[idx];
        
        // Debug info for tracing
        std.debug.print("[DEBUG] appendNodeString: idx={}, tag={}, value={}\n", 
            .{idx, node.tag, node.value});
        
        switch (@as(NodeTag, @enumFromInt(node.tag))) {
            .Integer => {
                try result.writer().print("{d}", .{node.value});
            },
            .Symbol => {
                const sym_id = @as(u32, @intCast(node.value));
                std.debug.print("[DEBUG] Symbol node: sym_id={}, symbols.len={}\n", 
                    .{sym_id, self.symbols.items.len});
                    
                if (sym_id >= self.symbols.items.len) {
                    std.debug.print("[ERROR] Symbol ID out of range: {}\n", .{sym_id});
                    return ExprError.SymbolNotFound;
                }
                const sym = self.symbols.items[sym_id];
                std.debug.print("[DEBUG] Found symbol: id={}, name_len={}\n", 
                    .{sym.id, sym.name_len});
                    
                // Access the symbol name safely without using sentinel slice
                const name_ptr = @as([*]const u8, @ptrCast(sym.name_ptr));
                try result.appendSlice(name_ptr[0..sym.name_len]);
            },
            .Add, .Sub, .Mul, .Div, .Mod, .Pow, .Min, .Max, .Eq, .Ne, .Lt, .Le => {
                try result.append('(');
                try self.appendNodeString(result, node.left);
                
                switch (@as(NodeTag, @enumFromInt(node.tag))) {
                    .Add => try result.appendSlice(" + "),
                    .Sub => try result.appendSlice(" - "),
                    .Mul => try result.appendSlice(" * "),
                    .Div => try result.appendSlice(" / "),
                    .Mod => try result.appendSlice(" % "),
                    .Pow => try result.appendSlice(" ^ "),
                    .Min => try result.appendSlice(" min "),
                    .Max => try result.appendSlice(" max "),
                    .Eq => try result.appendSlice(" == "),
                    .Ne => try result.appendSlice(" != "),
                    .Lt => try result.appendSlice(" < "),
                    .Le => try result.appendSlice(" <= "),
                    else => unreachable,
                }
                
                try self.appendNodeString(result, node.right);
                try result.append(')');
            },
            else => {
                try result.writer().print("(unknown_op_{d})", .{node.tag});
            },
        }
    }
    
    /// Simplify the expression using the egg library
    pub fn simplify(self: *const Expr, allocator: std.mem.Allocator) !Expr {
        // Create a result pointer
        var result_ptr: [*]FFINode = undefined;
        var result_len: usize = 0;
        
        // Print debug info
        std.debug.print("[DEBUG] simplify: nodes.len={}, symbols.len={}\n", 
            .{self.nodes.items.len, self.symbols.items.len});
        
        for (self.symbols.items, 0..) |sym, i| {
            const name_ptr = @as([*]const u8, @ptrCast(sym.name_ptr));
            const name = name_ptr[0..sym.name_len];
            std.debug.print("[DEBUG] Symbol[{}]: id={}, name={s}\n", .{i, sym.id, name});
        }
        
        // Call the FFI function
        const success = egg_simplify(
            self.nodes.items.ptr,
            self.nodes.items.len,
            self.symbols.items.ptr,
            self.symbols.items.len,
            &result_ptr,
            &result_len,
        );
        
        if (!success) {
            return ExprError.FFIError;
        }
        
        // Create a new expression
        var result = Expr.init(allocator);
        errdefer result.deinit();
        
        // Get the result slice
        const result_slice = result_ptr[0..result_len];
        std.debug.print("[DEBUG] Raw Result: len={}\n", .{result_len});
        
        // Process the result nodes - fix any symbol node values
        // to make sure they point to valid symbols
        var fixed_nodes = std.ArrayList(FFINode).init(allocator);
        defer fixed_nodes.deinit();
        
        // Map to track original symbol IDs to our symbol IDs
        var symbol_id_map = std.AutoHashMap(i64, u32).init(allocator);
        defer symbol_id_map.deinit();
        
        // Keep track of symbol count to map different symbols to different IDs
        var symbol_count: u32 = 0;
        
        // Collect all symbol nodes and build a mapping
        for (result_slice) |node| {
            if (@as(NodeTag, @enumFromInt(node.tag)) == .Symbol) {
                const sym_id = node.value;
                std.debug.print("[DEBUG] Found symbol in result: id={}\n", .{sym_id});
                // Only add to map if not already there
                if (!symbol_id_map.contains(sym_id)) {
                    // Add this as a new symbol, with incrementing IDs
                    // This ensures different symbols get different IDs
                    if (symbol_count < self.symbols.items.len) {
                        try symbol_id_map.put(sym_id, symbol_count);
                        std.debug.print("[DEBUG] Adding symbol mapping: {}=>{}\n", 
                            .{sym_id, symbol_count});
                        symbol_count += 1;
                    } else {
                        // If we run out of symbols in the original, use the first one
                        try symbol_id_map.put(sym_id, 0);
                        std.debug.print("[DEBUG] Too many symbols, mapping {}=>0\n", .{sym_id});
                    }
                }
            }
        }
        
        // Copy and fix the nodes
        for (result_slice) |node| {
            var fixed_node = node;
            
            // Fix symbol nodes
            if (@as(NodeTag, @enumFromInt(node.tag)) == .Symbol) {
                if (symbol_id_map.get(node.value)) |mapped_id| {
                    fixed_node.value = @as(i64, @intCast(mapped_id));
                    std.debug.print("[DEBUG] Mapped symbol ID: {}->{}  \n", .{node.value, mapped_id});
                } else {
                    std.debug.print("[ERROR] Could not map symbol ID: {}\n", .{node.value});
                    fixed_node.value = 0; // Default to first symbol if not found
                }
            }
            
            try fixed_nodes.append(fixed_node);
        }
        
        // Use the fixed nodes
        try result.nodes.appendSlice(fixed_nodes.items);
        
        // Deep copy the symbol table with proper string duplication
        for (self.symbols.items) |sym| {
            // Create a copy of the symbol name (safely without relying on sentinel value)
            const name_ptr = @as([*]const u8, @ptrCast(sym.name_ptr));
            const name = name_ptr[0..sym.name_len];
            
            // Create a new null-terminated string copy
            const symbol_name = try allocator.dupeZ(u8, name);
            
            // Add to the symbol table
            try result.symbols.append(.{
                .id = sym.id,
                .name_ptr = symbol_name,
                .name_len = sym.name_len,
            });
            
            // Add to the symbol map - use symbol name as key
            try result.symbol_map.put(name, sym.id);
        }
        
        // Free the result from the FFI call
        egg_free_result(result_ptr, result_len);
        
        // Print debug info for the result
        std.debug.print("[DEBUG] Result: nodes.len={}, symbols.len={}\n", 
            .{result.nodes.items.len, result.symbols.items.len});
            
        for (result.nodes.items, 0..) |node, i| {
            std.debug.print("[DEBUG] ResultNode[{}]: tag={}, value={}\n", 
                .{i, node.tag, node.value});
        }
        
        for (result.symbols.items, 0..) |sym, i| {
            const name_ptr = @as([*]const u8, @ptrCast(sym.name_ptr));
            const name = name_ptr[0..sym.name_len];
            std.debug.print("[DEBUG] ResultSymbol[{}]: id={}, name={s}\n", .{i, sym.id, name});
        }
        
        return result;
    }
};

/// Direct simplification function that uses the Egg library
/// This replaces the old engine-based approach with a simpler direct function call
pub fn simplifyWithEgg(allocator: std.mem.Allocator, expr: Expr) !Expr {
    // Use the egg backend directly
    return expr.simplify(allocator);
}

/// Native simplification function placeholder
/// In the full implementation, this would contain a Zig-native implementation
pub fn simplify(allocator: std.mem.Allocator, expr: Expr) !Expr {
    // Native implementation not yet available in this example
    // Fall back to egg implementation
    return simplifyWithEgg(allocator, expr);
}

// Test the simplified FFI interface with a simple example
pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Create an expression: (x + 0) * 1, which should simplify to x
    var expr = Expr.init(allocator);
    defer expr.deinit();
    
    // Add nodes for the expression
    const x = try expr.symbol("x");
    const zero = try expr.integer(0);
    const one = try expr.integer(1);
    
    const x_plus_0 = try expr.add(x, zero);
    const result = try expr.mul(x_plus_0, one);
    
    // Print the original expression
    const expr_str = try expr.toString(allocator, result);
    defer allocator.free(expr_str);
    
    std.debug.print("Original expression: {s}\n", .{expr_str});
    
    // Simplify the expression using the direct function
    var simplified = try simplifyWithEgg(allocator, expr);
    defer simplified.deinit();
    
    // Get the root node (the result of simplification)
    const root = @as(u32, 0); // In simplified expr, root should be at index 0
    
    // Print the simplified expression
    const simplified_str = try simplified.toString(allocator, root);
    defer allocator.free(simplified_str);
    
    std.debug.print("Simplified expression: {s}\n", .{simplified_str});
}