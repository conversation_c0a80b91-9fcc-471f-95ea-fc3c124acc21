// Test simplifier directly
const std = @import("std");
const zing = @import("zing");
const Simplifier = @import("zing").core.symbolic.simplify.Simplifier;

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("=== Testing Simplifier ===\n", .{});
    
    var arena = std.heap.ArenaAllocator.init(gpa.allocator());
    defer arena.deinit();
    
    std.debug.print("Creating simplifier...\n", .{});
    var simplifier = try Simplifier.init(arena.allocator());
    defer simplifier.deinit();
    
    std.debug.print("Creating expr pool...\n", .{});
    var expr_pool = std.heap.MemoryPool(zing.Expr).init(arena.allocator());
    
    std.debug.print("Setting expr pool on simplifier...\n", .{});
    simplifier.setExprPool(&expr_pool);
    
    std.debug.print("Creating test expression...\n", .{});
    const expr = try expr_pool.create();
    expr.* = .{
        .tag = .integer,
        .data = .{ .integer = 42 },
    };
    
    std.debug.print("Simplifying expression...\n", .{});
    const result = try simplifier.simplify(expr);
    
    std.debug.print("Result: tag={}, value={}\n", .{result.tag, result.data.integer});
    std.debug.print("=== Test Complete ===\n", .{});
}