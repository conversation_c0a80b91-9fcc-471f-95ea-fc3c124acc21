const std = @import("std");
const symbolic = @import("../src/symbolic.zig");
const contextMod = @import("../src/context.zig");
const egraph = @import("../src/symbolic/egraph.zig");

pub fn main() !void {
    // Initialize an allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create a context
    var ctx = try contextMod.init(allocator);
    defer contextMod.deinit(ctx);

    // Create a simple expression: x + 0
    const x = try contextMod.symbol(ctx, "x");
    const zero = try contextMod.integer(ctx, 0);
    const expr = try contextMod.add(ctx, x, zero);

    std.debug.print("Testing FFI expression conversion and simplification\n", .{});
    std.debug.print("Original expression: ", .{});
    try contextMod.formatToWriter(std.io.getStdOut().writer(), ctx, expr);
    std.debug.print("\n", .{});

    // Convert to FFI
    std.debug.print("Converting to FFI...\n", .{});
    var ffi_expr = try egraph.convertToFFI(ctx, expr, allocator);
    defer {
        ffi_expr.deinit();
        allocator.destroy(ffi_expr);
    }

    // Print the FFI expression
    std.debug.print("FFI expression has {} nodes and {} symbols\n", 
        .{ffi_expr.nodes.items.len, ffi_expr.symbols.items.len});

    // Simplify the FFI expression
    std.debug.print("Simplifying FFI expression...\n", .{});
    var simplified = try ffi_expr.simplify(allocator);
    defer simplified.deinit();

    // Print simplified FFI expression
    std.debug.print("Simplified FFI expression has {} nodes and {} symbols\n", 
        .{simplified.nodes.items.len, simplified.symbols.items.len});

    // Convert back to SymExpr
    std.debug.print("Converting back to SymExpr...\n", .{});
    const sym_expr = try egraph.convertFromFFI(ctx, &simplified);

    // Print the result
    std.debug.print("Simplified expression: ", .{});
    try contextMod.formatToWriter(std.io.getStdOut().writer(), ctx, sym_expr);
    std.debug.print("\n", .{});

    // Check if the simplification worked correctly (should be just 'x')
    std.debug.print("Is equal to 'x': {}\n", .{contextMod.nodesEqual(ctx, sym_expr, x)});

    std.debug.print("\nDirect simplification test\n", .{});
    // Try direct simplification with egg
    const direct_result = try egraph.simplifyWithEgg(ctx, expr);
    std.debug.print("Direct simplification result: ", .{});
    try contextMod.formatToWriter(std.io.getStdOut().writer(), ctx, direct_result);
    std.debug.print("\n", .{});
    std.debug.print("Is equal to 'x': {}\n", .{contextMod.nodesEqual(ctx, direct_result, x)});
}