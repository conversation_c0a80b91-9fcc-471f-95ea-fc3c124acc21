const std = @import("std");
const symbolic = @import("symbolic");
const shape = @import("shape");

pub fn main() !void {
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    // Create a symbolic context
    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);

    // Enable debug mode to see more output
    ctx.setDebugMode(true);

    // Create a shape tracker with a symbolic dimension
    var tracker = try shape.ShapeTracker.init(ctx, allocator, &[_]shape.Dim{
        shape.Dim{ .Symbolic = try symbolic.symbol(ctx, "batch_size") },
        shape.Dim{ .Concrete = 28 },
        shape.Dim{ .Concrete = 28 },
    });
    defer tracker.deinit();

    // Print initial shape
    std.debug.print("Initial shape: ", .{});
    printDims(tracker.dims());
    
    // Calculate total elements (this will use simplifyNative)
    std.debug.print("\nCalculating total elements...\n", .{});
    const total = try shape.Symbolic.numElements(&tracker);
    std.debug.print("Total elements expression: {any}\n", .{total});
    
    // Create a evaluatable version
    var map = std.StringHashMap(i64).init(allocator);
    defer map.deinit();
    try map.put("batch_size", 32);
    
    const value = try symbolic.eval(ctx, total, &map);
    std.debug.print("Evaluated with batch_size=32: {}\n", .{value});
    
    std.debug.print("\nTest completed successfully!\n", .{});
}

fn printDims(dims: []const shape.Dim) void {
    std.debug.print("[", .{});
    for (dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(", ", .{});
        switch (dim) {
            .Concrete => |size| std.debug.print("{}", .{size}),
            .Symbolic => std.debug.print("sym", .{}),
        }
    }
    std.debug.print("]\n", .{});
}