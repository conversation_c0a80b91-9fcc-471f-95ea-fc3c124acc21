const std = @import("std");
const zing = @import("../src/zing.zig");
const TensorLS = @import("../src/tensor/tensor_luminal_style.zig").TensorLS;

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();
    
    // Initialize core
    var ctx = try zing.Core.init(allocator);
    defer ctx.deinit();
    
    // Create an input tensor
    const shape_id = try ctx.shape.newShape(&[_]zing.types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
        .{ .concrete = 4 },
    });
    const view_id = try ctx.shape.newDefaultView(shape_id);
    const input_node = try ctx.graph.newNodeInput(view_id, .f32);
    
    // Create a Luminal-style tensor
    var tensor = try TensorLS.fromNode(&ctx, input_node);
    
    std.debug.print("Initial graph nodes: {}\\n", .{ctx.graph.nodes.items.len});
    std.debug.print("Initial views: {}\\n", .{ctx.shape.views.count()});
    
    // Shape operations - these DON'T create graph nodes!
    
    // Reshape to [6, 4]
    const reshaped = try tensor.reshape(&[_]i64{6, 4});
    std.debug.print("After reshape - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // Permute dimensions [1, 0]
    const permuted = try reshaped.permute(&[_]u32{1, 0});
    std.debug.print("After permute - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // Slice [:, 0:2]
    const sliced = try permuted.slice(&[_]zing.types.Range{
        .{ .start = null, .end = null },
        .{ .start = 0, .end = 2 },
    });
    std.debug.print("After slice - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // Check if contiguous
    std.debug.print("Is sliced tensor contiguous? {}\\n", .{sliced.isContiguous()});
    
    // Force contiguous - this DOES create a graph node
    const contiguous = try sliced.contiguous();
    std.debug.print("After contiguous - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // Computational operations - these create graph nodes
    
    // Create another tensor
    const other_shape_id = try ctx.shape.newShape(&[_]zing.types.Dim{
        .{ .concrete = 4 },
        .{ .concrete = 2 },
    });
    const other_view_id = try ctx.shape.newDefaultView(other_shape_id);
    const other_node = try ctx.graph.newNodeConstant(other_view_id);
    var other_tensor = try TensorLS.fromNode(&ctx, other_node);
    
    // Add tensors
    const result = try contiguous.add(other_tensor);
    std.debug.print("After add - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // Multiply
    const mul_result = try result.multiply(other_tensor);
    std.debug.print("After multiply - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // High-level operation (decomposed)
    const neg_result = try mul_result.neg();
    std.debug.print("After neg (high-level) - nodes: {}, views: {}\\n", .{
        ctx.graph.nodes.items.len,
        ctx.shape.views.count(),
    });
    
    // Print final graph structure
    std.debug.print("\\nFinal graph structure:\\n", .{});
    for (ctx.graph.nodes.items) |node| {
        std.debug.print("  Node {}: {} operation\\n", .{ node.id, node.op });
    }
    
    // Expected output:
    // - Multiple view increments for shape ops
    // - Graph nodes only for: input, constant, contiguous, add, multiply, and decomposed neg
    // - Shape operations are "free" - no graph complexity
}