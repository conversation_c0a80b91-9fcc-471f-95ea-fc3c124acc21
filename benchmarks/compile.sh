#!/bin/bash
# Simple script to compile benchmark files with the correct libraries
# Usage: ./compile.sh [filename.zig] [optimization]
# Example: ./compile.sh egg_ffi_batch_vs_nonbatch.zig ReleaseFast

# Default values
FILENAME=${1:-egg_ffi_batch_vs_nonbatch.zig}
OPTIMIZATION=${2:-ReleaseFast}

# Extract the base name without extension
BASENAME=$(basename "$FILENAME" .zig)

echo "Compiling $FILENAME with optimization level $OPTIMIZATION..."

# Compile the file
zig build-exe "$FILENAME" \
    -I../src/symbolic/egraph \
    -L../egg_ffi/target/release \
    -lc -legg_ffi \
    -rpath ../egg_ffi/target/release \
    -O "$OPTIMIZATION"

echo "Compilation complete. Run with ./$BASENAME"
