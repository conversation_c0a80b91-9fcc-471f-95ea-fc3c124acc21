const std = @import("std");
const time = std.time;
const c_api = @cImport({
    @cInclude("egg_ffi.h");
});

// Constants for the benchmark
const NUM_ITERATIONS = 5; // Number of benchmark iterations
const NUM_EXPRESSIONS = 5000; // Number of expressions to simplify in each iteration
const NUM_NODES_PER_EXPR = 9; // Number of nodes per expression (more complex expression)
const RULE_ITERATIONS = 5; // Number of rule iterations to run

// Batch node representation (must match the FFI definition)
const BatchNode = extern struct {
    tag: u8,
    _pad: [7]u8,
    data0: u64,
    data1: usize,
    data2: i32,
};

// Binary operation types
const BinaryOp = enum(c_int) {
    Add = 0,
    Sub = 1,
    Mul = 2,
    Div = 3,
    Mod = 4,
    Pow = 5,
    Min = 6,
    Max = 7,
    Eq = 8,
    Ne = 9,
    Lt = 10,
    Le = 11,
};

// Rule categories for rule selection
const RuleCategory = enum(u32) {
    Arithmetic = 1,
    Commutative = 2,
    Associative = 4,
    Distributive = 8,
    Identity = 16,
    ConstantFold = 32,
    Boolean = 64,
    All = 0xFFFFFFFF,
};

// Error codes for batch operations
const BatchError = enum(u32) {
    Ok = 0,
    Invalid = 1,
    Memory = 2,
    Panic = 3,
};

// Get the last error message from the FFI interface
fn getLastError() ?[]const u8 {
    const err_ptr = c_api.egg_get_last_error();
    if (err_ptr == null) return null;
    return std.mem.span(err_ptr);
}

// Benchmark the non-batch approach
fn benchmarkNonBatch(allocator: std.mem.Allocator) !u64 {
    // Create an EGraph
    const egraph_id = c_api.egg_create_egraph();
    if (egraph_id == 0) {
        if (getLastError()) |err| {
            std.log.err("Failed to create EGraph: {s}", .{err});
        }
        return error.EGraphCreationFailed;
    }
    defer _ = c_api.egg_free_egraph(egraph_id);

    // Start the timer
    const start_time = time.nanoTimestamp();

    // Process NUM_EXPRESSIONS expressions
    for (0..NUM_EXPRESSIONS) |_| {
        // Create a more complex expression: ((x + 0) * 1) + ((y * 0) + (z / 1))
        const x_symbol = try allocator.dupeZ(u8, "x");
        defer allocator.free(x_symbol);
        const y_symbol = try allocator.dupeZ(u8, "y");
        defer allocator.free(y_symbol);
        const z_symbol = try allocator.dupeZ(u8, "z");
        defer allocator.free(z_symbol);

        const x = c_api.egg_add_symbol(egraph_id, x_symbol.ptr);
        const y = c_api.egg_add_symbol(egraph_id, y_symbol.ptr);
        const z = c_api.egg_add_symbol(egraph_id, z_symbol.ptr);
        const zero = c_api.egg_add_constant(egraph_id, 0);
        const one = c_api.egg_add_constant(egraph_id, 1);
        
        // Left side: (x + 0) * 1
        const x_plus_0 = c_api.egg_add_binary_op(egraph_id, @intFromEnum(BinaryOp.Add), x, zero);
        const left = c_api.egg_add_binary_op(egraph_id, @intFromEnum(BinaryOp.Mul), x_plus_0, one);
        
        // Right side: (y * 0) + (z / 1)
        const y_mul_0 = c_api.egg_add_binary_op(egraph_id, @intFromEnum(BinaryOp.Mul), y, zero);
        const z_div_1 = c_api.egg_add_binary_op(egraph_id, @intFromEnum(BinaryOp.Div), z, one);
        const right = c_api.egg_add_binary_op(egraph_id, @intFromEnum(BinaryOp.Add), y_mul_0, z_div_1);
        
        // Final expression: left + right
        const expr = c_api.egg_add_binary_op(egraph_id, @intFromEnum(BinaryOp.Add), left, right);

        // Run rules
        _ = c_api.egg_run_rules_with_mask(egraph_id, RULE_ITERATIONS, @intFromEnum(RuleCategory.Identity));

        // Extract the best expression
        const result = c_api.egg_extract_best(egraph_id, expr);
        if (result != null) {
            c_api.egg_free_string(result);
        }
    }

    // End the timer
    const end_time = time.nanoTimestamp();
    return @intCast(end_time - start_time);
}

// Benchmark the batch approach
fn benchmarkBatch(allocator: std.mem.Allocator) !u64 {
    // Create an EGraph
    const egraph_id = c_api.egg_create_egraph();
    if (egraph_id == 0) {
        if (getLastError()) |err| {
            std.log.err("Failed to create EGraph: {s}", .{err});
        }
        return error.EGraphCreationFailed;
    }
    defer _ = c_api.egg_free_egraph(egraph_id);

    // Start the timer
    const start_time = time.nanoTimestamp();

    // Process NUM_EXPRESSIONS expressions
    for (0..NUM_EXPRESSIONS) |_| {
        // Create a batch for the expression: ((x + 0) * 1) + ((y * 0) + (z / 1))
        var batch = try allocator.alloc(BatchNode, NUM_NODES_PER_EXPR);
        defer allocator.free(batch);

        // Create symbol names
        const x_name = "x";
        const x_c_str = try allocator.dupeZ(u8, x_name);
        defer allocator.free(x_c_str);
        const x_ptr = @intFromPtr(x_c_str.ptr);

        const y_name = "y";
        const y_c_str = try allocator.dupeZ(u8, y_name);
        defer allocator.free(y_c_str);
        const y_ptr = @intFromPtr(y_c_str.ptr);

        const z_name = "z";
        const z_c_str = try allocator.dupeZ(u8, z_name);
        defer allocator.free(z_c_str);
        const z_ptr = @intFromPtr(z_c_str.ptr);

        // Node 0: x (Symbol)
        batch[0] = BatchNode{
            .tag = 1, // Symbol
            ._pad = [_]u8{0} ** 7,
            .data0 = x_ptr,
            .data1 = x_name.len,
            .data2 = -1,
        };

        // Node 1: 0 (Integer)
        batch[1] = BatchNode{
            .tag = 0, // Integer
            ._pad = [_]u8{0} ** 7,
            .data0 = @bitCast(@as(i64, 0)),
            .data1 = 0,
            .data2 = -1,
        };

        // Node 2: x + 0 (Add)
        batch[2] = BatchNode{
            .tag = 2, // Add
            ._pad = [_]u8{0} ** 7,
            .data0 = 0,
            .data1 = 0, // Left child: x
            .data2 = 1, // Right child: 0
        };

        // Node 3: 1 (Integer)
        batch[3] = BatchNode{
            .tag = 0, // Integer
            ._pad = [_]u8{0} ** 7,
            .data0 = @bitCast(@as(i64, 1)),
            .data1 = 0,
            .data2 = -1,
        };

        // Node 4: (x + 0) * 1 (Mul) - left side
        batch[4] = BatchNode{
            .tag = 4, // Mul
            ._pad = [_]u8{0} ** 7,
            .data0 = 0,
            .data1 = 2, // Left child: x + 0
            .data2 = 3, // Right child: 1
        };

        // Node 5: y (Symbol)
        batch[5] = BatchNode{
            .tag = 1, // Symbol
            ._pad = [_]u8{0} ** 7,
            .data0 = y_ptr,
            .data1 = y_name.len,
            .data2 = -1,
        };

        // Node 6: y * 0 (Mul)
        batch[6] = BatchNode{
            .tag = 4, // Mul
            ._pad = [_]u8{0} ** 7,
            .data0 = 0,
            .data1 = 5, // Left child: y
            .data2 = 1, // Right child: 0
        };

        // Node 7: z (Symbol)
        batch[7] = BatchNode{
            .tag = 1, // Symbol
            ._pad = [_]u8{0} ** 7,
            .data0 = z_ptr,
            .data1 = z_name.len,
            .data2 = -1,
        };

        // Node 8: z / 1 (Div)
        batch[8] = BatchNode{
            .tag = 5, // Div
            ._pad = [_]u8{0} ** 7,
            .data0 = 0,
            .data1 = 7, // Left child: z
            .data2 = 3, // Right child: 1
        };

        // Simplify the batch
        const root_idx = batch.len - 1; // The root node is the last node
        _ = c_api.egg_simplify_batch(
            egraph_id,
            @ptrCast(batch.ptr),
            batch.len,
            root_idx,
            RULE_ITERATIONS,
            @intFromEnum(RuleCategory.Identity),
        );

        // Free any strings allocated by the batch API
        c_api.egg_free_batch_strings(@ptrCast(batch.ptr), batch.len);
    }

    // End the timer
    const end_time = time.nanoTimestamp();
    return @intCast(end_time - start_time);
}

// Run the benchmark
pub fn main() !void {
    // Initialize allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Print benchmark parameters
    std.debug.print("Benchmark Parameters:\n", .{});
    std.debug.print("  Iterations: {d}\n", .{NUM_ITERATIONS});
    std.debug.print("  Expressions per iteration: {d}\n", .{NUM_EXPRESSIONS});
    std.debug.print("  Nodes per expression: {d}\n", .{NUM_NODES_PER_EXPR});
    std.debug.print("  Rule iterations: {d}\n", .{RULE_ITERATIONS});
    std.debug.print("\n", .{});

    // Run the non-batch benchmark
    std.debug.print("Running non-batch benchmark...\n", .{});
    var non_batch_times = try allocator.alloc(u64, NUM_ITERATIONS);
    defer allocator.free(non_batch_times);

    for (0..NUM_ITERATIONS) |i| {
        std.debug.print("  Iteration {d}...", .{i + 1});
        non_batch_times[i] = try benchmarkNonBatch(allocator);
        std.debug.print(" {d:.2} ms\n", .{@as(f64, @floatFromInt(non_batch_times[i])) / 1_000_000.0});
    }

    // Run the batch benchmark
    std.debug.print("\nRunning batch benchmark...\n", .{});
    var batch_times = try allocator.alloc(u64, NUM_ITERATIONS);
    defer allocator.free(batch_times);

    for (0..NUM_ITERATIONS) |i| {
        std.debug.print("  Iteration {d}...", .{i + 1});
        batch_times[i] = try benchmarkBatch(allocator);
        std.debug.print(" {d:.2} ms\n", .{@as(f64, @floatFromInt(batch_times[i])) / 1_000_000.0});
    }

    // Calculate statistics
    var non_batch_total: u64 = 0;
    var batch_total: u64 = 0;

    for (0..NUM_ITERATIONS) |i| {
        non_batch_total += non_batch_times[i];
        batch_total += batch_times[i];
    }

    const non_batch_avg = non_batch_total / NUM_ITERATIONS;
    const batch_avg = batch_total / NUM_ITERATIONS;

    // Print results
    std.debug.print("\nResults:\n", .{});
    std.debug.print("  Non-batch average: {d} ns ({d:.2} ms)\n", .{ non_batch_avg, @as(f64, @floatFromInt(non_batch_avg)) / 1_000_000.0 });
    std.debug.print("  Batch average: {d} ns ({d:.2} ms)\n", .{ batch_avg, @as(f64, @floatFromInt(batch_avg)) / 1_000_000.0 });
    std.debug.print("  Speedup: {d:.2}x\n", .{@as(f64, @floatFromInt(non_batch_avg)) / @as(f64, @floatFromInt(batch_avg))});
}
