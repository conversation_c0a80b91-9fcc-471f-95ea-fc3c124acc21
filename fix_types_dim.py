#!/usr/bin/env python3
"""
<PERSON>ript to systematically fix all files using the old types.Dim API to use the new expression-based API.
"""

import os
import re
import subprocess
from pathlib import Path

def run_command(cmd):
    """Run a shell command and return stdout, stderr, and return code."""
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return result.stdout, result.stderr, result.returncode

def find_files_with_types_dim(directory):
    """Find all files containing 'types.Dim'."""
    stdout, _, _ = run_command(f'grep -r "types\\.Dim" "{directory}" --include="*.zig" -l')
    if stdout:
        return [line.strip() for line in stdout.split('\n') if line.strip()]
    return []

def fix_file(file_path):
    """Fix a single file to convert from types.Dim to expression-based API."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern 1: Simple concrete dim creation
        # types.Dim{ .concrete = N } -> expr_N or similar
        # This is more complex, let's handle it case by case
        
        # Pattern 2: Array declarations
        # [_]types.Dim{ types.Dim{ .concrete = ... }, ... }
        # This needs to be converted to expressions
        
        # For now, let's just identify files that need manual fixing
        has_types_dim = 'types.Dim' in content
        has_concrete = '.concrete' in content
        has_symbolic = '.symbolic' in content
        
        needs_manual_fix = has_types_dim or has_concrete or has_symbolic
        
        if needs_manual_fix:
            print(f"File needs manual fixing: {file_path}")
            if has_types_dim:
                print(f"  - Contains types.Dim")
            if has_concrete:
                print(f"  - Contains .concrete access")
            if has_symbolic:
                print(f"  - Contains .symbolic access")
        
        return needs_manual_fix
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function."""
    base_dir = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/mycode/zing"
    
    print("Finding files with types.Dim...")
    files = find_files_with_types_dim(base_dir)
    
    print(f"Found {len(files)} files with types.Dim:")
    
    test_files = []
    core_files = []
    other_files = []
    
    for file_path in files:
        if 'test' in file_path.lower():
            test_files.append(file_path)
        elif 'src/core' in file_path:
            core_files.append(file_path)
        else:
            other_files.append(file_path)
    
    print(f"\nTest files ({len(test_files)}):")
    for f in sorted(test_files):
        print(f"  {f}")
    
    print(f"\nCore files ({len(core_files)}):")
    for f in sorted(core_files):
        print(f"  {f}")
    
    print(f"\nOther files ({len(other_files)}):")
    for f in sorted(other_files):
        print(f"  {f}")
    
    # Analyze each file
    print(f"\nAnalyzing files for required fixes...")
    needs_manual = []
    
    for file_path in files:
        if fix_file(file_path):
            needs_manual.append(file_path)
    
    print(f"\nFiles needing manual fixes: {len(needs_manual)}")
    for f in sorted(needs_manual):
        print(f"  {f}")

if __name__ == "__main__":
    main()