const std = @import("std");

pub fn build(b: *std.Build) void {
    const target = b.standardTargetOptions(.{});
    const optimize = b.standardOptimizeOption(.{});

    // Create options module
    const options = b.addOptions();
    options.addOption(bool, "verbose", false);
    const options_module = options.createModule();

    // V2 Core module
    const core_module = b.addModule("core", .{
        .root_source_file = b.path("src/core/core.zig"),
    });
    core_module.addImport("options", options_module);
    
    // V2 Tensor module
    const tensor_module = b.addModule("tensor", .{
        .root_source_file = b.path("src/tensor/mod.zig"),
    });
    tensor_module.addImport("core", core_module);

    // Create the main test step
    const test_step = b.step("test", "Run V2 core tests");
    
    // Create a test-all step for comprehensive testing
    const test_all_step = b.step("test-all", "Run all V2 core tests including comprehensive tests");
    
    // Add compiler integration test
    const compiler_test_exe = b.addExecutable(.{
        .name = "compiler_test",
        .root_source_file = b.path("examples/compiler_test.zig"),
        .target = target,
        .optimize = optimize,
    });
    compiler_test_exe.root_module.addImport("core", core_module);
    
    const run_compiler_test = b.addRunArtifact(compiler_test_exe);
    const compiler_test_step = b.step("test-compiler", "Run compiler integration test");
    compiler_test_step.dependOn(&run_compiler_test.step);

    // V2 Core tests - all tests from core directory
    
    // 1. Symbolic Engine Tests
    const v2_symbolic_tests = b.addTest(.{
        .name = "v2-symbolic-tests",
        .root_source_file = b.path("src/core/tests/test_symbolic.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_tests.root_module.addImport("core", core_module);
    v2_symbolic_tests.linkLibC();
    
    const run_v2_symbolic_tests = b.addRunArtifact(v2_symbolic_tests);
    test_step.dependOn(&run_v2_symbolic_tests.step);
    test_all_step.dependOn(&run_v2_symbolic_tests.step);

    // 1b. Constraint Solving Tests  
    const v2_constraint_tests = b.addTest(.{
        .name = "v2-constraint-tests", 
        .root_source_file = b.path("src/core/tests/test_constraint_solving.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_constraint_tests.root_module.addImport("core", core_module);
    v2_constraint_tests.linkLibC();
    
    const run_v2_constraint_tests = b.addRunArtifact(v2_constraint_tests);
    test_step.dependOn(&run_v2_constraint_tests.step);
    test_all_step.dependOn(&run_v2_constraint_tests.step);

    // 1c. Polynomial Expansion Tests  
    const v2_polynomial_tests = b.addTest(.{
        .name = "v2-polynomial-tests", 
        .root_source_file = b.path("src/core/tests/test_polynomial_expansion.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_polynomial_tests.root_module.addImport("core", core_module);
    v2_polynomial_tests.linkLibC();
    
    const run_v2_polynomial_tests = b.addRunArtifact(v2_polynomial_tests);
    test_step.dependOn(&run_v2_polynomial_tests.step);
    test_all_step.dependOn(&run_v2_polynomial_tests.step);

    // 1d. Enhanced Algebraic Manipulation Tests  
    const v2_enhanced_algebraic_tests = b.addTest(.{
        .name = "v2-enhanced-algebraic-tests", 
        .root_source_file = b.path("src/core/tests/test_enhanced_algebraic.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_enhanced_algebraic_tests.root_module.addImport("core", core_module);
    v2_enhanced_algebraic_tests.linkLibC();
    
    const run_v2_enhanced_algebraic_tests = b.addRunArtifact(v2_enhanced_algebraic_tests);
    test_step.dependOn(&run_v2_enhanced_algebraic_tests.step);
    test_all_step.dependOn(&run_v2_enhanced_algebraic_tests.step);
    
    // 2. Shape Engine Tests
    const v2_shape_tests = b.addTest(.{
        .name = "v2-shape-tests",
        .root_source_file = b.path("src/core/tests/test_shape.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_tests.root_module.addImport("core", core_module);
    v2_shape_tests.linkLibC();
    
    const run_v2_shape_tests = b.addRunArtifact(v2_shape_tests);
    test_step.dependOn(&run_v2_shape_tests.step);
    test_all_step.dependOn(&run_v2_shape_tests.step);
    
    
    // 3. Graph Engine Tests
    const v2_graph_tests = b.addTest(.{
        .name = "v2-graph-tests",
        .root_source_file = b.path("src/core/tests/test_graph.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_graph_tests.root_module.addImport("core", core_module);
    v2_graph_tests.linkLibC();
    
    const run_v2_graph_tests = b.addRunArtifact(v2_graph_tests);
    test_step.dependOn(&run_v2_graph_tests.step);
    test_all_step.dependOn(&run_v2_graph_tests.step);
    
    // 4. Core Integration Tests
    const v2_integration_tests = b.addTest(.{
        .name = "v2-integration-tests",
        .root_source_file = b.path("src/core/tests/test_integration.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_integration_tests.root_module.addImport("core", core_module);
    v2_integration_tests.linkLibC();
    
    const run_v2_integration_tests = b.addRunArtifact(v2_integration_tests);
    test_step.dependOn(&run_v2_integration_tests.step);
    test_all_step.dependOn(&run_v2_integration_tests.step);
    
    
    // 6. Core Tests
    const v2_core_tests = b.addTest(.{
        .name = "v2-core-tests",
        .root_source_file = b.path("src/core/tests/test.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_core_tests.root_module.addImport("core", core_module);
    v2_core_tests.linkLibC();
    
    const run_v2_core_tests = b.addRunArtifact(v2_core_tests);
    test_step.dependOn(&run_v2_core_tests.step);
    test_all_step.dependOn(&run_v2_core_tests.step);
    
    // 7. Graph Module Tests
    const v2_graph_module_tests = b.addTest(.{
        .name = "v2-graph-module-tests",
        .root_source_file = b.path("src/core/graph/test.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_graph_module_tests.root_module.addImport("core", core_module);
    v2_graph_module_tests.linkLibC();
    
    const run_v2_graph_module_tests = b.addRunArtifact(v2_graph_module_tests);
    test_step.dependOn(&run_v2_graph_module_tests.step);
    test_all_step.dependOn(&run_v2_graph_module_tests.step);
    
    // 8. Enhanced Symbolic Tests
    const v2_symbolic_enhanced_tests = b.addTest(.{
        .name = "v2-symbolic-enhanced-tests",
        .root_source_file = b.path("src/core/tests/test_symbolic_enhanced.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_enhanced_tests.root_module.addImport("core", core_module);
    v2_symbolic_enhanced_tests.linkLibC();
    
    const run_v2_symbolic_enhanced_tests = b.addRunArtifact(v2_symbolic_enhanced_tests);
    test_step.dependOn(&run_v2_symbolic_enhanced_tests.step);
    test_all_step.dependOn(&run_v2_symbolic_enhanced_tests.step);
    
    // 9. Shape Inference Tests (formerly constraint solver)
    const v2_shape_inference_tests = b.addTest(.{
        .name = "v2-shape-inference-tests",
        .root_source_file = b.path("src/core/tests/test_shape_inference.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_inference_tests.root_module.addImport("core", core_module);
    v2_shape_inference_tests.linkLibC();
    
    const run_v2_shape_inference_tests = b.addRunArtifact(v2_shape_inference_tests);
    test_step.dependOn(&run_v2_shape_inference_tests.step);
    test_all_step.dependOn(&run_v2_shape_inference_tests.step);
    
    // 10. Data Store Tests
    const v2_data_store_tests = b.addTest(.{
        .name = "v2-data-store-tests",
        .root_source_file = b.path("src/core/tests/test_data_store.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_data_store_tests.root_module.addImport("core", core_module);
    v2_data_store_tests.root_module.addImport("tensor", tensor_module);
    v2_data_store_tests.linkLibC();
    
    const run_v2_data_store_tests = b.addRunArtifact(v2_data_store_tests);
    test_step.dependOn(&run_v2_data_store_tests.step);
    test_all_step.dependOn(&run_v2_data_store_tests.step);
    
    // 11. Comprehensive Shape Engine Tests
    const v2_shape_comprehensive_tests = b.addTest(.{
        .name = "v2-shape-comprehensive-tests",
        .root_source_file = b.path("src/core/tests/test_shape_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_comprehensive_tests.root_module.addImport("core", core_module);
    v2_shape_comprehensive_tests.linkLibC();
    
    const run_v2_shape_comprehensive_tests = b.addRunArtifact(v2_shape_comprehensive_tests);
    test_all_step.dependOn(&run_v2_shape_comprehensive_tests.step);
    
    
    // 13. Shape All Operations Tests
    const v2_shape_all_operations_tests = b.addTest(.{
        .name = "v2-shape-all-operations-tests",
        .root_source_file = b.path("src/core/tests/test_shape_all_operations.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_all_operations_tests.root_module.addImport("core", core_module);
    v2_shape_all_operations_tests.linkLibC();
    
    const run_v2_shape_all_operations_tests = b.addRunArtifact(v2_shape_all_operations_tests);
    test_step.dependOn(&run_v2_shape_all_operations_tests.step);
    test_all_step.dependOn(&run_v2_shape_all_operations_tests.step);
    
    // NEW SHAPE TESTS
    
    // 16. V2 Shape Parity Tests
    const v2_shape_parity_tests = b.addTest(.{
        .name = "v2-shape-parity-tests",
        .root_source_file = b.path("src/core/shape/tests/test_v2_shape_parity.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_parity_tests.root_module.addImport("core", core_module);
    v2_shape_parity_tests.linkLibC();
    
    const run_v2_shape_parity_tests = b.addRunArtifact(v2_shape_parity_tests);
    test_all_step.dependOn(&run_v2_shape_parity_tests.step);
    test_step.dependOn(&run_v2_shape_parity_tests.step);
    
    // 12. Comprehensive Symbolic Engine Tests
    const v2_symbolic_comprehensive_tests = b.addTest(.{
        .name = "v2-symbolic-comprehensive-tests",
        .root_source_file = b.path("src/core/tests/test_symbolic_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_comprehensive_tests.root_module.addImport("core", core_module);
    v2_symbolic_comprehensive_tests.linkLibC();
    
    const run_v2_symbolic_comprehensive_tests = b.addRunArtifact(v2_symbolic_comprehensive_tests);
    test_all_step.dependOn(&run_v2_symbolic_comprehensive_tests.step);
    
    // Add specific test steps for comprehensive tests
    const test_shape_comprehensive_step = b.step("test-shape-comprehensive", "Run comprehensive shape engine tests");
    test_shape_comprehensive_step.dependOn(&run_v2_shape_comprehensive_tests.step);
    
    const test_symbolic_comprehensive_step = b.step("test-symbolic-comprehensive", "Run comprehensive symbolic engine tests");
    test_symbolic_comprehensive_step.dependOn(&run_v2_symbolic_comprehensive_tests.step);
    
    
    const test_shape_all_operations_step = b.step("test-shape-all-operations", "Run shape all operations tests");
    test_shape_all_operations_step.dependOn(&run_v2_shape_all_operations_tests.step);
    
    // Memory Improvements Test
    const v2_memory_improvements_tests = b.addTest(.{
        .name = "v2-memory-improvements-tests",
        .root_source_file = b.path("src/core/tests/test_memory_improvements.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_memory_improvements_tests.root_module.addImport("core", core_module);
    v2_memory_improvements_tests.linkLibC();
    
    const run_v2_memory_improvements_tests = b.addRunArtifact(v2_memory_improvements_tests);
    test_step.dependOn(&run_v2_memory_improvements_tests.step);
    test_all_step.dependOn(&run_v2_memory_improvements_tests.step);
    
    // NEW COMPREHENSIVE TESTS
    
    // Expression Generation Comprehensive Tests  
    const v2_expression_generation_comprehensive_tests = b.addTest(.{
        .name = "v2-expression-generation-comprehensive-tests",
        .root_source_file = b.path("src/core/shape/tests/test_expression_generation.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_expression_generation_comprehensive_tests.root_module.addImport("core", core_module);
    v2_expression_generation_comprehensive_tests.linkLibC();
    
    const run_v2_expression_generation_comprehensive_tests = b.addRunArtifact(v2_expression_generation_comprehensive_tests);
    test_step.dependOn(&run_v2_expression_generation_comprehensive_tests.step);
    test_all_step.dependOn(&run_v2_expression_generation_comprehensive_tests.step);
    
    
    // Shape Comprehensive Tests
    const v2_shape_comprehensive_new_tests = b.addTest(.{
        .name = "v2-shape-comprehensive-new-tests",
        .root_source_file = b.path("src/core/shape/tests/test_shape_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_comprehensive_new_tests.root_module.addImport("core", core_module);
    v2_shape_comprehensive_new_tests.linkLibC();
    
    const run_v2_shape_comprehensive_new_tests = b.addRunArtifact(v2_shape_comprehensive_new_tests);
    test_step.dependOn(&run_v2_shape_comprehensive_new_tests.step);
    test_all_step.dependOn(&run_v2_shape_comprehensive_new_tests.step);
    
    // Integration Comprehensive Tests
    const v2_integration_comprehensive_tests = b.addTest(.{
        .name = "v2-integration-comprehensive-tests",
        .root_source_file = b.path("src/core/tests/test_integration_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_integration_comprehensive_tests.root_module.addImport("core", core_module);
    v2_integration_comprehensive_tests.root_module.addImport("tensor", tensor_module);
    v2_integration_comprehensive_tests.linkLibC();
    
    const run_v2_integration_comprehensive_tests = b.addRunArtifact(v2_integration_comprehensive_tests);
    test_step.dependOn(&run_v2_integration_comprehensive_tests.step);
    test_all_step.dependOn(&run_v2_integration_comprehensive_tests.step);
    
    // Shape Inference Comprehensive Tests
    const v2_shape_inference_comprehensive_tests = b.addTest(.{
        .name = "v2-shape-inference-comprehensive-tests",
        .root_source_file = b.path("src/core/tests/test_shape_inference_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_shape_inference_comprehensive_tests.root_module.addImport("core", core_module);
    v2_shape_inference_comprehensive_tests.linkLibC();
    
    const run_v2_shape_inference_comprehensive_tests = b.addRunArtifact(v2_shape_inference_comprehensive_tests);
    test_step.dependOn(&run_v2_shape_inference_comprehensive_tests.step);
    test_all_step.dependOn(&run_v2_shape_inference_comprehensive_tests.step);
    
    // Tensor Manipulation Comprehensive Tests
    const v2_tensor_manipulation_comprehensive_tests = b.addTest(.{
        .name = "v2-tensor-manipulation-comprehensive-tests",
        .root_source_file = b.path("src/tensor/tests/test_manipulation_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_manipulation_comprehensive_tests.root_module.addImport("core", core_module);
    v2_tensor_manipulation_comprehensive_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_manipulation_comprehensive_tests.linkLibC();
    
    const run_v2_tensor_manipulation_comprehensive_tests = b.addRunArtifact(v2_tensor_manipulation_comprehensive_tests);
    test_step.dependOn(&run_v2_tensor_manipulation_comprehensive_tests.step);
    test_all_step.dependOn(&run_v2_tensor_manipulation_comprehensive_tests.step);
    
    // Add comprehensive test step
    const test_comprehensive_step = b.step("test-comprehensive", "Run all comprehensive tests");
    test_comprehensive_step.dependOn(&run_v2_expression_generation_comprehensive_tests.step);
    test_comprehensive_step.dependOn(&run_v2_shape_comprehensive_new_tests.step);
    test_comprehensive_step.dependOn(&run_v2_integration_comprehensive_tests.step);
    test_comprehensive_step.dependOn(&run_v2_shape_inference_comprehensive_tests.step);
    test_comprehensive_step.dependOn(&run_v2_tensor_manipulation_comprehensive_tests.step);
    
    // Memory Bug Debugging Test
    const v2_memory_bug_tests = b.addTest(.{
        .name = "v2-memory-bug-tests",
        .root_source_file = b.path("src/core/tests/test_memory_bug.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_memory_bug_tests.root_module.addImport("core", core_module);
    v2_memory_bug_tests.linkLibC();
    
    const run_v2_memory_bug_tests = b.addRunArtifact(v2_memory_bug_tests);
    const test_bug_step = b.step("test-bug", "Debug memory bug");
    test_bug_step.dependOn(&run_v2_memory_bug_tests.step);
    
    // ==============================================================
    // V2 Tensor Operation Tests
    // ==============================================================
    
    // 1. Tensor Creation Operations Tests
    const v2_tensor_creation_tests = b.addTest(.{
        .name = "v2-tensor-creation-tests",
        .root_source_file = b.path("src/tensor/tests/test_creation_ops.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_creation_tests.root_module.addImport("core", core_module);
    v2_tensor_creation_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_creation_tests.linkLibC();
    
    const run_v2_tensor_creation_tests = b.addRunArtifact(v2_tensor_creation_tests);
    test_step.dependOn(&run_v2_tensor_creation_tests.step);
    test_all_step.dependOn(&run_v2_tensor_creation_tests.step);
    
    // 2. Tensor Pointwise Operations Tests
    const v2_tensor_pointwise_tests = b.addTest(.{
        .name = "v2-tensor-pointwise-tests",
        .root_source_file = b.path("src/tensor/tests/test_pointwise_ops.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_pointwise_tests.root_module.addImport("core", core_module);
    v2_tensor_pointwise_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_pointwise_tests.linkLibC();
    
    const run_v2_tensor_pointwise_tests = b.addRunArtifact(v2_tensor_pointwise_tests);
    test_step.dependOn(&run_v2_tensor_pointwise_tests.step);
    test_all_step.dependOn(&run_v2_tensor_pointwise_tests.step);
    
    // 3. Tensor Reduction Operations Tests
    const v2_tensor_reduction_tests = b.addTest(.{
        .name = "v2-tensor-reduction-tests",
        .root_source_file = b.path("src/tensor/tests/test_reduction_ops.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_reduction_tests.root_module.addImport("core", core_module);
    v2_tensor_reduction_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_reduction_tests.linkLibC();
    
    const run_v2_tensor_reduction_tests = b.addRunArtifact(v2_tensor_reduction_tests);
    test_step.dependOn(&run_v2_tensor_reduction_tests.step);
    test_all_step.dependOn(&run_v2_tensor_reduction_tests.step);
    
    // 3.5. Tensor Manipulation Operations Tests
    const v2_tensor_manipulation_tests = b.addTest(.{
        .name = "v2-tensor-manipulation-tests",
        .root_source_file = b.path("src/tensor/tests/test_manipulation_basic.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_manipulation_tests.root_module.addImport("core", core_module);
    v2_tensor_manipulation_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_manipulation_tests.linkLibC();
    
    const run_v2_tensor_manipulation_tests = b.addRunArtifact(v2_tensor_manipulation_tests);
    test_step.dependOn(&run_v2_tensor_manipulation_tests.step);
    test_all_step.dependOn(&run_v2_tensor_manipulation_tests.step);
    
    // 3.5.2. Advanced Manipulation Tests (placeholder operations)
    const v2_tensor_manipulation_advanced_tests = b.addTest(.{
        .name = "v2-tensor-manipulation-advanced-tests",
        .root_source_file = b.path("src/tensor/tests/test_manipulation_advanced.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_manipulation_advanced_tests.root_module.addImport("core", core_module);
    v2_tensor_manipulation_advanced_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_manipulation_advanced_tests.linkLibC();
    
    const run_v2_tensor_manipulation_advanced_tests = b.addRunArtifact(v2_tensor_manipulation_advanced_tests);
    test_step.dependOn(&run_v2_tensor_manipulation_advanced_tests.step);
    test_all_step.dependOn(&run_v2_tensor_manipulation_advanced_tests.step);
    
    // 3.6. Symbolic Tensor Integration Tests  
    const v2_symbolic_tensor_tests = b.addTest(.{
        .name = "v2-symbolic-tensor-tests",
        .root_source_file = b.path("src/tensor/tests/test_symbolic_tensor_ops.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_symbolic_tensor_tests.root_module.addImport("core", core_module);
    v2_symbolic_tensor_tests.root_module.addImport("tensor", tensor_module);
    v2_symbolic_tensor_tests.linkLibC();
    
    const run_v2_symbolic_tensor_tests = b.addRunArtifact(v2_symbolic_tensor_tests);
    test_step.dependOn(&run_v2_symbolic_tensor_tests.step);
    test_all_step.dependOn(&run_v2_symbolic_tensor_tests.step);
    
    // 4. Tensor Linear Algebra Operations Tests
    const v2_tensor_linalg_tests = b.addTest(.{
        .name = "v2-tensor-linalg-tests",
        .root_source_file = b.path("src/tensor/tests/test_linalg_ops.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_linalg_tests.root_module.addImport("core", core_module);
    v2_tensor_linalg_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_linalg_tests.linkLibC();
    
    const run_v2_tensor_linalg_tests = b.addRunArtifact(v2_tensor_linalg_tests);
    test_step.dependOn(&run_v2_tensor_linalg_tests.step);
    test_all_step.dependOn(&run_v2_tensor_linalg_tests.step);
    
    
    
    
    
    // Add expression generation tests
    const v2_expression_generation_tests = b.addTest(.{
        .name = "v2-expression-generation-tests",
        .root_source_file = b.path("src/core/shape/tests/test_expression_generation.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_expression_generation_tests.root_module.addImport("core", core_module);
    v2_expression_generation_tests.linkLibC();
    
    const run_v2_expression_generation_tests = b.addRunArtifact(v2_expression_generation_tests);
    test_step.dependOn(&run_v2_expression_generation_tests.step);
    test_all_step.dependOn(&run_v2_expression_generation_tests.step);
    
    // Add optimized views test for tensor operations
    const v2_tensor_optimized_views_tests = b.addTest(.{
        .name = "v2-tensor-optimized-views-tests",
        .root_source_file = b.path("src/tensor/tests/test_optimized_views.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_optimized_views_tests.root_module.addImport("core", core_module);
    v2_tensor_optimized_views_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_optimized_views_tests.linkLibC();
    
    const run_v2_tensor_optimized_views_tests = b.addRunArtifact(v2_tensor_optimized_views_tests);
    test_step.dependOn(&run_v2_tensor_optimized_views_tests.step);
    test_all_step.dependOn(&run_v2_tensor_optimized_views_tests.step);
    
    // Add Luminal parity tests
    const v2_tensor_luminal_parity_tests = b.addTest(.{
        .name = "v2-tensor-luminal-parity-tests",
        .root_source_file = b.path("src/tensor/tests/test_luminal_parity.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_luminal_parity_tests.root_module.addImport("core", core_module);
    v2_tensor_luminal_parity_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_luminal_parity_tests.linkLibC();
    
    const run_v2_tensor_luminal_parity_tests = b.addRunArtifact(v2_tensor_luminal_parity_tests);
    test_step.dependOn(&run_v2_tensor_luminal_parity_tests.step);
    test_all_step.dependOn(&run_v2_tensor_luminal_parity_tests.step);
    
    // Add Luminal parity operations tests
    const v2_tensor_luminal_parity_ops_tests = b.addTest(.{
        .name = "v2-tensor-luminal-parity-ops-tests",
        .root_source_file = b.path("src/tensor/tests/test_luminal_parity_operations.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_luminal_parity_ops_tests.root_module.addImport("core", core_module);
    v2_tensor_luminal_parity_ops_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_luminal_parity_ops_tests.linkLibC();
    
    const run_v2_tensor_luminal_parity_ops_tests = b.addRunArtifact(v2_tensor_luminal_parity_ops_tests);
    test_step.dependOn(&run_v2_tensor_luminal_parity_ops_tests.step);
    test_all_step.dependOn(&run_v2_tensor_luminal_parity_ops_tests.step);
    
    // Add separate tensor test step
    const test_tensor_step = b.step("test-tensor", "Run tensor operation tests");
    test_tensor_step.dependOn(&run_v2_tensor_creation_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_pointwise_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_reduction_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_linalg_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_optimized_views_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_manipulation_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_manipulation_advanced_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_luminal_parity_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_luminal_parity_ops_tests.step);
    
    // ==============================================================
    // V2 Tensor Validation Tests (pre-compiler tests)
    // ==============================================================
    
    // Graph Structure Validation Tests
    const v2_tensor_graph_structure_tests = b.addTest(.{
        .name = "v2-tensor-graph-structure-tests",
        .root_source_file = b.path("src/tensor/tests/test_graph_structure_validation.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_graph_structure_tests.root_module.addImport("core", core_module);
    v2_tensor_graph_structure_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_graph_structure_tests.linkLibC();
    
    const run_v2_tensor_graph_structure_tests = b.addRunArtifact(v2_tensor_graph_structure_tests);
    test_step.dependOn(&run_v2_tensor_graph_structure_tests.step);
    test_all_step.dependOn(&run_v2_tensor_graph_structure_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_graph_structure_tests.step);
    
    // Shape Inference Validation Tests
    const v2_tensor_shape_inference_tests = b.addTest(.{
        .name = "v2-tensor-shape-inference-tests",
        .root_source_file = b.path("src/tensor/tests/test_shape_inference_validation.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_shape_inference_tests.root_module.addImport("core", core_module);
    v2_tensor_shape_inference_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_shape_inference_tests.linkLibC();
    
    const run_v2_tensor_shape_inference_tests = b.addRunArtifact(v2_tensor_shape_inference_tests);
    test_step.dependOn(&run_v2_tensor_shape_inference_tests.step);
    test_all_step.dependOn(&run_v2_tensor_shape_inference_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_shape_inference_tests.step);
    
    // Error Handling Validation Tests
    const v2_tensor_error_handling_tests = b.addTest(.{
        .name = "v2-tensor-error-handling-tests",
        .root_source_file = b.path("src/tensor/tests/test_error_handling_validation.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_error_handling_tests.root_module.addImport("core", core_module);
    v2_tensor_error_handling_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_error_handling_tests.linkLibC();
    
    const run_v2_tensor_error_handling_tests = b.addRunArtifact(v2_tensor_error_handling_tests);
    test_step.dependOn(&run_v2_tensor_error_handling_tests.step);
    test_all_step.dependOn(&run_v2_tensor_error_handling_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_error_handling_tests.step);
    
    // API Surface Validation Tests
    const v2_tensor_api_surface_tests = b.addTest(.{
        .name = "v2-tensor-api-surface-tests",
        .root_source_file = b.path("src/tensor/tests/test_api_surface_validation.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_tensor_api_surface_tests.root_module.addImport("core", core_module);
    v2_tensor_api_surface_tests.root_module.addImport("tensor", tensor_module);
    v2_tensor_api_surface_tests.linkLibC();
    
    const run_v2_tensor_api_surface_tests = b.addRunArtifact(v2_tensor_api_surface_tests);
    test_step.dependOn(&run_v2_tensor_api_surface_tests.step);
    test_all_step.dependOn(&run_v2_tensor_api_surface_tests.step);
    test_tensor_step.dependOn(&run_v2_tensor_api_surface_tests.step);
    
    // Add dedicated test step for tensor validation tests
    const test_tensor_validation_step = b.step("test-tensor-validation", "Run tensor validation tests (pre-compiler)");
    test_tensor_validation_step.dependOn(&run_v2_tensor_graph_structure_tests.step);
    test_tensor_validation_step.dependOn(&run_v2_tensor_shape_inference_tests.step);
    test_tensor_validation_step.dependOn(&run_v2_tensor_error_handling_tests.step);
    test_tensor_validation_step.dependOn(&run_v2_tensor_api_surface_tests.step);
    
    // ==============================================================
    // V2 Compiler Tests
    // ==============================================================
    
    // Comprehensive compiler tests
    const v2_compiler_comprehensive_tests = b.addTest(.{
        .name = "v2-compiler-comprehensive-tests",
        .root_source_file = b.path("src/core/compiler/tests/test_compiler_comprehensive.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_compiler_comprehensive_tests.root_module.addImport("core", core_module);
    v2_compiler_comprehensive_tests.linkLibC();
    
    const run_v2_compiler_comprehensive_tests = b.addRunArtifact(v2_compiler_comprehensive_tests);
    test_step.dependOn(&run_v2_compiler_comprehensive_tests.step);
    test_all_step.dependOn(&run_v2_compiler_comprehensive_tests.step);
    
    // Pattern engine detailed tests
    const v2_pattern_engine_tests = b.addTest(.{
        .name = "v2-pattern-engine-tests",
        .root_source_file = b.path("src/core/compiler/tests/test_pattern_engine_detailed.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_pattern_engine_tests.root_module.addImport("core", core_module);
    v2_pattern_engine_tests.linkLibC();
    
    const run_v2_pattern_engine_tests = b.addRunArtifact(v2_pattern_engine_tests);
    test_step.dependOn(&run_v2_pattern_engine_tests.step);
    test_all_step.dependOn(&run_v2_pattern_engine_tests.step);
    
    // Memory management tests
    const v2_memory_management_tests = b.addTest(.{
        .name = "v2-memory-management-tests",
        .root_source_file = b.path("src/core/compiler/tests/test_memory_management.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_memory_management_tests.root_module.addImport("core", core_module);
    v2_memory_management_tests.linkLibC();
    
    const run_v2_memory_management_tests = b.addRunArtifact(v2_memory_management_tests);
    test_step.dependOn(&run_v2_memory_management_tests.step);
    test_all_step.dependOn(&run_v2_memory_management_tests.step);
    
    // Simple compiler test (keep for quick validation)
    const v2_compiler_simple_tests = b.addTest(.{
        .name = "v2-compiler-simple-tests",
        .root_source_file = b.path("src/core/compiler/tests/test_simple.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_compiler_simple_tests.root_module.addImport("core", core_module);
    v2_compiler_simple_tests.linkLibC();
    
    const run_v2_compiler_simple_tests = b.addRunArtifact(v2_compiler_simple_tests);
    
    // Create a compiler test step
    const test_compiler_step = b.step("test-compiler-unit", "Run compiler unit tests");
    test_compiler_step.dependOn(&run_v2_compiler_comprehensive_tests.step);
    test_compiler_step.dependOn(&run_v2_pattern_engine_tests.step);
    test_compiler_step.dependOn(&run_v2_memory_management_tests.step);
    test_compiler_step.dependOn(&run_v2_compiler_simple_tests.step);
    
    // Core library for applications
    const core_lib = b.addStaticLibrary(.{
        .name = "zing-core",
        .root_source_file = b.path("src/core/core.zig"),
        .target = target,
        .optimize = optimize,
    });
    b.installArtifact(core_lib);
    
    // Example application
    const example_exe = b.addExecutable(.{
        .name = "zing-example",
        .root_source_file = b.path("examples/v2_simple.zig"),
        .target = target,
        .optimize = optimize,
    });
    example_exe.root_module.addImport("core", core_module);
    b.installArtifact(example_exe);
    
    const run_cmd = b.addRunArtifact(example_exe);
    const run_step = b.step("run", "Run the V2 example");
    run_step.dependOn(&run_cmd.step);
    
    // Luminal parity demo
    const luminal_parity_demo_exe = b.addExecutable(.{
        .name = "luminal-parity-demo",
        .root_source_file = b.path("examples/luminal_parity_demo.zig"),
        .target = target,
        .optimize = optimize,
    });
    luminal_parity_demo_exe.root_module.addImport("core", core_module);
    luminal_parity_demo_exe.root_module.addImport("tensor", tensor_module);
    luminal_parity_demo_exe.linkLibC();
    b.installArtifact(luminal_parity_demo_exe);
    
    const run_luminal_demo_cmd = b.addRunArtifact(luminal_parity_demo_exe);
    const run_luminal_step = b.step("luminal-demo", "Run the Luminal parity demo");
    run_luminal_step.dependOn(&run_luminal_demo_cmd.step);
    
    // Add test for v2_simple example
    const v2_simple_tests = b.addTest(.{
        .name = "v2-simple-tests",
        .root_source_file = b.path("examples/test_v2_simple.zig"),
        .target = target,
        .optimize = optimize,
    });
    v2_simple_tests.root_module.addImport("core", core_module);
    v2_simple_tests.linkLibC();
    
    const run_v2_simple_tests = b.addRunArtifact(v2_simple_tests);
    test_step.dependOn(&run_v2_simple_tests.step);
    
    // Add specific test step for v2_simple
    const test_v2_simple_step = b.step("test-simple", "Run V2 simple example tests");
    test_v2_simple_step.dependOn(&run_v2_simple_tests.step);
    
    // Debug crash executable
    const debug_crash_exe = b.addExecutable(.{
        .name = "debug-crash",
        .root_source_file = b.path("debug_crash.zig"),
        .target = target,
        .optimize = optimize,
    });
    debug_crash_exe.root_module.addImport("core", core_module);
    debug_crash_exe.root_module.addImport("tensor", tensor_module);
    b.installArtifact(debug_crash_exe);
    
    const run_debug_crash = b.addRunArtifact(debug_crash_exe);
    const debug_crash_step = b.step("debug-crash", "Run debug crash test");
    debug_crash_step.dependOn(&run_debug_crash.step);
}