const std = @import("std");
const core = @import("core");
const tensor = @import("tensor");

pub fn main() !void {
    const allocator = std.heap.page_allocator;
    
    std.debug.print("Starting minimal crash test...\n", .{});
    
    // Try to initialize Core
    std.debug.print("Initializing Core...\n", .{});
    var ctx = core.Core.init(allocator) catch |err| {
        std.debug.print("Core.init failed: {}\n", .{err});
        return;
    };
    defer ctx.deinit();
    
    std.debug.print("Core initialized successfully\n", .{});
    
    // Try to create a simple tensor
    std.debug.print("Creating tensor...\n", .{});
    const result = tensor.creation.constant(ctx, 5.0) catch |err| {
        std.debug.print("tensor.creation.constant failed: {}\n", .{err});
        return;
    };
    
    std.debug.print("Tensor created successfully: {}\n", .{result});
}