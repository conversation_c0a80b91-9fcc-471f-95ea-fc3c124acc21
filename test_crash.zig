const std = @import("std");
const Core = @import("src/core/core.zig").Core;
const tensor = @import("src/tensor/mod.zig");

pub fn main() !void {
    std.debug.print("Starting crash test...\n", .{});
    
    // Initialize allocator
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    std.debug.print("Created allocator\n", .{});
    
    // Initialize core - This is where it might crash
    const core = try Core.init(gpa.allocator());
    defer core.deinit();
    
    std.debug.print("Created core\n", .{});
    
    // Try to create a simple constant
    const t = try tensor.creation.constant(core, 5.0);
    
    std.debug.print("Created tensor with ID: {}\n", .{@intFromEnum(t)});
    std.debug.print("Test passed!\n", .{});
}