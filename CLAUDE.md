# CLAUDE.md


# Idiomatic Zig 0.14 Guidelines

## Summary

This document provides a comprehensive guide to idiomatic Zig 0.14, drawing from official documentation, release notes, and established conventions within the Zig ecosystem. It covers formatting, naming, documentation, error handling, memory management, compile-time features, modularization, the build system, testing, C interoperability, and key language features. The aim is to help developers write clear, maintainable, and efficient Zig code.

## Formatting and Whitespace

The Zig compiler comes with a built-in formatter, `zig fmt`, which enforces a consistent style. Adhering to `zig fmt` is the primary way to ensure idiomatic formatting.

* **Indentation & Braces**:
    * Use 4 spaces per indent level. [Verified: `zig fmt` behavior]
    * Place the opening brace `{` on the same line as its corresponding declaration (e.g., `fn foo() void {`, `if (condition) {`). [Verified: `zig fmt` behavior]
    * For single-statement blocks in `if`, `else`, `while`, and `for`, braces are generally optional if the statement is on the same line and is simple (like `break`, `continue`, `return`). However, `zig fmt` will often add them or reformat, and for clarity, especially with more complex single statements, using braces is good practice.

* **Lists & Trailing Commas**:
    * For multi-line lists (struct initializers, function call arguments, array literals, etc.), place each element on its own line. [Verified: `zig fmt` behavior]
    * A trailing comma after the last element in a multi-line list is allowed and often preferred as it simplifies adding new elements and results in cleaner diffs. `zig fmt` will typically enforce or preserve this. [Verified: Language Feature, `zig fmt` behavior]

* **Line Length**:
    * While there isn't a strict compiler-enforced maximum line length, aiming for a maximum of around 80-100 characters per line is a common convention to enhance readability and improve code review in diff tools. The Zig standard library often adheres to this.

## Naming Conventions

These conventions are largely followed by the Zig standard library and `zig fmt`. (Source: Zig 0.14.0 Documentation, `zigcc/zig-idioms` and "Learning Zig - Style Guide" reflecting common practice aligned with std lib)

* **Functions**:
    * Use `camelCase` for most functions (e.g., `myFunction`, `calculateValue`).
    * Constructors or factory-like functions that return types may use `PascalCase` (e.g., `ArrayList.init`, `@TypeOf()`). This is because they are often used in type contexts. [Verified: Standard Library Practice, Language Reference for builtins like `@TypeOf`]

* **Types & Structs**:
    * Use `PascalCase` for all types, including structs, enums, unions, and opaque types (e.g., `MyStruct`, `StatusEnum`, `DataUnion`). [Verified: Language Reference, Standard Library Practice]
    * Zero-field "namespace" structs (used to group related functions and declarations) also typically use `PascalCase` as they are defining a type that acts as a namespace (e.g., `std.mem`, `std.json`).

* **Variables & Constants**:
    * Use `snake_case` for local variables, function parameters, and constants (e.g., `my_variable`, `max_value`, `const http_port = 8080;`). [Verified: Language Reference, Standard Library Practice]

* **Import & File Names**:
    * Use `snake_case` for file and directory names (e.g., `my_module.zig`, `utils/helpers.zig`).
    * If a file primarily exports a single public type, the filename *may* match the type's `PascalCase` (e.g., `ArrayList.zig`), though `snake_case` is also common and acceptable.

* **Error Sets & Enum Variants**:
    * Use `PascalCase` for error set names and enum variants (e.g., `error{FileNotFound, AccessDenied}`, `enum { MyVariant, AnotherOne }`). [Verified: Language Reference, Standard Library Practice]

* **Function Naming in Namespaces**:
    * Avoid redundancy. If a function is part of a struct (namespace), its name should not repeat the struct's name if the context is clear. For example, prefer `my_context.createResource()` over `my_context.createMyContextResource()`.

## Documentation

Official Zig documentation guidelines encourage clear and concise comments. (Source: Zig 0.14.0 Documentation)

* **Doc Comments**:
    * Use `///` (three slashes) immediately before a public declaration (functions, types, global constants/variables) to generate API documentation.
    * Use `//!` (two slashes and an exclamation mark) at the beginning of a file (before any other code) for module-level documentation. This documents the file itself.
    * Regular comments `//` are ignored by the documentation generator but can be interleaved.

* **Content Guidelines**:
    * Focus on explaining *why* the code is the way it is, its purpose, and any non-obvious behavior or contracts.
    * Avoid restating what is obvious from the function signature or name. If a name is self-explanatory (e.g., `fn add(a: i32, b: i32) i32`), the doc comment might be minimal or focus on edge cases.
    * For functions with multiple overloads or enum variants with shared characteristics, it can be helpful to duplicate essential shared details in each doc comment to aid IDE tooltips and generated documentation.

* **Error Documentation**:
    * For functions returning error unions (`!T`), meticulously document all possible errors the function can return.
    * If error handling is non-trivial or requires specific recovery strategies, provide examples or clear instructions.

## Error Handling

Zig's error handling is explicit and designed for robustness. (Source: Zig 0.14.0 Documentation - Language Reference on Errors)

* **Error Unions & `try`/`catch`**:
    * Model recoverable failures using error unions (e.g., `!MyType`, which is equivalent to `error!MyType`). An error union can hold either a value of `MyType` or an error from the function's error set.
    * Propagate errors up the call stack using `try`. For example, `const result = try potentiallyFailingFunction();`. If the function returns an error, `try` will immediately return that error from the current function.
    * Handle errors explicitly using `catch`. For example, `potentiallyFailingFunction() catch |err| { // handle err }`. You can also capture the payload if the error is an error union itself: `potentiallyFailingFunction() catch |err| => |payload| { // handle err with payload }`.

* **Cleanup with `defer` and `errdefer`**:
    * `defer` schedules an expression to be executed when the current scope is exited, regardless of how it's exited (normal return, error return via `try`, or explicit `return`).
    * `errdefer` schedules an expression to be executed only if the current scope is exited due to an error.
    * Use these to guarantee resource cleanup (e.g., freeing memory, closing files) and maintain a leak-free state, especially in functions that can fail.
        ```zig
        const file = try std.fs.cwd().openFile("data.txt", .{});
        defer file.close(); // Ensures file is closed on normal exit or error exit.

        const data = try file.readToEndAlloc(allocator, 1024 * 1024);
        errdefer allocator.free(data); // Frees 'data' only if a subsequent 'try' fails.
        // ... process data ...
        return data;
        ```

* **Diagnostic Pattern for Context-Rich Errors**:
    * For operations where errors need more context than a simple error tag (e.g., parsing errors needing line/column numbers), consider having the fallible function take a mutable pointer to a diagnostic struct as an out-parameter. The function can then populate this struct with detailed information upon error. This avoids bloating the error set itself with many highly specific error variants.

* **Error Translation and Sets**:
    * Functions implicitly have an error set, which is the union of all errors they can return directly or propagate via `try`.
    * When calling functions from different modules or C libraries that may have their own error types/codes, translate these into the current function's or module's defined error set for consistency and clearer API contracts. This often involves a `switch` on the caught error.

## Memory Management

Zig provides manual memory management with a strong emphasis on explicitness and control. (Source: Zig 0.14.0 Documentation - Standard Library `std.mem.Allocator`, Release Notes on Unmanaged Containers)

* **Explicit Allocators**:
    * **Always pass a `std.mem.Allocator` instance** as a parameter to any function that needs to allocate memory dynamically.
    * Avoid global allocators. This makes memory usage explicit and allows users of a library to choose their allocation strategy (e.g., arena for temporary allocations, general-purpose allocator for long-lived data).
        ```zig
        fn createObject(allocator: std.mem.Allocator, size: usize) !*MyObject {
            const ptr = try allocator.create(MyObject);
            // ... initialize object ...
            return ptr;
        }
        ```

* **"Unmanaged" Style Containers (Prominent in 0.14.0)**:
    * The standard library increasingly favors "unmanaged" container types (e.g., `std.ArrayListUnmanaged`, `std.HashMapUnmanaged`, `std.StringHashMapUnmanaged`).
    * These containers *do not* store an allocator instance internally. Instead, the allocator must be passed to each method that might perform allocation (e.g., `append`, `put`).
    * This makes every allocation point explicit, aligns with Zig's philosophy, and can reduce the size of container structs.
    * Key differences and usage patterns:
        ```zig
        // Managed version - stores allocator internally
        var managed_list = std.ArrayList(u32).init(allocator);
        try managed_list.append(42);
        defer managed_list.deinit(); // No need to pass allocator for deinit

        // Unmanaged version - allocator passed to each method
        var unmanaged_list = std.ArrayListUnmanaged(u32){};
        try unmanaged_list.append(allocator, 42);
        defer unmanaged_list.deinit(allocator); // Must pass allocator for deinit

        // HashMap example
        var unmanaged_map = std.StringHashMapUnmanaged(u32){};
        try unmanaged_map.put(allocator, "key", 42);
        defer unmanaged_map.deinit(allocator);
        ```
    * Unmanaged containers are preferred for new code, while older "managed" variants that stored an allocator internally are maintained but less emphasized.

* **Resource Cleanup with `defer` and `errdefer`**:
    * As mentioned in Error Handling, use `defer` and `errdefer` consistently to pair allocation (`allocator.create`, `allocator.alloc`) with deallocation (`allocator.destroy`, `allocator.free`) within the same scope or logical unit of ownership.

* **Initialization Patterns**:
    * These are conventions with some flexibility in the Zig ecosystem, not rigid rules. The standard library and community practices show some variations:

    * **`init` / `deinit`**: Often used for types that manage resources *without necessarily allocating memory for themselves directly from a passed-in allocator at `init` time*, or for types that are primarily stack-allocated but need structured setup/teardown (e.g., an arena allocator itself is initialized). `deinit` handles releasing any resources acquired during `init` or usage.
        ```zig
        var fixed_buffer_allocator = std.heap.FixedBufferAllocator.init(buffer);
        const allocator = fixed_buffer_allocator.allocator();
        // ... use allocator ...
        // No explicit deinit for FixedBufferAllocator itself in this pattern usually,
        // as it just manages the provided buffer.
        ```
        Standard library containers like `ArrayList` also use this pattern despite managing heap memory:
        ```zig
        var list = std.ArrayList(i32).init(allocator);
        defer list.deinit(); // Releases any memory allocated by the list
        try list.append(42);
        ```
        And for unmanaged variants, `init` is optional and mostly for consistency:
        ```zig
        var list = std.ArrayListUnmanaged(i32){};
        // list.init(allocator); // Not needed for ArrayListUnmanaged, it's zero-initialized
        try list.append(gpa, 42);
        defer list.deinit(gpa);
        ```
    * **`createX` / `destroyX`**: Typically used for functions or types where the `create` operation itself performs an allocation for the primary resource or acquires an external resource (like a file handle, network socket). `destroyX` releases these.
        ```zig
        const my_object = try MyType.create(allocator, params);
        defer MyType.destroy(allocator, my_object);
        ```
    * The choice between these patterns often depends on ownership semantics, allocation responsibility, and API design goals. Some libraries may use other patterns like `new`/`free` for specific use cases. The key is consistency within a module or related API group.

* **Ownership Semantics**:
    * Clearly document and manage ownership of memory and other resources.
    * Prefer explicit transfer of ownership (e.g., a function allocating and returning memory that the caller must free) or clear borrowing rules (passing pointers/slices that don't imply ownership transfer).

* **Arena Allocators (`std.heap.ArenaAllocator`)**:
    * Highly recommended for managing groups of allocations that have the same lifetime.
    * You allocate many objects from the arena, and then free all of them at once by deinitializing the arena itself. This is efficient and helps prevent memory leaks for temporary or phase-based allocations.

* **Understanding `undefined`**:
    * Variables declared without an initial value are `undefined` by default (unless a default is specified in a struct, for example). Reading from `undefined` memory is a safety bug.
    * `undefined` can be useful for performance-critical scenarios where memory is guaranteed to be written to before being read (e.g., initializing a buffer that will be filled by an I/O operation).
    * However, for safety, prefer zero-initialization (`= 0`, `std.mem.zeroes()`, or `.{}` for structs if applicable) unless `undefined` provides a necessary and proven performance benefit in a well-understood context.

## `comptime`: Compile-Time Execution and Generics

Zig's `comptime` feature allows code to be executed at compile time, enabling powerful generics, reflection, and metaprogramming. (Source: Zig 0.14.0 Documentation - Language Reference on `comptime`)

* **`comptime` Parameters for Generics**:
    * Pass types, values, and other compile-time known entities as `comptime` parameters to functions to create generic data structures and algorithms.
        ```zig
        fn max(comptime T: type, a: T, b: T) T {
            return if (a > b) a else b;
        }
        const m = max(i32, 10, 20);
        ```
    * Keep compile-time logic straightforward. Complex `comptime` code can increase compile times, though Zig's compiler is optimized for this.

* **`comptime` Code Blocks**:
    * Code within a `comptime { ... }` block is executed at compile time. This can be used for assertions, static data generation, or complex initializations.

* **Type System Introspection (`@TypeOf`, `std.meta`)**:
    * Use `@TypeOf(value)` to get the type of a value at compile time.
    * The `std.meta` module provides functions for type introspection and manipulation (e.g., `std.meta.Tag`, `std.meta.fields`).
    * Use these tools to implement compile-time type constraints, traits, or perform type-based dispatch in generic code.

* **Reflection with Builtins**:
    * Use builtins like `@typeName(T: type) []const u8`, `@tagName(value: anytype) []const u8` (for enums/unions), `@field(StructType, field_name_comptime_string)`, and `@fieldParentPtr` for reflection-based generic code, such as serializers or generic data inspectors.

## Modules and Imports

Zig uses a simple file-based module system. (Source: Zig 0.14.0 Documentation - Language Reference on Imports)

* **Import Grouping**:
    * A common convention is to group `@import` statements:
        1.  Standard library (e.g., `const std = @import("std");`)
        2.  External packages/dependencies
        3.  Local project files
    * Within each group, sort imports alphabetically by the assigned constant name.

* **Files as Namespaces**:
    * Each Zig file (`.zig`) implicitly defines a struct type. Declarations within the file become fields of this struct.
    * Import a file using `const my_alias = @import("path/to/file.zig");`. Then access its public declarations via `my_alias.somePublicDecl`.

* **Visibility (`pub`)**:
    * Declarations (functions, variables, types) are private to the file by default.
    * Use the `pub` keyword to make a declaration accessible from other files that import the current file (e.g., `pub const MyPublicConstant = 123;`, `pub fn myPublicFunction() void {}`).
    * Global mutable variables (`var`) should generally not be `pub`. If external access is needed, provide accessor functions (`getX`, `setX`).

* **API Design**:
    * Expose a minimal, focused public API. Carefully consider what needs to be `pub`.
    * Use `opaque {}` types to hide implementation details of a struct if its internal layout should not be part of the public API. This allows changing the internals without breaking dependents.
        ```zig
        // my_module.zig
        const MyHandle = opaque {};
        pub fn createHandle() *MyHandle { /* ... */ }
        pub fn doSomethingWithHandle(handle: *MyHandle) void { /* ... */ }
        ```

* **Module Organization**:
    * Organize code into files and directories based on domain concepts or logical components rather than strictly by technical layers (e.g., group related data structures and their operations together).
    * Avoid overly deep directory hierarchies for simpler import paths.

## The Build System (`build.zig`)

Zig's build system is integrated, written in Zig itself, and highly flexible. (Source: Zig 0.14.0 Documentation - Build System, Release Notes for 0.14.0)

* **`build.zig` File**:
    * Every Zig project typically has a `build.zig` file in its root directory.
    * This file defines how the project is built, including compilation options, dependencies, tests, and custom build steps.
    * The entry point is a `pub fn build(b: *std.Build) void { ... }`.
    * Basic structure example:
        ```zig
        // build.zig
        const std = @import("std");

        pub fn build(b: *std.Build) void {
            // Standard target options
            const target = b.standardTargetOptions(.{});
            const optimize = b.standardOptimizeOption(.{});

            // Create an executable
            const exe = b.addExecutable(.{
                .name = "my_app",
                .root_source_file = .{ .path = "src/main.zig" },
                .target = target,
                .optimize = optimize,
            });

            // Make exe available via install step
            b.installArtifact(exe);

            // Add test step
            const unit_tests = b.addTest(.{
                .root_source_file = .{ .path = "src/main.zig" },
                .target = target,
                .optimize = optimize,
            });

            const run_unit_tests = b.addRunArtifact(unit_tests);

            // Create a test step that runs the tests
            const test_step = b.step("test", "Run unit tests");
            test_step.dependOn(&run_unit_tests.step);
        }
        ```

* **Key Concepts**:
    * **Artifacts**: Executables (`b.addExecutable(...)`), static libraries (`b.addStaticLibrary(...)`), shared libraries (`b.addSharedLibrary(...)`).
    * **Steps**: Actions performed during the build (e.g., compiling an artifact, running tests, copying files). Steps can depend on other steps.
    * **Options**: Setting target architecture, CPU features, optimization modes (`.ReleaseSmall`, `.ReleaseFast`, `.Debug`), enabling/disabling safety.
    * **Dependencies**: Adding dependencies on other Zig packages or C libraries. Zig 0.14.0 uses a structured approach with `.addModule` and `.addPackage` methods.
        ```zig
        // Adding a dependency on another package
        const dep = b.dependency("some_dependency", .{
            .target = target,
            .optimize = optimize,
        });
        exe.addModule("dep", dep.module("some_module"));

        // Another example with a C dependency
        exe.linkLibC();
        exe.addIncludePath(.{ .path = "path/to/includes" });
        exe.addCSourceFile(.{ .file = .{ .path = "path/to/source.c" }, .flags = &.{"-Wall"} });
        ```
    * **`LazyPath`**: Used for specifying file paths that might not exist until a previous build step creates them. Many build system APIs now require `LazyPath`.

* **Idiomatic Practices**:
    * Clearly define standard build steps (e.g., a default step to build the main artifact, a "test" step).
    * Make build options configurable (e.g., via `b.option(...)` for command-line flags).
        ```zig
        // Example of a configurable build option
        const enable_feature = b.option(bool, "feature", "Enable special feature") orelse false;
        if (enable_feature) {
            exe.defineCMacro("ENABLE_FEATURE", "1");
        }
        ```
    * Use the build system to manage C code compilation and linking if your project uses C interop.
    * Take advantage of **File System Watching** (`--watch` flag with `-fincremental`) for faster recompilation during development.
    * Use `addRunArtifact` for creating run steps that execute your binaries.

## Testing and Benchmarking

Zig has built-in testing support. (Source: Zig 0.14.0 Documentation - Language Reference on `test`)

* **Writing Tests**:
    * Define tests within `test "description" { ... }` blocks directly in your `.zig` source files.
    * Import the standard testing utilities: `const std = @import("std"); const expect = std.testing.expect; const expectEqual = std.testing.expectEqual;` etc.
    * Test blocks are compiled only when `zig build test` or `zig test` is invoked.

* **Running Tests**:
    * Use `zig build test` to compile and run all tests in a project defined by `build.zig`.
    * Use `zig test my_file.zig` to compile and run tests in a specific file.

* **Test Allocation**:
    * Use `std.testing.allocator` for any dynamic memory allocations needed within test blocks.
    * This allocator tracks allocations and automatically checks for memory leaks at the end of each test case. If leaks are detected, the test will fail.

* **Test Fixtures and Helpers**:
    * For common setup and teardown logic, create private helper functions within your test file or a dedicated test utility module.
    * Pass `std.testing.allocator` to these helpers if they need to allocate.

* **Benchmarking**:
    * For simple micro-benchmarks, `std.time.Timer` (or `std.builtin.Timer` in older versions, check specific 0.14 API for `std.time.Timer.start()` and `read()`) can be used to measure execution time of code paths.
        ```zig
        var timer = try std.time.Timer.start();
        // ... code to benchmark ...
        const elapsed_ns = timer.read();
        std.debug.print("Code took {d}ns\n", .{elapsed_ns});
        ```
    * For more structured benchmarking, consider external tools or writing focused test cases that iterate many times.

## C Interoperability (FFI)

Zig is designed for excellent C interoperability. (Source: Zig 0.14.0 Documentation - Working with C)

* **`@cImport`**:
    * Use `@cImport` to directly import C header files. This translates C declarations (types, functions, variables, macros) into their Zig equivalents.
        ```zig
        const c = @cImport({
            @cInclude("stdio.h");
            @cInclude("my_c_library.h");
        });
        ```
    * Zig can parse a large subset of C, including complex macros.

* **Wrapping C APIs**:
    * It's good practice to create safe Zig wrapper functions around C API calls.
    * These wrappers can:
        * Translate C error codes (often integers) into Zig error sets.
        * Convert C string conventions (null-terminated) to/from Zig slices.
        * Manage pointer nullability more explicitly using Zig's optionals.
        * Handle memory management for data passed to/from C.

* **Resource Ownership with C Libraries**:
    * Be meticulous about documenting and managing resource ownership when interacting with C libraries.
    * If a C function returns a pointer to allocated memory, the Zig wrapper must ensure it's correctly freed using the corresponding C library's free function.
    * If Zig allocates memory passed to C, ensure C doesn't try to free it with its own `free` unless specifically designed to do so.

* **C ABI Callbacks (Exporting Zig functions to C)**:
    * Use `export fn` to define Zig functions that can be called from C code. These functions will adhere to the C ABI.
        ```zig
        export fn zig_callback_function(arg1: c_int, arg2: [*c]const u8) void {
            // ...
        }
        ```
    * The 0.14.0 release notes state that the `@export` builtin operand is now a pointer, so for exporting functions or variables, you would use `&myFunction` or `&myVariable`.

* **Compiling and Linking Against C Code**:
    * Use the `build.zig` file to compile C source files and link against C libraries. Zig's build system can act as a C/C++ compiler.
        ```zig
        // In build.zig
        const exe = b.addExecutable(.{ .name = "my_app", .root_source_file = .{ .path = "src/main.zig" } });
        exe.addCSourceFile(.{ .file = .{ .path = "src/c_code.c" }, .flags = &.{"-Wall"} });
        exe.linkSystemLibrary("c"); // Link against libc
        exe.linkSystemLibrary("m"); // Link against libm
        ```

## API Design Patterns and Ergonomics

Idiomatic Zig 0.14.0 API design follows specific patterns that balance explicitness with usability. (Source: Zig 0.14.0 Standard Library patterns, community best practices)

* **Factory Functions and Builders**:
    * Use factory functions that return types for creating complex objects with configuration
    * Builder pattern using structs with methods for step-by-step construction
        ```zig
        // Factory function returning a type
        pub fn TensorBuilder(allocator: Allocator) type {
            return struct {
                const Self = @This();
                allocator: Allocator,
                core: *Core,
                
                pub fn init(allocator: Allocator, core: *Core) Self {
                    return Self{ .allocator = allocator, .core = core };
                }
                
                pub fn zeros(self: *Self, shape: []const i64) !NodeId {
                    // Implementation with explicit allocator usage
                }
            };
        }
        ```

* **Namespace Organization**:
    * Use zero-field structs as namespaces to group related functions
    * Provide both low-level and high-level APIs through structured re-exports
        ```zig
        pub const tensor = struct {
            // High-level convenience functions
            pub const zeros = creation.zeros;
            pub const reshape = manipulation.reshape;
            
            // Modules available for advanced usage
            pub const creation = @import("creation.zig");
            pub const manipulation = @import("manipulation.zig");
        };
        ```

* **Method Chaining (Explicit Error Handling)**:
    * Allow method chaining while keeping explicit error handling
    * Return wrapper structs that carry context between operations
        ```zig
        pub const TensorHandle = struct {
            core: *Core,
            node_id: NodeId,
            
            pub fn reshape(self: TensorHandle, new_shape: []const i64) !TensorHandle {
                const new_node = try manipulation.reshape(self.core, self.node_id, new_shape);
                return TensorHandle{ .core = self.core, .node_id = new_node };
            }
        };
        ```

* **Configuration Structs**:
    * Use structs with default values for complex function parameters
    * Leverage Zig's struct literal syntax for clean APIs
        ```zig
        pub const TensorConfig = struct {
            dtype: DataType = .f32,
            device: Device = .cpu,
            requires_grad: bool = false,
        };
        
        pub fn zeros(allocator: Allocator, shape: []const i64, config: TensorConfig) !Tensor {
            // Implementation
        }
        
        // Usage: clean syntax with defaults
        const tensor = try zeros(allocator, &[_]i64{2, 3}, .{});
        const tensor_f64 = try zeros(allocator, &[_]i64{2, 3}, .{ .dtype = .f64 });
        ```

* **Slice-Based APIs**:
    * Prefer slices over variadic functions for better type safety
    * Use comptime for known-size arrays when appropriate
        ```zig
        // Prefer this
        pub fn createTensor(allocator: Allocator, shape: []const i64) !Tensor
        
        // Over variadic approaches
        ```

* **Error Context Patterns**:
    * Provide detailed error information without bloating error sets
    * Use diagnostic structs for complex error scenarios
        ```zig
        pub const ShapeError = struct {
            expected_rank: usize,
            actual_rank: usize,
            operation: []const u8,
        };
        
        pub fn reshape(tensor: Tensor, shape: []const i64, diagnostic: ?*ShapeError) !Tensor {
            // Populate diagnostic on error without changing error set
        }
        ```

* **Resource Management Patterns**:
    * Use RAII-style patterns with explicit cleanup
    * Combine arena allocators with structured resource management
        ```zig
        pub const ComputeContext = struct {
            arena: ArenaAllocator,
            core: Core,
            
            pub fn init(backing_allocator: Allocator) !ComputeContext {
                var arena = ArenaAllocator.init(backing_allocator);
                const core = try Core.init(arena.allocator());
                return ComputeContext{ .arena = arena, .core = core };
            }
            
            pub fn deinit(self: *ComputeContext) void {
                self.core.deinit();
                self.arena.deinit();
            }
            
            pub fn allocator(self: *ComputeContext) Allocator {
                return self.arena.allocator();
            }
        };
        ```

* **Type-Safe Enums and Unions**:
    * Use enums for configuration options instead of booleans or magic numbers
    * Leverage tagged unions for type-safe variant handling
        ```zig
        pub const OptimizationLevel = enum {
            none,
            basic,
            aggressive,
        };
        
        pub const TensorData = union(enum) {
            f32: []f32,
            f64: []f64,
            i32: []i32,
            
            pub fn dtype(self: TensorData) DataType {
                return switch (self) {
                    .f32 => .f32,
                    .f64 => .f64,
                    .i32 => .i32,
                };
            }
        };
        ```

* **Composable APIs**:
    * Design functions to work well together through consistent interfaces
    * Use common parameter patterns across related functions
        ```zig
        // Consistent allocator-first pattern
        pub fn zeros(allocator: Allocator, shape: []const i64) !Tensor
        pub fn ones(allocator: Allocator, shape: []const i64) !Tensor
        pub fn full(allocator: Allocator, shape: []const i64, value: f32) !Tensor
        ```

* **Anti-Patterns to Avoid**:
    * **Hidden Allocations**: Never hide allocator usage from the caller
        ```zig
        // ❌ BAD - Hidden global allocator
        pub fn createTensor(shape: []const i64) !Tensor {
            const data = try global_allocator.alloc(f32, calculateSize(shape));
            // ...
        }
        
        // ✅ GOOD - Explicit allocator parameter
        pub fn createTensor(allocator: Allocator, shape: []const i64) !Tensor {
            const data = try allocator.alloc(f32, calculateSize(shape));
            // ...
        }
        ```
    
    * **Silent Error Handling**: Avoid APIs that hide errors or use sentinel values
        ```zig
        // ❌ BAD - Silent error handling
        pub fn divide(a: f32, b: f32) f32 {
            if (b == 0) return std.math.nan(f32); // Hidden error
        }
        
        // ✅ GOOD - Explicit error handling
        pub fn divide(a: f32, b: f32) !f32 {
            if (b == 0) return error.DivisionByZero;
            return a / b;
        }
        ```
    
    * **Method Chaining with Hidden Errors**: Don't hide error handling in fluent interfaces
        ```zig
        // ❌ BAD - Hidden error handling in method chains
        tensor.reshape(&[_]i64{6}).transpose().slice(...);  // Errors are hidden!
        
        // ✅ GOOD - Explicit error handling at each step
        const reshaped = try tensor.reshape(&[_]i64{6});
        const transposed = try reshaped.transpose();
        const sliced = try transposed.slice(...);
        
        // ✅ ALSO GOOD - Explicit error handling with method chaining
        pub const TensorHandle = struct {
            pub fn reshape(self: TensorHandle, shape: []const i64) !TensorHandle {
                // Explicit error return - each step can fail
            }
        };
        ```
    
    * **Over-Generic APIs**: Avoid unnecessary generics that complicate usage
        ```zig
        // ❌ BAD - Unnecessary complexity
        pub fn process(comptime T: type, comptime U: type, data: T) U {
            // When simple concrete types would suffice
        }
        
        // ✅ GOOD - Use generics only when truly needed
        pub fn processData(data: []const f32) []f32
        ```

## Key Zig 0.14.0 Specific Features and Idioms

(Source: Zig 0.14.0 Release Notes)

* **Labeled `switch` statements**:
    * These allow `break` and `continue` to target specific enclosing `switch` statements, which is particularly useful for implementing state machines more cleanly.
    * The 0.14.0 release notes mention this resulted in a tokenizer performance improvement.
        ```zig
        // Example from release notes context
        OuterSwitch: switch (state) {
            .Start => |payload| {
                // ...
                switch (payload.event) {
                    .SomeEvent => continue :OuterSwitch, // Continue the outer switch
                    // ...
                }
            },
        }
        ```

* **`@branchHint` builtin**:
    * Replaces the older `@setCold`. Provides more fine-grained control for performance tuning by giving hints to the compiler about the likelihood of branches.
    * Example: `@branchHint(.Likely)` or `@branchHint(.Unlikely)` within an `if` condition's block or on a `while` condition.
    * Use sparingly and based on profiling.

* **ZON (Zig Object Notation) Support (`std.json.zon` or `std.zig.zon`)**:
    * The standard library has improved support for ZON, a superset of JSON that is also valid Zig syntax for anonymous struct literals.
    * Use `std.json.parseFromSlice` with `std.json.Ast.parseZon` for parsing ZON, and `std.json.stringify` for serializing data structures to ZON/JSON.
    * This is useful for configuration files and data interchange where human readability and Zig literal compatibility are desired.

* **`@export` operand is now a pointer**:
    * When using `@export` to expose symbols for C linkage, you now pass a pointer to the function or variable:
        ```zig
        var my_global: i32 = 10;
        fn my_func() void {}

        // In build.zig or another appropriate place for export definitions:
        // For a global variable:
        // @export(&my_global, .{ .name = "exported_my_global", .linkage = .Strong });
        // For a function:
        // @export(&my_func, .{ .name = "exported_my_func", .linkage = .Strong });
        ```
        (Note: The exact syntax for using `@export` in this manner is typically handled by build options or specific `@export` declarations for functions/variables intended for C ABI compatibility, ensure to check latest docs for usage in 0.14.0). For functions to be callable from C, the `export fn` syntax is more direct.
