// Root exports for Zing V2

// Core infrastructure
pub const Core = @import("core/core.zig").Core;
pub const types = @import("core/types.zig");
pub const errors = @import("core/errors.zig");

// Engines (some are in core, some are external)
pub const SymbolicEngine = @import("core/symbolic/engine.zig").SymbolicEngine;
pub const ShapeEngine = @import("core/shape/engine.zig").ShapeEngine;
pub const GraphEngine = @import("core/graph/engine.zig").GraphEngine;
// TODO: Add when compiler module is available
// pub const CompilerEngine = @import("compiler/engine.zig").CompilerEngine;

// TODO: Add when backends are available
// pub const cpu = @import("backends/cpu/executor.zig");

// Tensor operations layer
pub const tensor = @import("tensor/mod.zig");

// Neural network components
// pub const nn = @import("nn/mod.zig");