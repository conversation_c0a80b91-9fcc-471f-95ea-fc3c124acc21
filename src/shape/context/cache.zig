/// Shape Operation Caching Module
///
/// This module implements an efficient caching system for shape operations
/// using hash-based content-addressable storage with pre-computed hashes.
/// Features include:
///
/// - Content-addressable storage for O(1) lookups
/// - Pre-computed hash values for performance
/// - LRU/FIFO/Random eviction policies
/// - Performance statistics tracking
/// - Specialized hashing for shape operations
/// - Thread-safe access if needed
const std = @import("std");
const types = @import("../types.zig");
const shape_options = @import("../shape_options.zig");
const symbolic = @import("symbolic");

/// Import types for convenience
const ShapeTracker = types.ShapeTracker;
const ShapeOp = types.ShapeOp;
const OpNode = types.OpNode;
const CacheKey = types.CacheKey;
const CacheEntry = types.CacheEntry;
const Cache = types.Cache;
const Dim = types.Dim;
const Expr = types.Expr;
const ShapeContext = types.ShapeContext;
const Allocator = std.mem.Allocator;

/// Advanced cache statistics
pub const CacheMetrics = struct {
    hits: usize = 0,
    misses: usize = 0,
    evictions: usize = 0,
    lookups: usize = 0,
    inserts: usize = 0,
    time_saved_ns: u64 = 0,
    peak_usage: usize = 0,
};

/// Cache eviction policy
pub const EvictionPolicy = enum {
    /// Least Recently Used - evict oldest accessed entries
    lru,

    /// First In First Out - evict oldest inserted entries
    fifo,

    /// Random Replacement - evict random entries
    random,
};

/// Computes a comprehensive cache key for a shape operation
/// This is more detailed than the basic key computation in types.zig
pub fn computeOpKey(op: *const OpNode) CacheKey {
    var hasher = std.hash.Wyhash.init(0);

    // Hash operation type
    const op_tag: u8 = @intFromEnum(@as(std.meta.Tag(ShapeOp), op.opData));
    std.hash.autoHash(&hasher, op_tag);

    // Hash previous operations depth
    std.hash.autoHash(&hasher, op.depth);

    // Hash previous operations pointer
    if (op.previousOps) |prev| {
        std.hash.autoHash(&hasher, @intFromPtr(prev));
    } else {
        std.hash.autoHash(&hasher, @as(usize, 0));
    }

    // Hash operation-specific data
    switch (op.opData) {
        .reshape => |data| {
            // Hash dimensions
            for (data.dims) |dim| {
                hashDimension(&hasher, dim);
            }
        },
        .slice => |data| {
            // Hash axis and slice parameters
            std.hash.autoHash(&hasher, data.axis);
            hashExpression(&hasher, data.start);
            hashExpression(&hasher, data.end);
            hashExpression(&hasher, data.step);
        },
        .transpose => |data| {
            // Hash permutation
            for (data.perm) |p| {
                hashExpression(&hasher, p);
            }
        },
        .broadcast => |data| {
            // Hash target dimensions
            for (data.dims) |dim| {
                hashDimension(&hasher, dim);
            }
        },
        .expand => |data| {
            // Hash axis and dimension
            std.hash.autoHash(&hasher, data.axis);
            hashDimension(&hasher, data.dim);
        },
        .squeeze => |data| {
            // Hash axis
            std.hash.autoHash(&hasher, data.axis);
        },
        .concat => |data| {
            // Hash axis and shapes
            std.hash.autoHash(&hasher, data.axis);
            for (data.shapes) |shape| {
                std.hash.autoHash(&hasher, @intFromPtr(shape.lastOpNode));
            }
        },
    }

    // Generate final hash
    const hash = hasher.final();

    return CacheKey{
        .hash_value = hash,
        .sourceId = hash, // Use hash as source ID for determinism
    };
}

/// Compute a cache key for a shape tracker's current state
pub fn computeTrackerKey(tracker: *const ShapeTracker) CacheKey {
    var hasher = std.hash.Wyhash.init(0);

    // Hash operation chain (just the pointer, since OpNode content is immutable)
    if (tracker.lastOpNode) |node| {
        std.hash.autoHash(&hasher, @intFromPtr(node));
    } else {
        std.hash.autoHash(&hasher, @as(usize, 0));
    }

    // Hash initial dimensions
    for (tracker.initialDims) |dim| {
        hashDimension(&hasher, dim);
    }

    // Hash view properties
    std.hash.autoHash(&hasher, tracker.is_view);
    if (tracker.base_offset) |offset| {
        hashExpression(&hasher, offset);
    }

    // Generate final hash
    const hash = hasher.final();

    return CacheKey{
        .hash_value = hash,
        .sourceId = hash,
    };
}

/// Compute a cache key for a shape operation on a specific tracker
pub fn computeOpOnTrackerKey(op: *const ShapeOp, tracker: *const ShapeTracker) CacheKey {
    var hasher = std.hash.Wyhash.init(0);

    // Hash operation type
    const op_tag: u8 = @intFromEnum(@as(std.meta.Tag(ShapeOp), op.*));
    std.hash.autoHash(&hasher, op_tag);

    // Hash the tracker's key
    const tracker_key = computeTrackerKey(tracker);
    std.hash.autoHash(&hasher, tracker_key.hash_value);

    // Hash operation-specific data
    switch (op.*) {
        .reshape => |data| {
            // Hash dimensions
            for (data.dims) |dim| {
                hashDimension(&hasher, dim);
            }
        },
        .slice => |data| {
            // Hash axis and slice parameters
            std.hash.autoHash(&hasher, data.axis);
            hashExpression(&hasher, data.start);
            hashExpression(&hasher, data.end);
            hashExpression(&hasher, data.step);
        },
        .transpose => |data| {
            // Hash permutation
            for (data.perm) |p| {
                hashExpression(&hasher, p);
            }
        },
        .broadcast => |data| {
            // Hash target dimensions
            for (data.dims) |dim| {
                hashDimension(&hasher, dim);
            }
        },
        .expand => |data| {
            // Hash axis and dimension
            std.hash.autoHash(&hasher, data.axis);
            hashDimension(&hasher, data.dim);
        },
        .squeeze => |data| {
            // Hash axis
            std.hash.autoHash(&hasher, data.axis);
        },
        .concat => |data| {
            // Hash axis and shapes
            std.hash.autoHash(&hasher, data.axis);
            for (data.shapes) |shape| {
                const shape_key = computeTrackerKey(shape);
                std.hash.autoHash(&hasher, shape_key.hash_value);
            }
        },
    }

    // Generate final hash
    const hash = hasher.final();

    return CacheKey{
        .hash_value = hash,
        .sourceId = hash,
    };
}

/// Hash a dimension value for consistent key generation
pub fn hashDimension(hasher: *std.hash.Wyhash, dim: Dim) void {
    // Hash the tag first to distinguish between concrete and symbolic
    const tag: u8 = @intFromEnum(@as(std.meta.Tag(Dim), dim));
    std.hash.autoHash(hasher, tag);

    // Hash the content based on type
    switch (dim) {
        .concrete => |v| std.hash.autoHash(hasher, v),
        .symbolic => |expr| hashExpression(hasher, expr),
    }
}

/// Hash a symbolic expression for consistent key generation
fn hashExpression(hasher: *std.hash.Wyhash, expr: Expr) void {
    // Handle null expressions
    if (!symbolic.validateExpr(@ptrCast(@alignCast(@constCast(expr))))) {
        std.hash.autoHash(hasher, @as(u64, 0));
        return;
    }

    // Hash a fixed value for the tag since we can't access it directly
    // This is a simplification but should work for most cases
    std.hash.autoHash(hasher, @as(u8, 1));

    // Since we can't access the tag directly in the opaque type,
    // we'll just hash the pointer value as a simplification
    std.hash.autoHash(hasher, @intFromPtr(expr));
}

/// Store a result in the cache if caching is enabled
pub fn storeInCache(ctx: *ShapeContext, key: CacheKey, result: *ShapeTracker) !void {
    if (!ctx.options.enable_caching) return;

    // Create a new cache entry
    const entry = CacheEntry{
        .result = result,
        .hits = 0,
    };

    // If we're at capacity, consider eviction
    if (ctx.cache.entries.count() >= ctx.options.max_cached_shapes) {
        try evictFromCache(ctx);
    }

    // Store in cache - handle potential out of memory error
    try ctx.cache.entries.put(ctx.allocator, key, entry);

    // Update LRU tracking if using LRU policy
    if (ctx.options.eviction_policy == .lru or ctx.options.eviction_policy == .fifo) {
        // Add to LRU list
        var lru_list = &ctx.cache.lru_entries;
        try lru_list.append(ctx.allocator, key);
    }
}

/// Look up a result in the cache
pub fn lookupInCache(ctx: *ShapeContext, key: CacheKey) ?*ShapeTracker {
    if (!ctx.options.enable_caching) return null;

    // Look up the key in the cache
    const entry_ptr = ctx.cache.entries.getPtr(key) orelse return null;

    // Record the hit and update stats
    entry_ptr.hits += 1;

    // Update LRU tracking if using LRU policy
    if (ctx.options.eviction_policy == .lru) {
        // Move to end of LRU list (most recently used)
        var lru_list = &ctx.cache.lru_entries;

        // Find and remove the key
        for (lru_list.items, 0..) |lru_key, i| {
            if (lru_key.sourceId == key.sourceId) {
                _ = lru_list.orderedRemove(i);
                break;
            }
        }

        // Add to the end (most recently used)
        lru_list.append(ctx.allocator, key) catch {
            // If append fails, just continue - not critical
        };
    }

    return entry_ptr.result;
}

/// Evict entries from the cache based on policy
fn evictFromCache(ctx: *ShapeContext) !void {
    if (ctx.cache.entries.count() == 0) return;

    const policy = ctx.options.eviction_policy;

    switch (policy) {
        .lru, .fifo => {
            var lru_list = &ctx.cache.lru_entries;

            // Get the oldest entry (first in list)
            if (lru_list.items.len > 0) {
                const key = lru_list.orderedRemove(0);
                _ = ctx.cache.entries.fetchRemove(key);
            } else {
                // Fall back to random if LRU list is empty
                try evictRandomEntry(ctx);
            }
        },
        .random => {
            try evictRandomEntry(ctx);
        },
    }
}

/// Helper to evict a random entry
fn evictRandomEntry(ctx: *ShapeContext) !void {
    // Get all keys
    var keys = std.ArrayListUnmanaged(CacheKey){};
    defer keys.deinit(ctx.allocator);

    // Collect all keys
    var it = ctx.cache.entries.keyIterator();
    while (it.next()) |key| {
        try keys.append(ctx.allocator, key.*);
    }

    // Pick a random key
    if (keys.items.len > 0) {
        // Use a fast hash of current nanotime for pseudo-randomness
        const current_time = std.time.nanoTimestamp();
        // First truncate to i64, then cast to u64 to avoid the i128->u64 error
        const random_seed = @as(u64, @intCast(@as(i64, @truncate(current_time))));
        const random_index = @mod(random_seed, keys.items.len);

        const key = keys.items[random_index];
        _ = ctx.cache.entries.fetchRemove(key);
    }
}

/// Clear the cache but keep capacity
pub fn clearCache(ctx: *ShapeContext) void {
    ctx.cache.entries.clearRetainingCapacity();

    // Clear LRU tracking
    if (ctx.options.eviction_policy == .lru or ctx.options.eviction_policy == .fifo) {
        ctx.cache.lru_entries.clearRetainingCapacity();
    }
}

/// Initialize LRU tracking for the cache if needed
pub fn initLruTracking(ctx: *ShapeContext) !void {
    if (ctx.options.eviction_policy == .lru or ctx.options.eviction_policy == .fifo) {
        // Ensure LRU tracking list has required capacity
        try ctx.cache.lru_entries.ensureTotalCapacity(ctx.allocator, ctx.options.max_cached_shapes);
    }
}

/// Get comprehensive cache statistics
pub fn getCacheStats(ctx: *const ShapeContext) CacheMetrics {
    const stats = CacheMetrics{
        .hits = ctx.stats.cache_hits,
        .misses = ctx.stats.cache_misses,
        .evictions = ctx.stats.cache_evictions,
        .lookups = ctx.stats.cache_hits + ctx.stats.cache_misses,
        .inserts = ctx.stats.cache_inserts,
        .time_saved_ns = ctx.stats.cache_time_saved_ns,
        .peak_usage = ctx.stats.cache_peak_usage,
    };

    return stats;
}

/// Record time spent computing a result (for measuring cache benefits)
pub fn recordComputationTime(ctx: *ShapeContext, start_time: i128, key: CacheKey) void {
    if (!ctx.options.enable_stats) return;

    const end_time = std.time.nanoTimestamp();
    const elapsed = @as(u64, @intCast(end_time - start_time));

    // Store time for this operation
    if (ctx.cache.entries.getPtr(key)) |entry| {
        entry.computation_time_ns = elapsed;
    }

    // Update total time stats
    ctx.stats.total_computation_time_ns += elapsed;
}

/// Record time saved by cache hit
pub fn recordCacheHit(ctx: *ShapeContext, key: CacheKey) void {
    if (!ctx.options.enable_stats) return;

    ctx.stats.cache_hits += 1;

    // Record time saved (if we have a previous computation time)
    if (ctx.cache.entries.getPtr(key)) |entry| {
        if (entry.computation_time_ns > 0) {
            ctx.stats.cache_time_saved_ns += entry.computation_time_ns;
        }
    }
}

/// Record cache miss
pub fn recordCacheMiss(ctx: *ShapeContext) void {
    if (!ctx.options.enable_stats) return;

    ctx.stats.cache_misses += 1;
}

/// Initialize cached operations for commonly used shapes
pub fn initCommonShapeCaching(ctx: *ShapeContext) !void {
    if (!ctx.options.enable_caching) return;

    // This function can be used to pre-populate the cache with common
    // operations like identity reshape/transpose, which are very frequent
    // in neural networks

    // Currently a placeholder for future optimization
    // We properly check ctx.options.enable_caching above, so we use ctx
}
