const std = @import("std");
const testing = std.testing;
const shape = @import("shape");
const symbolic = @import("symbolic");
// Use the debug_print via the shape module to avoid duplicate imports
const debug = shape.debug_print;
// Import test helpers
const test_helpers = @import("test_helpers.zig");
const createTestContext = test_helpers.createTestContext;
const destroyTestContext = test_helpers.destroyTestContext;

// This file consolidates tests for integration between the shape module and symbolic module
// Includes tests for:
// - Symbolic shape tracking
// - Symbolic dimension handling
// - Integration between shape operations and symbolic expressions
// - Shape tracking with symbolic dimensions

// Test basic symbolic shape initialization
test "symbolic: create shape with symbolic dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");

    // Create a shape with symbolic dimensions
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.SymExpr{ a, b });
    defer ctx.destroyTracker(tracker);

    // Verify the dimensions
    try testing.expectEqual(@as(usize, 2), tracker.rank());
}

test "symbolic: reshape with symbolic dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");
    const c = try ctx.createConstant(4);

    // a*b = reshapeDim (unknown value)
    const product = try ctx.createBinaryOp(.Mul, a, b);

    // Create a shape with symbolic dimensions [a, b]
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b });
    defer ctx.destroyTracker(tracker);

    // Reshape to [product/c, c] = [a*b/4, 4]
    const div = try ctx.createBinaryOp(.Div, product, c);
    try ctx.applySymbolicReshape(tracker, &[_]symbolic.Expr{ div, c });

    // Verify the new shape
    try testing.expectEqual(@as(usize, 2), tracker.rank());

    // Check that a*b/4 * 4 = a*b (dimension preservation)
    const dim_product = try ctx.createBinaryOp(.Mul, div, c);
    try testing.expect(try ctx.exprsEqual(product, dim_product));
}

test "symbolic: broadcast with symbolic dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const one = try ctx.createConstant(1);

    // Create shapes [1, a] and [b, a]
    const tracker1 = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ one, a });
    defer ctx.destroyTracker(tracker1);

    const b = try ctx.createVariable("b");
    const tracker2 = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ b, a });
    defer ctx.destroyTracker(tracker2);

    // Broadcast [1, a] to [b, a]
    try ctx.applySymbolicBroadcast(tracker1, &[_]symbolic.Expr{ b, a });

    // Verify the result
    try testing.expectEqual(@as(usize, 2), tracker1.rank());

    // Since the first dimension was originally 1, it should have a stride of 0 after broadcast
    try testing.expectEqual(@as(isize, 0), tracker1.getSymbolicStrides()[0]);

    // Shape equality check
    const shape_equal = try ctx.shapesEqual(tracker1, tracker2);
    try testing.expect(shape_equal);
}

test "symbolic: slice with symbolic dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");

    // Create a shape [a, b]
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b });
    defer ctx.destroyTracker(tracker);

    // Create start and end indices for slicing
    const start_idx = try ctx.createConstant(1);
    const a_minus_1 = try ctx.createBinaryOp(.Sub, a, try ctx.createConstant(1));

    // Slice from [1, 0] to [a-1, b]
    try ctx.applySymbolicSlice(tracker, &[_]symbolic.Expr{ start_idx, try ctx.createConstant(0) }, &[_]symbolic.Expr{ a_minus_1, b });

    // Verify dimensions: [a-1-1, b-0] = [a-2, b]
    const expected_dim0 = try ctx.createBinaryOp(.Sub, a, try ctx.createConstant(2));
    try testing.expect(try ctx.exprsEqual(expected_dim0, tracker.getSymbolicDims()[0]));
    try testing.expect(try ctx.exprsEqual(b, tracker.getSymbolicDims()[1]));

    // Verify offset is updated: 1*stride[0] + 0*stride[1]
    const expected_offset = try ctx.createBinaryOp(.Mul, start_idx, try ctx.createFromValue(tracker.getSymbolicStrides()[0]));
    try testing.expect(try ctx.exprsEqual(expected_offset, tracker.getSymbolicOffset()));
}

test "symbolic: permutation with symbolic dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");
    const c = try ctx.createVariable("c");

    // Create a shape [a, b, c]
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b, c });
    defer ctx.destroyTracker(tracker);

    // Apply permutation [2, 0, 1] (c, a, b)
    try ctx.applySymbolicPermutation(tracker, &[_]usize{ 2, 0, 1 });

    // Verify dimensions
    try testing.expect(try ctx.exprsEqual(c, tracker.getSymbolicDims()[0]));
    try testing.expect(try ctx.exprsEqual(a, tracker.getSymbolicDims()[1]));
    try testing.expect(try ctx.exprsEqual(b, tracker.getSymbolicDims()[2]));

    // Verify strides are updated correctly
    try testing.expectEqual(@as(isize, 1), tracker.getSymbolicStrides()[0]);
    const b_times_c = try ctx.createBinaryOp(.Mul, b, c);
    const expected_stride = try ctx.createFromValue(tracker.getSymbolicStrides()[1]);
    try testing.expect(try ctx.exprsEqual(b_times_c, expected_stride));
}

test "symbolic: indexing with symbolic dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");

    // Create a shape [a, b]
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b });
    defer ctx.destroyTracker(tracker);

    // Create indices
    const i = try ctx.createVariable("i");
    const j = try ctx.createVariable("j");

    // Calculate offset for indices [i, j]
    const offset = try ctx.mapSymbolicIndicesToOffset(tracker, &[_]symbolic.Expr{ i, j });

    // Expected offset: i*b + j
    const i_times_b = try ctx.createBinaryOp(.Mul, i, b);
    const expected = try ctx.createBinaryOp(.Add, i_times_b, j);

    try testing.expect(try ctx.exprsEqual(expected, offset));
}

test "symbolic: constrained dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");

    // Add constraint a > 0
    const zero = try ctx.createConstant(0);
    const constraint = try ctx.createConstraint(.GreaterThan, a, zero);
    try ctx.addConstraint(constraint);

    // Create a shape [a, b]
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b });
    defer ctx.destroyTracker(tracker);

    // Add constraint b = 2*a
    const two = try ctx.createConstant(2);
    const two_a = try ctx.createBinaryOp(.Mul, two, a);
    const eq_constraint = try ctx.createConstraint(.Equal, b, two_a);
    try ctx.addConstraint(eq_constraint);

    // Now reshape to [b, a] which should be valid since we know b = 2*a
    try ctx.applySymbolicReshape(tracker, &[_]symbolic.Expr{ b, a });

    // Verify the reshape result
    try testing.expect(try ctx.exprsEqual(b, tracker.getSymbolicDims()[0]));
    try testing.expect(try ctx.exprsEqual(a, tracker.getSymbolicDims()[1]));
}

test "symbolic: complex operations chain" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");
    const c = try ctx.createConstant(2);

    // Create a shape [a, b]
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b });
    defer ctx.destroyTracker(tracker);

    // 1. First slice it to [a-1, b-1]
    const one = try ctx.createConstant(1);
    const a_minus_1 = try ctx.createBinaryOp(.Sub, a, one);
    const b_minus_1 = try ctx.createBinaryOp(.Sub, b, one);

    try ctx.applySymbolicSlice(tracker, &[_]symbolic.Expr{ one, one }, &[_]symbolic.Expr{ a, b });

    // 2. Then permute [a-1, b-1] -> [b-1, a-1]
    try ctx.applySymbolicPermutation(tracker, &[_]usize{ 1, 0 });

    // 3. Then broadcast to [b-1, a-1, c] (add a new dimension)
    const new_shape = &[_]symbolic.Expr{ b_minus_1, a_minus_1, c };
    try ctx.applySymbolicBroadcast(tracker, new_shape);

    // Verify final dimensions
    try testing.expectEqual(@as(usize, 3), tracker.rank());
    try testing.expect(try ctx.exprsEqual(b_minus_1, tracker.getSymbolicDims()[0]));
    try testing.expect(try ctx.exprsEqual(a_minus_1, tracker.getSymbolicDims()[1]));
    try testing.expect(try ctx.exprsEqual(c, tracker.getSymbolicDims()[2]));

    // Verify strides - the new dimension should have stride 0
    try testing.expectEqual(@as(isize, 0), tracker.getSymbolicStrides()[2]);
}

test "symbolic: integration with non-symbolic operations" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a non-symbolic tracker
    const tracker = try ctx.createShapeTracker(&[_]usize{ 10, 20 });
    defer ctx.destroyTracker(tracker);

    // Convert to symbolic tracker
    const sym_tracker = try ctx.convertToSymbolic(tracker);
    defer ctx.destroyTracker(sym_tracker);

    // Verify the conversion
    try testing.expectEqual(@as(usize, 2), sym_tracker.rank());

    const ten = try ctx.createConstant(10);
    const twenty = try ctx.createConstant(20);

    try testing.expect(try ctx.exprsEqual(ten, sym_tracker.getSymbolicDims()[0]));
    try testing.expect(try ctx.exprsEqual(twenty, sym_tracker.getSymbolicDims()[1]));

    // Apply symbolic operations
    const a = try ctx.createVariable("a");
    try ctx.applySymbolicBroadcast(sym_tracker, &[_]symbolic.Expr{ a, twenty });

    // Verify the result
    try testing.expect(try ctx.exprsEqual(a, sym_tracker.getSymbolicDims()[0]));
    try testing.expect(try ctx.exprsEqual(twenty, sym_tracker.getSymbolicDims()[1]));

    // Check strides - first dimension should now have stride 0
    try testing.expectEqual(@as(isize, 0), sym_tracker.getSymbolicStrides()[0]);
}

test "symbolic: simplification and evaluation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");

    // Create a shape with a complex expression: [a + b, a * b]
    const sum = try ctx.createBinaryOp(.Add, a, b);
    const product = try ctx.createBinaryOp(.Mul, a, b);

    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ sum, product });
    defer ctx.destroyTracker(tracker);

    // Evaluate with a=3, b=4
    try ctx.bindVariable(a, 3);
    try ctx.bindVariable(b, 4);

    const evaluated = try ctx.evaluateSymbolicTracker(tracker);
    defer ctx.destroyTracker(evaluated);

    // Expected: [3+4, 3*4] = [7, 12]
    try testing.expectEqualSlices(usize, &[_]usize{ 7, 12 }, evaluated.dims);
}

test "symbolic: error cases and validation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create symbolic variables
    const a = try ctx.createVariable("a");
    const b = try ctx.createVariable("b");
    const negative_one = try ctx.createConstant(-1);

    // Try to create a tracker with a negative dimension
    const invalid_dim = try ctx.createBinaryOp(.Mul, a, negative_one);
    testing.expectError(error.InvalidSymbolicDimension, ctx.createSymbolicTracker(&[_]symbolic.Expr{ invalid_dim, b }));

    // Create valid tracker
    const tracker = try ctx.createSymbolicTracker(&[_]symbolic.Expr{ a, b });
    defer ctx.destroyTracker(tracker);

    // Invalid reshape (dimensions don't match)
    const c = try ctx.createVariable("c");
    testing.expectError(error.InvalidSymbolicReshape, ctx.applySymbolicReshape(tracker, &[_]symbolic.Expr{c}));

    // Invalid broadcast (incompatible dimensions)
    const two = try ctx.createConstant(2);
    const two_a = try ctx.createBinaryOp(.Mul, two, a);
    testing.expectError(error.IncompatibleBroadcastDimensions, ctx.applySymbolicBroadcast(tracker, &[_]symbolic.Expr{ two_a, c }));
}
