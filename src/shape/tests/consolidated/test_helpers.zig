/// Test Helpers for Consolidated Tests
///
/// This file provides helper functions to bridge the old and new APIs
/// for the consolidated tests. It allows the tests to run with the current
/// API implementation while maintaining the same test structure.
const std = @import("std");
const shape = @import("shape");
const symbolic = @import("symbolic");
const shape_options = @import("shape_options");

/// ShapeContext wrapper that provides the old API methods
pub const TestContext = struct {
    ctx: *shape.ShapeContext,
    symCtx: *symbolic.SymbolicContext,
    allocator: std.mem.Allocator,

    /// Create a new TestContext
    pub fn init(allocator: std.mem.Allocator) !TestContext {
        const ctx = try shape.createContext(allocator);
        const symCtx = symbolic.fromCommonContext(ctx.symbolicCtx);
        return TestContext{
            .ctx = ctx,
            .symCtx = symCtx,
            .allocator = allocator,
        };
    }

    /// Create a new TestContext with options
    pub fn initWithOptions(allocator: std.mem.Allocator, options: shape.Options) !TestContext {
        const ctx = try shape.createContextWithOptions(allocator, options);
        const symCtx = symbolic.fromCommonContext(ctx.symbolicCtx);
        return TestContext{
            .ctx = ctx,
            .symCtx = symCtx,
            .allocator = allocator,
        };
    }

    /// Destroy the TestContext
    pub fn deinit(self: *TestContext) void {
        shape.destroyContext(self.ctx);
    }

    /// Create a ShapeTracker with concrete dimensions
    pub fn createShapeTracker(self: *TestContext, dims: []const usize) !*shape.ShapeTracker {
        // Convert usize array to Dim array
        const dimArray = try self.allocator.alloc(shape.Dim, dims.len);
        defer self.allocator.free(dimArray);

        for (dims, 0..) |d, i| {
            dimArray[i] = shape.dim(self.ctx, d);
        }

        return shape.tracker(self.ctx, dimArray);
    }

    /// Create a symbolic variable
    pub fn createVariable(self: *TestContext, name: []const u8) !symbolic.SymExpr {
        return symbolic.symbol(self.symCtx, name);
    }

    /// Create a symbolic constant
    pub fn createConstant(self: *TestContext, value: i64) !symbolic.SymExpr {
        return symbolic.constant(self.symCtx, @intCast(value));
    }

    /// Create a binary operation
    pub fn createBinaryOp(self: *TestContext, op: symbolic.BinaryOp, left: symbolic.SymExpr, right: symbolic.SymExpr) !symbolic.SymExpr {
        return symbolic.binaryOp(self.symCtx, op, left, right);
    }

    /// Create a symbolic tracker
    pub fn createSymbolicTracker(self: *TestContext, dims: []const symbolic.SymExpr) !*shape.ShapeTracker {
        // Convert SymExpr array to Dim array
        const dimArray = try self.allocator.alloc(shape.Dim, dims.len);
        defer self.allocator.free(dimArray);

        for (dims, 0..) |d, i| {
            const commonExpr = symbolic.toCommonNode(d);
            dimArray[i] = shape.Dim{ .symbolic = commonExpr };
        }

        return shape.tracker(self.ctx, dimArray);
    }

    /// Destroy a ShapeTracker
    pub fn destroyTracker(self: *TestContext, tracker: *shape.ShapeTracker) void {
        shape.destroyTracker(self.ctx, tracker);
    }

    /// Apply reshape operation
    pub fn applyReshape(self: *TestContext, tracker: *shape.ShapeTracker, new_dims: []const usize) !void {
        // Convert usize array to Dim array
        const dimArray = try self.allocator.alloc(shape.Dim, new_dims.len);
        defer self.allocator.free(dimArray);

        for (new_dims, 0..) |d, i| {
            dimArray[i] = shape.dim(self.ctx, d);
        }

        // Create a new tracker with the reshape operation
        const new_tracker = try shape.reshape(self.ctx, tracker, dimArray);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Apply permutation operation
    pub fn applyPermutation(self: *TestContext, tracker: *shape.ShapeTracker, axes: []const usize) !void {
        // Create a new tracker with the permute operation
        const new_tracker = try shape.permute(self.ctx, tracker, axes);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Apply slice operation
    pub fn applySlice(self: *TestContext, tracker: *shape.ShapeTracker, start: []const usize, end: []const usize) !void {
        // Create a new tracker with the slice operation
        const new_tracker = try shape.slice(self.ctx, tracker, start, end, null, null);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Apply broadcast operation
    pub fn applyBroadcast(self: *TestContext, tracker: *shape.ShapeTracker, new_dims: []const usize) !void {
        // Convert usize array to Dim array
        const dimArray = try self.allocator.alloc(shape.Dim, new_dims.len);
        defer self.allocator.free(dimArray);

        for (new_dims, 0..) |d, i| {
            dimArray[i] = shape.dim(self.ctx, d);
        }

        // Create a new tracker with the broadcast operation
        const new_tracker = try shape.broadcast(self.ctx, tracker, dimArray);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Apply expand operation
    pub fn applyExpand(self: *TestContext, tracker: *shape.ShapeTracker, new_dims: []const usize) !void {
        // Convert usize array to Dim array
        const dimArray = try self.allocator.alloc(shape.Dim, new_dims.len);
        defer self.allocator.free(dimArray);

        for (new_dims, 0..) |d, i| {
            dimArray[i] = shape.dim(self.ctx, d);
        }

        // Create a new tracker with the expand operation
        const new_tracker = try shape.expand(self.ctx, tracker, dimArray, null);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Apply squeeze operation
    pub fn applySqueeze(self: *TestContext, tracker: *shape.ShapeTracker) !void {
        // Create a new tracker with the squeeze operation
        const new_tracker = try shape.squeeze(self.ctx, tracker, null);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Apply squeeze operation with specific dimensions
    pub fn applySqueezeWithDims(self: *TestContext, tracker: *shape.ShapeTracker, dims: []const usize) !void {
        // Create a new tracker with the squeeze operation
        const new_tracker = try shape.squeeze(self.ctx, tracker, dims);

        // Copy the new tracker's data to the original tracker
        tracker.* = new_tracker.*;

        // Destroy the new tracker (but not its data)
        self.allocator.destroy(new_tracker);
    }

    /// Create a concatenation of trackers
    pub fn createConcatenation(self: *TestContext, trackers: []const *shape.ShapeTracker, dim: usize) !*shape.ShapeTracker {
        return shape.concat(self.ctx, trackers, dim);
    }

    /// Create an immutable slice
    pub fn sliceImmutable(self: *TestContext, tracker: *shape.ShapeTracker, start: []const usize, end: []const usize) !*shape.ShapeTracker {
        return shape.slice(self.ctx, tracker, start, end);
    }

    /// Create an immutable permutation
    pub fn permuteImmutable(self: *TestContext, tracker: *shape.ShapeTracker, axes: []const usize) !*shape.ShapeTracker {
        return shape.permute(self.ctx, tracker, axes);
    }

    /// Create an immutable reshape
    pub fn reshapeImmutable(self: *TestContext, tracker: *shape.ShapeTracker, new_dims: []const usize) !*shape.ShapeTracker {
        // Convert usize array to Dim array
        const dimArray = try self.allocator.alloc(shape.Dim, new_dims.len);
        defer self.allocator.free(dimArray);

        for (new_dims, 0..) |d, i| {
            dimArray[i] = shape.dim(self.ctx, d);
        }

        return shape.reshape(self.ctx, tracker, dimArray);
    }

    /// Create a view of a tracker
    pub fn createView(self: *TestContext, tracker: *shape.ShapeTracker) !*shape.ShapeTracker {
        // Clone the tracker
        return shape.tracker(self.ctx, tracker.dims());
    }

    /// Map indices to offset
    pub fn mapIndicesToOffset(self: *TestContext, tracker: *shape.ShapeTracker, indices: []const usize) !isize {
        _ = self; // Use self to avoid unused parameter warning
        var offset: isize = tracker.offset;
        const strides = tracker.strides;

        for (indices, 0..) |idx, i| {
            offset += @as(isize, @intCast(idx)) * strides[i];
        }

        return offset;
    }

    /// Check if two expressions are equal
    pub fn exprsEqual(self: *TestContext, a: symbolic.SymExpr, b: symbolic.SymExpr) !bool {
        return symbolic.exprsEqual(self.symCtx, a, b);
    }

    /// Check if two shapes are equal
    pub fn shapesEqual(self: *TestContext, a: *shape.ShapeTracker, b: *shape.ShapeTracker) !bool {
        if (a.rank() != b.rank()) return false;

        const a_dims = a.dims();
        const b_dims = b.dims();

        for (a_dims, 0..) |dim_a, i| {
            const dim_b = b_dims[i];

            // Compare concrete dimensions directly
            if (dim_a == .concrete and dim_b == .concrete) {
                if (dim_a.concrete != dim_b.concrete) return false;
            } else if (dim_a == .symbolic and dim_b == .symbolic) {
                // For symbolic dimensions, we need to check if they're equivalent
                const expr_a = symbolic.fromCommonNode(dim_a.symbolic);
                const expr_b = symbolic.fromCommonNode(dim_b.symbolic);
                if (!try self.exprsEqual(expr_a, expr_b)) return false;
            } else {
                // One is concrete, one is symbolic
                return false;
            }
        }

        return true;
    }
};

/// Create a test context for testing
pub fn createTestContext(allocator: std.mem.Allocator) !*TestContext {
    const test_ctx = try allocator.create(TestContext);
    test_ctx.* = try TestContext.init(allocator);
    return test_ctx;
}

/// Create a test context with options for testing
pub fn createTestContextWithOptions(allocator: std.mem.Allocator, options: shape.Options) !*TestContext {
    const test_ctx = try allocator.create(TestContext);
    test_ctx.* = try TestContext.initWithOptions(allocator, options);
    return test_ctx;
}

/// Destroy a test context
pub fn destroyTestContext(ctx: *TestContext) void {
    ctx.deinit();
    ctx.allocator.destroy(ctx);
}
