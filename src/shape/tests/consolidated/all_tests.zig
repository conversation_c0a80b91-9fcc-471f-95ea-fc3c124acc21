const std = @import("std");

// This file consolidates all shape module tests
// Run all tests with: zig test src/shape/tests/consolidated/all_tests.zig

// Core functionality tests
pub usingnamespace @import("core_tests.zig");

// Memory management and allocation tests
pub usingnamespace @import("memory_tests.zig");

// Shape operations tests
pub usingnamespace @import("operations_tests.zig");

// Integration with symbolic module
pub usingnamespace @import("integration_tests.zig");

// Feature flags and configuration tests
pub usingnamespace @import("feature_flags_tests.zig");

// Immutable operations tests
pub usingnamespace @import("immutable_operations_tests.zig");

test "meta: all test modules can be imported" {
    // This test ensures all test modules compile and can be imported
    std.testing.refAllDeclsRecursive(@This());
}
