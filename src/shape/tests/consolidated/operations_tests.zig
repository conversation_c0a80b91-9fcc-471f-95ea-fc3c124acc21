const std = @import("std");
const testing = std.testing;
const shape = @import("shape");
const symbolic = @import("symbolic");
const shape_options = @import("shape_options");

// This file tests the shape operations API
// It focuses on the immutable operations pattern where each operation
// returns a new ShapeTracker rather than modifying the existing one.

// Helper function to create a context for testing
fn createTestContext(allocator: std.mem.Allocator) !*shape.ShapeContext {
    return shape.createContext(allocator);
}

// Helper function to destroy a context
fn destroyTestContext(ctx: *shape.ShapeContext) void {
    shape.destroyContext(ctx);
}

// Helper function to create a shape tracker with concrete dimensions
fn createTracker(ctx: *shape.ShapeContext, dims: []const usize) !*shape.ShapeTracker {
    // Convert usize array to Dim array
    var dim_array = try ctx.allocator.alloc(shape.Dim, dims.len);
    defer ctx.allocator.free(dim_array);

    for (dims, 0..) |d, i| {
        dim_array[i] = shape.dim(ctx, d);
    }

    return shape.tracker(ctx, dim_array);
}

// Helper function to verify dimensions
fn verifyDims(tracker: *shape.ShapeTracker, expected_dims: []const usize) !void {
    const dims = tracker.dims();
    try testing.expectEqual(expected_dims.len, dims.len);

    for (expected_dims, 0..) |expected, i| {
        try testing.expectEqual(expected, dims[i].concrete);
    }
}

// This file consolidates tests for all shape operations
// Includes tests for:
// - Basic shape operations (reshape, permute, etc.)
// - View operations (operations that don't modify the underlying data)
// - Immutable operations (operations that create new shapes)
// - Additional operation tests and edge cases

// Test reshape operations
test "reshape: basic reshaping" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test reshaping a 6-element tensor from [2, 3] to [3, 2]
    const tracker = try createTracker(ctx, &[_]usize{ 2, 3 });
    defer shape.destroyTracker(ctx, tracker);

    // Create new dimensions for reshape
    var new_dims = try ctx.allocator.alloc(shape.Dim, 2);
    defer ctx.allocator.free(new_dims);
    new_dims[0] = shape.dim(ctx, 3);
    new_dims[1] = shape.dim(ctx, 2);

    // Apply reshape operation (immutable)
    const reshaped = try shape.reshape(ctx, tracker, new_dims);
    defer shape.destroyTracker(ctx, reshaped);

    // Verify the dimensions
    try verifyDims(reshaped, &[_]usize{ 3, 2 });
}

test "reshape: invalid reshape should error" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a 24-element tensor
    const tracker = try createTracker(ctx, &[_]usize{ 2, 3, 4 });
    defer shape.destroyTracker(ctx, tracker);

    // Try to reshape to 8 elements (should fail)
    var new_dims = try ctx.allocator.alloc(shape.Dim, 3);
    defer ctx.allocator.free(new_dims);
    new_dims[0] = shape.dim(ctx, 2);
    new_dims[1] = shape.dim(ctx, 2);
    new_dims[2] = shape.dim(ctx, 2);

    // This should fail because 2*2*2 = 8 != 24
    try testing.expectError(error.InvalidReshape, shape.reshape(ctx, tracker, new_dims));
}

// Test permute/transpose operations
test "permute: simple transpose" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test transposing a [2, 3] tensor to [3, 2]
    const tracker = try createTracker(ctx, &[_]usize{ 2, 3 });
    defer shape.destroyTracker(ctx, tracker);

    // Apply permutation operation (immutable)
    const permuted = try shape.permute(ctx, tracker, &[_]usize{ 1, 0 });
    defer shape.destroyTracker(ctx, permuted);

    // Verify the dimensions
    try verifyDims(permuted, &[_]usize{ 3, 2 });
}

test "permute: 3D permutation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test permuting a [2, 3, 4] tensor
    const tracker = try createTracker(ctx, &[_]usize{ 2, 3, 4 });
    defer shape.destroyTracker(ctx, tracker);

    // Apply permutation operation (immutable)
    const permuted = try shape.permute(ctx, tracker, &[_]usize{ 2, 0, 1 });
    defer shape.destroyTracker(ctx, permuted);

    // Verify the dimensions
    try verifyDims(permuted, &[_]usize{ 4, 2, 3 });
}

test "permute: invalid permutation should error" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    const tracker = try createTracker(ctx, &[_]usize{ 2, 3, 4 });
    defer shape.destroyTracker(ctx, tracker);

    // Duplicate axis
    try testing.expectError(error.InvalidPermutation, shape.permute(ctx, tracker, &[_]usize{ 0, 0, 2 }));

    // Wrong number of axes
    try testing.expectError(error.InvalidPermutation, shape.permute(ctx, tracker, &[_]usize{ 0, 1 }));

    // Out of bounds axis
    try testing.expectError(error.InvalidPermutation, shape.permute(ctx, tracker, &[_]usize{ 0, 1, 3 }));
}

// Test slice operations
test "slice: basic slicing" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test slicing a [10, 10] tensor to get [5, 5] starting at [2, 3]
    const tracker = try createTracker(ctx, &[_]usize{ 10, 10 });
    defer shape.destroyTracker(ctx, tracker);

    // Apply slice operation using the helper function with concrete values
    const sliced = try shape.sliceWithConcreteValues(ctx, tracker, 0, 2, 7, 1);
    defer shape.destroyTracker(ctx, sliced);

    // Apply another slice on the second dimension
    const sliced2 = try shape.sliceWithConcreteValues(ctx, sliced, 1, 3, 8, 1);
    defer shape.destroyTracker(ctx, sliced2);

    // Verify the dimensions
    try verifyDims(sliced2, &[_]usize{ 5, 5 });
}

test "slice: invalid slice should error" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    const tracker = try createTracker(ctx, &[_]usize{ 10, 10 });
    defer shape.destroyTracker(ctx, tracker);

    // Out of bounds end
    try testing.expectError(error.SliceOutOfBounds, shape.sliceWithConcreteValues(ctx, tracker, 0, 5, 11, 1));

    // End before start
    try testing.expectError(error.SliceOutOfBounds, shape.sliceWithConcreteValues(ctx, tracker, 0, 8, 7, 1));
}

// Test broadcast operations
test "broadcast: simple broadcasting" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test broadcasting [1, 3] to [2, 3]
    const tracker = try createTracker(ctx, &[_]usize{ 1, 3 });
    defer shape.destroyTracker(ctx, tracker);

    // Create target tracker for broadcast
    const target = try createTracker(ctx, &[_]usize{ 2, 3 });
    defer shape.destroyTracker(ctx, target);

    // Apply broadcast operation (immutable)
    const broadcasted = try shape.broadcastTo(ctx, tracker, target);
    defer shape.destroyTracker(ctx, broadcasted);

    // Verify the dimensions
    try verifyDims(broadcasted, &[_]usize{ 2, 3 });
}

test "broadcast: invalid broadcast should error" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Cannot broadcast [2, 3] to [2, 4] (3 != 4 and 3 != 1)
    const tracker = try createTracker(ctx, &[_]usize{ 2, 3 });
    defer shape.destroyTracker(ctx, tracker);

    // Create target tracker for broadcast
    const target = try createTracker(ctx, &[_]usize{ 2, 4 });
    defer shape.destroyTracker(ctx, target);

    // This should fail because 3 cannot broadcast to 4
    try testing.expectError(error.BroadcastMismatch, shape.broadcastTo(ctx, tracker, target));
}

// Test expand operations
test "expand: basic expansion" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test expanding [3] to [1, 3, 1]
    const tracker = try createTracker(ctx, &[_]usize{3});
    defer shape.destroyTracker(ctx, tracker);

    // Apply expand operations (immutable)
    // First expand at axis 0 to add a dimension of size 1
    const dim1 = shape.dim(ctx, 1);
    const expanded1 = try shape.expand(ctx, tracker, 0, dim1);
    defer shape.destroyTracker(ctx, expanded1);

    // Then expand at axis 2 to add another dimension of size 1
    const dim2 = shape.dim(ctx, 1);
    const expanded = try shape.expand(ctx, expanded1, 2, dim2);
    defer shape.destroyTracker(ctx, expanded);

    // Verify the dimensions
    try verifyDims(expanded, &[_]usize{ 1, 3, 1 });
}

// Test operation composition (chains of operations)
test "composition: multiple operations" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a shape and apply multiple operations
    const tracker = try createTracker(ctx, &[_]usize{ 10, 10 });
    defer shape.destroyTracker(ctx, tracker);

    // Slice [10, 10] -> [5, 10] using the helper function with concrete values
    const sliced = try shape.sliceWithConcreteValues(ctx, tracker, 0, 2, 7, 1);
    defer shape.destroyTracker(ctx, sliced);

    // Slice [5, 10] -> [5, 5] using the helper function with concrete values
    const sliced2 = try shape.sliceWithConcreteValues(ctx, sliced, 1, 2, 7, 1);
    defer shape.destroyTracker(ctx, sliced2);

    // Transpose [5, 5] -> [5, 5] (but with different strides)
    const transposed = try shape.permute(ctx, sliced2, &[_]usize{ 1, 0 });
    defer shape.destroyTracker(ctx, transposed);

    // Reshape [5, 5] -> [25]
    var new_dims = try ctx.allocator.alloc(shape.Dim, 1);
    defer ctx.allocator.free(new_dims);
    new_dims[0] = shape.dim(ctx, 25);

    const reshaped = try shape.reshape(ctx, transposed, new_dims);
    defer shape.destroyTracker(ctx, reshaped);

    // Verify the dimensions
    try verifyDims(reshaped, &[_]usize{25});
}

// Test squeeze operations
test "squeeze: basic squeeze" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test squeezing [1, 3, 1] to [3]
    const tracker = try ctx.createShapeTracker(&[_]usize{ 1, 3, 1 });
    defer ctx.destroyTracker(tracker);

    try ctx.applySqueeze(tracker);
    const dims = tracker.dims();
    try testing.expectEqual(@as(usize, 1), dims.len);
    try testing.expectEqual(@as(usize, 3), dims[0].concrete);
    // Strides are no longer directly accessible
    // try testing.expectEqualSlices(isize, &[_]isize{1}, tracker.strides);
}

test "squeeze: partial squeeze with dimensions" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Test squeezing only specified dimensions
    const tracker = try ctx.createShapeTracker(&[_]usize{ 1, 3, 1, 4, 1 });
    defer ctx.destroyTracker(tracker);

    try ctx.applySqueezeWithDims(tracker, &[_]usize{ 0, 2 }); // Squeeze dimensions 0 and 2
    const dims = tracker.dims();
    try testing.expectEqual(@as(usize, 3), dims.len);
    try testing.expectEqual(@as(usize, 3), dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), dims[1].concrete);
    try testing.expectEqual(@as(usize, 1), dims[2].concrete);
}

// Test concatenation operations
test "concat: basic concatenation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create two trackers to concatenate
    const tracker1 = try ctx.createShapeTracker(&[_]usize{ 2, 3 });
    defer ctx.destroyTracker(tracker1);

    const tracker2 = try ctx.createShapeTracker(&[_]usize{ 2, 4 });
    defer ctx.destroyTracker(tracker2);

    // Concatenate along dimension 1
    const concatTracker = try ctx.createConcatenation(&[_]*shape.ShapeTracker{ tracker1, tracker2 }, 1);
    defer ctx.destroyTracker(concatTracker);

    const dims = concatTracker.dims();
    try testing.expectEqual(@as(usize, 2), dims.len);
    try testing.expectEqual(@as(usize, 2), dims[0].concrete);
    try testing.expectEqual(@as(usize, 7), dims[1].concrete);
}

test "concat: multi-dimensional concatenation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create three 3D trackers to concatenate
    const tracker1 = try ctx.createShapeTracker(&[_]usize{ 2, 3, 4 });
    defer ctx.destroyTracker(tracker1);

    const tracker2 = try ctx.createShapeTracker(&[_]usize{ 2, 3, 5 });
    defer ctx.destroyTracker(tracker2);

    const tracker3 = try ctx.createShapeTracker(&[_]usize{ 2, 3, 3 });
    defer ctx.destroyTracker(tracker3);

    // Concatenate along dimension 2
    const concatTracker = try ctx.createConcatenation(&[_]*shape.ShapeTracker{ tracker1, tracker2, tracker3 }, 2);
    defer ctx.destroyTracker(concatTracker);

    const dims = concatTracker.dims();
    try testing.expectEqual(@as(usize, 3), dims.len);
    try testing.expectEqual(@as(usize, 2), dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), dims[1].concrete);
    try testing.expectEqual(@as(usize, 12), dims[2].concrete);
}

test "concat: invalid concatenation should error" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create trackers with incompatible shapes for concatenation
    const tracker1 = try ctx.createShapeTracker(&[_]usize{ 2, 3 });
    defer ctx.destroyTracker(tracker1);

    const tracker2 = try ctx.createShapeTracker(&[_]usize{ 3, 4 });
    defer ctx.destroyTracker(tracker2);

    // Try to concatenate along dimension 1, should fail because dim 0 doesn't match
    try testing.expectError(error.IncompatibleConcatDimensions, ctx.createConcatenation(&[_]*shape.ShapeTracker{ tracker1, tracker2 }, 1));
}

// Test immutable operations (operations that create new shapes)
test "immutable: chain operations and verify original unchanged" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create original tracker
    const original = try ctx.createShapeTracker(&[_]usize{ 10, 10 });
    defer ctx.destroyTracker(original);

    // Create a derived tracker through immutable operations
    const sliced = try ctx.sliceImmutable(original, &[_]usize{ 2, 2 }, &[_]usize{ 7, 7 });
    defer ctx.destroyTracker(sliced);

    const transposed = try ctx.permuteImmutable(sliced, &[_]usize{ 1, 0 });
    defer ctx.destroyTracker(transposed);

    const reshaped = try ctx.reshapeImmutable(transposed, &[_]usize{ 5, 5 });
    defer ctx.destroyTracker(reshaped);

    // Verify original is still [10, 10]
    const origDims = original.dims();
    try testing.expectEqual(@as(usize, 2), origDims.len);
    try testing.expectEqual(@as(usize, 10), origDims[0].concrete);
    try testing.expectEqual(@as(usize, 10), origDims[1].concrete);
    // Strides are no longer directly accessible
    // try testing.expectEqualSlices(isize, &[_]isize{ 10, 1 }, original.strides);

    // Verify final result is [5, 5]
    const reshapedDims = reshaped.dims();
    try testing.expectEqual(@as(usize, 2), reshapedDims.len);
    try testing.expectEqual(@as(usize, 5), reshapedDims[0].concrete);
    try testing.expectEqual(@as(usize, 5), reshapedDims[1].concrete);
}

// Test operation sequences with verification
test "sequence: verify indexing after operations" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a 3x3 tensor
    const tracker = try ctx.createShapeTracker(&[_]usize{ 3, 3 });
    defer ctx.destroyTracker(tracker);

    // Apply transpose
    try ctx.applyPermutation(tracker, &[_]usize{ 1, 0 });

    // Verify some index mappings
    // In a 3x3 tensor with normal layout, the element at [1,2] would be at position 1*3+2 = 5
    // After transpose, the element at [1,2] should be at position that corresponds to [2,1] in the original,
    // which is 2*3+1 = 7
    const result1 = try ctx.mapIndicesToOffset(tracker, &[_]usize{ 1, 2 });
    try testing.expectEqual(@as(isize, 7), result1);

    // Apply a slice starting at [1,1]
    try ctx.applySlice(tracker, &[_]usize{ 1, 1 }, &[_]usize{ 3, 3 });

    // Now verify the mapping again
    // Before slicing, [0,0] mapped to position [1,1] in the transposed tensor, which is [1,1] in the original
    // So [0,0] should now map to 1*3+1 = 4 plus the offset from slicing
    const result2 = try ctx.mapIndicesToOffset(tracker, &[_]usize{ 0, 0 });
    try testing.expectEqual(@as(isize, 4), result2);
}

// Test view operations specifically
test "view: operations that create views" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a view of a transposed tensor
    const original = try ctx.createShapeTracker(&[_]usize{ 3, 4 });
    defer ctx.destroyTracker(original);

    // Create transposed view
    const view = try ctx.createView(original);
    defer ctx.destroyTracker(view);

    try ctx.applyPermutation(view, &[_]usize{ 1, 0 });

    // Verify original is unchanged
    const origDims = original.dims();
    try testing.expectEqual(@as(usize, 2), origDims.len);
    try testing.expectEqual(@as(usize, 3), origDims[0].concrete);
    try testing.expectEqual(@as(usize, 4), origDims[1].concrete);
    try testing.expectEqualSlices(isize, &[_]isize{ 4, 1 }, original.strides);

    // Verify view is transposed
    const viewDims = view.dims();
    try testing.expectEqual(@as(usize, 2), viewDims.len);
    try testing.expectEqual(@as(usize, 4), viewDims[0].concrete);
    try testing.expectEqual(@as(usize, 3), viewDims[1].concrete);
    try testing.expectEqualSlices(isize, &[_]isize{ 1, 4 }, view.strides);

    // Verify both point to the same memory
    try testing.expect(view.offset == original.offset);
}
