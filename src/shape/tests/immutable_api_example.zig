const std = @import("std");
const testing = std.testing;
const shape = @import("../shape.zig");
const symbolic = @import("../../symbolic/symbolic.zig");

// This file demonstrates the correct usage of the shape module's immutable API
// following the design principles outlined in the README.
//
// Key principles:
// 1. Immutable operations that return new trackers
// 2. Proper symbolic dimension handling
// 3. Context-based resource management
// 4. Correct cross-module integration

// Helper function to create a context for testing
fn createTestContext(allocator: std.mem.Allocator) !*shape.ShapeContext {
    return shape.createContext(allocator);
}

// Helper function to destroy a context
fn destroyTestContext(ctx: *shape.ShapeContext) void {
    shape.destroyContext(ctx);
}

// Test basic shape creation and dimension access
test "immutable: basic shape creation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create dimensions using the idiomatic API
    var dims = [_]shape.Dim{
        shape.dim(ctx, 2),
        shape.dim(ctx, 3),
    };

    // Create a shape tracker
    const tracker = try shape.tracker(ctx, &dims);
    defer shape.destroyTracker(ctx, tracker);

    // Verify dimensions
    const retrieved_dims = tracker.dims();
    try testing.expectEqual(@as(usize, 2), retrieved_dims.len);
    try testing.expectEqual(@as(usize, 2), retrieved_dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), retrieved_dims[1].concrete);
}

// Test reshape operation using the immutable API
test "immutable: reshape operation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a 2x3 shape
    var dims = [_]shape.Dim{
        shape.dim(ctx, 2),
        shape.dim(ctx, 3),
    };
    const tracker = try shape.tracker(ctx, &dims);
    defer shape.destroyTracker(ctx, tracker);

    // Create new dimensions for reshape (3x2)
    var new_dims = [_]shape.Dim{
        shape.dim(ctx, 3),
        shape.dim(ctx, 2),
    };

    // Apply reshape operation (immutable)
    const reshaped = try shape.reshape(ctx, tracker, &new_dims);
    defer shape.destroyTracker(ctx, reshaped);

    // Verify the original tracker is unchanged
    const orig_dims = tracker.dims();
    try testing.expectEqual(@as(usize, 2), orig_dims.len);
    try testing.expectEqual(@as(usize, 2), orig_dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), orig_dims[1].concrete);

    // Verify the new tracker has the reshaped dimensions
    const new_tracker_dims = reshaped.dims();
    try testing.expectEqual(@as(usize, 2), new_tracker_dims.len);
    try testing.expectEqual(@as(usize, 3), new_tracker_dims[0].concrete);
    try testing.expectEqual(@as(usize, 2), new_tracker_dims[1].concrete);
}

// Test permute (transpose) operation using the immutable API
test "immutable: permute operation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a 2x3 shape
    var dims = [_]shape.Dim{
        shape.dim(ctx, 2),
        shape.dim(ctx, 3),
    };
    const tracker = try shape.tracker(ctx, &dims);
    defer shape.destroyTracker(ctx, tracker);

    // Apply permute operation (immutable)
    const permuted = try shape.permute(ctx, tracker, &[_]usize{1, 0});
    defer shape.destroyTracker(ctx, permuted);

    // Verify the original tracker is unchanged
    const orig_dims = tracker.dims();
    try testing.expectEqual(@as(usize, 2), orig_dims.len);
    try testing.expectEqual(@as(usize, 2), orig_dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), orig_dims[1].concrete);

    // Verify the new tracker has the permuted dimensions
    const new_tracker_dims = permuted.dims();
    try testing.expectEqual(@as(usize, 2), new_tracker_dims.len);
    try testing.expectEqual(@as(usize, 3), new_tracker_dims[0].concrete);
    try testing.expectEqual(@as(usize, 2), new_tracker_dims[1].concrete);
}

// Test slice operation using the immutable API with symbolic expressions
test "immutable: slice operation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a 10x10 shape
    var dims = [_]shape.Dim{
        shape.dim(ctx, 10),
        shape.dim(ctx, 10),
    };
    const tracker = try shape.tracker(ctx, &dims);
    defer shape.destroyTracker(ctx, tracker);

    // Create symbolic expressions for slice parameters
    const start1 = try symbolic.integer(ctx.symbolicCtx, 2);
    const end1 = try symbolic.integer(ctx.symbolicCtx, 7);
    const step1 = try symbolic.integer(ctx.symbolicCtx, 1);

    // Apply slice operation (immutable)
    const sliced = try shape.slice(ctx, tracker, 0, start1, end1, step1);
    defer shape.destroyTracker(ctx, sliced);

    // Apply another slice on the second dimension
    const start2 = try symbolic.integer(ctx.symbolicCtx, 3);
    const end2 = try symbolic.integer(ctx.symbolicCtx, 8);
    const step2 = try symbolic.integer(ctx.symbolicCtx, 1);

    const sliced2 = try shape.slice(ctx, sliced, 1, start2, end2, step2);
    defer shape.destroyTracker(ctx, sliced2);

    // Verify the original tracker is unchanged
    const orig_dims = tracker.dims();
    try testing.expectEqual(@as(usize, 2), orig_dims.len);
    try testing.expectEqual(@as(usize, 10), orig_dims[0].concrete);
    try testing.expectEqual(@as(usize, 10), orig_dims[1].concrete);

    // Verify the first slice dimensions
    const sliced_dims = sliced.dims();
    try testing.expectEqual(@as(usize, 2), sliced_dims.len);
    try testing.expectEqual(@as(usize, 5), sliced_dims[0].concrete); // 7-2=5
    try testing.expectEqual(@as(usize, 10), sliced_dims[1].concrete);

    // Verify the second slice dimensions
    const sliced2_dims = sliced2.dims();
    try testing.expectEqual(@as(usize, 2), sliced2_dims.len);
    try testing.expectEqual(@as(usize, 5), sliced2_dims[0].concrete);
    try testing.expectEqual(@as(usize, 5), sliced2_dims[1].concrete); // 8-3=5
}

// Test broadcast operation using the immutable API
test "immutable: broadcast operation" {
    const ctx = try createTestContext(testing.allocator);
    defer destroyTestContext(ctx);

    // Create a 1x3 shape
    var dims1 = [_]shape.Dim{
        shape.dim(ctx, 1),
        shape.dim(ctx, 3),
    };
    const tracker1 = try shape.tracker(ctx, &dims1);
    defer shape.destroyTracker(ctx, tracker1);

    // Create a 2x3 shape as target
    var dims2 = [_]shape.Dim{
        shape.dim(ctx, 2),
        shape.dim(ctx, 3),
    };
    const tracker2 = try shape.tracker(ctx, &dims2);
    defer shape.destroyTracker(ctx, tracker2);

    // Apply broadcast operation (immutable)
    const broadcasted = try shape.broadcastTo(ctx, tracker1, tracker2);
    defer shape.destroyTracker(ctx, broadcasted);

    // Verify the original tracker is unchanged
    const orig_dims = tracker1.dims();
    try testing.expectEqual(@as(usize, 2), orig_dims.len);
    try testing.expectEqual(@as(usize, 1), orig_dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), orig_dims[1].concrete);

    // Verify the new tracker has the broadcast dimensions
    const new_tracker_dims = broadcasted.dims();
    try testing.expectEqual(@as(usize, 2), new_tracker_dims.len);
    try testing.expectEqual(@as(usize, 2), new_tracker_dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), new_tracker_dims[1].concrete);
}
