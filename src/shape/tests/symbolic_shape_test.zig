/// Shape Module - Symbolic Shape Integration Test
///
/// This file tests integration between shape and symbolic modules,
/// particularly focusing on symbolic dimensions and constraints.
const std = @import("std");
const shape = @import("shape");
const symbolic = @import("symbolic");

const Dim = shape.Dim;
const ShapeTracker = shape.ShapeTracker;

test "symbolic_shape:basic" {
    const testing = std.testing;
    const allocator = testing.allocator;

    // Create context
    const ctx = try shape.createContext(allocator);
    defer shape.destroyContext(ctx);

    // Create a symbolic expression for testing
    // Convert from common context type to symbolic context type
    const symbolic_ctx = symbolic.fromCommonContext(ctx.symbolicCtx);
    const batch_expr = try symbolic.symbol(symbolic_ctx, "batch");

    // Create Dim variants
    const concrete_dim = Dim{ .concrete = 10 };
    // Convert from symbolic Node to common.Node
    const common_batch_expr = symbolic.toCommonNode(batch_expr);
    const symbolic_dim = Dim{ .symbolic = common_batch_expr };

    // Test basic properties
    try testing.expect(concrete_dim == .concrete);
    try testing.expect(symbolic_dim == .symbolic);
    try testing.expectEqual(@as(usize, 10), concrete_dim.concrete);

    // Create bindings for evaluation
    var bindings = symbolic.createStringHashMap(allocator, i64);
    defer bindings.deinit();

    try bindings.put("batch", 32);
    try bindings.put("height", 224);
    try bindings.put("width", 224);

    // Test concrete value extraction
    const concrete_value = try concrete_dim.value();
    try testing.expectEqual(@as(usize, 10), concrete_value);

    // Create a shape tracker with concrete dimensions
    var tracker = try ShapeTracker.init(symbolic.fromCommonContext(ctx.symbolicCtx), allocator, &[_]usize{ 32, 3, 224, 224 });
    defer tracker.deinit();

    // Verify dimensions
    try testing.expectEqual(@as(usize, 4), tracker.rank());
    try testing.expectEqual(@as(usize, 32), tracker.dims()[0].concrete);
    try testing.expectEqual(@as(usize, 3), tracker.dims()[1].concrete);
    try testing.expectEqual(@as(usize, 224), tracker.dims()[2].concrete);
    try testing.expectEqual(@as(usize, 224), tracker.dims()[3].concrete);
}

test "symbolic_shape:creation" {
    const testing = std.testing;
    const allocator = testing.allocator;

    // Test creating dimensions through factory functions
    const dim1 = Dim.fromValue(42);
    try testing.expectEqual(@as(usize, 42), dim1.concrete);

    // Create with symbolic ctx
    const sym_ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(sym_ctx);

    // Convert to common context type first
    const common_sym_ctx = symbolic.toCommonContext(sym_ctx);
    const dim2 = try Dim.fromSymbol(common_sym_ctx, "channels");
    try testing.expect(dim2 == .symbolic);

    // Test legacy creation function
    const dim3 = Dim.createConcrete(128);
    try testing.expectEqual(@as(usize, 128), dim3.concrete);
}

test "symbolic_shape:operations" {
    const testing = std.testing;
    const allocator = testing.allocator;

    // Create context
    const ctx = try shape.createContext(allocator);
    defer shape.destroyContext(ctx);

    // Create a shape with concrete dimensions
    var tracker = try ShapeTracker.init(symbolic.fromCommonContext(ctx.symbolicCtx), allocator, &[_]usize{ 2, 3, 4 });
    defer tracker.deinit();

    // Test basic properties
    try testing.expectEqual(@as(usize, 3), tracker.rank());
    try testing.expectEqual(@as(usize, 2), tracker.dims()[0].concrete);
    try testing.expectEqual(@as(usize, 3), tracker.dims()[1].concrete);
    try testing.expectEqual(@as(usize, 4), tracker.dims()[2].concrete);

    // Test isScalar, isVector, isMatrix
    try testing.expect(!tracker.isScalar());
    try testing.expect(!tracker.isVector());
    try testing.expect(!tracker.isMatrix());

    // Create a vector
    var vector = try ShapeTracker.init(symbolic.fromCommonContext(ctx.symbolicCtx), allocator, &[_]usize{5});
    defer vector.deinit();

    try testing.expect(!vector.isScalar());
    try testing.expect(vector.isVector());
    try testing.expect(!vector.isMatrix());

    // Create a matrix
    var matrix = try ShapeTracker.init(symbolic.fromCommonContext(ctx.symbolicCtx), allocator, &[_]usize{ 6, 7 });
    defer matrix.deinit();

    try testing.expect(!matrix.isScalar());
    try testing.expect(!matrix.isVector());
    try testing.expect(matrix.isMatrix());
}
