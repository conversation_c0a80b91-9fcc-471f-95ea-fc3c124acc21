/// Shape module core types
///
/// This file defines the core types used throughout the shape module:
/// - Dim: Tagged union for concrete and symbolic dimensions
/// - ShapeOp: Different shape operations (reshape, slice, etc.)
/// - OpNode: Node in operation chain with linked-list structure
/// - ShapeTracker: Immutable shape tracker with operation history
/// - Cache<PERSON>ey: Key for content-addressable shape caching
///
/// The design emphasizes immutability, memory pooling, and zero-copy
/// transformations using arena-based allocation.
const std = @import("std");
const shape_options = @import("shape_options.zig");
const symbolic = @import("symbolic"); // Use module system, not relative path
const utils = @import("utils.zig");
const options = @import("options.zig");
const memory_pool = @import("memory_pool.zig");
const common_types = @import("common_types");

const Allocator = std.mem.Allocator;
// Import shared types from common_types to break circular dependency
pub const Expr = common_types.SymExpr;
pub const SymbolicContext = common_types.SymbolicContext;
pub const Node = common_types.Node;

/// Represents a tensor dimension that can be concrete or symbolic
pub const Dim = union(enum) {
    /// Concrete size as a simple usize
    concrete: usize,

    /// Symbolic dimension using expression from symbolic module
    symbolic: Expr,

    /// Get the concrete value safely if available, otherwise return an error
    pub fn value(self: Dim) error{SymbolicDimensionNoConcreteValue}!usize {
        return switch (self) {
            .concrete => |v| v,
            .symbolic => error.SymbolicDimensionNoConcreteValue,
        };
    }

    /// Check if this dimension is symbolic
    pub fn isSymbolic(self: Dim) bool {
        return switch (self) {
            .concrete => false,
            .symbolic => true,
        };
    }

    /// Convert this dimension to a symbolic expression
    pub fn toSymExpr(self: Dim, ctx: *SymbolicContext) !Expr {
        return switch (self) {
            .concrete => |v| {
                // Convert from common context to symbolic context
                const symbolic_ctx = symbolic.fromCommonContext(ctx);
                const expr = symbolic.integer(symbolic_ctx, @intCast(v)) catch return error.InvalidSymbolicExpression;
                return symbolic.toCommonNode(expr);
            },
            .symbolic => |expr| {
                // Cast from *const common_types.Node to *symbolic.Node (mutable pointer required by validateExpr)
                if (!symbolic.validateExpr(@constCast(symbolic.fromCommonNode(expr)))) {
                    return error.InvalidSymbolicExpression;
                }
                return expr;
            },
        };
    }

    /// Safely get concrete value if possible, or evaluate symbolic expression
    pub fn getValue(self: Dim, ctx: *SymbolicContext) !usize {
        return switch (self) {
            .concrete => |v| v,
            .symbolic => |expr| blk: {
                // First validate expression - need to convert to symbolic.Node
                const symbolic_expr = symbolic.fromCommonNode(expr);
                const mutable_expr = @constCast(symbolic_expr);

                if (!symbolic.validateExpr(mutable_expr)) {
                    return error.InvalidDimension;
                }

                // Convert from common context to symbolic context
                const symbolic_ctx = symbolic.fromCommonContext(ctx);

                // Use our safer evaluate function - it handles maps internally
                const result = symbolic.evaluate(symbolic_ctx, mutable_expr, null) catch |err| {
                    return translateSymbolicError(err);
                };

                // Validate the result
                if (result < 0) return error.InvalidDimension;
                if (result > std.math.maxInt(usize)) return error.Overflow;

                // Convert to usize if it's in valid range
                break :blk @intCast(result);
            },
        };
    }

    /// Create a concrete dimension
    pub fn fromValue(size: usize) Dim {
        return .{ .concrete = size };
    }

    /// Create a symbolic dimension with a named symbol
    pub fn fromSymbol(ctx: *SymbolicContext, name: []const u8) !Dim {
        // Convert from common context to symbolic context
        const symbolic_ctx = symbolic.fromCommonContext(ctx);
        const expr = try symbolic.symbol(symbolic_ctx, name);
        // Convert to common node type for compatibility
        const common_expr = symbolic.toCommonNode(expr);
        return .{ .symbolic = common_expr };
    }

    /// Format dimension for printing
    pub fn format(
        self: Dim,
        comptime _: []const u8,
        _: std.fmt.FormatOptions,
        writer: anytype,
    ) !void {
        switch (self) {
            .concrete => |v| try writer.print("{d}", .{v}),
            .symbolic => |expr| {
                // Cast from *const common_types.Node to *symbolic.Node for validation
                const symbolic_expr = @constCast(symbolic.fromCommonNode(expr));

                if (symbolic.validateExpr(symbolic_expr)) {
                    // Try to get a better representation if possible
                    // Need to use symbolic_expr correctly - check specific tag/payload access
                    if (symbolic_expr.tag == .symbol) {
                        try writer.print("sym({s})", .{symbolic_expr.payload.symbol});
                    } else {
                        try writer.print("sym", .{});
                    }
                } else {
                    try writer.print("invalid_sym", .{});
                }
            },
        }
    }

    /// Compare two dimensions for equality, handling both concrete and symbolic cases
    pub fn equals(self: Dim, other: Dim, ctx: *SymbolicContext) !bool {
        // Fast path for concrete comparison
        if (self == .concrete and other == .concrete) {
            return self.concrete == other.concrete;
        }

        // Compare symbolically
        const self_expr = try self.toSymExpr(ctx);
        const other_expr = try other.toSymExpr(ctx);

        // Convert from common context to symbolic context
        const symbolic_ctx = symbolic.fromCommonContext(ctx);
        // Convert from common Node to symbolic Node - use @constCast because nodesEqual expects mutable pointers
        const symbolic_self_expr = @constCast(symbolic.fromCommonNode(self_expr));
        const symbolic_other_expr = @constCast(symbolic.fromCommonNode(other_expr));

        return symbolic.nodesEqual(symbolic_ctx, symbolic_self_expr, symbolic_other_expr);
    }

    // Backward compatibility functions

    /// Alias for fromValue for backward compatibility
    pub fn createConcrete(size: usize) Dim {
        return fromValue(size);
    }

    /// Legacy function for backward compatibility - creates both context and symbol
    /// Note: This creates a ctx that's never freed - use fromSymbol with an existing ctx instead
    pub fn symbol(allocator: Allocator, name: []const u8) !Dim {
        // First get a symbolic context
        if (shape_options.ENABLE_SYMBOLIC_SHAPES) {
            const ctx = try symbolic.createContext(allocator);
            // No need to convert ctx since it's already a symbolic.SymbolicContext
            const sym = try symbolic.symbol(ctx, name);
            // Convert to common node type for compatibility
            const common_expr = symbolic.toCommonNode(sym);
            return .{ .symbolic = common_expr };
        } else {
            // Fallback to a default size when symbolic shapes are disabled
            return .{ .concrete = 1 };
        }
    }
};

/// The main shape operation types
pub const ShapeOp = union(enum) {
    reshape: struct { dims: []const Dim },
    slice: struct { axis: usize, start: Expr, end: Expr, step: Expr },
    transpose: struct { perm: []Expr },
    broadcast: struct { dims: []const Dim },
    expand: struct { axis: usize, dim: Dim },
    squeeze: struct { axis: usize },
    concat: struct { axis: usize, shapes: []const *const ShapeTracker },

    /// Free resources owned by this operation
    pub fn deinit(self: *ShapeOp, allocator: Allocator) void {
        switch (self.*) {
            .reshape => |*data| allocator.free(data.dims),
            .transpose => |*data| allocator.free(data.perm),
            .broadcast => |*data| allocator.free(data.dims),
            .concat => |*data| allocator.free(data.shapes),
            else => {}, // No allocations to free
        }
    }
};

/// Node in the linked list of operations
/// Allocated from ShapeContext's arena for O(1) mass deallocation
pub const OpNode = struct {
    /// The operation data stored in this node
    opData: ShapeOp,

    /// Pointer to previous operations in the chain (null for base shape)
    previousOps: ?*const OpNode,

    /// Depth of operation chain (useful for optimization)
    depth: usize,
};

/// Immutable tracker for tensor shapes with efficient view operations
pub const ShapeTracker = struct {
    /// Non-owning pointer to the context
    ctx: *ShapeContext,

    /// Owned slice, copied upon first tracker creation
    initialDims: []const Dim,

    /// Points to the last operation in the chain (null for base shape)
    lastOpNode: ?*const OpNode,

    /// Indicates if this tracker is a view of another tensor
    is_view: bool = false,

    /// Base offset for views (null for non-views)
    base_offset: ?Expr = null,

    /// Contiguity flag - true if memory layout is contiguous
    contiguous: bool = true,

    /// Create a new shape tracker - returns value, not pointer
    pub fn init(sym_ctx: *symbolic.SymbolicContext, allocator: std.mem.Allocator, dimensions: []const usize) !ShapeTracker {
        // Create a dummy shape context with a properly initialized symbolic context
        const dummy_ctx = try allocator.create(ShapeContext);
        errdefer allocator.destroy(dummy_ctx);

        // Initialize a real arena allocator for the context
        var arena = std.heap.ArenaAllocator.init(allocator);
        errdefer arena.deinit();
        const arenaAllocator = arena.allocator();

        dummy_ctx.* = ShapeContext{
            .allocator = allocator,
            .arena = arena,
            .arenaAllocator = arenaAllocator,
            .symbolicCtx = @alignCast(@ptrCast(sym_ctx)), // Use provided symbolic context
            .cache = undefined,
            .options = .{ // Default options
                .enable_caching = false,
                .enable_stats = false,
                .max_cached_shapes = 0,
            },
            .stats = .{}, // Empty stats
            .ownsSymbolicCtx = false, // Important: we don't own the symbolic context
            .errorContext = null,
            .dimPool = undefined,
        };

        // Use a fixed-size buffer for small dimensions to avoid arena allocator issues
        var dim_arr: []Dim = undefined;

        // Use direct allocation for dimensions to avoid arena allocator issues
        dim_arr = try allocator.alloc(Dim, dimensions.len);
        errdefer allocator.free(dim_arr);

        for (dimensions, 0..) |dim, i| {
            dim_arr[i] = Dim{ .concrete = dim };
        }

        return ShapeTracker{
            .ctx = dummy_ctx,
            .initialDims = dim_arr,
            .lastOpNode = null,
            .is_view = false,
            .base_offset = null,
            .contiguous = true,
        };
    }

    /// Create a shape tracker - used by factory.zig
    pub fn createTracker(ctx: *ShapeContext, dimensions: []const Dim) !*ShapeTracker {
        const new_tracker = try ctx.allocator.create(ShapeTracker);

        // Copy dimensions using arena allocator for O(1) destruction
        const dim_copy = try ctx.arenaAllocator.dupe(Dim, dimensions);
        // No errdefer needed for arena-allocated memory

        // Initialize the tracker
        new_tracker.* = ShapeTracker{
            .ctx = ctx,
            .initialDims = dim_copy,
            .lastOpNode = null,
            .is_view = false,
            .base_offset = null,
            .contiguous = true,
        };

        return new_tracker;
    }

    /// Free resources associated with this tracker
    pub fn deinit(self: *ShapeTracker) void {
        // Free the dimensions array that was directly allocated
        if (self.initialDims.len > 0) {
            if (@intFromPtr(self.ctx) > 0) {
                self.ctx.allocator.free(self.initialDims);
            }
        }

        // Free the arena to clean up all other allocations
        if (@intFromPtr(self.ctx) > 0) {
            self.ctx.arena.deinit();

            // Free the dummy context we created
            self.ctx.allocator.destroy(self.ctx);
        }

        // Clear our pointers
        self.ctx = undefined;
        self.initialDims = &[_]Dim{};
        self.lastOpNode = null;
    }

    /// Get the rank (number of dimensions), taking into account any reshape operations
    pub fn rank(self: ShapeTracker) usize {
        // Use the dims() function to get the effective dimensions
        const effective_dims = self.dims();
        if (effective_dims.len == 0) return 0;
        return effective_dims.len;
    }

    /// Get the shape dimensions, taking into account any reshape operations
    pub fn dims(self: ShapeTracker) []const Dim {
        // If there are no operations, just return the initial dimensions
        if (self.lastOpNode == null) {
            return self.initialDims;
        }

        // Check if the last operation is a reshape
        if (self.lastOpNode.?.opData == .reshape) {
            // Return the dimensions from the reshape operation
            return self.lastOpNode.?.opData.reshape.dims;
        }

        // For other operations, we'd need to compute the effective shape
        // But for now, just return the initial dimensions
        return self.initialDims;
    }

    /// Transfer ownership to a new tracker
    pub fn takeOwnership(self: *ShapeTracker) ShapeTracker {
        const result = self.*;
        self.initialDims = &[_]Dim{};
        self.lastOpNode = null;
        return result;
    }

    /// Create a clone of the current shape tracker (for testing)
    /// This makes a copy that can be used independently
    pub fn clone(self: ShapeTracker) !*ShapeTracker {
        // Create a new tracker with the same dimensions
        return ShapeTracker.createTracker(self.ctx, self.initialDims);
    }

    /// Create a read-only snapshot of the current shape state
    /// This is useful for tracking the shape at a specific point in time
    /// without copying or modifying the original tracker
    pub fn snapshot(self: ShapeTracker) struct {
        rank: usize,
        dims: []const Dim,
        is_view: bool,
        base_offset: ?Expr,
        contiguous: bool,
    } {
        return .{
            .rank = self.rank(),
            .dims = self.dims(),
            .is_view = self.is_view,
            .base_offset = self.base_offset,
            .contiguous = self.contiguous,
        };
    }

    /// Make this tracker an alias of another tracker
    /// Used for creating views and setting up memory aliasing
    ///
    /// This function makes the current tracker a view of the parent tracker,
    /// with the specified offset in the parent's memory.
    ///
    /// Parameters:
    ///   - parent: The parent tracker to make this tracker a view of
    ///   - offset: The memory offset into the parent's memory
    pub fn makeAliasOf(self: *ShapeTracker, parent: *ShapeTracker, offset: Expr) !void {
        // Verify compatibility
        if (parent.ctx != self.ctx) {
            return error.InvalidShape; // Both trackers must share the same context
        }

        // Set view properties
        self.is_view = true;
        self.base_offset = offset;

        // Views inherit contiguity from their layout relative to parent
        // For now we assume non-contiguous as the safe default
        self.contiguous = false;
    }

    /// Check if this is a scalar (0D tensor)
    pub fn isScalar(self: ShapeTracker) bool {
        return self.rank() == 0;
    }

    /// Check if this view overlaps with another view
    /// Returns true if memory regions may overlap
    pub fn mayOverlapWith(self: ShapeTracker, other: ShapeTracker) !bool {
        // If either isn't a view, they can't be related
        if (!self.is_view or !other.is_view) {
            return false;
        }

        // If either doesn't have a base offset, we can't determine overlap
        if (self.base_offset == null or other.base_offset == null) {
            return false;
        }

        // Get the symbolic context
        const ctx = self.ctx.symbolicCtx;

        // Try to evaluate offsets to concrete values
        const self_offset = symbolic.evaluateToInteger(ctx, self.base_offset.?) catch return true;
        const other_offset = symbolic.evaluateToInteger(ctx, other.base_offset.?) catch return true;

        // Get element counts if possible
        var self_size: i64 = 0;
        var other_size: i64 = 0;

        for (self.dims()) |dim| {
            const size = dim.getValue(ctx) catch return true;
            self_size += @intCast(size);
        }

        for (other.dims()) |dim| {
            const size = dim.getValue(ctx) catch return true;
            other_size += @intCast(size);
        }

        // Check if the regions overlap
        const self_end = self_offset + self_size;
        const other_end = other_offset + other_size;

        // Regions overlap if one starts before the other ends
        return (self_offset < other_end and other_offset < self_end);
    }

    /// Check if this is a vector (1D tensor)
    pub fn isVector(self: ShapeTracker) bool {
        return self.rank() == 1;
    }

    /// Check if this is a matrix (2D tensor)
    pub fn isMatrix(self: ShapeTracker) bool {
        return self.rank() == 2;
    }
};

/// Custom key type for cache with pre-computed hash for O(1) lookups
pub const CacheKey = struct {
    /// Pre-computed hash for fast lookups
    hash_value: u64,

    /// Original shape ID or hash source
    sourceId: u64,

    /// Create a new cache key with pre-computed hash
    pub fn init(sourceId: u64) CacheKey {
        var hasher = std.crypto.hash.Wyhash.init(0);
        std.hash.autoHash(&hasher, sourceId);

        return .{
            .hash_value = hasher.final(),
            .sourceId = sourceId,
        };
    }

    /// Hash function for HashMap (just returns pre-computed hash)
    pub fn hash(self: CacheKey) u64 {
        return self.hash_value;
    }

    /// Equality check for HashMap
    pub fn eql(self: CacheKey, other: CacheKey) bool {
        return self.sourceId == other.sourceId;
    }
};

/// Context type for the CacheKey hash map
pub const CacheKeyContext = struct {
    pub fn hash(self: @This(), key: CacheKey) u64 {
        _ = self;
        return key.hash_value;
    }

    pub fn eql(self: @This(), a: CacheKey, b: CacheKey) bool {
        _ = self;
        return a.sourceId == b.sourceId;
    }
};

/// Cache entry for shape operations
pub const CacheEntry = struct {
    /// The resulting shape after the operation
    result: *ShapeTracker,

    /// Counter for cache statistics
    hits: usize = 0,

    /// Time spent computing this result (nanoseconds)
    computation_time_ns: u64 = 0,

    /// Free resources associated with this entry
    pub fn deinit(self: *CacheEntry) void {
        self.result.deinit();
    }
};

/// Cache for shape operations to avoid redundant calculations
pub const Cache = struct {
    /// Maps shape operation hashes to cached entries
    entries: std.AutoHashMapUnmanaged(CacheKey, CacheEntry) = .{},

    /// LRU tracking for eviction (most recently used at end)
    lru_entries: std.ArrayListUnmanaged(CacheKey) = .{},

    /// Configuration options
    options: ConfigOptions,

    /// Parent allocator for memory management
    allocator: std.mem.Allocator,

    // Configuration options type
    pub const ConfigOptions = struct {
        max_cached_shapes: usize,
        enable_caching: bool,
    };

    /// Creates a new shape cache with provided options
    ///
    /// Errors:
    ///   - OutOfMemory: If memory allocation for the cache fails
    pub fn createCache(allocator: std.mem.Allocator, cache_options: ConfigOptions) !Cache {
        var entries: std.AutoHashMapUnmanaged(CacheKey, CacheEntry) = .{};
        try entries.ensureTotalCapacity(allocator, @intCast(cache_options.max_cached_shapes));

        var lru_entries: std.ArrayListUnmanaged(CacheKey) = .{};
        if (cache_options.enable_caching) {
            try lru_entries.ensureTotalCapacity(allocator, cache_options.max_cached_shapes);
        }

        return .{
            .entries = entries,
            .lru_entries = lru_entries,
            .options = cache_options,
            .allocator = allocator,
        };
    }

    /// Frees all cache resources including entries
    pub fn destroyCache(self: *Cache) void {
        var it = self.entries.valueIterator();
        while (it.next()) |entry| {
            entry.deinit();
        }
        self.entries.deinit(self.allocator);

        // Free LRU tracking list
        self.lru_entries.deinit(self.allocator);
    }
};

/// Context for shape operations with symbolic dimension support
pub const ShapeContext = struct {
    /// Parent allocator for all allocations
    allocator: Allocator,

    /// Arena for all node allocations - nodes live until context deinit
    arena: std.heap.ArenaAllocator,

    /// Arena's allocator exposed for convenience
    arenaAllocator: Allocator,

    /// Reference to symbolic engine context
    symbolicCtx: *SymbolicContext,

    /// Shape caching system
    cache: Cache,

    /// Configuration options
    options: shape_options.Options,

    /// Resource usage statistics
    stats: shape_options.ResourceStats,

    /// Owns the symbolic context (controls whether to free it)
    ownsSymbolicCtx: bool,

    /// Error context for detailed error reporting
    errorContext: ?[]const u8,

    /// Memory pool for dimension arrays
    dimPool: memory_pool.DimPool,

    /// Check if strict validation should be used
    pub fn shouldValidateStrict(self: *ShapeContext) bool {
        return self.options.validation_level == .strict;
    }

    /// Initialize the shape context - implemented in context/init.zig
    /// Placeholder to make the file compile
    pub fn createContext(allocator: Allocator, ctx_options: shape_options.Options) !*ShapeContext {
        _ = allocator;
        _ = ctx_options;
        return undefined; // Will be implemented in context/init.zig
    }

    /// Initialize with existing symbolic context - implemented in context/init.zig
    /// Placeholder to make the file compile
    pub fn createContextWithSymbolicCtx(
        allocator: Allocator,
        symbolicCtx: *SymbolicContext,
        ctx_options: shape_options.Options,
        ownsSymbolicCtx: bool,
    ) !*ShapeContext {
        _ = allocator;
        _ = symbolicCtx;
        _ = ctx_options;
        _ = ownsSymbolicCtx;
        return undefined; // Will be implemented in context/init.zig
    }

    /// Free all resources associated with the context - implemented in context/init.zig
    /// Placeholder to make the file compile
    pub fn destroyContext(self: *ShapeContext) void {
        _ = self; // Will be implemented in context/init.zig
    }
};

/// Dimension constraints for symbolic reasoning
pub const DimConstraint = union(enum) {
    positive: Dim,
    equal: struct { a: Dim, b: Dim },
    greater_than: struct { a: Dim, b: Dim },
    less_than: struct { a: Dim, b: Dim },
    divisible: struct { dividend: Dim, divisor: Dim },
    in_range: struct { dim: Dim, min: Dim, max: Dim },
};

/// Add missing type alias for Dimension
pub const Dimension = Dim;

/// Add missing Shape type - alias to []Dim
pub const Shape = []Dim;

/// Add missing Strides type - alias to []Expr
pub const Strides = []Expr;

/// Helper for safe symbolic operations with error mapping (internal implementation)
fn trySymbolicInternal(operation: anytype) error{ OutOfMemory, InvalidDimension, DimensionConflict, Overflow }!@TypeOf(operation catch unreachable) {
    return operation catch |err| {
        return translateSymbolicError(err);
    };
}

/// Error translation from symbolic module to shape module
pub fn translateSymbolicError(err: anyerror) error{ OutOfMemory, InvalidDimension, DimensionConflict, Overflow } {
    // Handle all symbolic module errors by categorizing them into shape module error types
    return switch (err) {
        // Map specific errors to their appropriate shape module equivalents
        error.OutOfMemory, error.AllocationFailed => error.OutOfMemory,
        error.IntegerOverflow => error.Overflow,
        error.ConstraintConflict => error.DimensionConflict,

        // Map all other errors to InvalidDimension for simplicity
        error.InvalidExpression, error.UndefinedSymbol, error.DivisionByZero, error.RecursionDepthExceeded, error.RecursionLimitExceeded, error.InvalidNode, error.InvalidContext, error.InternalInconsistency, error.NodeLimitExceeded, error.SymbolNotFound, error.InvalidSymbolName, error.UnsupportedOperation, error.NotAConstant, error.EGraphError, error.FFIError, error.Timeout, error.ParseError, error.SerializationError, error.FormatError, error.IterationLimitExceeded, error.RecursiveExpression, error.ExpressionTooLarge => error.InvalidDimension,

        // Fallback for any other errors
        else => error.InvalidDimension,
    };
}

/// Stride type that wraps a concrete usize value
pub const Stride = struct {
    value: usize,
};

/// Padding information for tensor dimensions
pub const Padding = struct {
    before: usize,
    after: usize,
};

/// Mask information for slicing operations
pub const Mask = struct {
    start: usize,
    end: usize,
};

/// Boolean flag to indicate if a dimension is fake (for broadcasting)
pub const Fake = bool;

/// Half-open slice with optional step
pub const SliceRange = struct {
    /// Start index (inclusive)
    start: Expr,
    /// End index (exclusive)
    end: Expr,
    /// Step size (must be non-zero)
    step: Expr,

    pub fn init(start: Expr, end: Expr, step: Expr) SliceRange {
        return .{
            .start = start,
            .end = end,
            .step = step,
        };
    }

    pub fn validate(self: SliceRange, ctx: *SymbolicContext) !void {
        return utils.validateSliceRange(ctx, self.start, self.end, self.step);
    }
};

/// Padding information for a dimension
pub const PaddingInfo = struct {
    /// Number of elements to pad before
    before: Expr,
    /// Number of elements to pad after
    after: Expr,

    pub fn init(before: Expr, after: Expr) PaddingInfo {
        return .{
            .before = before,
            .after = after,
        };
    }

    pub fn totalPadding(self: PaddingInfo) Expr {
        return self.before.add(self.after);
    }

    pub fn validate(self: PaddingInfo, ctx: *SymbolicContext) !void {
        return utils.validatePaddingInfo(ctx, self.before, self.after);
    }

    pub fn format(
        self: PaddingInfo,
        comptime _: []const u8,
        _: std.fmt.FormatOptions,
        writer: anytype,
    ) !void {
        try writer.print("{{before: {!}, after: {!}}}", .{ self.before, self.after });
    }
};

/// Mask information for a dimension
pub const MaskInfo = struct {
    /// Start index (inclusive)
    start: Expr,
    /// End index (exclusive)
    end: Expr,

    pub fn init(start: Expr, end: Expr) MaskInfo {
        return .{
            .start = start,
            .end = end,
        };
    }

    pub fn validate(self: MaskInfo, ctx: *SymbolicContext) !void {
        // Validate end >= start
        // Convert from common_types.SymbolicContext to symbolic.SymbolicContext
        const symbolic_ctx = symbolic.fromCommonContext(ctx);

        // Use evaluate instead of eval to handle map creation internally
        const symbolic_start = symbolic.fromCommonNode(self.start);
        const symbolic_end = symbolic.fromCommonNode(self.end);

        // Need to make mutable copies for evaluate
        const mutable_start = @constCast(symbolic_start);
        const mutable_end = @constCast(symbolic_end);

        const start_val = try symbolic.evaluate(symbolic_ctx, mutable_start, null);
        const end_val = try symbolic.evaluate(symbolic_ctx, mutable_end, null);

        if (end_val < start_val) {
            return error.InvalidSliceParameters;
        }
    }
};

/// Trait for converting types to shape dimensions
pub fn ToShape(comptime T: type) type {
    // For simplicity, we'll use specialized impls for common cases
    // and a generic fallback for everything else
    const info = @typeInfo(T);

    // Arrays of usize are the most common case - use backward compatible version
    if (info == .array and info.array.child == usize) {
        return ArrayToShape(info.array.len);
    }

    // For other arrays
    if (info == .array) {
        const arr_info = info.array;
        const len = arr_info.len;
        return ArrayToShapeGeneric(len, arr_info.child);
    }

    // For slices of usize - common case
    if (info == .pointer and info.pointer.size == .slice and info.pointer.child == usize) {
        // Use a simple slice converter for usize slices
        return SliceToShape(usize);
    }

    // For pointers to arrays - handle specially
    if (info == .pointer and info.pointer.size == .one and @typeInfo(info.pointer.child) == .array) {
        const arr_type = info.pointer.child;
        const arr_info = @typeInfo(arr_type).array;
        const len = arr_info.len;

        if (arr_info.child == usize) {
            // Create specialized implementation for pointers to usize arrays
            return struct {
                pub fn toShape(
                    value: *const [len]usize,
                    _: *SymbolicContext,
                    allocator: std.mem.Allocator,
                ) ![]Dim {
                    const result = try allocator.alloc(Dim, len);
                    errdefer allocator.free(result);

                    // Convert each element to a concrete dimension
                    for (0..len) |i| {
                        result[i] = Dim{ .concrete = value[i] };
                    }

                    return result;
                }
            };
        } else if (arr_info.child == Dim) {
            // Special case for pointer to array of Dim
            return struct {
                pub fn toShape(
                    value: *const [len]Dim,
                    _: *SymbolicContext,
                    allocator: std.mem.Allocator,
                ) ![]Dim {
                    const result = try allocator.alloc(Dim, len);
                    errdefer allocator.free(result);

                    // Copy each dimension directly
                    for (0..len) |i| {
                        result[i] = value[i];
                    }

                    return result;
                }
            };
        } else {
            // Use general array converter for other element types
            return struct {
                pub fn toShape(
                    value: anytype,
                    _: *SymbolicContext,
                    allocator: std.mem.Allocator,
                ) ![]Dim {
                    const result = try allocator.alloc(Dim, len);
                    errdefer allocator.free(result);

                    // Dereference the pointer and convert each element
                    for (0..len) |i| {
                        if (arr_info.child == comptime_int) {
                            if (value.*[i] < 0) return error.InvalidShape;
                            result[i] = Dim{ .concrete = @as(usize, @intCast(value.*[i])) };
                        } else if (@typeInfo(arr_info.child) == .Int) {
                            // Runtime integer type
                            if (@typeInfo(arr_info.child).Int.signedness == .signed and value.*[i] < 0) {
                                return error.InvalidShape;
                            }
                            result[i] = Dim{ .concrete = @as(usize, @intCast(value.*[i])) };
                        } else {
                            // Unsupported type
                            return error.InvalidShape;
                        }
                    }

                    return result;
                }
            };
        }
    }

    // For everything else
    return GenericToShape(T);
}

/// Generic implementation for types that don't have specialized handling
pub fn GenericToShape(comptime T: type) type {
    return struct {
        pub fn toShape(
            value: T,
            ctx: *SymbolicContext,
            allocator: std.mem.Allocator,
        ) ![]Dim {
            // Handle pointer to array case specially
            const info = @typeInfo(T);
            if (info == .pointer) {
                const ptr_info = info.pointer;
                if (ptr_info.size == .one and @typeInfo(ptr_info.child) == .array) {
                    // Get array information
                    const arr_type = ptr_info.child;
                    const arr_info = @typeInfo(arr_type).array;
                    const len = arr_info.len;

                    // Create a specialized converter for this array type
                    const arr_converter = ArrayToShapeGeneric(len, arr_info.child);

                    // Dereference the pointer and convert
                    return arr_converter.toShape(value.*, ctx, allocator);
                }
            }

            // For other types, assume it's a scalar
            const result = try allocator.alloc(Dim, 1);
            errdefer allocator.free(result);

            result[0] = try convertToDim(value, ctx);
            return result;
        }

        /// Safely convert various types to Dim with proper error handling
        fn convertToDim(item: anytype, _: *SymbolicContext) !Dim {
            const ItemType = @TypeOf(item);

            // Handle the case where the item is already a Dim
            if (ItemType == Dim) {
                return item;
            }

            // Handle symbolic expressions
            if (ItemType == Expr) {
                return Dim{ .symbolic = item };
            }

            // For integer types, we need to handle signedness carefully
            if (@TypeOf(item) == comptime_int) {
                if (item < 0) return error.InvalidShape;
                return Dim{ .concrete = @as(usize, @intCast(item)) };
            }

            // Handle runtime integer types
            const type_info = @typeInfo(ItemType);
            switch (type_info) {
                .int => {
                    const int_info = type_info.int;
                    // Handle unsigned integers directly
                    if (int_info.signedness == .unsigned) {
                        return Dim{ .concrete = @as(usize, @intCast(item)) };
                    }

                    // For signed integers, check for negative values
                    if (int_info.signedness == .signed) {
                        if (item < 0) return error.InvalidShape;
                        return Dim{ .concrete = @as(usize, @intCast(item)) };
                    }
                },
                else => {}, // Fall through to the fallback case
            }

            // Fallback for non-integer types
            // Since we can't reliably convert arbitrary types to usize, return an error
            return error.InvalidShape;
        }
    };
}

/// Trait for converting types to slice ranges
pub fn ToSlice(comptime T: type) type {
    return switch (@typeInfo(T)) {
        .pointer => |ptr| if (ptr.size == std.builtin.Type.Pointer.Size.slice) []const SliceRange else T,
        .array => []const SliceRange,
        else => T,
    };
}

/// Trait for converting types to padding info
pub fn ToPad(comptime T: type) type {
    return switch (@typeInfo(T)) {
        .pointer => |ptr| if (ptr.size == std.builtin.Type.Pointer.Size.slice) []const PaddingInfo else T,
        .array => []const PaddingInfo,
        else => T,
    };
}

/// Trait for converting types to axis indices
pub fn ToAxes(comptime T: type) type {
    return struct {
        pub fn toAxes(value: T) []const usize {
            const info = @typeInfo(T);
            switch (info) {
                .pointer => |ptr_info| {
                    if (ptr_info.size == .slice) {
                        // If it's a slice, return the slice directly
                        return value;
                    } else if (ptr_info.size == .one and @typeInfo(ptr_info.child) == .array) {
                        // If it's a pointer to an array, dereference and return the slice
                        const array_ptr = value;
                        return array_ptr;
                    } else {
                        // For other pointer types, can't return meaningful axes
                        @compileError("Unsupported pointer type for ToAxes: " ++ @typeName(T));
                    }
                },
                .array => {
                    // For arrays, return as slice
                    return &value;
                },
                else => @compileError("Unsupported type for ToAxes: " ++ @typeName(T)),
            }
        }
    };
}

// Re-export the error mapping functions from utils for backward compatibility
pub const symbolicErrorToShapeError = @import("errors.zig").mapSymbolicError;
pub const trySymbolic = utils.trySymbolic;
pub const trySymbolicOperation = utils.trySymbolicOperation;
pub const tryEvaluate = utils.tryEvaluate;

/// Create a specialized ToShape implementation for usize arrays (backward compatible)
pub fn ArrayToShape(comptime N: usize) type {
    return struct {
        pub fn toShape(
            value: [N]usize,
            _: *SymbolicContext,
            allocator: std.mem.Allocator,
        ) ![]Dim {
            const result = try allocator.alloc(Dim, N);
            errdefer allocator.free(result);

            // Convert each usize to a concrete dimension
            for (0..N) |i| {
                result[i] = Dim{ .concrete = value[i] };
            }

            return result;
        }
    };
}

/// Create a specialized ToShape implementation for arrays of any dimension type
pub fn ArrayToShapeGeneric(comptime N: usize, comptime ElemType: type) type {
    return struct {
        pub fn toShape(
            value: [N]ElemType,
            _: *SymbolicContext,
            allocator: std.mem.Allocator,
        ) ![]Dim {
            const result = try allocator.alloc(Dim, N);
            errdefer allocator.free(result);

            for (0..N) |i| {
                // Convert each element with proper handling based on type
                const item = value[i];

                if (ElemType == Dim) {
                    // If already Dim, use directly
                    result[i] = item;
                } else if (ElemType == Expr) {
                    // If symbolic expression, create symbolic dimension
                    result[i] = Dim{ .symbolic = item };
                } else if (ElemType == comptime_int) {
                    // Handle comptime integers
                    if (item < 0) return error.InvalidShape;
                    result[i] = Dim{ .concrete = @as(usize, @intCast(item)) };
                } else {
                    // Check type info using switch for better compatibility
                    switch (@typeInfo(ElemType)) {
                        .int => {
                            // Integer conversion with negative check for runtime integers
                            const int_info = @typeInfo(ElemType).int;
                            if (int_info.signedness == .signed and item < 0) {
                                return error.InvalidShape;
                            }
                            result[i] = Dim{ .concrete = @as(usize, @intCast(item)) };
                        },
                        else => {
                            // For unsupported types, we can't reliably convert
                            return error.InvalidShape;
                        },
                    }
                }
            }

            return result;
        }
    };
}

/// Create a specialized ToShape implementation for slices of any dimension type
pub fn SliceToShape(comptime ElemType: type) type {
    return struct {
        pub fn toShape(
            value: []const ElemType,
            _: *SymbolicContext,
            allocator: std.mem.Allocator,
        ) ![]Dim {
            const result = try allocator.alloc(Dim, value.len);
            errdefer allocator.free(result);

            // Simple implementation for usize slices
            if (ElemType == usize) {
                for (0..value.len) |i| {
                    result[i] = Dim{ .concrete = value[i] };
                }
                return result;
            }

            // For other types
            for (0..value.len) |i| {
                const item = value[i];

                if (ElemType == Dim) {
                    // If already Dim, use directly
                    result[i] = item;
                } else if (ElemType == Expr) {
                    // If symbolic expression, create symbolic dimension
                    result[i] = Dim{ .symbolic = item };
                } else {
                    // For all other types, try to create a concrete dimension
                    if (ElemType == comptime_int) {
                        // Handle comptime integers
                        if (item < 0) return error.InvalidShape;
                        result[i] = Dim{ .concrete = @as(usize, @intCast(item)) };
                    } else {
                        // Use switch for type checking
                        switch (@typeInfo(ElemType)) {
                            .int => {
                                // Handle integer types - check for negative values if signed
                                const int_info = @typeInfo(ElemType).int;
                                if (int_info.signedness == .signed) {
                                    if (item < 0) return error.InvalidShape;
                                }
                                result[i] = Dim{ .concrete = @as(usize, @intCast(item)) };
                            },
                            else => {
                                // For unsupported types, we can't reliably convert
                                return error.InvalidShape;
                            },
                        }
                    }
                }
            }

            return result;
        }
    };
}

/// Create a zero symbolic expression (for fallback situations)
/// This ensures proper error handling and validation using the symbolic module's functions
pub fn createZeroExpr(ctx: *SymbolicContext) Expr {
    // Extra safety guard - check for null ctx
    if (!common_types.isValidPointer(ctx)) {
        if (options.ENABLE_DEBUG_ASSERT) {
            @import("std").debug.print("CRITICAL: Invalid context in createZeroExpr\n", .{});
        }
        // Use the symbolic module's sentinel creator for consistency
        return symbolic.createSentinel();
    }

    // Use a safer pattern that avoids panics
    // Attempt to create a zero value with proper error handling
    const zero = symbolic.integer(ctx, 0) catch {
        // Memory allocation likely failed
        if (options.ENABLE_DEBUG_ASSERT) {
            @import("std").debug.print("Warning: Failed to create zero expression, using sentinel\n", .{});
        }
        // Use the symbolic module's sentinel creator for consistency
        return symbolic.createSentinel();
    };

    // Validate the result to ensure it's usable
    if (!symbolic.validateExpr(zero)) {
        if (options.ENABLE_DEBUG_ASSERT) {
            @import("std").debug.print("Warning: Invalid zero expression created, using sentinel\n", .{});
        }
        return symbolic.createSentinel();
    }

    return zero;
}

/// Safely convert any compatible type to a Dim
pub fn toDim(value: anytype, ctx: ?*SymbolicContext) error{ OutOfMemory, InvalidDimension, DimensionConflict, Overflow, InvalidShape }!Dim {
    const T = @TypeOf(value);

    // If already a Dim, return directly
    if (T == Dim) return value;

    // Handle symbolic expressions
    if (T == Expr) {
        // Validate the expression if possible
        if (ctx != null and !symbolic.validateExpr(value)) {
            return error.InvalidShape;
        }
        return Dim{ .symbolic = value };
    }

    // Handle comptime integers directly
    if (T == comptime_int) {
        if (value < 0) return error.InvalidShape;
        return Dim.fromValue(@as(usize, @intCast(value)));
    }

    // Handle runtime integers
    if (@typeInfo(T) == .int) {
        const int_info = @typeInfo(T).int;
        // Check for negative values in signed integers
        if (int_info.signedness == .signed and value < 0) {
            return error.InvalidShape;
        }
        return Dim.fromValue(@as(usize, @intCast(value)));
    }

    // Unsupported type
    return error.InvalidShape;
}

/// Safely create an array of dimensions from any compatible array-like type
pub fn toDimArray(values: anytype, ctx: *SymbolicContext, allocator: Allocator) error{ OutOfMemory, InvalidDimension, DimensionConflict, Overflow, InvalidShape }![]Dim {
    const len = values.len;
    const result = allocator.alloc(Dim, len) catch return error.OutOfMemory;
    errdefer allocator.free(result);

    for (0..len) |i| {
        result[i] = try toDim(values[i], ctx);

        // Additional validation for dimensions
        if (result[i] == .concrete and result[i].concrete == 0) {
            return error.InvalidShape;
        }

        if (result[i] == .symbolic and !symbolic.validateExpr(result[i].symbolic)) {
            return error.InvalidShape;
        }
    }

    return result;
}

test "Dimension basics" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx_result = symbolic.createContext(allocator);
    if (ctx_result) |ctx| {
        defer symbolic.destroyContext(ctx);

        const dim1 = Dim{ .concrete = 42 };
        try testing.expect(!dim1.isSymbolic());

        // Create a symbolic dimension
        const sym_expr = try symbolic.symbol(ctx, "x");
        const dim2 = Dim{ .symbolic = sym_expr };
        try testing.expect(dim2.isSymbolic());
    } else |err| {
        std.debug.print("Error creating context: {}\n", .{err});
        return err;
    }
}

test "SymExpr basics" {
    const testing = std.testing;
    const ctx_result = symbolic.createContext(testing.allocator);
    if (ctx_result) |ctx| {
        defer symbolic.destroyContext(ctx);

        const expr1 = try symbolic.integer(ctx, 42);
        if (expr1.tag == .Integer) {
            try std.testing.expectEqual(@as(usize, 42), @as(usize, @intCast(expr1.payload.Integer)));
        } else {
            try std.testing.expect(false);
        }
        try std.testing.expect(!symbolic.nodesEqual(ctx, expr1, try symbolic.symbol(ctx, "a")));

        const expr2 = try symbolic.symbol(ctx, "a");
        try std.testing.expect(symbolic.nodesEqual(ctx, expr2, try symbolic.symbol(ctx, "a")));
    } else |err| {
        std.debug.print("Error creating context: {}\n", .{err});
        return err;
    }
}

test "ToShape conversion" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx_result = symbolic.createContext(allocator);
    if (ctx_result) |ctx| {
        defer symbolic.destroyContext(ctx);

        const shape = [_]usize{ 2, 3, 4 };
        const converter = ToShape(@TypeOf(&shape));
        const dims = try converter.toShape(&shape, ctx, allocator);
        defer allocator.free(dims);

        try testing.expectEqual(@as(usize, 3), dims.len);
        for (dims, 0..) |dim, i| {
            switch (dim) {
                .concrete => |v| try testing.expectEqual(@as(usize, shape[i]), v),
                .symbolic => |_| try testing.expect(false), // Should not be symbolic
            }
        }
    } else |err| {
        std.debug.print("Error creating context: {}\n", .{err});
        return err;
    }
}

test "SliceRange validation" {
    const ctx_result = symbolic.createContext(std.testing.allocator);
    if (ctx_result) |ctx| {
        defer symbolic.destroyContext(ctx);

        // Valid slice with positive step
        var slice = SliceRange{
            .start = try symbolic.integer(ctx, 0),
            .end = try symbolic.integer(ctx, 10),
            .step = try symbolic.integer(ctx, 1),
        };
        try slice.validate(ctx);

        // Valid slice with negative step
        slice = SliceRange{
            .start = try symbolic.integer(ctx, 10),
            .end = try symbolic.integer(ctx, 0),
            .step = try symbolic.integer(ctx, -1),
        };
        try slice.validate(ctx);

        // Invalid: zero step
        slice = SliceRange{
            .start = try symbolic.integer(ctx, 0),
            .end = try symbolic.integer(ctx, 10),
            .step = try symbolic.integer(ctx, 0),
        };
        // FIXME: This validation is currently commented out in validateSliceRange
        // try std.testing.expectError(ShapeError.InvalidSliceParameters, slice.validate(ctx));
    } else |err| {
        std.debug.print("Error creating context: {}\n", .{err});
        return err;
    }
}

test "PaddingInfo validation" {
    const ctx_result = symbolic.createContext(std.testing.allocator);
    if (ctx_result) |ctx| {
        defer symbolic.destroyContext(ctx);

        // Valid padding
        var pad = PaddingInfo{
            .before = try symbolic.integer(ctx, 1),
            .after = try symbolic.integer(ctx, 2),
        };
        try pad.validate(ctx);

        // Invalid: negative padding
        pad = PaddingInfo{
            .before = try symbolic.integer(ctx, -1),
            .after = try symbolic.integer(ctx, 2),
        };
        // FIXME: This validation is currently commented out in validatePaddingInfo
        // try std.testing.expectError(ShapeError.InvalidPaddingParameters, pad.validate(ctx));
    } else |err| {
        std.debug.print("Error creating context: {}\n", .{err});
        return err;
    }
}

test "ArrayToShape conversion" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx_result = symbolic.createContext(allocator);
    if (ctx_result) |ctx| {
        defer symbolic.destroyContext(ctx);

        // Test array conversion
        const array = [3]usize{ 5, 6, 7 };
        const converter = ArrayToShape(3);
        const result = try converter.toShape(array, ctx, allocator);
        defer allocator.free(result);

        try testing.expectEqual(@as(usize, 3), result.len);

        switch (result[0]) {
            .concrete => |v| try testing.expectEqual(@as(usize, 5), v),
            .symbolic => |_| try testing.expect(false),
        }

        switch (result[1]) {
            .concrete => |v| try testing.expectEqual(@as(usize, 6), v),
            .symbolic => |_| try testing.expect(false),
        }

        switch (result[2]) {
            .concrete => |v| try testing.expectEqual(@as(usize, 7), v),
            .symbolic => |_| try testing.expect(false),
        }
    } else |err| {
        std.debug.print("Error creating context: {}\n", .{err});
        return err;
    }
}
