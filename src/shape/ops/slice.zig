const std = @import("std");
const symbolic = @import("symbolic");
const types_mod = @import("../types.zig");
const layout_mod = @import("../layout.zig");
const tracker_mod = @import("../tracker.zig");
const error_mod = @import("../errors.zig");
const symbolic_bridge = @import("../symbolic_bridge.zig");
const memory_pool = @import("../memory_pool.zig");
const context_mod = @import("../context/init.zig");
const cache_mod = @import("../context/cache.zig");

const Allocator = std.mem.Allocator;
const ShapeTracker = tracker_mod.ShapeTracker;
const SymExpr = symbolic.SymExpr;
const Dim = types_mod.Dim;
const MaskInfo = types_mod.MaskInfo;
const ShapeError = error_mod.ShapeError;
const SymbolicContext = symbolic.SymbolicContext;
const ShapeContext = types_mod.ShapeContext;
const ShapeOp = types_mod.ShapeOp;
const OpNode = types_mod.OpNode;
const CacheKey = types_mod.CacheKey;

// Use the centralized error handling from symbolic_bridge
const runSymbolicOp = symbolic_bridge.runSymbolicOp;
const validateExpr = symbolic_bridge.validateExpr;
const validateContext = symbolic_bridge.validateContext;
const validateDim = symbolic_bridge.validateDim;

/// Result type for slice validation operations
///
/// This struct encapsulates the slice parameters validation result.
pub const SliceValidationResult = struct {
    /// The validated axis
    axis: usize,
    /// The validated starting index
    start: SymExpr,
    /// The validated ending index
    end: SymExpr,
    /// The validated step size
    step: SymExpr,
};

/// Validate a slice operation before performing it
///
/// This function validates that the slice parameters are valid:
/// 1. Checks that the axis is within bounds
/// 2. Validates the symbolic expressions for start, end, and step
/// 3. Ensures step is non-zero
/// 4. Verifies bounds for concrete dimensions when possible
///
/// Returns a SliceValidationResult which contains the validated parameters.
pub fn validateSlice(ctx: *SymbolicContext, tracker: *const ShapeTracker, axis: usize, start: SymExpr, end: SymExpr, step: SymExpr) ShapeError!SliceValidationResult {
    // Validate context first
    if (!validateContext(ctx)) {
        return ShapeError.InvalidShape;
    }

    // Check if tracker is valid
    if (@intFromPtr(tracker) < symbolic.MIN_VALID_PTR) {
        return ShapeError.InvalidShape;
    }

    if (tracker.data.len == 0) {
        return ShapeError.InvalidShape;
    }

    // Check if axis is valid
    if (axis >= tracker.data.len) return ShapeError.InvalidAxes;

    // Validate expressions
    if (!validateExpr(start) or !validateExpr(end) or !validateExpr(step)) {
        return ShapeError.InvalidExpression;
    }

    // Check if step is non-zero
    const zero = try runSymbolicOp(SymExpr, symbolic.integer(ctx, 0));
    const is_zero = try runSymbolicOp(bool, symbolic.nodesEqual(ctx, step, zero));
    if (is_zero) return ShapeError.ZeroSliceStep;

    // Check if start/end are in bounds
    // This is particularly important for concrete dimensions
    const dim = tracker.dims()[axis];
    if (dim == .Concrete) {
        const dim_size = dim.Concrete;

        // For concrete dimensions, we can attempt to evaluate symbolic expressions
        // to check bounds
        var start_val: i64 = undefined;
        var eval_success = symbolic_bridge.safeEvaluate(ctx, start) catch {
            // If we can't evaluate, we assume it's valid and let runtime checks catch issues
            return SliceValidationResult{
                .axis = axis,
                .start = start,
                .end = end,
                .step = step,
            };
        };
        start_val = @intCast(eval_success);

        if (start_val < 0 or start_val >= dim_size) {
            return ShapeError.InvalidSliceParameters;
        }

        // Similarly check end bounds
        var end_val: i64 = undefined;
        eval_success = symbolic_bridge.safeEvaluate(ctx, end) catch {
            // If we can't evaluate, we assume it's valid
            return SliceValidationResult{
                .axis = axis,
                .start = start,
                .end = end,
                .step = step,
            };
        };
        end_val = @intCast(eval_success);

        if (end_val < 0 or end_val > dim_size) {
            return ShapeError.InvalidSliceParameters;
        }
    } else {
        // For symbolic dimensions, validate the dimension
        if (!validateDim(dim, ctx)) {
            return ShapeError.InvalidShape;
        }
    }

    // All validation passed, return successful result
    return SliceValidationResult{
        .axis = axis,
        .start = start,
        .end = end,
        .step = step,
    };
}

/// Computes a cache key for a slice operation
fn computeSliceKey(_: *ShapeContext, tracker: *const ShapeTracker, axis: usize, start: SymExpr, end: SymExpr, step: SymExpr) !CacheKey {
    var hasher = std.hash.Wyhash.init(0);

    // Hash operation type
    std.hash.autoHash(&hasher, @as(u8, @intFromEnum(std.meta.Tag(ShapeOp).slice)));

    // Hash tracker pointer or its key
    if (tracker.lastOpNode) |node| {
        std.hash.autoHash(&hasher, @intFromPtr(node));
    } else {
        // For trackers without operations, hash their initial dimensions
        for (tracker.initialDims) |dim| {
            cache_mod.hashDimension(&hasher, dim);
        }
    }

    // Hash slice parameters
    std.hash.autoHash(&hasher, axis);
    cache_mod.hashExpression(&hasher, start);
    cache_mod.hashExpression(&hasher, end);
    cache_mod.hashExpression(&hasher, step);

    // Finalize hash
    const hash = hasher.final();

    return CacheKey{
        .hash_value = hash,
        .sourceId = hash,
    };
}

/// Slice a tensor along a specific axis with immutable operations
///
/// This function implements the immutable operation pattern, creating a new
/// ShapeTracker with the slice operation. The original tracker is not modified.
///
/// Parameters:
///   - ctx: The shape context containing allocation and caching resources
///   - tracker: The shape tracker to slice (not modified)
///   - axis: The axis to slice along
///   - start: Starting index (symbolic expression)
///   - end: Ending index (symbolic expression)
///   - step: Step size (symbolic expression)
///
/// Returns:
///   - A new ShapeTracker with the slice operation applied
///   - ShapeError if the operation is invalid or fails
///
/// Example:
///   ```zig
///   const start = try symbolic.integer(ctx.symbolicCtx, 0);
///   const end = try symbolic.integer(ctx.symbolicCtx, 5);
///   const step = try symbolic.integer(ctx.symbolicCtx, 1);
///   const new_tracker = try slice(ctx, tracker, 0, start, end, step);
///   ```
///
/// For convenience, use sliceWithConcreteValues when you have concrete usize values.
pub fn slice(ctx: *ShapeContext, tracker: *const ShapeTracker, axis: usize, start: SymExpr, end: SymExpr, step: SymExpr) ShapeError!*ShapeTracker {
    // Record operation in stats
    if (ctx.options.enable_stats) {
        context_mod.recordOperationPerformed(ctx);
    }

    // Validate slice parameters
    _ = try validateSlice(ctx.symbolicCtx, tracker, axis, start, end, step);

    // Try to find the result in cache
    const operation_key = try computeSliceKey(ctx, tracker, axis, start, end, step);
    if (ctx.options.enable_caching) {
        if (context_mod.getCachedResult(ctx, operation_key)) |cached_result| {
            // Cache hit - record and return cached result
            if (ctx.options.enable_stats) {
                context_mod.recordCacheHit(ctx);
            }
            return cached_result;
        }
    }

    // Record timestamp for performance measurement
    const start_time = if (ctx.options.enable_stats) std.time.nanoTimestamp() else 0;

    // Create a new ShapeOp for the slice operation
    const op_data = ShapeOp{
        .slice = .{
            .axis = axis,
            .start = start,
            .end = end,
            .step = step,
        },
    };

    // Create a new OpNode in the arena
    const new_op_node = try ctx.arenaAllocator.create(OpNode);
    new_op_node.* = .{
        .opData = op_data,
        .previousOps = tracker.lastOpNode,
        .depth = if (tracker.lastOpNode) |node| node.depth + 1 else 0,
    };

    // Create a new ShapeTracker
    const new_tracker = try ctx.allocator.create(ShapeTracker);
    new_tracker.* = .{
        .ctx = ctx,
        .initialDims = tracker.initialDims, // Share initialDims
        .lastOpNode = new_op_node,
        .is_view = true, // Slice creates a view
        .base_offset = tracker.base_offset, // Base offset will be calculated during evaluation
        .contiguous = false, // Slice almost always breaks contiguity
    };

    // Record depth in stats
    if (ctx.options.enable_stats) {
        context_mod.recordChainDepth(ctx, new_op_node.depth);
    }

    // Record completion time and cache the result
    if (ctx.options.enable_stats) {
        const end_time = std.time.nanoTimestamp();
        const elapsed: u64 = @intCast(end_time - start_time);

        if (ctx.options.enable_caching) {
            // Store result in cache with timing information
            try context_mod.cacheResult(ctx, operation_key, new_tracker);
            ctx.stats.cache_misses += 1;
            ctx.stats.operation_time_ns += elapsed;
        }
    } else if (ctx.options.enable_caching) {
        // Just store in cache without timing
        try context_mod.cacheResult(ctx, operation_key, new_tracker);
    }

    return new_tracker;
}

/// Helper function for slicing with concrete usize values
///
/// This function converts concrete usize values to symbolic expressions
/// and then calls the main slice function. This simplifies common use cases
/// where concrete indices are known.
///
/// Parameters:
///   - ctx: The shape context containing allocation and caching resources
///   - tracker: The shape tracker to slice (not modified)
///   - axis: The axis to slice along
///   - start: Starting index as concrete usize
///   - end: Ending index as concrete usize
///   - step: Step size as concrete usize
///
/// Returns:
///   - A new ShapeTracker with the slice operation applied
///   - ShapeError if the operation is invalid or fails
///
/// Example:
///   ```zig
///   const new_tracker = try sliceWithConcreteValues(ctx, tracker, 0, 0, 5, 1);
///   ```
pub fn sliceWithConcreteValues(ctx: *ShapeContext, tracker: *const ShapeTracker, axis: usize, start: usize, end: usize, step: usize) ShapeError!*ShapeTracker {
    // Convert concrete values to symbolic expressions
    const start_expr = try symbolic_bridge.integerExpr(ctx.symbolicCtx, @intCast(start));
    const end_expr = try symbolic_bridge.integerExpr(ctx.symbolicCtx, @intCast(end));
    const step_expr = try symbolic_bridge.integerExpr(ctx.symbolicCtx, @intCast(step));

    // Call the main slice function with explicit type casting
    const start_sym: SymExpr = @ptrCast(start_expr);
    const end_sym: SymExpr = @ptrCast(end_expr);
    const step_sym: SymExpr = @ptrCast(step_expr);
    return slice(ctx, tracker, axis, start_sym, end_sym, step_sym);
}

// Tests for the slice module
test "slice - validation" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);

    // Create a tracker to test
    var tracker = try ShapeTracker.init(ctx, allocator, &[_]usize{ 10, 20, 30 });
    defer tracker.deinit();

    // Test valid slice
    const start = try symbolic_bridge.integerExpr(ctx, 2);
    const end = try symbolic_bridge.integerExpr(ctx, 8);
    const step = try symbolic_bridge.integerExpr(ctx, 2);

    const validation = try validateSlice(ctx, &tracker, 0, start, end, step);
    try testing.expectEqual(@as(usize, 0), validation.axis);
    try testing.expect(symbolic_bridge.dimsEqual(ctx, Dim{ .Symbolic = start }, Dim{ .Symbolic = validation.start }));
    try testing.expect(symbolic_bridge.dimsEqual(ctx, Dim{ .Symbolic = end }, Dim{ .Symbolic = validation.end }));
    try testing.expect(symbolic_bridge.dimsEqual(ctx, Dim{ .Symbolic = step }, Dim{ .Symbolic = validation.step }));

    // Test invalid slice (axis out of bounds)
    try testing.expectError(ShapeError.InvalidAxes, validateSlice(ctx, &tracker, 3, start, end, step));

    // Test invalid slice (zero step)
    const zero_step = try symbolic_bridge.integerExpr(ctx, 0);
    try testing.expectError(ShapeError.ZeroSliceStep, validateSlice(ctx, &tracker, 0, start, end, zero_step));

    // Test invalid slice (start out of bounds)
    const bad_start = try symbolic_bridge.integerExpr(ctx, 15);
    try testing.expectError(ShapeError.InvalidSliceParameters, validateSlice(ctx, &tracker, 0, bad_start, end, step));
}

test "slice - direct mutation" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);

    // Create a tracker to test
    var tracker = try ShapeTracker.init(ctx, allocator, &[_]usize{ 10, 20, 30 });
    defer tracker.deinit();

    // Check original properties
    const original_stride_0 = tracker.strides()[0];
    try testing.expectEqual(@as(usize, 10), tracker.dims()[0].Concrete);
    try testing.expect(!tracker.is_view);
    try testing.expect(tracker.base_offset == null);

    // Create slice parameters
    const start = try symbolic_bridge.integerExpr(ctx, 2);
    const end = try symbolic_bridge.integerExpr(ctx, 8);
    const step = try symbolic_bridge.integerExpr(ctx, 2);

    // Perform slice operation
    try slice(ctx, &tracker, 0, start, end, step);

    // Check results
    try testing.expectEqual(@as(usize, 3), tracker.rank()); // Rank should not change
    try testing.expect(tracker.dims()[0].isSymbolic()); // Now symbolic after slice
    try testing.expectEqual(@as(usize, 20), tracker.dims()[1].Concrete); // Unchanged
    try testing.expectEqual(@as(usize, 30), tracker.dims()[2].Concrete); // Unchanged

    // Step is reflected in stride
    const new_stride_0 = tracker.strides()[0];
    const expected_stride = try symbolic_bridge.mulExpr(ctx, original_stride_0, step);
    try testing.expect(symbolic_bridge.dimsEqual(ctx, Dim{ .Symbolic = expected_stride }, Dim{ .Symbolic = new_stride_0 }));

    // Should now be a view
    try testing.expect(tracker.is_view);
    try testing.expect(tracker.base_offset != null);

    // Base offset calculation: start * original_stride
    const expected_offset = try symbolic_bridge.mulExpr(ctx, start, original_stride_0);
    try testing.expect(symbolic_bridge.dimsEqual(ctx, Dim{ .Symbolic = expected_offset }, Dim{ .Symbolic = tracker.base_offset.? }));
}

test "slice - consecutive slices" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);

    // Create a tracker to test
    var tracker = try ShapeTracker.init(ctx, allocator, &[_]usize{ 100, 200, 300 });
    defer tracker.deinit();

    // First slice on axis 0
    {
        const start0 = try symbolic_bridge.integerExpr(ctx, 10);
        const end0 = try symbolic_bridge.integerExpr(ctx, 50);
        const step0 = try symbolic_bridge.integerExpr(ctx, 2);
        try slice(ctx, &tracker, 0, start0, end0, step0);
    }

    // Then slice on axis 1
    {
        const start1 = try symbolic_bridge.integerExpr(ctx, 50);
        const end1 = try symbolic_bridge.integerExpr(ctx, 150);
        const step1 = try symbolic_bridge.integerExpr(ctx, 5);
        try slice(ctx, &tracker, 1, start1, end1, step1);
    }

    // Verify both slices were applied
    try testing.expectEqual(@as(usize, 3), tracker.rank());
    try testing.expect(tracker.dims()[0].isSymbolic());
    try testing.expect(tracker.dims()[1].isSymbolic());
    try testing.expectEqual(@as(usize, 300), tracker.dims()[2].Concrete);

    // Should still be a view
    try testing.expect(tracker.is_view);
    try testing.expect(tracker.base_offset != null);
}

test "sliceWithConcreteValues - immutable API" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    // Create a shape context
    const shape_ctx = try context_mod.createContext(allocator);
    defer context_mod.destroyContext(shape_ctx);

    // Create a shape tracker with concrete dimensions
    var dims = [_]Dim{
        Dim{ .Concrete = 10 },
        Dim{ .Concrete = 20 },
    };
    const tracker = try tracker_mod.createTracker(shape_ctx, &dims);
    defer tracker_mod.destroyTracker(shape_ctx, tracker);

    // Use the helper function to slice with concrete values
    const sliced = try sliceWithConcreteValues(shape_ctx, tracker, 0, 2, 8, 2);
    defer tracker_mod.destroyTracker(shape_ctx, sliced);

    // Verify the original tracker is unchanged
    try testing.expectEqual(@as(usize, 10), tracker.dims()[0].Concrete);
    try testing.expectEqual(@as(usize, 20), tracker.dims()[1].Concrete);
    try testing.expect(!tracker.is_view);

    // Verify the new tracker has the sliced dimensions
    try testing.expectEqual(@as(usize, 3), sliced.dims()[0].Concrete); // (8-2)/2 = 3
    try testing.expectEqual(@as(usize, 20), sliced.dims()[1].Concrete); // Unchanged
    try testing.expect(sliced.is_view);
    try testing.expect(sliced.base_offset != null);

    // Apply another slice to the sliced tracker
    const sliced2 = try sliceWithConcreteValues(shape_ctx, sliced, 1, 5, 15, 1);
    defer tracker_mod.destroyTracker(shape_ctx, sliced2);

    // Verify the second slice
    try testing.expectEqual(@as(usize, 3), sliced2.dims()[0].Concrete); // Unchanged
    try testing.expectEqual(@as(usize, 10), sliced2.dims()[1].Concrete); // 15-5 = 10
    try testing.expect(sliced2.is_view);
    try testing.expect(sliced2.base_offset != null);
}
