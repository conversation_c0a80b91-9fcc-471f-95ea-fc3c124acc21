const std = @import("std");
const symbolic = @import("symbolic");
const types_mod = @import("../types.zig");
const layout_mod = @import("../layout.zig");
// Use types_mod to access ShapeTracker instead of importing tracker.zig directly
const error_mod = @import("../errors.zig");
const symbolic_bridge = @import("../symbolic_bridge.zig");
const memory_pool = @import("../memory_pool.zig");
const context_mod = @import("../context/init.zig");
const cache_mod = @import("../context/cache.zig");

const Allocator = std.mem.Allocator;
const ShapeTracker = types_mod.ShapeTracker;
const SymExpr = symbolic.SymExpr;
const Dim = types_mod.Dim;
const Expr = types_mod.Expr;
const PaddingInfo = types_mod.PaddingInfo;
const MaskInfo = types_mod.MaskInfo;
const ShapeError = error_mod.ShapeError;
const SymbolicContext = types_mod.SymbolicContext;
const ShapeContext = types_mod.ShapeContext;
const ShapeOp = types_mod.ShapeOp;
const OpNode = types_mod.OpNode;
const CacheKey = types_mod.CacheKey;

// Basic functions for error handling and validation
const MIN_VALID_PTR = symbolic_bridge.MIN_VALID_PTR;

// Helper functions for validation
fn validateContext(ctx: *SymbolicContext) bool {
    return @intFromPtr(ctx) >= MIN_VALID_PTR;
}

fn validateDim(dim: Dim, ctx: *SymbolicContext) bool {
    return switch (dim) {
        .concrete => true,
        .symbolic => |expr| @intFromPtr(expr) >= MIN_VALID_PTR and
            @intFromPtr(ctx) >= MIN_VALID_PTR,
    };
}

fn runSymbolicOp(comptime ResultType: type, operation: anytype) !ResultType {
    return operation catch |err| {
        // Use the consistent error translation from the bridge module
        return symbolic_bridge.translateSymbolicError(err);
    };
}

/// Result type for permute validation operations
///
/// This struct encapsulates the validation result and provides explicit ownership
/// semantics for the axes array. The caller is responsible for calling deinit()
/// when done using the result.
pub const PermuteValidationResult = struct {
    /// The validated permutation axes
    axes: []usize,
    /// The allocator used for the axes
    allocator: Allocator,

    /// Free the memory allocated for axes
    pub fn deinit(self: @This()) void {
        self.allocator.free(self.axes);
    }
};

/// Validate a permutation operation before performing it
///
/// This function validates that the provided permutation axes are valid for the shape:
/// 1. Validates context and tracker
/// 2. Checks that the number of axes matches the shape rank
/// 3. Ensures all axes are within bounds
/// 4. Verifies there are no duplicate axes
///
/// Returns a PermuteValidationResult which must be destroyed with deinit() when done.
pub fn validatePermute(ctx: *SymbolicContext, tracker: *const ShapeTracker, axes_input: anytype) ShapeError!PermuteValidationResult {
    // Validate context
    if (!validateContext(ctx)) {
        return ShapeError.InvalidShape;
    }

    // Check if tracker is valid
    if (@intFromPtr(tracker) < MIN_VALID_PTR) {
        return ShapeError.InvalidShape;
    }

    // Get the dimensions and check the rank
    const rank = tracker.rank();
    if (rank == 0) {
        return ShapeError.InvalidShape;
    }

    // Validate dimensions
    const dims = tracker.dims();
    for (dims) |dim| {
        if (!validateDim(dim, ctx)) {
            return ShapeError.InvalidAxes;
        }
    }

    // For compatibility, we'll use a temporary allocator
    const allocator = std.heap.page_allocator;

    // Convert input to axes array
    const axes_converter = types_mod.ToAxes(@TypeOf(axes_input));
    // Create a copy of the axes for our own use
    const axes_input_slice = axes_converter.toAxes(axes_input);
    const axes = try allocator.dupe(usize, axes_input_slice);
    errdefer allocator.free(axes);

    // Validate axes
    if (axes.len != rank) {
        return ShapeError.InvalidAxes;
    }

    // Check for out of bounds and duplicates
    var seen = try allocator.alloc(bool, rank);
    defer allocator.free(seen);

    for (seen) |*s| {
        s.* = false;
    }

    for (axes) |axis| {
        if (axis >= rank) {
            return ShapeError.InvalidAxes; // Use InvalidAxes instead of AxisOutOfBounds
        }

        if (seen[axis]) {
            return ShapeError.InvalidAxes; // Use InvalidAxes for duplicate axis errors
        }

        seen[axis] = true;
    }

    // All validation passed, return successful result
    return PermuteValidationResult{
        .axes = axes,
        .allocator = allocator,
    };
}

/// Computes a cache key for a permute operation
fn computePermuteKey(_: *ShapeContext, tracker: *const ShapeTracker, axes: []const usize) !CacheKey {
    var hasher = std.hash.Wyhash.init(0);

    // Hash operation type
    std.hash.autoHash(&hasher, @as(u8, @intFromEnum(std.meta.Tag(ShapeOp).transpose)));

    // Hash tracker pointer or its key
    if (tracker.lastOpNode) |node| {
        std.hash.autoHash(&hasher, @intFromPtr(node));
    } else {
        // For trackers without operations, hash their initial dimensions
        for (tracker.initialDims) |dim| {
            cache_mod.hashDimension(&hasher, dim);
        }
    }

    // Hash permutation
    for (axes) |axis| {
        std.hash.autoHash(&hasher, axis);
    }

    // Finalize hash
    const hash = hasher.final();

    return CacheKey{
        .hash_value = hash,
        .sourceId = hash,
    };
}

/// Convert permutation axes to symbolic expressions
/// Used for storing in the operation node
fn axesToExpressions(ctx: *ShapeContext, axes: []const usize) ![]Expr {
    // Allocate expressions array in arena for persistent storage
    const perm_exprs = try ctx.arenaAllocator.alloc(Expr, axes.len);

    // Convert each axis to a symbolic expression
    for (axes, 0..) |axis, i| {
        const sym_expr = try symbolic.integer(symbolic_bridge.getSymbolicContext(ctx.symbolicCtx), @intCast(axis));
        perm_exprs[i] = symbolic.toCommonNode(sym_expr);
    }

    return perm_exprs;
}

/// @deprecated Use axesToExpressions instead
const axes_to_expressions = axesToExpressions;

/// Permute tensor dimensions using immutable operations
///
/// This function implements the immutable operation pattern, creating a new
/// ShapeTracker with the permutation. The original tracker is not modified.
///
/// Parameters:
///   - ctx: The shape context containing allocation and caching resources
///   - tracker: The shape tracker to permute (not modified)
///   - axes_input: The new dimension ordering, can be array, slice, or compatible type
///
/// Returns:
///   - A new ShapeTracker with the permutation applied
///   - ShapeError if the operation is invalid or fails
///
/// Example:
///   ```zig
///   const new_tracker = try permute(ctx, tracker, &[_]usize{1, 0, 2});
///   ```
pub fn permute(ctx: *ShapeContext, tracker: *const ShapeTracker, axes_input: anytype) ShapeError!*ShapeTracker {
    // Record operation in stats
    if (ctx.options.enable_stats) {
        context_mod.recordOperationPerformed(ctx);
    }

    // Validate and convert the axes
    // Use the correct context type - ctx.symbolicCtx is already types_mod.SymbolicContext
    const validation = try validatePermute(ctx.symbolicCtx, tracker, axes_input);
    defer validation.deinit();

    // Try to find the result in cache
    const operation_key = try computePermuteKey(ctx, tracker, validation.axes);
    if (ctx.options.enable_caching) {
        if (context_mod.getCachedResult(ctx, operation_key)) |cached_result| {
            // Cache hit - record and return cached result
            if (ctx.options.enable_stats) {
                context_mod.recordCacheHit(ctx);
            }
            return cached_result;
        }
    }

    // Record timestamp for performance measurement
    const start_time = if (ctx.options.enable_stats) std.time.nanoTimestamp() else 0;

    // Convert axes to expressions and store in the arena
    const perm_exprs = try error_mod.trySymbolic(axesToExpressions(ctx, validation.axes));

    // Create a new ShapeOp for the permute operation
    const op_data = ShapeOp{
        .transpose = .{
            .perm = perm_exprs,
        },
    };

    // Create a new OpNode in the arena
    const new_op_node = try ctx.arenaAllocator.create(OpNode);
    new_op_node.* = .{
        .opData = op_data,
        .previousOps = tracker.lastOpNode,
        .depth = if (tracker.lastOpNode) |node| node.depth + 1 else 0,
    };

    // Create a new ShapeTracker
    const new_tracker = try ctx.allocator.create(ShapeTracker);
    new_tracker.* = .{
        .ctx = ctx,
        .initialDims = tracker.initialDims, // Share initialDims
        .lastOpNode = new_op_node,
        .is_view = true, // Transpose creates a view
        .base_offset = tracker.base_offset, // Preserve base offset
        .contiguous = false, // Transpose almost always breaks contiguity
    };

    // Record depth in stats
    if (ctx.options.enable_stats) {
        context_mod.recordChainDepth(ctx, new_op_node.depth);
    }

    // Record completion time and cache the result
    if (ctx.options.enable_stats) {
        const end_time = std.time.nanoTimestamp();
        const elapsed = @as(u64, @intCast(end_time - start_time));

        if (ctx.options.enable_caching) {
            // Store result in cache with timing information
            try context_mod.cacheResult(ctx, operation_key, new_tracker);
            ctx.stats.cache_misses += 1;
            ctx.stats.total_computation_time_ns += elapsed;
        }
    } else if (ctx.options.enable_caching) {
        // Just store in cache without timing
        try context_mod.cacheResult(ctx, operation_key, new_tracker);
    }

    return new_tracker;
}

// Tests for the permute module
test "permute - validation" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx = try symbolic.createContext(allocator);
    defer symbolic.destroyContext(ctx);

    // Create a tracker to test
    var tracker = try ShapeTracker.init(ctx, allocator, &[_]usize{ 2, 3, 4 });
    defer tracker.deinit();

    // Test valid permutation
    const valid_result = try validatePermute(ctx, &tracker, &[_]usize{ 2, 0, 1 });
    defer valid_result.deinit();
    try testing.expectEqual(@as(usize, 3), valid_result.axes.len);
    try testing.expectEqual(@as(usize, 2), valid_result.axes[0]);
    try testing.expectEqual(@as(usize, 0), valid_result.axes[1]);
    try testing.expectEqual(@as(usize, 1), valid_result.axes[2]);

    // Test invalid permutation (out of bounds axis)
    try testing.expectError(ShapeError.InvalidAxes, validatePermute(ctx, &tracker, &[_]usize{ 0, 1, 3 }));

    // Test invalid permutation (wrong number of axes)
    try testing.expectError(ShapeError.InvalidAxes, validatePermute(ctx, &tracker, &[_]usize{ 0, 1 }));

    // Test invalid permutation (duplicate axis)
    try testing.expectError(ShapeError.InvalidAxes, validatePermute(ctx, &tracker, &[_]usize{ 0, 0, 1 }));
}

test "permute - immutable operation" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    // First create a ShapeContext with idiomatic function name
    var shape_ctx = try types_mod.ShapeContext.createContext(allocator, .{
        .enable_stats = false,
        .enable_caching = true,
        .max_cached_shapes = 16,
    });
    defer shape_ctx.destroyContext();

    // Create a tracker with idiomatic dim constructor
    var initial_tracker = try types_mod.createTracker(shape_ctx, &[_]Dim{
        types_mod.dim(null, 2), // Using idiomatic dim constructor
        types_mod.dim(null, 3),
        types_mod.dim(null, 4),
    });
    defer initial_tracker.deinit();

    // Permute the tracker, which should return a new tracker
    const new_tracker = try permute(shape_ctx, initial_tracker, &[_]usize{ 2, 0, 1 });
    defer new_tracker.deinit();

    // Since the permute operation doesn't immediately modify the shape data,
    // we need to resolve the shape by getting the effective dimensions
    const original_dims = try types_mod.getShape(initial_tracker); // Using idiomatic name
    defer shape_ctx.allocator.free(original_dims);
    const new_dims = try types_mod.getShape(new_tracker); // Using idiomatic name
    defer shape_ctx.allocator.free(new_dims);

    // Original tracker should be unchanged
    try testing.expectEqual(@as(usize, 3), original_dims.len);
    try testing.expectEqual(@as(usize, 2), original_dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), original_dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), original_dims[2].concrete);

    // New tracker should have permuted dimensions
    try testing.expectEqual(@as(usize, 3), new_dims.len);
    try testing.expectEqual(@as(usize, 4), new_dims[0].concrete);
    try testing.expectEqual(@as(usize, 2), new_dims[1].concrete);
    try testing.expectEqual(@as(usize, 3), new_dims[2].concrete);

    // Check the operation chain - new tracker should have a lastOpNode
    try testing.expect(new_tracker.lastOpNode != null);

    // If we have a lastOpNode, verify it's a permute operation
    if (new_tracker.lastOpNode) |node| {
        try testing.expect(std.meta.activeTag(node.opData) == .transpose);
    }
}

test "permute - with symbolic dimensions" {
    const testing = std.testing;
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    // First create a ShapeContext with idiomatic function
    var shape_ctx = try types_mod.ShapeContext.createContext(allocator, .{
        .enable_stats = false,
        .enable_caching = true,
        .max_cached_shapes = 16,
    });
    defer shape_ctx.destroyContext();

    // Create a symbolic dimension using the bridge's idiomatic function
    const batch_dim = try symbolic_bridge.symbolExpr(shape_ctx.symbolicCtx, "batch");

    // Create a tracker with idiomatic dim constructors
    var initial_tracker = try types_mod.createTracker(shape_ctx, &[_]Dim{
        types_mod.dim(null, 2), // Using idiomatic concrete dim constructor
        symbolic_bridge.dimFromExpr(batch_dim), // Using idiomatic symbolic dim constructor
        types_mod.dim(null, 4),
    });
    defer initial_tracker.deinit();

    // Verify initial state
    try testing.expectEqual(@as(usize, 3), initial_tracker.rank());
    try testing.expectEqual(@as(usize, 2), initial_tracker.dims()[0].concrete);
    try testing.expect(initial_tracker.dims()[1].isSymbolic());
    try testing.expectEqual(@as(usize, 4), initial_tracker.dims()[2].concrete);

    // Permute the tracker, which should return a new tracker
    const new_tracker = try permute(shape_ctx, initial_tracker, &[_]usize{ 2, 0, 1 });
    defer new_tracker.deinit();

    // Since the permute operation doesn't immediately modify the shape data,
    // we need to resolve the shape by getting the effective dimensions
    const original_dims = try types_mod.getShape(initial_tracker); // Idiomatic name
    defer shape_ctx.allocator.free(original_dims);
    const new_dims = try types_mod.getShape(new_tracker); // Idiomatic name
    defer shape_ctx.allocator.free(new_dims);

    // Check the result - dimensions should be permuted
    try testing.expectEqual(@as(usize, 3), new_dims.len);
    try testing.expectEqual(@as(usize, 4), new_dims[0].concrete);
    try testing.expectEqual(@as(usize, 2), new_dims[1].concrete);
    try testing.expect(new_dims[2].isSymbolic());

    // Original tracker should be unchanged
    try testing.expectEqual(@as(usize, 3), original_dims.len);
    try testing.expectEqual(@as(usize, 2), original_dims[0].concrete);
    try testing.expect(original_dims[1].isSymbolic());
    try testing.expectEqual(@as(usize, 4), original_dims[2].concrete);
}

/// Mutable permutation implementation for backwards compatibility
/// @deprecated Use permute instead which returns a new tracker
pub fn permuteMutating(ctx: *SymbolicContext, tracker: *ShapeTracker, axes: anytype) !void {
    // Validate the operation
    _ = try validatePermute(ctx, tracker, axes);

    // Create a temporary ShapeContext from the SymbolicContext
    const temp_shape_ctx = try symbolic_bridge.temporaryShapeContext(symbolic_bridge.adaptSymbolicContext(ctx), tracker.ctx.allocator);
    defer symbolic_bridge.releaseContext(temp_shape_ctx, tracker.ctx.allocator);

    // Get the permuted shape
    const permuted_tracker = try permute(temp_shape_ctx, tracker, axes);
    defer tracker.ctx.allocator.destroy(permuted_tracker);

    // Update the original tracker with the permuted shape's information
    // First free existing resources
    tracker.ctx.allocator.free(tracker.dims);
    tracker.ctx.allocator.free(tracker.strides);

    // Copy the permuted shape's dimensions and strides to the original tracker
    tracker.dims = try tracker.ctx.allocator.dupe(Dim, permuted_tracker.dims);
    tracker.strides = try tracker.ctx.allocator.dupe(isize, permuted_tracker.strides);
    tracker.is_view = permuted_tracker.is_view;
    tracker.base_offset = permuted_tracker.base_offset;
    tracker.contiguous = permuted_tracker.contiguous;
    tracker.lastOpNode = permuted_tracker.lastOpNode;
}
