const std = @import("std");

/// Tensor module specific errors
/// These errors relate to tensor operations and manipulations
pub const TensorError = error{
    // Tensor validation errors
    InvalidTensor,
    InvalidDimensions,
    InvalidShape,
    IncompatibleShapes,
    
    // Operation-specific errors
    UnsupportedDataType,
    UnsupportedOperation,
    ShapeMismatch,
    BroadcastFailure,
    IncompatibleShapesForExpand,
    CanOnlyExpandDimensionsOfSize1,
    
    // Dimension-specific errors
    MatmulRequires2DTensors,
    BmmRequires3DTensors,
    SymbolicDimsNotYetSupported,
    IncompatibleDimensionsForMatmul,
    
    // General errors
    OperationNotImplemented,
    InvalidIndex,
    OutOfBounds,
    InvalidArgument,
    InvalidArgumentCount,
};

/// Format tensor errors for display
pub fn format(
    err: TensorError,
    comptime fmt: []const u8,
    options: std.fmt.FormatOptions,
    writer: anytype,
) !void {
    _ = fmt;
    _ = options;
    
    const description = switch (err) {
        error.InvalidTensor => "Invalid tensor",
        error.InvalidDimensions => "Invalid tensor dimensions",
        error.InvalidShape => "Invalid tensor shape",
        error.IncompatibleShapes => "Incompatible tensor shapes",
        error.UnsupportedDataType => "Unsupported data type",
        error.UnsupportedOperation => "Unsupported tensor operation",
        error.ShapeMismatch => "Shape mismatch for operation",
        error.BroadcastFailure => "Cannot broadcast tensors with these shapes",
        error.IncompatibleShapesForExpand => "Incompatible shapes for tensor expansion",
        error.CanOnlyExpandDimensionsOfSize1 => "Can only expand dimensions of size 1",
        error.MatmulRequires2DTensors => "Matrix multiplication requires 2D tensors",
        error.BmmRequires3DTensors => "Batch matrix multiplication requires 3D tensors",
        error.SymbolicDimsNotYetSupported => "Symbolic dimensions not yet supported for this operation",
        error.IncompatibleDimensionsForMatmul => "Incompatible inner dimensions for matrix multiplication",
        error.OperationNotImplemented => "Operation not implemented",
        error.InvalidIndex => "Invalid tensor index",
        error.OutOfBounds => "Index out of bounds",
        error.InvalidArgument => "Invalid argument for tensor operation",
        error.InvalidArgumentCount => "Incorrect number of arguments for tensor operation",
    };
    
    try writer.writeAll(description);
}