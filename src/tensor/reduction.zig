/// Reduction operations for tensors
///
/// This module provides operations that reduce tensors along specified dimensions
/// such as sum, mean, max, min, and product.
const std = @import("std");
const core = @import("core");
const Core = core.Core;
const core_types = core.types;
const utils = @import("utils.zig");
const types = @import("types.zig");

// Use common types from types module
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ShapeId = types.ShapeId;
const ZingError = types.ZingError;

/// Sum along specified dimensions
pub fn sum(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId {
    if (dims) |d| {
        const node = ctx.graph.getNode(a) orelse return error.InvalidNode;
        const view = ctx.shape.getView(node.output_view_id);
        const shape = view.shape_id;
        
        // Calculate output shape after reduction
        const out_shape_id = try calculateReducedShape(ctx, shape, d, keepdim);
        const out_view_raw = try ctx.shape.newDefaultView(out_shape_id);
        const out_view = @as(core_types.ViewId, out_view_raw);
        const result = try ctx.graph.newNodeReduceSum(a, d, out_view);
        return result;
    } else {
        // Sum over all dimensions
        const node = ctx.graph.getNode(a) orelse return error.InvalidNode;
        const view = ctx.shape.getView(node.output_view_id);
        const shape = view.shape_id;
        const dims_count = ctx.shape.getShape(shape).dims.len;
        
        // Create array of all dimension indices
        const all_dims = try ctx.arena.allocator().alloc(i32, dims_count);
        for (all_dims, 0..) |*dim, i| {
            dim.* = @intCast(i);
        }
        
        const out_shape_id = try calculateReducedShape(ctx, shape, all_dims, keepdim);
        const out_view_raw = try ctx.shape.newDefaultView(out_shape_id);
        const out_view = @as(core_types.ViewId, out_view_raw);
        const result = try ctx.graph.newNodeReduceSum(a, all_dims, out_view);
        return result;
    }
}

/// Maximum along specified dimensions
pub fn max(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId {
    if (dims) |d| {
        const node = ctx.graph.getNode(a) orelse return error.InvalidNode;
        const view = ctx.shape.getView(node.output_view_id);
        const shape = view.shape_id;
        const out_shape_id = try calculateReducedShape(ctx, shape, d, keepdim);
        const out_view_raw = try ctx.shape.newDefaultView(out_shape_id);
        const out_view = @as(core_types.ViewId, out_view_raw);
        const result = try ctx.graph.newNodeReduceMax(a, d, out_view);
        return result;
    } else {
        // Max over all dimensions
        const node = ctx.graph.getNode(a) orelse return error.InvalidNode;
        const view = ctx.shape.getView(node.output_view_id);
        const shape = view.shape_id;
        const dims_count = ctx.shape.getShape(shape).dims.len;
        
        const all_dims = try ctx.arena.allocator().alloc(i32, dims_count);
        for (all_dims, 0..) |*dim, i| {
            dim.* = @intCast(i);
        }
        
        const out_shape_id = try calculateReducedShape(ctx, shape, all_dims, keepdim);
        const out_view_raw = try ctx.shape.newDefaultView(out_shape_id);
        const out_view = @as(core_types.ViewId, out_view_raw);
        const result = try ctx.graph.newNodeReduceMax(a, all_dims, out_view);
        return result;
    }
}

/// Minimum along specified dimensions (decomposed as -max(-a))
pub fn min(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId {
    // reduce_min(a) = -reduce_max(-a)
    const pointwise = @import("pointwise.zig");
    const neg_a = try pointwise.neg(ctx, a);
    const max_neg_a = try max(ctx, neg_a, dims, keepdim);
    return try pointwise.neg(ctx, max_neg_a);
}

/// Mean along specified dimensions (decomposed as sum / count)
pub fn mean(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId {
    // First get the sum
    const sum_result = try sum(ctx, a, dims, keepdim);
    
    // Calculate the count of elements being averaged using symbolic expressions
    const node = ctx.graph.getNode(a) orelse return error.InvalidNode;
    const view = ctx.shape.getView(node.output_view_id);
    const shape = view.shape_id;
    const shape_info = ctx.shape.getShape(shape);
    
    var count_expr = try ctx.symbolic.newIntegerExpr(1);
    var has_symbolic = false;
    
    if (dims) |d| {
        // Count elements in specified dimensions
        for (d) |dim_idx| {
            const normalized_idx = if (dim_idx < 0) 
                @as(usize, @intCast(@as(i64, @intCast(shape_info.dims.len)) + dim_idx))
            else 
                @as(usize, @intCast(dim_idx));
            const dim_expr = shape_info.dims[normalized_idx];
            count_expr = try ctx.symbolic.newBinaryExpr(.multiply, count_expr, dim_expr);
            // Check if we can evaluate this expression to determine if it's symbolic
            const is_evaluable = ctx.symbolic.evaluate(dim_expr, null) catch null;
            if (is_evaluable == null) {
                has_symbolic = true;
            }
        }
    } else {
        // Count all elements
        for (shape_info.dims) |dim_expr| {
            count_expr = try ctx.symbolic.newBinaryExpr(.multiply, count_expr, dim_expr);
            // Check if we can evaluate this expression to determine if it's symbolic
            const is_evaluable = ctx.symbolic.evaluate(dim_expr, null) catch null;
            if (is_evaluable == null) {
                has_symbolic = true;
            }
        }
    }
    
    // For concrete cases, we can evaluate to get the actual count
    const count = if (has_symbolic) 
        1.0  // Use 1.0 as placeholder, will be handled symbolically
    else 
        @as(f32, @floatFromInt(try ctx.symbolic.evaluate(count_expr, null)));
    
    // Create constant for count with broadcasting
    const pointwise = @import("pointwise.zig");
    const count_node = try utils.createBroadcastableConstant(ctx, sum_result, count);
    
    // Divide sum by count
    return try pointwise.div(ctx, sum_result, count_node);
}

/// Product along specified dimensions (decomposed using exp(sum(log(x))))
pub fn prod(ctx: *Core, a: NodeId, dims: ?[]const i32, keepdim: bool) !NodeId {
    const pointwise = @import("pointwise.zig");
    
    // Take log of input
    const log_a = try pointwise.log(ctx, a);
    
    // Sum the logs
    const sum_logs = try sum(ctx, log_a, dims, keepdim);
    
    // Take exp to get product
    return try pointwise.exp(ctx, sum_logs);
}

/// Helper function to calculate shape after reduction
fn calculateReducedShape(ctx: *Core, shape_id: ShapeId, dims: []const i32, keepdim: bool) !ShapeId {
    const shape = ctx.shape.getShape(shape_id);
    const allocator = ctx.arena.allocator();
    
    // Mark which dimensions to reduce
    var reduce_mask = try allocator.alloc(bool, shape.dims.len);
    for (reduce_mask) |*m| m.* = false;
    
    for (dims) |dim_idx| {
        const idx = if (dim_idx < 0) 
            @as(usize, @intCast(@as(i64, @intCast(shape.dims.len)) + dim_idx))
        else 
            @as(usize, @intCast(dim_idx));
        reduce_mask[idx] = true;
    }
    
    // Calculate output shape
    var out_dims = std.ArrayList(*core.types.Expr).init(allocator);
    defer out_dims.deinit();
    
    for (shape.dims, 0..) |dim_expr, i| {
        if (reduce_mask[i]) {
            if (keepdim) {
                const one_expr = try ctx.symbolic.newIntegerExpr(1);
                try out_dims.append(one_expr);
            }
        } else {
            try out_dims.append(dim_expr);
        }
    }
    
    return try ctx.shape.newShape(out_dims.items);
}