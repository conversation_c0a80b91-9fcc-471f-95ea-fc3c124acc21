/// Tensor Module - Object-Oriented API for Tensor Operations (V2)
///
/// This file implements the Tensor struct that provides method chaining
/// for building computation graphs. It wraps node IDs and delegates to
/// the functional API in other files.

const std = @import("std");
const core = @import("core");
const utils_mod = @import("utils.zig");
const types_mod = @import("types.zig");

// Import types directly from core through utils
const NodeId = utils_mod.NodeId;
const ViewId = utils_mod.ViewId;
const ShapeId = utils_mod.ShapeId;
const ZingError = utils_mod.ZingError;


// Export operation modules
pub const creation = @import("creation.zig");
pub const linalg = @import("linalg.zig");
pub const pointwise = @import("pointwise.zig");
pub const manipulation = @import("manipulation.zig");
pub const reduction = @import("reduction.zig");

// Export utilities
pub const utils = utils_mod;

// Export type system
pub const types = types_mod;

/// Tensor provides an object-oriented API with method chaining
/// Built on top of the functional API (node IDs)
pub const Tensor = struct {
    ctx: *core.Core,
    id: NodeId,
    
    /// Create a constant tensor
    pub fn constant(ctx: *core.Core, data: anytype) !Tensor {
        const id = try creation.constant(ctx, data);
        return Tensor{ .ctx = ctx, .id = id };
    }
    
    /// Create a tensor filled with zeros
    pub fn zeros(ctx: *core.Core, shape_dims: []const *types.Expr) !Tensor {
        const id = try creation.zeros(ctx, shape_dims);
        return Tensor{ .ctx = ctx, .id = id };
    }
    
    /// Create a tensor filled with ones
    pub fn ones(ctx: *core.Core, shape_dims: []const *types.Expr) !Tensor {
        const id = try creation.ones(ctx, shape_dims);
        return Tensor{ .ctx = ctx, .id = id };
    }
    
    // Element-wise operations
    
    pub fn add(self: Tensor, other: Tensor) !Tensor {
        const id = try pointwise.add(self.ctx, self.id, other.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn sub(self: Tensor, other: Tensor) !Tensor {
        const id = try pointwise.sub(self.ctx, self.id, other.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn mul(self: Tensor, other: Tensor) !Tensor {
        const id = try pointwise.mul(self.ctx, self.id, other.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn div(self: Tensor, other: Tensor) !Tensor {
        const id = try pointwise.div(self.ctx, self.id, other.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn neg(self: Tensor) !Tensor {
        const id = try pointwise.neg(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn exp(self: Tensor) !Tensor {
        const id = try pointwise.exp(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn log(self: Tensor) !Tensor {
        const id = try pointwise.log(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn sqrt(self: Tensor) !Tensor {
        const id = try pointwise.sqrt(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn abs(self: Tensor) !Tensor {
        const id = try pointwise.abs(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn sin(self: Tensor) !Tensor {
        const id = try pointwise.sin(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn cos(self: Tensor) !Tensor {
        const id = try pointwise.cos(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn tanh(self: Tensor) !Tensor {
        const id = try pointwise.tanh(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn relu(self: Tensor) !Tensor {
        const id = try pointwise.relu(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    // Linear algebra operations
    
    pub fn matmul(self: Tensor, other: Tensor) !Tensor {
        const id = try linalg.matmul(self.ctx, self.id, other.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    pub fn dot(self: Tensor, other: Tensor) !Tensor {
        const id = try linalg.dot(self.ctx, self.id, other.id);
        return Tensor{ .ctx = self.ctx, .id = id };
    }
    
    // TODO: Implement outer product in linalg module
    // pub fn outer(self: Tensor, other: Tensor) !Tensor {
    //     const id = try linalg.outer(self.ctx, self.id, other.id);
    //     return Tensor{ .ctx = self.ctx, .id = id };
    // }
    
    // Shape manipulation
    
    pub fn reshape(self: Tensor, new_shape: []const *types.Expr) !Tensor {
        // Convert expressions to i64 for the manipulation function
        var shape_i64 = try self.ctx.arena.allocator().alloc(i64, new_shape.len);
        for (new_shape, 0..) |expr, i| {
            const val = self.ctx.symbolic.evaluate(expr, null) catch {
                return error.CannotReshapeWithSymbolicDims;
            };
            shape_i64[i] = @intCast(val);
        }
        const result_raw = try manipulation.reshape(self.ctx, self.id, shape_i64);
        const result_id = result_raw;
        return Tensor{ .ctx = self.ctx, .id = result_id };
    }
    
    pub fn transpose(self: Tensor, axes: ?[]const u32) !Tensor {
        // Convert axes to dim0 and dim1 for the manipulation function
        var dim0: ?i64 = null;
        var dim1: ?i64 = null;
        if (axes) |ax| {
            // If axes provided, use the specific transpose dimensions
            if (ax.len >= 2) {
                dim0 = @intCast(ax[0]);
                dim1 = @intCast(ax[1]);
            }
        }
        const result_raw = try manipulation.transpose(self.ctx, self.id, dim0, dim1);
        const result_id = result_raw;
        return Tensor{ .ctx = self.ctx, .id = result_id };
    }
    
    pub fn permute(self: Tensor, axes: []const u32) !Tensor {
        // Convert u32 axes to i32 for manipulation function
        const axes_i32 = try self.ctx.arena.allocator().alloc(i32, axes.len);
        for (axes, 0..) |axis, i| {
            axes_i32[i] = @intCast(axis);
        }
        const result = try manipulation.permute(self.ctx, self.id, axes_i32);
        return Tensor{ .ctx = self.ctx, .id = result };
    }
    
    pub fn squeeze(self: Tensor, axes: ?[]const u32) !Tensor {
        var axes_i64: ?[]const i64 = null;
        if (axes) |ax| {
            const converted = try self.ctx.arena.allocator().alloc(i64, ax.len);
            for (ax, 0..) |axis, i| {
                converted[i] = @intCast(axis);
            }
            axes_i64 = converted;
        }
        const result_raw = try manipulation.squeeze(self.ctx, self.id, axes_i64);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    pub fn unsqueeze(self: Tensor, axis: u32) !Tensor {
        // Convert u32 to i32 for manipulation function
        const axis_i32: i32 = @intCast(axis);
        const result_raw = try manipulation.unsqueeze(self.ctx, self.id, axis_i32);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    pub fn expand(self: Tensor, shape_dims: []const *types.Expr) !Tensor {
        // Convert expressions to i64 for the manipulation function
        var shape_i64 = try self.ctx.arena.allocator().alloc(i64, shape_dims.len);
        for (shape_dims, 0..) |expr, i| {
            const val = self.ctx.symbolic.evaluate(expr, null) catch {
                return error.CannotExpandWithSymbolicDims;
            };
            shape_i64[i] = @intCast(val);
        }
        
        const result_raw = try manipulation.expand(self.ctx, self.id, shape_i64);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    pub fn slice(self: Tensor, start: []const i64, end: []const i64) !Tensor {
        const result_raw = try manipulation.slice(self.ctx, self.id, start, end);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    /// Explicit contiguous operation - materializes view transformations
    pub fn contiguous(self: Tensor) !Tensor {
        const new_id = try manipulation.contiguous(self.ctx, self.id);
        return Tensor{ .ctx = self.ctx, .id = new_id };
    }
    
    pub fn concat(tensors: []const Tensor, axis: u32) !Tensor {
        if (tensors.len == 0) return error.EmptyTensorList;
        
        const tensor_ids = try tensors[0].ctx.arena.allocator().alloc(NodeId, tensors.len);
        for (tensors, 0..) |t, i| {
            tensor_ids[i] = t.id;
        }
        
        const result = try manipulation.concat(tensors[0].ctx, tensor_ids, @intCast(axis));
        return Tensor{ .ctx = tensors[0].ctx, .id = result };
    }
    
    // Reduction operations
    
    pub fn sum(self: Tensor, axes: ?[]const i32, keepdims: bool) !Tensor {
        const result_raw = try reduction.sum(self.ctx, self.id, axes, keepdims);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    pub fn mean(self: Tensor, axes: ?[]const i32, keepdims: bool) !Tensor {
        const result_raw = try reduction.mean(self.ctx, self.id, axes, keepdims);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    pub fn max(self: Tensor, axes: ?[]const i32, keepdims: bool) !Tensor {
        const result_raw = try reduction.max(self.ctx, self.id, axes, keepdims);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    pub fn min(self: Tensor, axes: ?[]const i32, keepdims: bool) !Tensor {
        const result_raw = try reduction.min(self.ctx, self.id, axes, keepdims);
        return Tensor{ .ctx = self.ctx, .id = result_raw };
    }
    
    // TODO: Implement variance and std in reduction module
    // pub fn variance(self: Tensor, axes: ?[]const i32, keepdims: bool) !Tensor {
    //     const id = try reduction.variance(self.ctx, self.id, axes, keepdims);
    //     return Tensor{ .ctx = self.ctx, .id = id };
    // }
    
    // pub fn std(self: Tensor, axes: ?[]const i32, keepdims: bool) !Tensor {
    //     const id = try reduction.std(self.ctx, self.id, axes, keepdims);
    //     return Tensor{ .ctx = self.ctx, .id = id };
    // }
    
    /// Utility methods
    
    /// Get the shape dimensions of this tensor
    pub fn shape(self: Tensor) ![]const *types.Expr {
        const node = utils.getNodeOrError(self.ctx, self.id) catch return error.InvalidNodeId;
        const view_id = node.output_view_id;
        const view = self.ctx.shape.getView(view_id);
        const shape_obj = self.ctx.shape.getShape(view.shape_id);
        return shape_obj.dims;
    }
    
    /// Get the data type of this tensor
    pub fn dtype(self: Tensor) !core.types.DataType {
        const node = utils.getNodeOrError(self.ctx, self.id) catch return error.InvalidNodeId;
        return node.dtype;
    }
    
    pub fn rank(self: Tensor) !usize {
        const dims = try self.shape();
        return dims.len;
    }
    
    pub fn numel(self: Tensor) !usize {
        const dims = try self.shape();
        var total: usize = 1;
        for (dims) |expr| {
            const val = self.ctx.symbolic.evaluate(expr, null) catch {
                return error.SymbolicDimension;
            };
            total *= @intCast(val);
        }
        return total;
    }
};

/// TensorBuilder for fluent error handling in complex operation chains
/// 
/// This builder pattern allows chaining multiple tensor operations while
/// deferring error handling until the final build() call. If any operation
/// in the chain fails, the error is captured and subsequent operations are
/// skipped until build() is called.
///
/// Example usage:
/// ```zig
/// const result = try TensorBuilder.from(input_tensor)
///     .add(other_tensor)
///     .mul(scale_tensor)
///     .relu()
///     .build();
/// ```
pub const TensorBuilder = struct {
    ctx: *core.Core,
    id: NodeId,
    err: ?anyerror = null,
    
    pub fn from(tensor: Tensor) TensorBuilder {
        return .{ .ctx = tensor.ctx, .id = tensor.id };
    }
    
    pub fn add(self: *TensorBuilder, other: Tensor) *TensorBuilder {
        if (self.err != null) return self;
        const result = pointwise.add(self.ctx, self.id, other.id) catch |e| {
            self.err = e;
            return self;
        };
        self.id = result;
        return self;
    }
    
    pub fn mul(self: *TensorBuilder, other: Tensor) *TensorBuilder {
        if (self.err != null) return self;
        const result = pointwise.mul(self.ctx, self.id, other.id) catch |e| {
            self.err = e;
            return self;
        };
        self.id = result;
        return self;
    }
    
    pub fn matmul(self: *TensorBuilder, other: Tensor) *TensorBuilder {
        if (self.err != null) return self;
        const result = linalg.matmul(self.ctx, self.id, other.id) catch |e| {
            self.err = e;
            return self;
        };
        self.id = result;
        return self;
    }
    
    pub fn relu(self: *TensorBuilder) *TensorBuilder {
        if (self.err != null) return self;
        const result = pointwise.relu(self.ctx, self.id) catch |e| {
            self.err = e;
            return self;
        };
        self.id = result;
        return self;
    }
    
    pub fn reshape(self: *TensorBuilder, shape: []const *types.Expr) *TensorBuilder {
        if (self.err != null) return self;
        // Convert expressions to i64 for the manipulation function
        var shape_i64 = self.ctx.arena.allocator().alloc(i64, shape.len) catch |e| {
            self.err = e;
            return self;
        };
        for (shape, 0..) |expr, i| {
            const val = self.ctx.symbolic.evaluate(expr, null) catch {
                self.err = error.CannotReshapeWithSymbolicDims;
                return self;
            };
            shape_i64[i] = @intCast(val);
        }
        const result = manipulation.reshape(self.ctx, self.id, shape_i64) catch |e| {
            self.err = e;
            return self;
        };
        self.id = result;
        return self;
    }
    
    pub fn build(self: TensorBuilder) !Tensor {
        if (self.err) |e| return e;
        return Tensor{ .ctx = self.ctx, .id = self.id };
    }
};