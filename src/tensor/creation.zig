/// Tensor creation operations - V2
///
/// This module provides functions for creating tensors from various data sources
/// including constants, zeros, ones, and other common initialization patterns.
const std = @import("std");
const core = @import("core");
const Core = core.Core;
const core_types = core.types;
const graph_types = core.graph.types;
const data_module = core.data;
const utils = @import("utils.zig");
const types = @import("types.zig");

// Use common types from types module
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ShapeId = types.ShapeId;
const ZingError = types.ZingError;
const DataType = types.DataType;


// Create constant tensor from data - infers shape from data type
pub fn constant(c: *Core, data: anytype) !NodeId {
    // Determine shape from data
    const T = @TypeOf(data);
    const shape_dims = try inferShapeFromData(c, T, data);
    const shape_id = try c.shape.newShape(shape_dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node with appropriate data type
    const dtype = try inferDataType(T);
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store the data in the data store
    try storeConstantData(c, node_id, data, dtype);
    
    return node_id;
}

// Create constant tensor with explicit shape - useful for testing
pub fn constantWithShape(c: *Core, data: anytype, shape: []const i64) !NodeId {
    // Convert shape to expressions
    const shape_dims = try c.arena.allocator().alloc(*core_types.Expr, shape.len);
    for (shape, 0..) |dim, i| {
        shape_dims[i] = try c.symbolic.newIntegerExpr(@intCast(dim));
    }
    
    const shape_id = try c.shape.newShape(shape_dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node with appropriate data type
    const T = @TypeOf(data);
    const dtype = try inferDataType(T);
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store the data in the data store
    try storeConstantData(c, node_id, data, dtype);
    
    return node_id;
}

// Create zeros tensor with given shape
pub fn zeros(c: *Core, shape: []const *types.Expr) !NodeId {
    const shape_id = try c.shape.newShape(shape);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node with zeros
    const dtype = DataType.f32;
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store zeros pattern for this node
    try c.data.setConstantPattern(node_id, data_module.DataStore.Pattern.zeros);
    
    return node_id;
}

// Create ones tensor with given shape
pub fn ones(c: *Core, shape: []const *types.Expr) !NodeId {
    const shape_id = try c.shape.newShape(shape);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node with ones
    const dtype = DataType.f32;
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store ones pattern for this node
    try c.data.setConstantPattern(node_id, data_module.DataStore.Pattern.ones);
    
    return node_id;
}

// Create tensor filled with a specific value
pub fn full(c: *Core, shape: []const *types.Expr, value: f32) !NodeId {
    const shape_id = try c.shape.newShape(shape);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node
    const dtype = DataType.f32;
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store the constant value
    try c.data.setConstantFull(node_id, value);
    
    return node_id;
}

// Create identity matrix
pub fn eye(c: *Core, n: usize, dtype: DataType) !NodeId {
    const shape_dims = [_]*core_types.Expr{
        try c.symbolic.newIntegerExpr(@intCast(n)),
        try c.symbolic.newIntegerExpr(@intCast(n)),
    };
    const shape_id = try c.shape.newShape(&shape_dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Store identity pattern for this node
    try c.data.setConstantPattern(node_id, data_module.DataStore.Pattern.identity);
    
    return node_id;
}

// Create linearly spaced values
pub fn linspace(c: *Core, start: f32, stop: f32, num: usize) !NodeId {
    const shape_dims = [_]*core_types.Expr{
        try c.symbolic.newIntegerExpr(@intCast(num)),
    };
    const shape_id = try c.shape.newShape(&shape_dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node
    const dtype = DataType.f32;
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Calculate linspace data
    const data = try calculateLinspace(c, start, stop, num);
    try c.data.setConstantData(f32, node_id, data, c);
    
    return node_id;
}

// Create tensor with values from range
pub fn arange(c: *Core, start: f32, stop: f32, step: f32) !NodeId {
    const num_elements = @as(usize, @intFromFloat(@floor((stop - start) / step)));
    const shape_dims = [_]*core_types.Expr{
        try c.symbolic.newIntegerExpr(@intCast(num_elements)),
    };
    const shape_id = try c.shape.newShape(&shape_dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create constant node
    const dtype = DataType.f32;
    const node_id = try c.graph.newNodeConstantTyped(view_id, dtype);
    
    // Calculate range data
    const data = try calculateArange(c, start, stop, step, num_elements);
    try c.data.setConstantData(f32, node_id, data, c);
    
    return node_id;
}

// Helper function to infer shape from data, capturing full multi-dimensional structure
fn inferShapeFromData(c: *Core, comptime T: type, data: T) ![]const *core_types.Expr {
    const type_info = @typeInfo(T);
    
    switch (type_info) {
        .array => {
            // Get compile-time array shape dimensions
            const dimensions = comptime getArrayDimensions(T);
            
            // Allocate space for all dimensions
            var dims = try c.arena.allocator().alloc(*core_types.Expr, dimensions.len);
            
            // Fill in dimensions from compile-time info
            for (dimensions, 0..) |dim, i| {
                dims[i] = try c.symbolic.newIntegerExpr(@intCast(dim));
            }
            
            return dims;
        },
        .int, .float, .bool, .comptime_int, .comptime_float => {
            // Scalar - return empty shape
            return &[_]*core_types.Expr{};
        },
        .pointer => |ptr_info| {
            if (ptr_info.size == .slice) {
                // For slices, we need to check the element type for nested arrays
                if (data.len > 0) {
                    // Get the shape of the first element
                    const element_shape = try inferShapeFromData(c, @TypeOf(data[0]), data[0]);
                    
                    // Allocate for all dimensions (outer + element shape)
                    var dims = try c.arena.allocator().alloc(*core_types.Expr, 1 + element_shape.len);
                    
                    // Set the first dimension from slice length
                    dims[0] = try c.symbolic.newIntegerExpr(@intCast(data.len));
                    
                    // Copy the element shape dimensions
                    for (element_shape, 0..) |dim, i| {
                        dims[i + 1] = dim;
                    }
                    
                    return dims;
                } else {
                    // Empty slice
                    var dims = try c.arena.allocator().alloc(*core_types.Expr, 1);
                    dims[0] = try c.symbolic.newIntegerExpr(@intCast(0));
                    return dims;
                }
            }
            
            // Handle pointer to array
            if (ptr_info.child == u8) {
                // String literal or u8 array
                var dims = try c.arena.allocator().alloc(*core_types.Expr, 1);
                dims[0] = try c.symbolic.newIntegerExpr(@intCast(data.len));
                return dims;
            }
            
            // For regular pointer to array, analyze the array type recursively
            if (@typeInfo(ptr_info.child) == .array) {
                // Get compile-time dimensions for the array type
                const dimensions = comptime getArrayDimensions(ptr_info.child);
                
                // Allocate space for the dimensions
                var dims = try c.arena.allocator().alloc(*core_types.Expr, dimensions.len);
                
                // Fill in dimensions from compile-time info
                for (dimensions, 0..) |dim, i| {
                    dims[i] = try c.symbolic.newIntegerExpr(@intCast(dim));
                }
                
                return dims;
            }
            
            return error.UnsupportedDataType;
        },
        else => return error.UnsupportedDataType,
    }
}

// Compile-time function to get dimensions of an array type
fn getArrayDimensions(comptime T: type) []const usize {
    // Base case: not an array type
    const type_info = @typeInfo(T);
    if (type_info != .array) {
        return &[_]usize{};
    }
    
    // For array types
    const array_info = type_info.array;
    const len = array_info.len;
    
    // Recursively determine child dimensions
    const child_dimensions = getArrayDimensions(array_info.child);
    
    // Create a new array with all dimensions
    if (child_dimensions.len == 0) {
        // No nested dimensions, just return the array length
        return &[_]usize{len};
    }
    
    // We have nested dimensions
    // Here we need a compiler trick to create a static array with computed length
    const result = struct {
        // Create a struct with a comptime field for the dimensions array
        const dims_len = 1 + child_dimensions.len;
        const DimArray = [dims_len]usize;
        
        fn getDimensions() DimArray {
            var result_dims: DimArray = undefined;
            result_dims[0] = len;
            // Copy child dimensions
            comptime var i = 0;
            inline while (i < child_dimensions.len) : (i += 1) {
                result_dims[i + 1] = child_dimensions[i];
            }
            return result_dims;
        }
        
        // Static dimensions array
        const dimensions: DimArray = getDimensions();
    };
    
    return &result.dimensions;
}

// Helper function to infer data type from compile-time type
fn inferDataType(comptime T: type) !DataType {
    const type_info = @typeInfo(T);
    
    return switch (type_info) {
        .int => |int_info| {
            if (int_info.signedness == .unsigned) {
                return if (int_info.bits <= 32) DataType.u32 else DataType.u64;
            } else {
                return if (int_info.bits <= 32) DataType.i32 else DataType.i64;
            }
        },
        .float => |float_info| {
            return switch (float_info.bits) {
                16 => DataType.f16,
                32 => DataType.f32,
                64 => DataType.f64,
                else => error.UnsupportedDataType,
            };
        },
        .comptime_float => DataType.f32,  // Default comptime floats to f32
        .comptime_int => DataType.i32,    // Default comptime ints to i32
        .bool => DataType.bool,
        .array => |array_info| {
            return inferDataType(array_info.child);
        },
        .pointer => |ptr_info| {
            if (ptr_info.size == .slice) {
                return inferDataType(ptr_info.child);
            }
            // Handle pointer to array
            const child_info = @typeInfo(ptr_info.child);
            if (child_info == .array) {
                return inferDataType(child_info.array.child);
            }
            return inferDataType(ptr_info.child);
        },
        else => error.UnsupportedDataType,
    };
}

// Helper function to store constant data
fn storeConstantData(c: *Core, node_id: NodeId, data: anytype, dtype: DataType) !void {
    const T = @TypeOf(data);
    const type_info = @typeInfo(T);
    
    switch (type_info) {
        .int, .float, .bool, .comptime_int, .comptime_float => {
            // Scalar data
            const value = data;
            switch (dtype) {
                .f32 => try c.data.setConstantData(f32, node_id, &[_]f32{
                    switch (@typeInfo(T)) {
                        .float => |_| @as(f32, @floatCast(value)),
                        .comptime_float => @as(f32, @floatCast(value)),
                        .int => |_| @as(f32, @floatFromInt(value)),
                        .comptime_int => @as(f32, @floatFromInt(value)),
                        else => unreachable,
                    }
                }, c),
                .f64 => try c.data.setConstantData(f64, node_id, &[_]f64{
                    switch (@typeInfo(T)) {
                        .float => |_| @as(f64, @floatCast(value)),
                        .comptime_float => @as(f64, @floatCast(value)),
                        .int => |_| @as(f64, @floatFromInt(value)),
                        .comptime_int => @as(f64, @floatFromInt(value)),
                        else => unreachable,
                    }
                }, c),
                .i32 => try c.data.setConstantData(i32, node_id, &[_]i32{
                    switch (@typeInfo(T)) {
                        .int => |_| @as(i32, @intCast(value)),
                        .comptime_int => @as(i32, @intCast(value)),
                        .float => |_| @as(i32, @intFromFloat(value)),
                        .comptime_float => @as(i32, @intFromFloat(value)),
                        else => unreachable,
                    }
                }, c),
                .i64 => try c.data.setConstantData(i64, node_id, &[_]i64{
                    switch (@typeInfo(T)) {
                        .int => |_| @as(i64, @intCast(value)),
                        .comptime_int => @as(i64, @intCast(value)),
                        .float => |_| @as(i64, @intFromFloat(value)),
                        .comptime_float => @as(i64, @intFromFloat(value)),
                        else => unreachable,
                    }
                }, c),
                .u32 => try c.data.setConstantData(u32, node_id, &[_]u32{
                    switch (@typeInfo(T)) {
                        .int => |_| @as(u32, @intCast(value)),
                        .comptime_int => @as(u32, @intCast(value)),
                        .float => |_| blk: {
                            if (value < 0) break :blk 0;
                            break :blk @as(u32, @intFromFloat(value));
                        },
                        .comptime_float => blk: {
                            if (value < 0) break :blk 0;
                            break :blk @as(u32, @intFromFloat(value));
                        },
                        else => unreachable,
                    }
                }, c),
                .u64 => try c.data.setConstantData(u64, node_id, &[_]u64{
                    switch (@typeInfo(T)) {
                        .int => |_| @as(u64, @intCast(value)),
                        .comptime_int => @as(u64, @intCast(value)),
                        .float => |_| blk: {
                            if (value < 0) break :blk 0;
                            break :blk @as(u64, @intFromFloat(value));
                        },
                        .comptime_float => blk: {
                            if (value < 0) break :blk 0;
                            break :blk @as(u64, @intFromFloat(value));
                        },
                        else => unreachable,
                    }
                }, c),
                .bool => try c.data.setConstantData(bool, node_id, &[_]bool{
                    switch (@typeInfo(T)) {
                        .bool => value,
                        else => unreachable,
                    }
                }, c),
                else => return error.UnsupportedDataType,
            }
        },
        .array => |array_info| {
            // Array data
            const slice = &data;
            try storeSliceData(c, node_id, slice, array_info.child, dtype);
        },
        .pointer => |ptr_info| {
            if (ptr_info.size == .slice) {
                // Slice data
                try storeSliceData(c, node_id, data, ptr_info.child, dtype);
            } else {
                // Handle pointer to array
                const child_info = @typeInfo(ptr_info.child);
                if (child_info == .array) {
                    // Dereference the pointer to get the array
                    const array_data = data.*;
                    try storeSliceData(c, node_id, &array_data, child_info.array.child, dtype);
                } else {
                    return error.UnsupportedDataType;
                }
            }
        },
        else => return error.UnsupportedDataType,
    }
}

// Helper function to recursively flatten nested arrays
fn flattenDataGeneric(comptime TargetType: type, list: *std.ArrayList(TargetType), data: anytype) !void {
    const T = @TypeOf(data);
    const info = @typeInfo(T);
    
    if (info == .array) {
        for (data) |item| {
            try flattenDataGeneric(TargetType, list, item);
        }
    } else if (info == .pointer) {
        const child_info = @typeInfo(info.pointer.child);
        if (child_info == .array) {
            for (data) |item| {
                try flattenDataGeneric(TargetType, list, item);
            }
        } else {
            // Handle pointer to single value
            try appendValue(TargetType, list, data.*);
        }
    } else {
        // Base case - we have a primitive value
        try appendValue(TargetType, list, data);
    }
}

// Helper to append value with type coercion
fn appendValue(comptime TargetType: type, list: *std.ArrayList(TargetType), value: anytype) !void {
    const T = @TypeOf(value);
    const info = @typeInfo(T);
    // Check target type at compile time
    _ = @typeInfo(TargetType);
    
    var converted_value: TargetType = undefined;
    
    if (TargetType == f32 or TargetType == f64) {
        // Converting to float
        switch (info) {
            .comptime_float, .float => converted_value = @as(TargetType, @floatCast(value)),
            .comptime_int, .int => converted_value = @as(TargetType, @floatFromInt(value)),
            .bool => converted_value = if (value) 1.0 else 0.0,
            else => @compileError("Cannot convert type to float"),
        }
    } else if (TargetType == i32 or TargetType == i64 or TargetType == u32 or TargetType == u64) {
        // Converting to integer
        switch (info) {
            .comptime_int, .int => converted_value = @as(TargetType, @intCast(value)),
            .comptime_float, .float => converted_value = @as(TargetType, @intFromFloat(value)),
            .bool => converted_value = if (value) 1 else 0,
            else => @compileError("Cannot convert type to integer"),
        }
    } else if (TargetType == bool) {
        // Converting to boolean
        switch (info) {
            .bool => converted_value = value,
            .comptime_int, .int => converted_value = (value != 0),
            .comptime_float, .float => converted_value = (value != 0),
            else => @compileError("Cannot convert type to boolean"),
        }
    } else {
        @compileError("Unsupported target type for conversion");
    }
    
    try list.append(converted_value);
}

// Helper to store slice data
fn storeSliceData(c: *Core, node_id: NodeId, data: anytype, comptime T: type, dtype: DataType) !void {
    _ = T;  // For future type validation
    
    switch (dtype) {
        .f32 => {
            var flat_elements = std.ArrayList(f32).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            // Flatten multi-dimensional array into 1D for storage
            try flattenDataGeneric(f32, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(f32, node_id, buffer, c);
        },
        .f64 => {
            var flat_elements = std.ArrayList(f64).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            try flattenDataGeneric(f64, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(f64, node_id, buffer, c);
        },
        .i32 => {
            var flat_elements = std.ArrayList(i32).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            try flattenDataGeneric(i32, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(i32, node_id, buffer, c);
        },
        .i64 => {
            var flat_elements = std.ArrayList(i64).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            try flattenDataGeneric(i64, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(i64, node_id, buffer, c);
        },
        .u32 => {
            var flat_elements = std.ArrayList(u32).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            try flattenDataGeneric(u32, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(u32, node_id, buffer, c);
        },
        .u64 => {
            var flat_elements = std.ArrayList(u64).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            try flattenDataGeneric(u64, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(u64, node_id, buffer, c);
        },
        .bool => {
            var flat_elements = std.ArrayList(bool).init(c.arena.allocator());
            defer flat_elements.deinit();
            
            try flattenDataGeneric(bool, &flat_elements, data);
            const buffer = try flat_elements.toOwnedSlice();
            try c.data.setConstantData(bool, node_id, buffer, c);
        },
        else => return error.UnsupportedDataType,
    }
}

// Calculate linspace data
fn calculateLinspace(c: *Core, start: f32, stop: f32, num: usize) ![]f32 {
    var data = try c.arena.allocator().alloc(f32, num);
    if (num == 1) {
        data[0] = start;
    } else {
        const step = (stop - start) / @as(f32, @floatFromInt(num - 1));
        for (0..num) |i| {
            data[i] = start + @as(f32, @floatFromInt(i)) * step;
        }
    }
    return data;
}

// Calculate arange data
fn calculateArange(c: *Core, start: f32, stop: f32, step: f32, num: usize) ![]f32 {
    _ = stop;  // Already used in calculation of num
    var data = try c.arena.allocator().alloc(f32, num);
    var value = start;
    for (0..num) |i| {
        data[i] = value;
        value += step;
    }
    return data;
}

// Create variable tensor (trainable parameter)
pub fn variable(c: *Core, shape: []const *core_types.Expr, dtype: DataType) !NodeId {
    const shape_id = try c.shape.newShape(shape);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create variable node
    const node_id = try c.graph.newNodeVariable(view_id, dtype);
    
    // Register as parameter in data store
    try c.data.registerParameter(node_id);
    
    return node_id;
}

// Create variable with initial data
pub fn variableWithData(c: *Core, data: anytype) !NodeId {
    // Determine shape from data
    const T = @TypeOf(data);
    const shape_dims = try inferShapeFromData(c, T, data);
    const shape_id = try c.shape.newShape(shape_dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create variable node with appropriate data type
    const dtype = try inferDataType(T);
    const node_id = try c.graph.newNodeVariable(view_id, dtype);
    
    // Store the initial data and register as parameter
    try storeConstantData(c, node_id, data, dtype);
    try c.data.registerParameter(node_id);
    
    return node_id;
}

// Create input placeholder
pub fn placeholder(c: *Core, shape: []const *core_types.Expr, dtype: DataType) !NodeId {
    const shape_id = try c.shape.newShape(shape);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create input node
    const node_id = try c.graph.newNodeInput(view_id, dtype);
    
    // Register as input placeholder
    try c.data.registerInput(node_id);
    
    return node_id;
}

// Dim spec type for placeholderSymbolic
pub const DimSpec = struct { 
    size: ?usize,  // null means symbolic
    symbol: ?[]const u8  // optional symbol name
};

// Create input placeholder with symbolic dimensions
pub fn placeholderSymbolic(c: *Core, shape_spec: []const DimSpec, dtype: DataType) !NodeId {
    var dims = try c.arena.allocator().alloc(*core_types.Expr, shape_spec.len);
    
    for (shape_spec, 0..) |spec, i| {
        if (spec.size) |size| {
            dims[i] = try c.symbolic.newIntegerExpr(@intCast(size));
        } else {
            const symbol_name = spec.symbol orelse "dim";
            dims[i] = try c.symbolic.newSymbolExpr(symbol_name);
        }
    }
    
    const shape_id = try c.shape.newShape(dims);
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Create input node
    const node_id = try c.graph.newNodeInput(view_id, dtype);
    
    // Register as input placeholder
    try c.data.registerInput(node_id);
    
    return node_id;
}