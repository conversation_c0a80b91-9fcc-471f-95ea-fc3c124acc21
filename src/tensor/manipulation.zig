/// Tensor Manipulation Operations (Luminal-Style)
/// 
/// This module follows Luminal's design principles:
/// - View operations (reshape, transpose, permute, slice, etc.) are pure metadata transformations
/// - They do NOT create new graph nodes, only update the node's output_view_id
/// - Only computational operations create new graph nodes
/// - The only materialization point is the explicit contiguous() operation
///
/// View Operations (no graph nodes created):
/// - reshape, view, transpose, permute, squeeze, unsqueeze, expand, slice, as_strided, broadcastTo
///
/// Computational Operations (create graph nodes):
/// - concat, stack, split, select, gather, scatter, pad, flip, roll, etc.
/// - contiguous (explicit materialization)

const std = @import("std");
const core = @import("core");
const Core = core.Core;
const utils = @import("utils.zig");
const types = @import("types.zig");

// Use common types from types module
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ShapeId = types.ShapeId;
const ZingError = types.ZingError;

// Use utility functions directly
// Note: Removed conversion utilities as we now use type-safe IDs directly

/// Low-level view manipulation with arbitrary strides and offsets (as_strided)
/// Luminal-style pure view operation - the foundation of all view operations
pub fn as_strided(ctx: *Core, a: NodeId, size: []const i64, strides: []const i64, offset: i64) !NodeId {
    // Validate input
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    
    // Convert size to expressions array
    const size_dims = try ctx.arena.allocator().alloc(*core.types.Expr, size.len);
    for (size, 0..) |s, i| {
        size_dims[i] = try ctx.symbolic.newIntegerExpr(@intCast(s));
    }
    
    // Create new shape from size
    const new_shape_id = try ctx.shape.newShape(size_dims);
    
    // Convert strides for shape engine (copy to arena)
    const strides_copy = try ctx.arena.allocator().alloc(i64, strides.len);
    @memcpy(strides_copy, strides);
    
    // Ensure offset is non-negative for shape engine
    const offset_usize: usize = if (offset < 0) 0 else @intCast(offset);
    
    // Create custom view directly
    const new_view_id = try ctx.shape.createView(
        new_shape_id,
        strides_copy,
        offset_usize
    );
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Reshape tensor to new shape - Luminal-style pure view operation with optimization
pub fn reshapeOptimized(ctx: *Core, a: NodeId, shape: []const i64, optimize: bool) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Convert shape to expressions
    const shape_dims = try ctx.arena.allocator().alloc(*core.types.Expr, shape.len);
    for (shape, 0..) |dim, i| {
        shape_dims[i] = try ctx.symbolic.newIntegerExpr(@intCast(dim));
    }
    
    // Create new shape
    const new_shape_id = try ctx.shape.newShape(shape_dims);
    
    // Use pure view operation - now using newReshapedView directly
    var new_view_id = try ctx.shape.newReshapedView(current_view_id, new_shape_id);
    
    // Optionally optimize the view by collapsing dimensions
    if (optimize) {
        new_view_id = try ctx.shape.optimizeView(new_view_id);
    }
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Standard reshape function without optimization - now auto-inserts contiguous like Luminal
pub fn reshape(ctx: *Core, a: NodeId, shape: []const i64) !NodeId {
    // Luminal-style: Always insert contiguous before reshape
    const contiguous_a = try contiguous(ctx, a);
    return reshapeOptimized(ctx, contiguous_a, shape, false);
}

// Flatten tensor to 1D - Luminal-style pure view operation
pub fn flatten(ctx: *Core, a: NodeId) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    const current_view = ctx.shape.getView(current_view_id);
    const current_shape = ctx.shape.getShape(current_view.shape_id);
    
    // Calculate total elements using expression multiplication
    var total_elements_expr = try ctx.symbolic.newIntegerExpr(1);
    
    for (current_shape.dims) |expr| {
        total_elements_expr = try ctx.symbolic.newBinaryExpr(.multiply, total_elements_expr, expr);
    }
    
    // Create 1D shape with the computed total elements expression
    const flat_dims = [_]*core.types.Expr{total_elements_expr};
    const flat_shape_id = try ctx.shape.newShape(&flat_dims);
    
    // Use pure view operation - now using newReshapedView directly
    const new_view_id = try ctx.shape.newReshapedView(current_view_id, flat_shape_id);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Squeeze dimensions of size 1 - Luminal-style pure view operation
pub fn squeeze(ctx: *Core, a: NodeId, dims: ?[]const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Convert dims to u32 array if provided, or pass null for "squeeze all"
    const squeeze_axes: ?[]u32 = if (dims) |d| blk: {
        if (d.len == 0) {
            // Empty slice means squeeze all singleton dimensions
            break :blk null;
        }
        const axes = try ctx.arena.allocator().alloc(u32, d.len);
        for (d, 0..) |dim, i| {
            axes[i] = @intCast(dim);
        }
        break :blk axes;
    } else null;
    
    // Use pure view operation - no graph node created, now using newSqueezeView directly
    const new_view_id = try ctx.shape.newSqueezeView(current_view_id, squeeze_axes);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Add dimension of size 1 - Luminal-style pure view operation
pub fn unsqueeze(ctx: *Core, a: NodeId, dim: i32) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    const current_view = ctx.shape.getView(current_view_id);
    
    const normalize_dim = if (dim < 0) 
        @as(usize, @intCast(@as(i64, @intCast(ctx.shape.getShape(current_view.shape_id).dims.len + 1)) + dim))
    else 
        @as(usize, @intCast(dim));
    
    // Luminal-style: Create pure view transformation without graph nodes
    const size_expr = try ctx.symbolic.newIntegerExpr(1);
    const new_view_id = try ctx.shape.newExpandedView(current_view_id, normalize_dim, size_expr);
    
    // Update the node's view - this is how Luminal tracks transformations
    node.output_view_id = new_view_id;
    
    // Luminal principle: Return same node ID, only the view changes
    return a;
}

// Transpose tensor (swap dimensions) - Luminal-style pure view operation
pub fn transpose(ctx: *Core, a: NodeId, dim0: ?i64, dim1: ?i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    const current_view = ctx.shape.getView(current_view_id);
    const current_shape = ctx.shape.getShape(current_view.shape_id);
    
    // Create permutation array
    var perm = try ctx.arena.allocator().alloc(u32, current_shape.dims.len);
    for (0..current_shape.dims.len) |i| {
        perm[i] = @intCast(i);
    }
    
    // Swap dimensions
    if (dim0 != null and dim1 != null) {
        const d0: usize = @intCast(if (dim0.? < 0) @as(i64, @intCast(current_shape.dims.len)) + dim0.? else dim0.?);
        const d1: usize = @intCast(if (dim1.? < 0) @as(i64, @intCast(current_shape.dims.len)) + dim1.? else dim1.?);
        const temp = perm[d0];
        perm[d0] = perm[d1];
        perm[d1] = temp;
    } else {
        // Default transpose - swap last two dimensions
        if (current_shape.dims.len < 2) {
            // For 1D tensors, just return the input as-is
            return a;
        }
        const temp = perm[current_shape.dims.len - 2];
        perm[current_shape.dims.len - 2] = perm[current_shape.dims.len - 1];
        perm[current_shape.dims.len - 1] = temp;
    }
    
    // Use pure view operation - now using newPermutedView directly
    const new_view_id = try ctx.shape.newPermutedView(current_view_id, perm);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Permute dimensions - Luminal-style pure view operation
pub fn permute(ctx: *Core, a: NodeId, dims: []const i32) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    const current_view = ctx.shape.getView(current_view_id);
    const current_shape = ctx.shape.getShape(current_view.shape_id);
    
    // Validate that the permutation matches the tensor dimensions
    if (dims.len != current_shape.dims.len) {
        return types.TensorError.InvalidDimensions;
    }
    
    // Convert dims to u32 for shape engine
    const dims_u32 = try ctx.arena.allocator().alloc(u32, dims.len);
    for (dims, 0..) |dim, i| {
        if (dim < 0 or dim >= current_shape.dims.len) {
            return types.TensorError.InvalidDimensions;
        }
        dims_u32[i] = @intCast(dim);
    }
    
    // Use pure view operation - now using newPermutedView directly
    const new_view_id = try ctx.shape.newPermutedView(current_view_id, dims_u32);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// View tensor with new shape (must be compatible) - Luminal-style pure view operation
pub fn view(ctx: *Core, a: NodeId, shape: []const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Convert shape to expressions
    const shape_dims = try ctx.arena.allocator().alloc(*core.types.Expr, shape.len);
    for (shape, 0..) |dim, i| {
        shape_dims[i] = try ctx.symbolic.newIntegerExpr(@intCast(dim));
    }
    
    // Create new shape
    const new_shape_id = try ctx.shape.newShape(shape_dims);
    
    // Use pure view operation - now using newReshapedView directly
    const new_view_id = try ctx.shape.newReshapedView(current_view_id, new_shape_id);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Concatenate tensors along dimension - This is a computational operation, not a view
// In Luminal, concat() creates a new computation node as it combines multiple inputs
pub fn concat(ctx: *Core, tensors: []const NodeId, dim: i64) !NodeId {
    // Convert NodeId array to u32 array
    var raw_tensors = try ctx.arena.allocator().alloc(u32, tensors.len);
    for (tensors, 0..) |t, i| {
        raw_tensors[i] = @intFromEnum(t);
    }
    
    const result = try ctx.graph.concat(raw_tensors, dim);
    return @as(NodeId, @enumFromInt(result));
}

// Stack tensors along new dimension - This is a computational operation, not a view
// Stack creates a new dimension and copies data from multiple inputs
pub fn stack(ctx: *Core, tensors: []const NodeId, dim: i64) !NodeId {
    // Convert NodeId array to u32 array
    var raw_tensors = try ctx.arena.allocator().alloc(u32, tensors.len);
    for (tensors, 0..) |t, i| {
        raw_tensors[i] = @intFromEnum(t);
    }
    
    const result = try ctx.graph.stack(raw_tensors, dim);
    return @as(NodeId, @enumFromInt(result));
}

// Split tensor into chunks - This is a computational operation
// Split creates multiple output tensors from a single input
pub fn split(ctx: *Core, a: NodeId, split_size: i64, dim: i64) ![]NodeId {
    
    const raw_results = try ctx.graph.split(@intFromEnum(a), split_size, dim);
    
    // Convert u32 array to NodeId array
    var results = try ctx.arena.allocator().alloc(NodeId, raw_results.len);
    for (raw_results, 0..) |r, i| {
        results[i] = @as(NodeId, @enumFromInt(r));
    }
    
    return results;
}

// Split tensor into specific sizes - This is a computational operation
// Creates multiple output tensors with specified sizes
pub fn splitWithSizes(ctx: *Core, a: NodeId, split_sizes: []const i64, dim: i64) ![]NodeId {
    
    const raw_results = try ctx.graph.splitWithSizes(a, split_sizes, dim);
    
    // Convert u32 array to NodeId array
    var results = try ctx.arena.allocator().alloc(NodeId, raw_results.len);
    for (raw_results, 0..) |r, i| {
        results[i] = r;
    }
    
    return results;
}

// Unbind tensor along dimension (opposite of stack) - This is a computational operation
// Creates multiple outputs by removing a dimension
pub fn unbind(ctx: *Core, a: NodeId, dim: i64) ![]NodeId {
    
    const raw_results = try ctx.graph.unbind(a, dim);
    
    // Convert u32 array to NodeId array
    var results = try ctx.arena.allocator().alloc(NodeId, raw_results.len);
    for (raw_results, 0..) |r, i| {
        results[i] = r;
    }
    
    return results;
}

// Slice tensor - Luminal-style pure view operation
pub fn slice(ctx: *Core, a: NodeId, start: []const i64, end: []const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Convert start/end to slice ranges  
    const ranges = try ctx.arena.allocator().alloc(core.types.SliceRange, start.len);
    for (start, end, 0..) |s, e, i| {
        ranges[i] = core.types.SliceRange{
            .start = s,
            .end = e,
        };
    }
    
    // Use pure view operation - no graph node created
    const new_view_id = try ctx.shape.newSlicedView(current_view_id, ranges);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Select along dimension - This is a computational operation
// Creates a new tensor by selecting a specific index along a dimension
pub fn select(ctx: *Core, a: NodeId, dim: i64, index: i64) !NodeId {
    
    const result = try ctx.graph.select(a, dim, index);
    return result;
}

// Index select - This is a computational operation
// Creates a new tensor by selecting indices from the input
pub fn indexSelect(ctx: *Core, a: NodeId, dim: i64, indices: NodeId) !NodeId {
    
    
    const result = try ctx.graph.indexSelect(a, dim, indices);
    return result;
}

// Gather elements - This is a computational operation
// Gathers values according to indices, creating a new tensor
pub fn gather(ctx: *Core, a: NodeId, dim: i64, indices: NodeId) !NodeId {
    
    
    const result = try ctx.graph.gather(a, dim, indices);
    return result;
}

// Scatter elements - This is a computational operation
// Scatters values from src into the tensor according to indices
pub fn scatter(ctx: *Core, a: NodeId, dim: i64, indices: NodeId, src: NodeId) !NodeId {
    
    
    
    const result = try ctx.graph.scatter(a, dim, indices, src);
    return result;
}

// Diagonal operations - This is a computational operation
// Extracts or manipulates diagonal elements of a tensor
pub fn diagonal(ctx: *Core, a: NodeId, offset: i64, dim1: i64, dim2: i64) !NodeId {
    
    const result = try ctx.graph.diagonal(a, offset, dim1, dim2);
    return result;
}

// Repeat tensor along dimensions - This is a computational operation
// Creates a new tensor by repeating elements
pub fn repeat(ctx: *Core, a: NodeId, repeats: []const i64) !NodeId {
    
    const result = try ctx.graph.repeat(@intFromEnum(a), repeats);
    return @as(NodeId, @enumFromInt(result));
}

// Tile tensor - This is a computational operation
// Creates a new tensor by tiling the input
pub fn tile(ctx: *Core, a: NodeId, dims: []const i64) !NodeId {
    
    const result = try ctx.graph.tile(a, dims);
    return result;
}

// Expand tensor to new shape - Luminal-style pure view operation
// Fixed to support multiple dimensions, not just one
pub fn expand(ctx: *Core, a: NodeId, shape: []const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    const current_view = ctx.shape.getView(current_view_id);
    const current_shape = ctx.shape.getShape(current_view.shape_id);
    
    // Make sure shapes have same number of dimensions
    if (shape.len != current_shape.dims.len) {
        return types.TensorError.IncompatibleShapesForExpand;
    }
    
    // Start with current view and apply expansion one dimension at a time
    var working_view_id = current_view_id;
    
    // For each dimension, check if we need to expand
    for (shape, 0..) |target_size, dim_idx| {
        // Try to evaluate the current dimension size
        const current_expr = current_shape.dims[dim_idx];
        const current_size = ctx.symbolic.evaluate(current_expr, null) catch {
            // If we can't evaluate (symbolic), defer to shape engine
            // For now, skip symbolic expansion optimization
            continue;
        };
        
        // Only expand if needed
        if (target_size > current_size) {
            if (current_size != 1) {
                return types.TensorError.CanOnlyExpandDimensionsOfSize1;
            }
            
            // Expand this dimension
            const size_expr = try ctx.symbolic.newIntegerExpr(@intCast(target_size));
            working_view_id = try ctx.shape.newExpandedView(
                working_view_id, 
                dim_idx, 
                size_expr
            );
        }
    }
    
    // If we made any changes, update the view
    if (working_view_id != current_view_id) {
        node.output_view_id = working_view_id;
    }
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Expand tensor to match target shape - Luminal-style expandTo operation
// Allows expanding from lower rank to higher rank tensors
pub fn expandTo(ctx: *Core, a: NodeId, target_shape: []const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Convert target shape to expressions
    const target_shape_dims = try ctx.arena.allocator().alloc(*core.types.Expr, target_shape.len);
    for (target_shape, 0..) |dim, i| {
        target_shape_dims[i] = try ctx.symbolic.newIntegerExpr(@intCast(dim));
    }
    const target_shape_id = try ctx.shape.newShape(target_shape_dims);
    
    // Use the shape engine's broadcast operation which handles all the logic
    const new_view_id = try ctx.shape.newBroadcastView(current_view_id, target_shape_id);
    
    // Update the node's view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Broadcast tensor to shape - Converted to a view operation similar to expand
// Luminal-style pure view operation (previously was computational)
pub fn broadcastTo(ctx: *Core, a: NodeId, shape: []const i64) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Create target shape
    const shape_dims = try ctx.arena.allocator().alloc(*core.types.Expr, shape.len);
    for (shape, 0..) |dim, i| {
        shape_dims[i] = try ctx.symbolic.newIntegerExpr(@intCast(dim));
    }
    const target_shape_id = try ctx.shape.newShape(shape_dims);
    
    // Use the shape engine's broadcast operation to create a view
    const new_view_id = try ctx.shape.newBroadcastView(current_view_id, target_shape_id);
    
    // Update the node's output view to the new view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Make tensor contiguous with optional expression generation
pub fn contiguousWithExpressions(ctx: *Core, a: NodeId, use_expressions: bool) !NodeId {
    // Get the node - check if already contiguous
    const node = ctx.graph.getNode(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Check if the view is already contiguous
    if (ctx.shape.isContiguous(current_view_id)) {
        return a; // Already contiguous, return same node
    }
    
    // Note: Expression generation is handled internally by the shape engine
    // when needed. The use_expressions parameter is kept for API compatibility
    // but the shape engine will generate expressions as needed.
    _ = use_expressions;
    
    // Create a new contiguous node - this is the ONLY place we create nodes for view ops
    const result = try ctx.graph.newNodeContiguous(a, current_view_id);
    return result;
}

// Standard contiguous function without expressions
pub fn contiguous(ctx: *Core, a: NodeId) !NodeId {
    return contiguousWithExpressions(ctx, a, false);
}

// Pad tensor - This is a computational operation
// Creates a new tensor with padding around the input
pub fn pad(ctx: *Core, a: NodeId, padding: []const [2]i64, value: f32) !NodeId {
    
    const result = try ctx.graph.pad(a, padding, value);
    return result;
}

// Flip tensor along dimensions - This is a computational operation
// Creates a new tensor with elements flipped along specified dimensions
pub fn flip(ctx: *Core, a: NodeId, dims: []const i64) !NodeId {
    
    const result = try ctx.graph.flip(a, dims);
    return result;
}

// Roll tensor elements - This is a computational operation
// Shifts elements along dimensions and wraps around
pub fn roll(ctx: *Core, a: NodeId, shifts: []const i64, dims: []const i64) !NodeId {
    
    const result = try ctx.graph.roll(a, shifts, dims);
    return result;
}

// Meshgrid from tensors - This is a computational operation
// Creates a coordinate grid from input tensors
pub fn meshgrid(ctx: *Core, tensors: []const NodeId) ![]NodeId {
    // Convert NodeId array to u32 array
    var raw_tensors = try ctx.arena.allocator().alloc(u32, tensors.len);
    for (tensors, 0..) |t, i| {
        raw_tensors[i] = t;
    }
    
    const raw_results = try ctx.graph.meshgrid(raw_tensors);
    
    // Convert u32 array to NodeId array
    var results = try ctx.arena.allocator().alloc(NodeId, raw_results.len);
    for (raw_results, 0..) |r, i| {
        results[i] = r;
    }
    
    return results;
}

// Chunk tensor into equal parts - This is a computational operation
// Splits tensor into a specified number of chunks
pub fn chunk(ctx: *Core, a: NodeId, chunks: i64, dim: i64) ![]NodeId {
    
    const raw_results = try ctx.graph.chunk(a, chunks, dim);
    
    // Convert u32 array to NodeId array
    var results = try ctx.arena.allocator().alloc(NodeId, raw_results.len);
    for (raw_results, 0..) |r, i| {
        results[i] = r;
    }
    
    return results;
}

// General-purpose view operation optimizer
// Goes beyond reshapeOptimized to optimize any chain of view operations
pub fn optimizeViews(ctx: *Core, a: NodeId) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Run the shape engine's optimizer on the view
    const optimized_view_id = try ctx.shape.optimizeView(current_view_id);
    
    // Update the node's output view if the optimization changed the view
    if (optimized_view_id != current_view_id) {
        node.output_view_id = optimized_view_id;
    }
    
    return a;
}

// Cut out 'size' elements every 'spacing' elements in the last dimension
// Luminal-style operation for dilated convolutions and similar patterns
pub fn excise(ctx: *Core, a: NodeId, spacing: usize, size: usize) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    const current_view = ctx.shape.getView(current_view_id);
    const current_shape = ctx.shape.getShape(current_view.shape_id);
    
    if (current_shape.dims.len == 0) {
        return types.TensorError.InvalidDimensions;
    }
    
    // Apply excise to the last dimension
    const last_dim = current_shape.dims.len - 1;
    const new_view_id = try ctx.shape.newExciseView(current_view_id, last_dim, size, spacing);
    
    // Update the node's output view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}

// Pool elements along the last dimension with kernel, stride, and dilation
// Pools are exposed as a new dimension - used for efficient 1D pooling operations
pub fn poolLastDim(ctx: *Core, a: NodeId, kernel: usize, stride: usize, dilation: usize) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return types.TensorError.InvalidTensor;
    const current_view_id = node.output_view_id;
    
    // Use the shape engine's pool_last_dim operation
    const new_view_id = try ctx.shape.newPoolLastDimView(current_view_id, kernel, stride, dilation);
    
    // Update the node's output view
    node.output_view_id = new_view_id;
    
    // Return the same node ID - no new node created, only view changed
    return a;
}