/// Type definitions for tensor layer that bridge with core types
const std = @import("std");
const core = @import("core");
const core_types = core.types;

/// Re-export errors for use throughout tensor module
pub const errors = @import("errors.zig");
pub const TensorError = errors.TensorError;

/// Define the unified error set that combines core and tensor errors
pub const ZingError = core.errors.ZingError || TensorError;

/// Re-export key type-safe IDs from core
pub const NodeId = core_types.NodeId;
pub const ViewId = core_types.ViewId;
pub const ShapeId = core_types.ShapeId;

/// Re-export useful types from core - using Expr directly (Dim removed)
pub const Expr = core_types.Expr;
pub const DataType = core.types.DataType;

// Re-export conversion helpers from core
pub const nodeIdFromU32 = core_types.nodeIdFromU32;
pub const nodeIdToU32 = core_types.nodeIdToU32;
pub const viewIdFromU32 = core_types.viewIdFromU32;
pub const viewIdToU32 = core_types.viewIdToU32;
pub const shapeIdFromU32 = core_types.shapeIdFromU32;
pub const shapeIdToU32 = core_types.shapeIdToU32;

// Tensor-specific types
pub const TensorId = NodeId; // Tensor is just a node in the graph