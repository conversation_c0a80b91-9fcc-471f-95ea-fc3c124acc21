/// Linear algebra operations
///
/// This module provides matrix operations like matmul, dot product
/// and other linear algebra operations.
const std = @import("std");
const core = @import("core");
const Core = core.Core;
const utils = @import("utils.zig");
const types = @import("types.zig");

// Use common types from types module
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ZingError = types.ZingError;
const TensorError = types.TensorError;

// Use utility functions directly
const getOutputView = utils.getOutputView;

/// Matrix multiplication (M,K) @ (K,N) -> (M,N)
/// Decomposed as: expand, multiply, sum-reduce
pub fn matmul(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // Get shapes for validation
    const a_node = ctx.graph.getNode(a) orelse return TensorError.InvalidTensor;
    const b_node = ctx.graph.getNode(b) orelse return TensorError.InvalidTensor;
    
    const a_view = ctx.shape.getView(a_node.output_view_id);
    const b_view = ctx.shape.getView(b_node.output_view_id);
    
    const a_shape = ctx.shape.getShape(a_view.shape_id);
    const b_shape = ctx.shape.getShape(b_view.shape_id);
    
    // Verify shapes are 2D
    if (a_shape.dims.len != 2 or b_shape.dims.len != 2) {
        return TensorError.MatmulRequires2DTensors;
    }
    
    // Verify inner dimensions match using symbolic compatibility
    const a_inner = a_shape.dims[1];
    const b_inner = b_shape.dims[0];
    
    // Check dimension compatibility using the symbolic engine
    const dimensions_compatible = try ctx.symbolic.checkExprCompatibility(a_inner, b_inner);
    if (!dimensions_compatible) {
        return TensorError.IncompatibleDimensionsForMatmul;
    }
    
    // Step 1: Transpose b to get (N,K)
    const b_t = try transpose(ctx, b);
    
    
    // Step 2: Expand dimensions for broadcasting
    // a: (M,K) -> (M,1,K)
    const a_exp = try expand(ctx, a, 1, 1);
    
    // b_t: (N,K) -> (1,N,K)
    const b_t_exp = try expand(ctx, b_t, 0, 1);
    
    // Step 3: Element-wise multiply: (M,1,K) * (1,N,K) -> (M,N,K)
    const pointwise = @import("pointwise.zig");
    const mul = try pointwise.mul(ctx, a_exp, b_t_exp);
    
    // Step 4: Sum reduce over last dimension: (M,N,K) -> (M,N)
    const reduction = @import("reduction.zig");
    const axes = [_]i32{-1};  // Reduce over the last dimension
    return try reduction.sum(ctx, mul, &axes, false);
}

/// Matrix transpose (swap last two dimensions)
pub fn transpose(ctx: *Core, a: NodeId) !NodeId {
    const node = ctx.graph.getNode(a) orelse return TensorError.InvalidTensor;
    const view = ctx.shape.getView(node.output_view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Create permutation for transpose
    var perm = try ctx.arena.allocator().alloc(i32, shape.dims.len);
    
    // Keep all dimensions except last two in same order
    for (perm[0..shape.dims.len-2], 0..) |*p, i| {
        p.* = @intCast(i);
    }
    
    // Swap last two dimensions
    if (shape.dims.len >= 2) {
        perm[shape.dims.len - 2] = @intCast(shape.dims.len - 1);
        perm[shape.dims.len - 1] = @intCast(shape.dims.len - 2);
    }
    
    // Create a new permute node that implements the transpose
    const manipulation = @import("manipulation.zig");
    return try manipulation.permute(ctx, a, perm);
}

// Expand dimension (add a dimension of size n at the given axis)
fn expand(ctx: *Core, a: NodeId, axis: usize, size: i64) !NodeId {
    _ = size; // Parameter kept for API compatibility
    const manipulation = @import("manipulation.zig");
    return try manipulation.unsqueeze(ctx, a, @intCast(axis));
}

/// Batch matrix multiplication
pub fn bmm(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // Get shapes for validation
    const a_node = ctx.graph.getNode(a) orelse return TensorError.InvalidTensor;
    const b_node = ctx.graph.getNode(b) orelse return TensorError.InvalidTensor;
    
    const a_view = ctx.shape.getView(a_node.output_view_id);
    const b_view = ctx.shape.getView(b_node.output_view_id);
    
    const a_shape = ctx.shape.getShape(a_view.shape_id);
    const b_shape = ctx.shape.getShape(b_view.shape_id);
    
    // Verify shapes are 3D
    if (a_shape.dims.len != 3 or b_shape.dims.len != 3) {
        return TensorError.BmmRequires3DTensors;
    }
    
    // Similar to matmul but preserves batch dimension
    // (B,M,K) @ (B,K,N) -> (B,M,N)
    
    // Transpose last two dims of b
    const b_t = try transpose(ctx, b);
    
    
    // Expand for broadcasting
    // a: (B,M,K) -> (B,M,1,K)
    const a_exp = try expand(ctx, a, 2, 1);
    
    // b_t: (B,N,K) -> (B,1,N,K)
    const b_t_exp = try expand(ctx, b_t, 1, 1);
    
    // Multiply: (B,M,1,K) * (B,1,N,K) -> (B,M,N,K)
    const pointwise = @import("pointwise.zig");
    const mul = try pointwise.mul(ctx, a_exp, b_t_exp);
    
    // Sum reduce over last dimension: (B,M,N,K) -> (B,M,N)
    const reduction = @import("reduction.zig");
    const axes = [_]i32{-1};  // Reduce over the last dimension
    return try reduction.sum(ctx, mul, &axes, false);
}

// Dot product for 1D tensors
pub fn dot(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // Element-wise multiply
    const pointwise = @import("pointwise.zig");
    const mul = try pointwise.mul(ctx, a, b);
    
    // Sum reduce over all dimensions
    const reduction = @import("reduction.zig");
    return try reduction.sum(ctx, mul, null, false);
}