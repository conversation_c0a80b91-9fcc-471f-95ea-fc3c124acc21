/// Utility functions and types for the tensor module
///
/// This module provides common utility functions, type definitions,
/// and helper routines used by other parts of the tensor module.
const std = @import("std");
const core = @import("core");
const Core = core.Core;
const core_types = core.types;
// Import types from consolidated types.zig
const types = @import("types.zig");
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ShapeId = types.ShapeId;
const TensorError = types.TensorError;

/// Get a new output view for a tensor node
pub fn getOutputView(ctx: *Core, node_id: NodeId) !ViewId {
    const node = ctx.graph.getNode(node_id) orelse return TensorError.InvalidTensor;
    const old_view = ctx.shape.getView(node.output_view_id);
    
    // Create a new default view with same shape
    return try ctx.shape.newDefaultView(old_view.shape_id);
}

/// Get a broadcast output view for a binary operation
pub fn getBinaryOutputView(ctx: *Core, a: NodeId, b: NodeId) !ViewId {
    const a_node = ctx.graph.getNode(a) orelse return TensorError.InvalidTensor;
    const b_node = ctx.graph.getNode(b) orelse return TensorError.InvalidTensor;
    
    // Use shape engine's broadcast function
    return try ctx.shape.createBroadcastViewFromTwoInputs(a_node.output_view_id, b_node.output_view_id);
}

/// Create a constant tensor that can be broadcast with another tensor
pub fn createBroadcastableConstant(ctx: *Core, _: NodeId, _: f32) !NodeId {
    // First create a shape with dimension [1]
    const one_expr = try ctx.symbolic.newIntegerExpr(1);
    const scalar_shape = try ctx.shape.newShape(&.{one_expr});
    
    // Create a view for this shape
    const scalar_view_id = try ctx.shape.newDefaultView(scalar_shape);
    
    // Creating a scalar constant with that view
    const constant = try ctx.graph.newNodeConstant(scalar_view_id);
    return constant;
}

/// Get multi-dimensional shape from a tensor (returns native Dim array with symbolic support)
pub fn getTensorShape(ctx: *Core, a: NodeId) ![]const *core.types.Expr {
    const node = ctx.graph.getNode(a) orelse return TensorError.InvalidTensor;
    const view = ctx.shape.getView(node.output_view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Return the native dims array which supports both concrete and symbolic dimensions
    return shape.dims;
}

/// Get multi-dimensional shape from a tensor (legacy function returning concrete i64 values only)
pub fn getTensorShapeConcrete(ctx: *Core, a: NodeId) ![]const i64 {
    const node = ctx.graph.getNode(a) orelse return TensorError.InvalidTensor;
    const view = ctx.shape.getView(node.output_view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Convert expressions to i64 array (concrete values only)
    var result = try ctx.arena.allocator().alloc(i64, shape.dims.len);
    for (shape.dims, 0..) |expr, i| {
        const value = ctx.symbolic.evaluate(expr, null) catch {
            return TensorError.SymbolicDimsNotYetSupported;
        };
        result[i] = @intCast(value);
    }
    
    return result;
}

/// Convert a tensor to a flat 1D array (for testing and debugging)
pub fn to_array_1d(ctx: *Core, a: NodeId) ![]const f32 {
    // Make sure the tensor is contiguous
    const contiguous = try ctx.graph.makeContiguous(a);
    
    // Get the data
    const data = try ctx.graph.getTensorData(contiguous);
    
    return data;
}

/// View optimization utilities
pub fn optimizeViewChain(ctx: *Core, a: NodeId) !NodeId {
    const node = ctx.graph.getNodeMut(a) orelse return TensorError.InvalidTensor;
    
    // Use shape engine's view optimizer
    const optimized_view_id = try ctx.shape.optimizeView(node.output_view_id);
    
    // Update node's view if optimization changed it
    if (optimized_view_id != node.output_view_id) {
        node.output_view_id = optimized_view_id;
    }
    
    return a;
}

/// Helper to get node or return error
pub fn getNodeOrError(ctx: *Core, node_id: NodeId) !*core.graph.types.Node {
    return ctx.graph.getNode(node_id) orelse TensorError.InvalidTensor;
}