/// Tensor module for Zing
/// 
/// Provides tensor operations built on top of the core engine.
/// Operations are organized into categories:
/// - Creation (constants, zeros, etc.)
/// - Manipulation (reshape, transpose, etc.)
/// - Linear algebra (matmul, dot product, etc.)
/// - Pointwise (add, mul, exp, etc.)
/// - Reduction (sum, mean, max, etc.)

// Import and re-export submodules
pub const utils = @import("utils.zig");
pub const types = @import("types.zig");
pub const tensor = @import("tensor.zig");
pub const manipulation = @import("manipulation.zig");
pub const pointwise = @import("pointwise.zig");
pub const linalg = @import("linalg.zig");
pub const reduction = @import("reduction.zig");
pub const creation = @import("creation.zig");

// Re-export tensor creation functions for convenience
pub const constant = creation.constant;
pub const zeros = creation.zeros;
pub const ones = creation.ones;
pub const arange = creation.arange;
pub const eye = creation.eye;

// Re-export tensor constructor
pub const Tensor = tensor.Tensor;