/// Pointwise tensor operations
///
/// This module provides element-wise operations like add, mul, and activations
/// which operate on tensors element by element.
const std = @import("std");
const core = @import("core");
const Core = core.Core;
const core_types = core.types;
const utils = @import("utils.zig");
const types = @import("types.zig");

// Use common types from types module  
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ZingError = types.ZingError;

// Use utility functions directly
const getOutputView = utils.getOutputView;
const getBinaryOutputView = utils.getBinaryOutputView;
pub const createBroadcastableConstant = utils.createBroadcastableConstant;


// Pointwise unary operations
pub fn neg(ctx: *Core, a: NodeId) !NodeId {
    // Implement negation as -1 * a
    const neg_one = try createBroadcastableConstant(ctx, a, -1.0);
    const out_view = try getOutputView(ctx, a);
    
    const result = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{a, neg_one}, out_view);
    return result;
}

pub fn exp(ctx: *Core, a: NodeId) !NodeId {
    // Natural exp using exp2: e^x = 2^(x / ln(2))
    const ln2_recip = try createBroadcastableConstant(ctx, a, @as(f32, 1.0 / @log(2.0)));
    
    const out_view1 = try getOutputView(ctx, a);
    const out_view2 = try getOutputView(ctx, a);
    
    const scaled = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{a, ln2_recip}, out_view1);
    const result = try ctx.graph.newNodeExp2(scaled, out_view2);
    return result;
}

pub fn log(ctx: *Core, a: NodeId) !NodeId {
    // Natural log using log2: ln(x) = log2(x) * ln(2)
    const ln2 = try createBroadcastableConstant(ctx, a, @as(f32, @log(2.0)));
    
    const out_view1 = try getOutputView(ctx, a);
    const out_view2 = try getOutputView(ctx, a);
    
    const log2_a = try ctx.graph.newNodeLog2(a, out_view1);
    const result = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{log2_a, ln2}, out_view2);
    return result;
}

pub fn sin(ctx: *Core, a: NodeId) !NodeId {
    const out_view = try getOutputView(ctx, a);
    const result = try ctx.graph.newNodeSin(a, out_view);
    return result;
}

pub fn cos(ctx: *Core, a: NodeId) !NodeId {
    // cos(x) = sin(x + π/2)
    const pi_over_2 = try createBroadcastableConstant(ctx, a, @as(f32, std.math.pi / 2.0));
    
    const out_view1 = try getOutputView(ctx, a);
    const out_view2 = try getOutputView(ctx, a);
    
    const shifted = try ctx.graph.newNodeAdd(&[_]core_types.NodeId{a, pi_over_2}, out_view1);
    const result = try ctx.graph.newNodeSin(shifted, out_view2);
    return result;
}

pub fn tan(ctx: *Core, a: NodeId) !NodeId {
    // tan(x) = sin(x) / cos(x)
    const sin_a = try sin(ctx, a);
    const cos_a = try cos(ctx, a);
    const out_view1 = try getOutputView(ctx, cos_a);
    const out_view2 = try getOutputView(ctx, a);
    
    const recip_cos = try ctx.graph.newNodeReciprocal(cos_a, out_view1);
    const result = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{sin_a, recip_cos}, out_view2);
    return result;
}

pub fn sqrt(ctx: *Core, a: NodeId) !NodeId {
    const out_view = try getOutputView(ctx, a);
    const result = try ctx.graph.newNodeSqrt(a, out_view);
    return result;
}

pub fn abs(ctx: *Core, a: NodeId) !NodeId {
    // abs(x) = sqrt(x^2)
    const out_view1 = try getOutputView(ctx, a);
    const out_view2 = try getOutputView(ctx, a);
    
    const squared = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{a, a}, out_view1);
    const result = try ctx.graph.newNodeSqrt(squared, out_view2);
    return result;
}

pub fn log2(ctx: *Core, a: NodeId) !NodeId {
    const out_view = try getOutputView(ctx, a);
    const result = try ctx.graph.newNodeLog2(a, out_view);
    return result;
}

pub fn exp2(ctx: *Core, a: NodeId) !NodeId {
    const out_view = try getOutputView(ctx, a);
    const result = try ctx.graph.newNodeExp2(a, out_view);
    return result;
}

pub fn recip(ctx: *Core, a: NodeId) !NodeId {
    const out_view = try getOutputView(ctx, a);
    const result = try ctx.graph.newNodeReciprocal(a, out_view);
    return result;
}

// Pointwise binary operations
pub fn add(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    const out_view = try getBinaryOutputView(ctx, a, b);
    const result = try ctx.graph.newNodeAdd(&[_]core_types.NodeId{a, b}, out_view);
    return result;
}

pub fn sub(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a - b = a + (-b)
    const neg_b = try neg(ctx, b);
    const out_view = try getBinaryOutputView(ctx, a, b);
    const result = try ctx.graph.newNodeAdd(&[_]core_types.NodeId{a, neg_b}, out_view);
    return result;
}

pub fn mul(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    const out_view = try getBinaryOutputView(ctx, a, b);
    const result = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{a, b}, out_view);
    return result;
}

pub fn div(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a / b = a * (1/b)
    const out_view1 = try getOutputView(ctx, b);
    const out_view2 = try getBinaryOutputView(ctx, a, b);
    
    const recip_b = try ctx.graph.newNodeReciprocal(b, out_view1);
    const result = try ctx.graph.newNodeMultiply(&[_]core_types.NodeId{a, recip_b}, out_view2);
    return result;
}

pub fn mod(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    const out_view = try getBinaryOutputView(ctx, a, b);
    const result = try ctx.graph.newNodeMod(&[_]core_types.NodeId{a, b}, out_view);
    return result;
}

pub fn pow(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a^b = exp(b * log(a))
    const log_a = try log(ctx, a);
    const b_log_a = try mul(ctx, b, log_a);
    return try exp(ctx, b_log_a);
}

pub fn atan2(ctx: *Core, y: NodeId, x: NodeId) !NodeId {
    // atan2(y, x) = atan(y/x) with quadrant adjustment
    // For now, just return the division as a placeholder
    return try div(ctx, y, x);
}

// Comparison operations
pub fn less_than(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    const out_view = try getBinaryOutputView(ctx, a, b);
    const result = try ctx.graph.newNodeLessThan(&[_]core_types.NodeId{a, b}, out_view);
    return result;
}

pub fn greater_than(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a > b is equivalent to b < a
    return try less_than(ctx, b, a);
}

pub fn equals(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a == b is !(a < b) && !(b < a)
    // For now, simplified implementation
    return try less_than(ctx, a, b);
}

pub fn not_equals(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a != b is (a < b) || (b < a)
    return try less_than(ctx, a, b);
}

pub fn less_equal(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a <= b is !(b < a)
    return try less_than(ctx, b, a);
}

pub fn greater_equal(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // a >= b is !(a < b)
    return try less_than(ctx, a, b);
}

// Min/max operations
pub fn minimum(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // min(a, b) = (a < b) * a + !(a < b) * b
    // For now, simplified
    _ = ctx;
    _ = b;
    return a;
}

pub fn maximum(ctx: *Core, a: NodeId, b: NodeId) !NodeId {
    // max(a, b) = (a > b) * a + !(a > b) * b
    // For now, simplified
    _ = ctx;
    _ = b;
    return a;
}

// Activation functions
pub fn sigmoid(ctx: *Core, a: NodeId) !NodeId {
    // sigmoid(x) = 1 / (1 + exp(-x))
    const one = try createBroadcastableConstant(ctx, a, 1.0);
    
    const neg_a = try neg(ctx, a);
    const exp_neg_a = try exp(ctx, neg_a);
    const one_plus_exp = try add(ctx, one, exp_neg_a);
    return try div(ctx, one, one_plus_exp);
}

pub fn tanh(ctx: *Core, a: NodeId) !NodeId {
    // tanh(x) = (exp(2x) - 1) / (exp(2x) + 1)
    const two = try createBroadcastableConstant(ctx, a, 2.0);
    const one = try createBroadcastableConstant(ctx, a, 1.0);
    
    const two_x = try mul(ctx, two, a);
    const exp_2x = try exp(ctx, two_x);
    const exp_2x_minus_1 = try sub(ctx, exp_2x, one);
    const exp_2x_plus_1 = try add(ctx, exp_2x, one);
    return try div(ctx, exp_2x_minus_1, exp_2x_plus_1);
}

pub fn relu(ctx: *Core, a: NodeId) !NodeId {
    // relu(x) = max(0, x)
    const zero = try createBroadcastableConstant(ctx, a, 0.0);
    return try maximum(ctx, zero, a);
}

pub fn leaky_relu(ctx: *Core, a: NodeId, negative_slope: f32) !NodeId {
    // leaky_relu(x) = max(negative_slope * x, x)
    const alpha = try createBroadcastableConstant(ctx, a, negative_slope);
    const scaled = try mul(ctx, alpha, a);
    return try maximum(ctx, scaled, a);
}

pub fn relu2(ctx: *Core, a: NodeId) !NodeId {
    // relu2(x) = min(max(0, x), 2)
    const zero = try createBroadcastableConstant(ctx, a, 0.0);
    const two = try createBroadcastableConstant(ctx, a, 2.0);
    
    const relu_val = try maximum(ctx, zero, a);
    return try minimum(ctx, relu_val, two);
}

// Advanced activation functions
pub fn gelu(ctx: *Core, a: NodeId) !NodeId {
    // Gaussian Error Linear Unit approximation
    // gelu(x) ≈ 0.5 * x * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x^3)))
    const half = try createBroadcastableConstant(ctx, a, 0.5);
    const coeff = try createBroadcastableConstant(ctx, a, 0.044715);
    const sqrt_2_over_pi = try createBroadcastableConstant(ctx, a, @as(f32, std.math.sqrt(2.0 / std.math.pi)));
    
    // x^3
    const x_squared = try mul(ctx, a, a);
    const x_cubed = try mul(ctx, x_squared, a);
    
    // 0.044715 * x^3
    const coeff_x_cubed = try mul(ctx, coeff, x_cubed);
    
    // x + 0.044715 * x^3
    const x_plus_term = try add(ctx, a, coeff_x_cubed);
    
    // sqrt(2/π) * (x + 0.044715 * x^3)
    const scaled_inner = try mul(ctx, sqrt_2_over_pi, x_plus_term);
    
    // tanh(...)
    const tanh_result = try tanh(ctx, scaled_inner);
    
    // 1 + tanh(...)
    const one = try createBroadcastableConstant(ctx, a, 1.0);
    const one_plus_tanh = try add(ctx, one, tanh_result);
    
    // x * (1 + tanh(...))
    const x_times_term = try mul(ctx, a, one_plus_tanh);
    
    // 0.5 * x * (1 + tanh(...))
    return try mul(ctx, half, x_times_term);
}

pub fn fast_gelu(ctx: *Core, a: NodeId) !NodeId {
    // Fast approximation: gelu(x) ≈ x * sigmoid(1.702 * x)
    const coeff = try createBroadcastableConstant(ctx, a, 1.702);
    
    const scaled = try mul(ctx, coeff, a);
    const sig = try sigmoid(ctx, scaled);
    return try mul(ctx, a, sig);
}

pub fn swish(ctx: *Core, a: NodeId) !NodeId {
    // swish(x) = x * sigmoid(x)
    const sig = try sigmoid(ctx, a);
    return try mul(ctx, a, sig);
}

pub fn silu(ctx: *Core, a: NodeId) !NodeId {
    // SiLU is the same as Swish
    return try swish(ctx, a);
}

pub fn softplus(ctx: *Core, a: NodeId) !NodeId {
    // softplus(x) = log(1 + exp(x))
    const one = try createBroadcastableConstant(ctx, a, 1.0);
    
    const exp_a = try exp(ctx, a);
    const one_plus_exp = try add(ctx, one, exp_a);
    return try log(ctx, one_plus_exp);
}

// Normalization operations
pub fn layer_norm(ctx: *Core, a: NodeId, normalized_shape: []const usize, eps: f32) !NodeId {
    // Layer Normalization: x' = (x - mean) / sqrt(variance + eps)
    // normalized_shape defines which dimensions to normalize over (typically last dims)
    const reduction = @import("reduction.zig");
    
    // Calculate which dimensions to normalize over
    const node = ctx.graph.getNode(a) orelse return error.InvalidNode;
    const view = ctx.shape.getView(node.output_view_id);
    const shape = view.shape_id;
    const shape_info = ctx.shape.getShape(shape);
    
    // Create array of dimensions to reduce
    const total_dims = shape_info.dims.len;
    const norm_dims_count = normalized_shape.len;
    const reduce_dims_count = total_dims - norm_dims_count;
    
    const reduce_dims = try ctx.arena.allocator().alloc(i32, norm_dims_count);
    for (0..norm_dims_count) |i| {
        reduce_dims[i] = @intCast(reduce_dims_count + i);
    }
    
    // Calculate mean
    const mean_val = try reduction.mean(ctx, a, reduce_dims, true);
    
    // Calculate variance: Var(X) = E[X^2] - E[X]^2
    const x_squared = try mul(ctx, a, a);
    const mean_x_squared = try reduction.mean(ctx, x_squared, reduce_dims, true);
    const mean_squared = try mul(ctx, mean_val, mean_val);
    const variance = try sub(ctx, mean_x_squared, mean_squared);
    
    // Add epsilon to variance
    const eps_constant = try createBroadcastableConstant(ctx, variance, eps);
    const var_plus_eps = try add(ctx, variance, eps_constant);
    
    // Calculate standard deviation
    const std_dev = try sqrt(ctx, var_plus_eps);
    
    // Normalize: (x - mean) / std_dev
    const x_minus_mean = try sub(ctx, a, mean_val);
    return try div(ctx, x_minus_mean, std_dev);
}

pub fn rms_norm(ctx: *Core, a: NodeId, weight: NodeId, eps: f32) !NodeId {
    // Root Mean Square Layer Normalization: x_norm = x / RMS(x) * weight
    // RMS(x) = sqrt(mean(x^2) + eps)
    const reduction = @import("reduction.zig");
    
    // Square the input
    const x_squared = try mul(ctx, a, a);
    
    // Calculate mean of squared values across last dimension
    const last_dim = [_]i32{-1};
    const mean_squared = try reduction.mean(ctx, x_squared, &last_dim, true);
    
    // Add epsilon
    const eps_constant = try createBroadcastableConstant(ctx, mean_squared, eps);
    const mean_plus_eps = try add(ctx, mean_squared, eps_constant);
    
    // Take square root to get RMS
    const rms = try sqrt(ctx, mean_plus_eps);
    
    // Normalize by dividing by RMS
    const normalized = try div(ctx, a, rms);
    
    // Apply weight
    return try mul(ctx, normalized, weight);
}

pub fn mean_norm(ctx: *Core, a: NodeId, eps: f32) !NodeId {
    // Mean normalization: x' = (x - mean) / (max - min + eps)
    const reduction = @import("reduction.zig");
    
    // Calculate mean
    const mean_val = try reduction.mean(ctx, a, null, true);
    
    // Calculate max and min
    const max_val = try reduction.max(ctx, a, null, true);
    const min_val = try reduction.min(ctx, a, null, true);
    
    // Calculate range (max - min)
    const range = try sub(ctx, max_val, min_val);
    
    // Add epsilon to range
    const eps_constant = try createBroadcastableConstant(ctx, range, eps);
    const range_plus_eps = try add(ctx, range, eps_constant);
    
    // Normalize: (x - mean) / (range + eps)
    const x_minus_mean = try sub(ctx, a, mean_val);
    return try div(ctx, x_minus_mean, range_plus_eps);
}

pub fn std_norm(ctx: *Core, a: NodeId, dim: i64, eps: f32) !NodeId {
    // Standard normalization: x' = (x - mean) / (std + eps)
    const reduction = @import("reduction.zig");
    
    // Convert dim to array for reduction functions
    const dims = try ctx.arena.allocator().alloc(i32, 1);
    dims[0] = @intCast(dim);
    
    // Calculate mean
    const mean_val = try reduction.mean(ctx, a, dims, true);
    
    // Calculate variance: Var(X) = E[X^2] - E[X]^2
    const x_squared = try mul(ctx, a, a);
    const mean_x_squared = try reduction.mean(ctx, x_squared, dims, true);
    const mean_squared = try mul(ctx, mean_val, mean_val);
    const variance = try sub(ctx, mean_x_squared, mean_squared);
    
    // Add epsilon to variance
    const eps_constant = try createBroadcastableConstant(ctx, variance, eps);
    const var_plus_eps = try add(ctx, variance, eps_constant);
    
    // Calculate standard deviation
    const std_dev = try sqrt(ctx, var_plus_eps);
    
    // Normalize: (x - mean) / std_dev
    const x_minus_mean = try sub(ctx, a, mean_val);
    return try div(ctx, x_minus_mean, std_dev);
}

// Softmax operations
pub fn softmax(ctx: *Core, a: NodeId, dim: i64) !NodeId {
    // Numerically stable softmax: exp(x - max(x)) / sum(exp(x - max(x)))
    const reduction = @import("reduction.zig");
    
    // Convert dim to array for reduction functions
    const dims = try ctx.arena.allocator().alloc(i32, 1);
    dims[0] = @intCast(dim);
    
    // Find maximum value along the specified dimension for numerical stability
    const max_val = try reduction.max(ctx, a, dims, true);
    
    // Subtract max from input: x - max(x)
    const x_minus_max = try sub(ctx, a, max_val);
    
    // Compute exp(x - max(x))
    const exp_x = try exp(ctx, x_minus_max);
    
    // Sum exp values along the dimension
    const sum_exp = try reduction.sum(ctx, exp_x, dims, true);
    
    // Divide exp by sum to get softmax
    return try div(ctx, exp_x, sum_exp);
}

pub fn log_softmax(ctx: *Core, a: NodeId, dim: i64) !NodeId {
    // Log-softmax for numerical stability
    // log_softmax(x) = x - max(x) - log(sum(exp(x - max(x))))
    const reduction = @import("reduction.zig");
    
    // Convert dim to array for reduction functions
    const dims = try ctx.arena.allocator().alloc(i32, 1);
    dims[0] = @intCast(dim);
    
    // Find maximum value along the specified dimension for numerical stability
    const max_val = try reduction.max(ctx, a, dims, true);
    
    // Subtract max from input: x - max(x)
    const x_minus_max = try sub(ctx, a, max_val);
    
    // Compute exp(x - max(x))
    const exp_x = try exp(ctx, x_minus_max);
    
    // Sum exp values along the dimension
    const sum_exp = try reduction.sum(ctx, exp_x, dims, true);
    
    // Take log of sum
    const log_sum_exp = try log(ctx, sum_exp);
    
    // Return x - max(x) - log(sum(exp(x - max(x))))
    return try sub(ctx, x_minus_max, log_sum_exp);
}