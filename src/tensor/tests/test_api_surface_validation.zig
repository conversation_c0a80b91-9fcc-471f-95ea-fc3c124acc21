// Test suite for validating the API surface of tensor operations
// Tests that expected functions exist and can be called
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");

test "tensor module exports are accessible" {
    // Verify that main tensor modules are exported correctly
    _ = tensor.creation;
    _ = tensor.pointwise;
    _ = tensor.reduction;
    _ = tensor.manipulation;
    _ = tensor.linalg;
    _ = tensor.types;
    _ = tensor.utils;
}

test "basic creation operations work" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Test that basic creation functions exist and work
    const constant_result = try tensor.creation.constant(ctx, 42.0);
    try testing.expect(ctx.graph.getNode(constant_result) != null);
    
    const zeros_result = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(3),
        try ctx.symbolic.newIntegerExpr(4),
    });
    try testing.expect(ctx.graph.getNode(zeros_result) != null);
    
    const ones_result = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    try testing.expect(ctx.graph.getNode(ones_result) != null);
}

test "basic pointwise operations work" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    const x = try tensor.creation.constant(ctx, 1.0);
    const y = try tensor.creation.constant(ctx, 2.0);
    
    // Test basic binary operations
    const add_result = try tensor.pointwise.add(ctx, x, y);
    try testing.expect(ctx.graph.getNode(add_result) != null);
    
    const mul_result = try tensor.pointwise.mul(ctx, x, y);
    try testing.expect(ctx.graph.getNode(mul_result) != null);
    
    // Test basic unary operations
    const exp_result = try tensor.pointwise.exp(ctx, x);
    try testing.expect(ctx.graph.getNode(exp_result) != null);
    
    const neg_result = try tensor.pointwise.neg(ctx, x);
    try testing.expect(ctx.graph.getNode(neg_result) != null);
}

test "tensor operation chaining works" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Test that tensor operations can be chained
    const x = try tensor.creation.constant(ctx, 1.0);
    const y = try tensor.creation.constant(ctx, 2.0);
    
    // Chain multiple operations
    const step1 = try tensor.pointwise.add(ctx, x, y);
    const step2 = try tensor.pointwise.mul(ctx, step1, x);
    const step3 = try tensor.pointwise.exp(ctx, step2);
    
    // Verify all intermediate results are valid
    try testing.expect(ctx.graph.getNode(step1) != null);
    try testing.expect(ctx.graph.getNode(step2) != null);
    try testing.expect(ctx.graph.getNode(step3) != null);
}

test "tensor types are accessible" {
    // Test that essential types are available through the tensor module
    _ = tensor.types.NodeId;
    _ = tensor.types.TensorError;
    
    // Test that type instances can be created
    const valid_node = tensor.types.NodeId.invalid;
    try testing.expect(!valid_node.isValid());
}

test "module re-exports work" {
    // Test that the main tensor module re-exports work
    _ = tensor.constant;
    _ = tensor.zeros;
    _ = tensor.ones;
}

test "complex tensor computation" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Test a more complex computation using multiple operation types
    const a = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    const b = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    // Complex expression: exp(a + b) * (a - b)
    const sum = try tensor.pointwise.add(ctx, a, b);
    const diff = try tensor.pointwise.sub(ctx, a, b);
    const exp_sum = try tensor.pointwise.exp(ctx, sum);
    const result = try tensor.pointwise.mul(ctx, exp_sum, diff);
    
    // Verify final result exists
    try testing.expect(ctx.graph.getNode(result) != null);
}