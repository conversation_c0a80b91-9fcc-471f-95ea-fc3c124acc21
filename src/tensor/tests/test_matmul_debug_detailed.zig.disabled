const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const creation = tensor.creation;
const linalg = tensor.linalg;
const tensor_types = tensor.types;

test "debug matmul step by step" {
    const allocator = std.testing.allocator;
    const core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors A[2,3] and B[3,4]
    const a_shape = [_]types.Dim{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
    };
    const a = try creation.ones(core_instance, &a_shape);
    std.debug.print("\n=== Initial Tensors ===\n", .{});
    std.debug.print("A shape: [2,3]\n", .{});
    
    const b_shape = [_]types.Dim{
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    };
    const b = try creation.ones(core_instance, &b_shape);
    std.debug.print("B shape: [3,4]\n", .{});
    
    // Step 1: Transpose B to get [4,3]
    std.debug.print("\n=== Step 1: Transpose B ===\n", .{});
    const b_t = try linalg.transpose(core_instance, b);
    
    const b_t_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(b_t)).?;
    const b_t_view = core_instance.shape.getView(@intFromEnum(b_t_node.output_view_id));
    const b_t_shape = core_instance.shape.getShape(b_t_view.shape_id);
    
    std.debug.print("B_t shape: [", .{});
    for (b_t_shape.dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(",", .{});
        switch (dim) {
            .concrete => |size| std.debug.print("{}", .{size}),
            .symbolic => std.debug.print("?", .{}),
        }
    }
    std.debug.print("]\n", .{});
    
    // Step 2: Expand A from [2,3] to [2,1,3]
    std.debug.print("\n=== Step 2: Expand A ===\n", .{});
    const manipulation = @import("tensor").manipulation;
    const a_exp = try manipulation.unsqueeze(core_instance, a, 1);
    
    const a_exp_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(a_exp)).?;
    const a_exp_view = core_instance.shape.getView(@intFromEnum(a_exp_node.output_view_id));
    const a_exp_shape = core_instance.shape.getShape(a_exp_view.shape_id);
    
    std.debug.print("A_exp shape: [", .{});
    for (a_exp_shape.dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(",", .{});
        switch (dim) {
            .concrete => |size| std.debug.print("{}", .{size}),
            .symbolic => std.debug.print("?", .{}),
        }
    }
    std.debug.print("]\n", .{});
    
    // Step 3: Expand B_t from [4,3] to [1,4,3]
    std.debug.print("\n=== Step 3: Expand B_t ===\n", .{});
    const b_t_exp = try manipulation.unsqueeze(core_instance, b_t, 0);
    
    const b_t_exp_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(b_t_exp)).?;
    const b_t_exp_view = core_instance.shape.getView(@intFromEnum(b_t_exp_node.output_view_id));
    const b_t_exp_shape = core_instance.shape.getShape(b_t_exp_view.shape_id);
    
    std.debug.print("B_t_exp shape: [", .{});
    for (b_t_exp_shape.dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(",", .{});
        switch (dim) {
            .concrete => |size| std.debug.print("{}", .{size}),
            .symbolic => std.debug.print("?", .{}),
        }
    }
    std.debug.print("]\n", .{});
    
    // Step 4: Multiply to get [2,4,3] 
    std.debug.print("\n=== Step 4: Multiply ===\n", .{});
    
    const pointwise = @import("tensor").pointwise;
    const mul = try pointwise.mul(core_instance, a_exp, b_t_exp);
    
    const mul_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(mul)).?;
    const mul_view = core_instance.shape.getView(@intFromEnum(mul_node.output_view_id));
    const mul_shape = core_instance.shape.getShape(mul_view.shape_id);
    
    std.debug.print("mul shape: [", .{});
    for (mul_shape.dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(",", .{});
        switch (dim) {
            .concrete => |size| std.debug.print("{}", .{size}),
            .symbolic => std.debug.print("?", .{}),
        }
    }
    std.debug.print("]\n", .{});
    
    // Step 5: Reduce sum over last dimension to get [2,4]
    std.debug.print("\n=== Step 5: Reduce sum ===\n", .{});
    const reduction = @import("tensor").reduction;
    const axes = [_]i32{-1};  // Reduce over the last dimension
    const result = try reduction.sum(core_instance, mul, &axes, false);
    
    const result_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(result)).?;
    const result_view = core_instance.shape.getView(@intFromEnum(result_node.output_view_id));
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    std.debug.print("result shape: [", .{});
    for (result_shape.dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(",", .{});
        switch (dim) {
            .concrete => |size| std.debug.print("{}", .{size}),
            .symbolic => std.debug.print("?", .{}),
        }
    }
    std.debug.print("]\n", .{});
    
    std.debug.print("\n=== Expected: [2,4] ===\n", .{});
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    try testing.expectEqual(@as(usize, 2), result_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), result_shape.dims[1].concrete);
}