/// Advanced Manipulation Operations Test
/// Tests for concat, stack, split, repeat operations that require graph implementation
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const tensor = @import("tensor");

const Core = core.Core;
const types = core.types;
const creation = tensor.creation;
const manipulation = tensor.manipulation;
const tensor_types = tensor.types;

test "Advanced Manipulation: concat operations (not yet implemented)" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create two 2x3 tensors
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_a = try creation.zeros(core_instance, &dims);
    const tensor_b = try creation.ones(core_instance, &dims);
    
    // Test concat along dim 0 - should return NotImplemented for now
    const tensors = [_]tensor_types.NodeId{tensor_a, tensor_b};
    const concat_result = manipulation.concat(core_instance, &tensors, 0);
    try testing.expectError(error.NotImplemented, concat_result);
}

test "Advanced Manipulation: stack operations (not yet implemented)" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create two 2x3 tensors  
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_a = try creation.zeros(core_instance, &dims);
    const tensor_b = try creation.ones(core_instance, &dims);
    
    // Test stack along new dim 0 - should return NotImplemented for now
    const tensors = [_]tensor_types.NodeId{tensor_a, tensor_b};
    const stack_result = manipulation.stack(core_instance, &tensors, 0);
    try testing.expectError(error.NotImplemented, stack_result);
}

test "Advanced Manipulation: split operations (not yet implemented)" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 4x3 tensor
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_4x3 = try creation.zeros(core_instance, &dims);
    
    // Test split into chunks of size 2 - should return NotImplemented for now
    const split_result = manipulation.split(core_instance, tensor_4x3, 2, 0);
    try testing.expectError(error.NotImplemented, split_result);
}

test "Advanced Manipulation: repeat operations (not yet implemented)" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims);
    
    // Test repeat - should return NotImplemented for now
    const repeats = [_]i64{2, 3};
    const repeat_result = manipulation.repeat(core_instance, tensor_2x3, &repeats);
    try testing.expectError(error.NotImplemented, repeat_result);
}

test "Advanced Manipulation: all basic operations work" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims);
    
    // Test basic view operations individually (each on fresh tensor)
    _ = try manipulation.reshape(core_instance, tensor_2x3, &[_]i64{6});
    
    // Create fresh tensor for transpose test
    const tensor_2x3_b = try creation.zeros(core_instance, &dims);
    _ = try manipulation.transpose(core_instance, tensor_2x3_b, null, null);
    
    // Create fresh tensor for slice test  
    const tensor_2x3_c = try creation.zeros(core_instance, &dims);
    _ = try manipulation.slice(core_instance, tensor_2x3_c, &[_]i64{0, 0}, &[_]i64{2, 2});
    
    // Create fresh tensor for flatten test
    const tensor_2x3_d = try creation.zeros(core_instance, &dims);
    _ = try manipulation.flatten(core_instance, tensor_2x3_d);
    
    // Squeeze is now implemented - test with a tensor that has singleton dimensions
    const dims_with_ones = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(1), // Singleton
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(1), // Singleton
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_with_ones = try creation.zeros(core_instance, &dims_with_ones);
    const squeeze_result = try manipulation.squeeze(core_instance, tensor_with_ones, null); // Squeeze all singleton dimensions
    _ = squeeze_result; // Should succeed
    
    // These basic operations should succeed - they are view operations
}