// Test suite for tensor layer pointwise operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const tensor_types = tensor.types;
const NodeId = tensor_types.NodeId;
const pointwise = tensor.pointwise;
const creation = tensor.creation;

test "Tensor.pointwise: binary addition" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0, 4.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 5.0, 6.0, 7.0, 8.0 });
    
    // Test addition
    const result = try pointwise.add(core_instance, a, b);
    
    // Verify graph structure
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .add);
    try testing.expectEqual(result_node.?.inputs.len, 2);
}

test "Tensor.pointwise: binary subtraction" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a = try creation.constant(core_instance, &[_]f32{ 5.0, 6.0, 7.0, 8.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0, 4.0 });
    
    // Test subtraction (implemented as a + (-b))
    const result = try pointwise.sub(core_instance, a, b);
    
    // Verify graph structure - should be an add node
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .add);
}

test "Tensor.pointwise: binary multiplication" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a = try creation.constant(core_instance, &[_]f32{ 2.0, 3.0, 4.0, 5.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0, 4.0 });
    
    // Test multiplication
    const result = try pointwise.mul(core_instance, a, b);
    
    // Verify graph structure
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .multiply);
}

test "Tensor.pointwise: binary division" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a = try creation.constant(core_instance, &[_]f32{ 10.0, 20.0, 30.0, 40.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 2.0, 4.0, 5.0, 8.0 });
    
    // Test division (implemented as a * recip(b))
    const result = try pointwise.div(core_instance, a, b);
    
    // Verify graph structure - should be a multiply node
    
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .multiply);
}

test "Tensor.pointwise: comparison operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 5.0, 3.0, 7.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 2.0, 4.0, 3.0, 8.0 });
    
    // Test less than
    const lt = try pointwise.less_than(core_instance, a, b);
    
    try testing.expect(lt != @as(types.NodeId, @enumFromInt(0)));
    
    // Test greater than
    const gt = try pointwise.greater_than(core_instance, a, b);
    
    try testing.expect(gt != @as(types.NodeId, @enumFromInt(0)));
    
    // Test equals
    const eq = try pointwise.equals(core_instance, a, b);
    
    try testing.expect(eq != @as(types.NodeId, @enumFromInt(0)));
}

test "Tensor.pointwise: unary negation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, -2.0, 3.0, -4.0 });
    
    // Test negation
    const result = try pointwise.neg(core_instance, a);
    
    // Verify graph structure - should be multiply by -1
    
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .multiply);
}

test "Tensor.pointwise: exponential operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ 0.0, 1.0, 2.0, 3.0 });
    
    // Test exp
    const exp_result = try pointwise.exp(core_instance, a);
    
    try testing.expect(exp_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test log
    const b = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 4.0, 8.0 });
    const log_result = try pointwise.log(core_instance, b);
    
    try testing.expect(log_result != @as(types.NodeId, @enumFromInt(0)));
}

test "Tensor.pointwise: trigonometric operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ 0.0, 1.57, 3.14, 4.71 });
    
    // Test sin
    const sin_result = try pointwise.sin(core_instance, a);
    
    try testing.expect(sin_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test cos
    const cos_result = try pointwise.cos(core_instance, a);
    
    try testing.expect(cos_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test tan
    const tan_result = try pointwise.tan(core_instance, a);
    
    try testing.expect(tan_result != @as(types.NodeId, @enumFromInt(0)));
}

test "Tensor.pointwise: activation functions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ -2.0, -1.0, 0.0, 1.0, 2.0 });
    
    // Test ReLU
    const relu_result = try pointwise.relu(core_instance, a);
    
    try testing.expect(relu_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test Sigmoid
    const sigmoid_result = try pointwise.sigmoid(core_instance, a);
    
    try testing.expect(sigmoid_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test Tanh
    const tanh_result = try pointwise.tanh(core_instance, a);
    
    try testing.expect(tanh_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test GELU
    const gelu_result = try pointwise.gelu(core_instance, a);
    
    try testing.expect(gelu_result != @as(types.NodeId, @enumFromInt(0)));
}

test "Tensor.pointwise: normalization operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor with shape [2, 3]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const a = try creation.full(core_instance, &shape_dims, 1.0);
    
    // Create weight and bias
    const weight_dims = [_]*types.Expr{ try core_instance.symbolic.newIntegerExpr(3) };
    const weight = try creation.ones(core_instance, &weight_dims);
    
    // Test layer norm - normalize across the last dimension (dimension 1)
    const layer_norm_result = try pointwise.layer_norm(core_instance, a, &[_]usize{1}, 1e-5);
    
    try testing.expect(layer_norm_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test RMS norm
    const rms_norm_result = try pointwise.rms_norm(core_instance, a, weight, 1e-5);
    
    try testing.expect(rms_norm_result != @as(types.NodeId, @enumFromInt(0)));
}

test "Tensor.pointwise: softmax operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor with shape [4]
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0, 4.0 });
    
    // Test softmax
    const softmax_result = try pointwise.softmax(core_instance, a, 0);
    
    try testing.expect(softmax_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test log_softmax
    const log_softmax_result = try pointwise.log_softmax(core_instance, a, 0);
    
    try testing.expect(log_softmax_result != @as(types.NodeId, @enumFromInt(0)));
}

test "Tensor.pointwise: complex decompositions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ -1.0, 0.0, 1.0, 2.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 2.0, 1.0, 2.0, 3.0 });
    
    // Test power operation
    const pow_result = try pointwise.pow(core_instance, a, b);
    
    try testing.expect(pow_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test atan2
    const atan2_result = try pointwise.atan2(core_instance, a, b);
    try testing.expect(atan2_result != @as(types.NodeId, @enumFromInt(0)));
    
    // Test swish (x * sigmoid(x))
    const swish_result = try pointwise.swish(core_instance, a);
    
    try testing.expect(swish_result != @as(types.NodeId, @enumFromInt(0)));
}