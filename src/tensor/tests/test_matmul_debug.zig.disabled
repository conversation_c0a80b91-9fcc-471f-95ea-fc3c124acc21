const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const creation = tensor.creation;
const linalg = tensor.linalg;
const tensor_types = tensor.types;

test "debug matmul shape" {
    const allocator = std.testing.allocator;
    const core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a_shape = [_]types.Dim{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
    };
    const a = try creation.ones(core_instance, &a_shape);
    
    const b_shape = [_]types.Dim{
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    };
    const b = try creation.ones(core_instance, &b_shape);
    
    std.debug.print("\nA shape: 2x3, B shape: 3x4\n", .{});
    
    // Test chain multiplication: (A @ B)
    const ab = try linalg.matmul(core_instance, a, b);
    
    // Check ab result shape
    const ab_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(ab)).?;
    const ab_view = core_instance.shape.getView(@intFromEnum(ab_node.output_view_id));
    const ab_shape = core_instance.shape.getShape(ab_view.shape_id);
    
    std.debug.print("AB shape dims length: {}\n", .{ab_shape.dims.len});
    for (ab_shape.dims, 0..) |dim, i| {
        switch (dim) {
            .concrete => |size| std.debug.print("  Dim {}: {}\n", .{i, size}),
            .symbolic => std.debug.print("  Dim {}: symbolic\n", .{i}),
        }
    }
}