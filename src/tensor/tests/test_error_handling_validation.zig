// Test suite for validating error handling in tensor operations
// Tests that operations handle invalid inputs appropriately
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");

test "operations handle invalid tensor references" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Create a valid tensor first
    const valid_tensor = try tensor.creation.constant(ctx, 1.0);
    
    // Create an invalid NodeId
    const invalid_tensor = tensor.types.NodeId.invalid;
    
    // Test that operations handle invalid tensors appropriately
    // Note: The actual error handling depends on implementation
    const result1 = tensor.pointwise.add(ctx, invalid_tensor, valid_tensor);
    try testing.expectError(tensor.types.TensorError.InvalidTensor, result1);
    
    const result2 = tensor.pointwise.mul(ctx, valid_tensor, invalid_tensor);
    try testing.expectError(tensor.types.TensorError.InvalidTensor, result2);
}

test "mathematical operations handle edge cases gracefully" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    const x = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(2),
    });
    
    // Division by zero scenarios are handled at execution time, not graph building
    // But we can test that the graph building succeeds
    const zero = try tensor.creation.constant(ctx, 0.0);
    const div_result = try tensor.pointwise.div(ctx, x, zero);
    
    // Graph building should succeed - division by zero is a runtime issue
    try testing.expect(ctx.graph.getNode(div_result) != null);
    
    // Test sqrt of negative values - also a runtime issue for actual execution
    const negative = try tensor.creation.constant(ctx, -1.0);
    const sqrt_result = try tensor.pointwise.sqrt(ctx, negative);
    
    // Graph building should succeed
    try testing.expect(ctx.graph.getNode(sqrt_result) != null);
}

test "operations validate input count correctly" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Create valid tensors
    const a = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    // Test that operations requiring specific input counts work correctly
    // Unary operations
    const unary_result = try tensor.pointwise.neg(ctx, a);
    try testing.expect(ctx.graph.getNode(unary_result) != null);
    
    // Binary operations
    const b = try tensor.creation.constant(ctx, 2.0);
    const binary_result = try tensor.pointwise.add(ctx, a, b);
    try testing.expect(ctx.graph.getNode(binary_result) != null);
    
    // Verify operations produce valid outputs
    const unary_node = ctx.graph.getNode(unary_result).?;
    const binary_node = ctx.graph.getNode(binary_result).?;
    
    // Both should have valid view IDs
    try testing.expect(unary_node.output_view_id != types.ViewId.invalid);
    try testing.expect(binary_node.output_view_id != types.ViewId.invalid);
}

test "creation operations validate shape arguments" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Test valid shape creation
    const valid_tensor = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    try testing.expect(ctx.graph.getNode(valid_tensor) != null);
    
    // Test edge cases that should work
    const single_element = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(1),
    });
    try testing.expect(ctx.graph.getNode(single_element) != null);
    
    // Note: Negative and zero dimensions would be caught by symbolic expression validation
    // These tests focus on graph building behavior with valid symbolic expressions
}

test "tensor operations maintain graph consistency" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    const initial_node_count = ctx.graph.nodes.items.len;
    
    // Create several tensors and operations
    const a = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    const b = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    const op1 = try tensor.pointwise.add(ctx, a, b);
    const op2 = try tensor.pointwise.mul(ctx, op1, a);
    const op3 = try tensor.pointwise.exp(ctx, op2);
    
    const final_node_count = ctx.graph.nodes.items.len;
    
    // Should have created new nodes
    try testing.expect(final_node_count > initial_node_count);
    
    // All operations should produce valid nodes
    try testing.expect(ctx.graph.getNode(a) != null);
    try testing.expect(ctx.graph.getNode(b) != null);
    try testing.expect(ctx.graph.getNode(op1) != null);
    try testing.expect(ctx.graph.getNode(op2) != null);
    try testing.expect(ctx.graph.getNode(op3) != null);
}