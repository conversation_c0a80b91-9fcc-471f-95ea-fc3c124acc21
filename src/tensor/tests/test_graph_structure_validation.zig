// Test suite for validating tensor operations create correct graph structures
// Tests decomposition logic without requiring execution
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");

test "tensor creation operations build graph nodes" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    const initial_nodes = ctx.graph.nodes.items.len;
    
    // Test basic tensor creation
    const x = try tensor.creation.constant(ctx, 5.0);
    const final_nodes = ctx.graph.nodes.items.len;
    
    // Should have created at least one new node
    try testing.expect(final_nodes > initial_nodes);
    
    // Verify the node exists in the graph
    const result_node = ctx.graph.getNode(x);
    try testing.expect(result_node != null);
}

test "pointwise operations create computation nodes" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    const x = try tensor.creation.constant(ctx, 1.0);
    const y = try tensor.creation.constant(ctx, 2.0);
    
    const initial_nodes = ctx.graph.nodes.items.len;
    
    // Test basic pointwise operations
    const add_result = try tensor.pointwise.add(ctx, x, y);
    const mul_result = try tensor.pointwise.mul(ctx, x, y);
    
    const final_nodes = ctx.graph.nodes.items.len;
    
    // Should have created new nodes for operations
    try testing.expect(final_nodes > initial_nodes);
    
    // Verify operations created valid nodes
    try testing.expect(ctx.graph.getNode(add_result) != null);
    try testing.expect(ctx.graph.getNode(mul_result) != null);
}

test "tensor operations create valid graph structure" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Create input tensors
    const a = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    const b = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    // Perform operations that should create graph nodes
    const result1 = try tensor.pointwise.add(ctx, a, b);
    const result2 = try tensor.pointwise.mul(ctx, result1, a);
    
    // Verify all nodes exist in graph
    try testing.expect(ctx.graph.getNode(a) != null);
    try testing.expect(ctx.graph.getNode(b) != null);
    try testing.expect(ctx.graph.getNode(result1) != null);
    try testing.expect(ctx.graph.getNode(result2) != null);
}

test "complex operations decompose correctly" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    const x = try tensor.creation.constant(ctx, 1.0);
    
    const initial_nodes = ctx.graph.nodes.items.len;
    
    // Complex operations that should decompose to primitives
    const exp_result = try tensor.pointwise.exp(ctx, x);
    const neg_result = try tensor.pointwise.neg(ctx, x);
    
    const final_nodes = ctx.graph.nodes.items.len;
    
    // Should have created multiple nodes for decomposition
    try testing.expect(final_nodes > initial_nodes + 1);
    
    // Verify results are valid
    try testing.expect(ctx.graph.getNode(exp_result) != null);
    try testing.expect(ctx.graph.getNode(neg_result) != null);
}