// Test suite for tensor layer decomposed operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const pointwise = tensor.pointwise;

test "Tensor: negation operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constant
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const const_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test negation decomposition
    const neg_id = try pointwise.neg(core_instance, const_id);
    
    // Verify it's implemented as multiply by -1
    const neg_node = core_instance.graph.getNode(neg_id);
    try testing.expect(neg_node != null);
    try testing.expectEqual(neg_node.?.op, .multiply);
}

test "Tensor: subtraction operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constants
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const a_id = try core_instance.graph.newNodeConstant(view_id);
    const b_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test subtraction decomposition
    const sub_id = try pointwise.sub(core_instance, a_id, b_id);
    
    // Verify it's implemented as a + (-b)
    const sub_node = core_instance.graph.getNode(sub_id);
    try testing.expect(sub_node != null);
    try testing.expectEqual(sub_node.?.op, .add);
}

test "Tensor: division operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constants
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const a_id = try core_instance.graph.newNodeConstant(view_id);
    const b_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test division decomposition
    const div_id = try pointwise.div(core_instance, a_id, b_id);
    
    // Verify it's implemented as a * (1/b)
    const div_node = core_instance.graph.getNode(div_id);
    try testing.expect(div_node != null);
    try testing.expectEqual(div_node.?.op, .multiply);
}

test "Tensor: absolute value operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constant
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const const_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test abs decomposition
    const abs_id = try pointwise.abs(core_instance, const_id);
    
    // Verify it's implemented as sqrt(a * a)
    const abs_node = core_instance.graph.getNode(abs_id);
    try testing.expect(abs_node != null);
    try testing.expectEqual(abs_node.?.op, .sqrt);
}

test "Tensor: exponential operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constant
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const const_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test exp decomposition  
    const exp_id = try pointwise.exp(core_instance, const_id);
    
    // Verify it's implemented as exp2(a * (1/ln(2)))
    const exp_node = core_instance.graph.getNode(exp_id);
    try testing.expect(exp_node != null);
    try testing.expectEqual(exp_node.?.op, .exp2);
}

test "Tensor: natural logarithm operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constant
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const const_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test log decomposition
    const log_id = try pointwise.log(core_instance, const_id);
    
    // Verify it's implemented as log2(a) * ln(2)
    const log_node = core_instance.graph.getNode(log_id);
    try testing.expect(log_node != null);
    try testing.expectEqual(log_node.?.op, .multiply);
}

test "Tensor: cosine operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constant
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const const_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test cos decomposition
    const cos_id = try pointwise.cos(core_instance, const_id);
    
    // Verify it's implemented as sin(a + π/2)
    const cos_node = core_instance.graph.getNode(cos_id);
    try testing.expect(cos_node != null);
    try testing.expectEqual(cos_node.?.op, .sin);
}

test "Tensor: complex operation chain" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create constants
    const dims = [_]types.Dim{ types.Dim{ .concrete = 4 } };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const input_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test complex chain: sigmoid(x) = 1 / (1 + exp(-x))
    const neg_input = try pointwise.neg(core_instance, input_id);
    const exp_neg = try pointwise.exp(core_instance, neg_input);
    const one_id = try core_instance.graph.newNodeConstant(view_id);
    const one_plus_exp = try pointwise.add(core_instance, one_id, exp_neg);
    const sigmoid = try pointwise.div(core_instance, one_id, one_plus_exp);
    
    // Verify final operation
    const sigmoid_node = core_instance.graph.getNode(sigmoid);
    try testing.expect(sigmoid_node != null);
    try testing.expectEqual(sigmoid_node.?.op, .multiply); // Division is multiply by reciprocal
}