// Tests for Luminal parity operations added to the tensor module
//
// This test file validates the new operations we've added:
// 1. expandTo - Multi-dimensional expansion to arbitrary shape
// 2. reshape with automatic contiguous
// 3. excise - Cut out elements with spacing
// 4. poolLastDim - 1D pooling on last dimension

const std = @import("std");
const tensor = @import("tensor");
const core = @import("core");
const testing = std.testing;

test "expandTo - basic multi-dimensional expansion" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 1x3 tensor
    const data = [_]f32{ 1, 2, 3 };
    const shape = [_]i64{1, 3};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Expand to 4x3
    const target_shape = [_]i64{4, 3};
    const b = try tensor.manipulation.expandTo(ctx, a, &target_shape);
    
    // Verify it's a view operation (same node)
    try testing.expectEqual(a, b);
    
    // Verify the shape was updated
    const node = ctx.graph.getNode(b).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
}

test "expandTo - expand from lower rank to higher rank" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 3-element 1D tensor
    const data = [_]f32{ 1, 2, 3 };
    const shape = [_]i64{3};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Expand to 2x3x3 (adding 2 new dimensions)
    const target_shape = [_]i64{2, 3, 3};
    const b = try tensor.manipulation.expandTo(ctx, a, &target_shape);
    
    // Verify it's a view operation
    try testing.expectEqual(a, b);
    
    // Verify the resulting shape
    const node = ctx.graph.getNode(b).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    try testing.expectEqual(@as(usize, 3), result_shape.dims.len);
}

test "reshape - automatic contiguous insertion" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a tensor and apply a non-contiguous view operation first
    const data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const shape = [_]i64{2, 3};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Transpose to make it non-contiguous
    const transposed = try tensor.manipulation.transpose(ctx, a, null, null);
    
    // Now reshape - should auto-insert contiguous
    const new_shape = [_]i64{6};
    const reshaped = try tensor.manipulation.reshape(ctx, transposed, &new_shape);
    
    // The result should be a different node (due to contiguous operation)
    try testing.expect(transposed != reshaped);
    
    // Verify the shape
    const node = ctx.graph.getNode(reshaped).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    try testing.expectEqual(@as(usize, 1), result_shape.dims.len);
}

test "excise - cut out elements with spacing" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 1D tensor with 10 elements
    const data = [_]f32{ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 };
    const shape = [_]i64{10};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Cut out 1 element every 2 elements (keep every other element)
    const spacing: usize = 2;
    const size: usize = 1;
    const b = try tensor.manipulation.excise(ctx, a, spacing, size);
    
    // Verify it's a view operation
    try testing.expectEqual(a, b);
    
    // The excise operation should modify the shape
    const node = ctx.graph.getNode(b).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    
    // After excise(2, 1), we keep elements at indices 0, 2, 4, 6, 8
    // So the result should have 5 elements
    const expected_size = try ctx.symbolic.evaluate(result_shape.dims[0], null);
    try testing.expectEqual(@as(i64, 5), expected_size);
}

test "poolLastDim - basic 1D pooling" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 2x8 tensor
    const data = [_]f32{ 
        1, 2, 3, 4, 5, 6, 7, 8,
        9, 10, 11, 12, 13, 14, 15, 16 
    };
    const shape = [_]i64{2, 8};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Pool with kernel=2, stride=2, dilation=1
    const kernel: usize = 2;
    const stride: usize = 2;
    const dilation: usize = 1;
    const b = try tensor.manipulation.poolLastDim(ctx, a, kernel, stride, dilation);
    
    // Verify it's a view operation
    try testing.expectEqual(a, b);
    
    // Check the resulting shape
    const node = ctx.graph.getNode(b).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    
    // Should have 3 dimensions: [2, 4, 2]
    // - Original batch dimension: 2
    // - Number of windows: (8 - 2) / 2 + 1 = 4
    // - Kernel size: 2
    try testing.expectEqual(@as(usize, 3), result_shape.dims.len);
}

test "poolLastDim - with dilation" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 1D tensor with 12 elements
    const data = [_]f32{ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
    const shape = [_]i64{12};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Pool with kernel=3, stride=2, dilation=2
    const kernel: usize = 3;
    const stride: usize = 2; 
    const dilation: usize = 2;
    const b = try tensor.manipulation.poolLastDim(ctx, a, kernel, stride, dilation);
    
    // Verify it's a view operation
    try testing.expectEqual(a, b);
    
    // Check the resulting shape
    const node = ctx.graph.getNode(b).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    
    // Effective kernel size = 3 + (3-1) * 2 = 7
    // Number of windows = (12 - 7) / 2 + 1 = 3
    // Result shape: [3, 3] (3 windows, kernel size 3)
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
}

test "optimizeViews - view chain optimization" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a tensor and apply multiple view operations
    const data = [_]f32{ 1, 2, 3, 4, 5, 6, 7, 8 };
    const shape = [_]i64{2, 4};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Apply multiple view operations
    const transposed = try tensor.manipulation.transpose(ctx, a, null, null);
    const shape2 = [_]i64{2, 2, 2};
    const reshaped = try tensor.manipulation.view(ctx, transposed, &shape2);
    const perm = [_]i32{2, 0, 1};
    const permuted = try tensor.manipulation.permute(ctx, reshaped, &perm);
    
    // Optimize the view chain
    const optimized = try tensor.manipulation.optimizeViews(ctx, permuted);
    
    // Should still be the same node
    try testing.expectEqual(permuted, optimized);
    
    // The view should be optimized (though we can't easily test the internal optimization)
    const node = ctx.graph.getNode(optimized).?;
    _ = node; // View optimization happens internally in shape engine
}

test "expand vs expandTo comparison" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 1x1x3 tensor
    const data = [_]f32{ 1, 2, 3 };
    const shape = [_]i64{1, 1, 3};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Use regular expand (must match dimensions)
    const expand_shape = [_]i64{2, 3, 3};
    const b = try tensor.manipulation.expand(ctx, a, &expand_shape);
    
    // Create another tensor for expandTo test
    const a2 = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Use expandTo (same result)
    const c = try tensor.manipulation.expandTo(ctx, a2, &expand_shape);
    
    // Both should be view operations
    try testing.expectEqual(a, b);
    try testing.expectEqual(a2, c);
}

test "broadcastTo converted to view operation" {
    var arena = std.heap.ArenaAllocator.init(testing.allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Create a 1x3 tensor
    const data = [_]f32{ 1, 2, 3 };
    const shape = [_]i64{1, 3};
    const a = try tensor.creation.constantWithShape(ctx, &data, &shape);
    
    // Broadcast to 4x3
    const target_shape = [_]i64{4, 3};
    const b = try tensor.manipulation.broadcastTo(ctx, a, &target_shape);
    
    // Verify it's now a view operation (same node)
    try testing.expectEqual(a, b);
    
    // Verify the shape
    const node = ctx.graph.getNode(b).?;
    const view = ctx.shape.getView(node.output_view_id);
    const result_shape = ctx.shape.getShape(view.shape_id);
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
}