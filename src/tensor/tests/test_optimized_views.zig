const std = @import("std");
const testing = std.testing;
const arena = std.heap.ArenaAllocator;

// Import core and tensor modules
const core = @import("core");
const Core = core.Core;
const tensor = @import("../mod.zig");

/// Test suite for optimized view operations in tensor layer
pub fn runTests(allocator: std.mem.Allocator) !void {
    try testOptimizedReshape(allocator);
    try testContiguousWithExpressions(allocator);
    try testChainedOperations(allocator);
    try testDimensionCollapsing(allocator);
    try testFakeDimensionTracking(allocator);
    try testExpressionCombinations(allocator);
    try testOptimizedBroadcast(allocator);
    try testCompoundOptimizations(allocator);
}

/// Test reshape with optimization
fn testOptimizedReshape(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a 4D tensor
    const a = try tensor.creation.zeros(ctx, &[_]i64{2, 3, 4, 5});
    
    // Reshape with optimization
    const reshaped = try tensor.manipulation.reshape(ctx, a, &[_]i64{2, 60}, true);
    
    // Verify that the tensor was optimized
    const node = ctx.graph.getNode(reshaped).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Should have 2 dimensions
    try testing.expectEqual(@as(usize, 2), shape.dims.len);
    try testing.expectEqual(@as(usize, 2), shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 60), shape.dims[1].concrete);
    
    // Should have optimized strides
    try testing.expectEqual(@as(i64, 60), view.strides[0]);
    try testing.expectEqual(@as(i64, 1), view.strides[1]);
}

/// Test contiguous with expression generation
fn testContiguousWithExpressions(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a tensor and perform operations that will create a non-contiguous view
    const a = try tensor.creation.zeros(ctx, &[_]i64{3, 4, 5});
    const transposed = try tensor.manipulation.transpose(ctx, a, 0, 2);
    
    // Make contiguous with expression generation
    const contiguous = try tensor.manipulation.contiguous(ctx, transposed, true);
    
    // Verify expressions were generated
    const node = ctx.graph.getNode(transposed).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    
    try testing.expect(view.validity_expr != null);
    try testing.expect(view.mask_expr != null);
    
    // Verify contiguous view
    const contiguous_node = ctx.graph.getNode(contiguous).?;
    const contiguous_view_id = contiguous_node.output_view_id;
    
    try testing.expect(ctx.shape.isContiguous(contiguous_view_id));
}

/// Test chained operations with optimization
fn testChainedOperations(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a tensor
    const a = try tensor.creation.zeros(ctx, &[_]i64{2, 3, 4, 5});
    
    // Perform a series of operations
    const transposed = try tensor.manipulation.transpose(ctx, a, 1, 2);
    const reshaped = try tensor.manipulation.reshape(ctx, transposed, &[_]i64{2, 4, 15}, true);
    
    // Verify that the reshaping optimized the view
    const node = ctx.graph.getNode(reshaped).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Should have optimized shape
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    try testing.expectEqual(@as(usize, 2), shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 15), shape.dims[2].concrete);
    
    // Check for fake dimensions
    try testing.expect(!view.isFakeDim(0));
    try testing.expect(!view.isFakeDim(1));
    try testing.expect(!view.isFakeDim(2));
}

/// Test dimension collapsing optimizations
fn testDimensionCollapsing(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a tensor with adjacent dimensions that can be collapsed
    const a = try tensor.creation.zeros(ctx, &[_]i64{2, 3, 4, 1, 5});
    
    // Perform an operation that should trigger dimension collapsing
    const collapsed = try tensor.manipulation.reshape(ctx, a, &[_]i64{6, 4, 5}, true);
    
    // Verify the collapsed dimensions
    const node = ctx.graph.getNode(collapsed).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Should have optimized shape with 3 dims instead of 5
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    try testing.expectEqual(@as(usize, 6), shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 5), shape.dims[2].concrete);
    
    // Check optimized strides
    try testing.expectEqual(@as(i64, 20), view.strides[0]);
    try testing.expectEqual(@as(i64, 5), view.strides[1]);
    try testing.expectEqual(@as(i64, 1), view.strides[2]);
    
    // Create another tensor and check size-1 dimension collapsing
    const b = try tensor.creation.zeros(ctx, &[_]i64{2, 1, 3, 1, 4});
    const squeezed = try tensor.manipulation.squeeze(ctx, b, true);
    
    // Verify the squeezed dimensions are collapsed
    const squeezed_node = ctx.graph.getNode(squeezed).?;
    const squeezed_view_id = squeezed_node.output_view_id;
    const squeezed_view = ctx.shape.getView(squeezed_view_id);
    const squeezed_shape = ctx.shape.getShape(squeezed_view.shape_id);
    
    // Should have 3 dimensions after squeezing and collapsing size-1 dimensions
    try testing.expectEqual(@as(usize, 3), squeezed_shape.dims.len);
    try testing.expectEqual(@as(usize, 2), squeezed_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), squeezed_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), squeezed_shape.dims[2].concrete);
}

/// Test fake dimension tracking
fn testFakeDimensionTracking(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a tensor and perform operations that would create fake dimensions
    const a = try tensor.creation.zeros(ctx, &[_]i64{5, 10});
    
    // Add a fake dimension with unsqueeze
    const unsqueezed = try tensor.manipulation.unsqueeze(ctx, a, 1, true);
    
    // Verify the fake dimension tracking
    const node = ctx.graph.getNode(unsqueezed).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    
    // Dimension 1 should be marked as fake
    try testing.expect(!view.isFakeDim(0));
    try testing.expect(view.isFakeDim(1));
    try testing.expect(!view.isFakeDim(2));
    
    // Check shape after unsqueeze
    const shape = ctx.shape.getShape(view.shape_id);
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    try testing.expectEqual(@as(usize, 5), shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 1), shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 10), shape.dims[2].concrete);
    
    // Now do an operation that should preserve the fake dimension tracking
    const sliced = try tensor.manipulation.slice(ctx, unsqueezed, 0, 1, 3, true);
    
    // Check that fake dimension tracking is preserved
    const sliced_node = ctx.graph.getNode(sliced).?;
    const sliced_view_id = sliced_node.output_view_id;
    const sliced_view = ctx.shape.getView(sliced_view_id);
    
    try testing.expect(!sliced_view.isFakeDim(0));
    try testing.expect(sliced_view.isFakeDim(1));
    try testing.expect(!sliced_view.isFakeDim(2));
    
    // Verify removing fake dimension with squeeze
    const squeezed = try tensor.manipulation.squeeze(ctx, unsqueezed, true);
    
    // Should go back to original dimensions
    const squeezed_node = ctx.graph.getNode(squeezed).?;
    const squeezed_view_id = squeezed_node.output_view_id;
    const squeezed_view = ctx.shape.getView(squeezed_view_id);
    const squeezed_shape = ctx.shape.getShape(squeezed_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), squeezed_shape.dims.len);
    try testing.expectEqual(@as(usize, 5), squeezed_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 10), squeezed_shape.dims[1].concrete);
}

/// Test expression generation in combination with tensor operations
fn testExpressionCombinations(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a tensor
    const a = try tensor.creation.zeros(ctx, &[_]i64{3, 4, 5});
    
    // Perform a series of operations that generate expressions
    const sliced = try tensor.manipulation.slice(ctx, a, 1, 1, 3, true);
    const transposed = try tensor.manipulation.transpose(ctx, sliced, 0, 2);
    
    // Check that expressions were generated correctly
    const node = ctx.graph.getNode(transposed).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    
    // Should have validity and mask expressions
    try testing.expect(view.validity_expr != null);
    try testing.expect(view.mask_expr != null);
    
    // Perform an operation combining multiple tensor operations
    const b = try tensor.creation.zeros(ctx, &[_]i64{3, 2, 5});
    const concated = try tensor.manipulation.concat(ctx, transposed, b, 1, true);
    
    // Check the combined expressions
    const concated_node = ctx.graph.getNode(concated).?;
    const concated_view_id = concated_node.output_view_id;
    const concated_view = ctx.shape.getView(concated_view_id);
    
    // Should have complex validity expression
    try testing.expect(concated_view.validity_expr != null);
    
    // Verify that the expressions handle the operations correctly
    const result_shape = ctx.shape.getShape(concated_view.shape_id);
    try testing.expectEqual(@as(usize, 3), result_shape.dims.len);
    try testing.expectEqual(@as(usize, 5), result_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), result_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 3), result_shape.dims[2].concrete);
}

/// Test optimized broadcast operations
fn testOptimizedBroadcast(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create tensors with different shapes for broadcasting
    const a = try tensor.creation.zeros(ctx, &[_]i64{1, 5, 1});
    const b = try tensor.creation.zeros(ctx, &[_]i64{3, 1, 4});
    
    // Add them together (implicit broadcast)
    const result = try tensor.pointwise.add(ctx, a, b, true);
    
    // Verify the optimized broadcast view
    const node = ctx.graph.getNode(result).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Should have broadcast shape
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    try testing.expectEqual(@as(usize, 3), shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 5), shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), shape.dims[2].concrete);
    
    // Check for optimized fake dimension tracking in the broadcasts
    const a_node = ctx.graph.getNode(a).?;
    const a_view_id = a_node.output_view_id;
    const a_view = ctx.shape.getView(a_view_id);
    
    try testing.expect(a_view.isFakeDim(0));
    try testing.expect(!a_view.isFakeDim(1));
    try testing.expect(a_view.isFakeDim(2));
    
    const b_node = ctx.graph.getNode(b).?;
    const b_view_id = b_node.output_view_id;
    const b_view = ctx.shape.getView(b_view_id);
    
    try testing.expect(!b_view.isFakeDim(0));
    try testing.expect(b_view.isFakeDim(1));
    try testing.expect(!b_view.isFakeDim(2));
}

/// Test compound optimizations with multiple operations
fn testCompoundOptimizations(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var ctx = try Core.init(arena_instance.allocator());
    defer ctx.deinit();
    
    // Create a tensor
    const a = try tensor.creation.zeros(ctx, &[_]i64{2, 3, 4, 5});
    
    // Perform a complex sequence of operations
    const sliced = try tensor.manipulation.slice(ctx, a, 2, 1, 3, true);
    const unsqueezed = try tensor.manipulation.unsqueeze(ctx, sliced, 1, true);
    const transposed = try tensor.manipulation.transpose(ctx, unsqueezed, 0, 3);
    const reshaped = try tensor.manipulation.reshape(ctx, transposed, &[_]i64{5, 2, 6}, true);
    
    // Verify the compound optimizations
    const node = ctx.graph.getNode(reshaped).?;
    const view_id = node.output_view_id;
    const view = ctx.shape.getView(view_id);
    const shape = ctx.shape.getShape(view.shape_id);
    
    // Check final shape
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    try testing.expectEqual(@as(usize, 5), shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 2), shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 6), shape.dims[2].concrete);
    
    // Check for expression generation in compound operations
    try testing.expect(view.validity_expr != null);
    
    // Now perform a pointwise operation on this complex view
    const b = try tensor.creation.zeros(ctx, &[_]i64{5, 2, 6});
    const multiplied = try tensor.pointwise.mul(ctx, reshaped, b, true);
    
    // Verify optimization persists through pointwise operations
    const mul_node = ctx.graph.getNode(multiplied).?;
    const mul_view_id = mul_node.output_view_id;
    const mul_view = ctx.shape.getView(mul_view_id);
    
    // Check that optimizations persist
    try testing.expect(mul_view.validity_expr != null);
    
    // Perform a final contiguous operation to see if it correctly materializes the tensor
    const final = try tensor.manipulation.contiguous(ctx, multiplied, true);
    
    // The final view should be contiguous and have no expressions
    const final_node = ctx.graph.getNode(final).?;
    const final_view_id = final_node.output_view_id;
    
    try testing.expect(ctx.shape.isContiguous(final_view_id));
}