// Test suite for validating shape inference in tensor operations
// Tests that operations produce correct output shapes
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");

test "unary operations preserve input shapes" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Create tensor with specific shape
    const x = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    // Test that unary operations don't change the tensor structure
    const exp_result = try tensor.pointwise.exp(ctx, x);
    const neg_result = try tensor.pointwise.neg(ctx, x);
    
    // Verify all results exist (shape verification would require execution)
    try testing.expect(ctx.graph.getNode(exp_result) != null);
    try testing.expect(ctx.graph.getNode(neg_result) != null);
    
    // Get the nodes to verify they have the expected structure
    const x_node = ctx.graph.getNode(x).?;
    const exp_node = ctx.graph.getNode(exp_result).?;
    const neg_node = ctx.graph.getNode(neg_result).?;
    
    // Basic structure check - operations should have valid view IDs
    try testing.expect(x_node.output_view_id != types.ViewId.invalid);
    try testing.expect(exp_node.output_view_id != types.ViewId.invalid);
    try testing.expect(neg_node.output_view_id != types.ViewId.invalid);
}

test "binary operations handle compatible shapes" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Create tensors with same shapes
    const a = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    const b = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    // Test binary operations
    const add_result = try tensor.pointwise.add(ctx, a, b);
    const mul_result = try tensor.pointwise.mul(ctx, a, b);
    
    // Verify operations succeeded
    try testing.expect(ctx.graph.getNode(add_result) != null);
    try testing.expect(ctx.graph.getNode(mul_result) != null);
    
    // Verify nodes have valid view IDs
    const add_node = ctx.graph.getNode(add_result).?;
    const mul_node = ctx.graph.getNode(mul_result).?;
    try testing.expect(add_node.output_view_id != types.ViewId.invalid);
    try testing.expect(mul_node.output_view_id != types.ViewId.invalid);
}

test "tensor creation produces valid shapes" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Test various tensor creation operations
    const scalar = try tensor.creation.constant(ctx, 5.0);
    const matrix = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(4),
        try ctx.symbolic.newIntegerExpr(5),
    });
    const tensor_3d = try tensor.creation.ones(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
        try ctx.symbolic.newIntegerExpr(4),
    });
    
    // Verify all tensors were created successfully
    try testing.expect(ctx.graph.getNode(scalar) != null);
    try testing.expect(ctx.graph.getNode(matrix) != null);
    try testing.expect(ctx.graph.getNode(tensor_3d) != null);
    
    // Verify they have valid view IDs
    const scalar_node = ctx.graph.getNode(scalar).?;
    const matrix_node = ctx.graph.getNode(matrix).?;
    const tensor_3d_node = ctx.graph.getNode(tensor_3d).?;
    
    try testing.expect(scalar_node.output_view_id != types.ViewId.invalid);
    try testing.expect(matrix_node.output_view_id != types.ViewId.invalid);
    try testing.expect(tensor_3d_node.output_view_id != types.ViewId.invalid);
}

test "complex operations maintain shape consistency" {
    var ctx = try Core.init(testing.allocator);
    defer ctx.deinit();
    
    // Create tensors for complex operations
    const x = try tensor.creation.zeros(ctx, &[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(3),
        try ctx.symbolic.newIntegerExpr(3),
    });
    
    // Chain operations that should preserve shape structure
    const step1 = try tensor.pointwise.exp(ctx, x);
    const step2 = try tensor.pointwise.neg(ctx, step1);
    const step3 = try tensor.pointwise.add(ctx, x, step2);
    
    // Verify all operations succeeded and produced valid nodes
    try testing.expect(ctx.graph.getNode(step1) != null);
    try testing.expect(ctx.graph.getNode(step2) != null);
    try testing.expect(ctx.graph.getNode(step3) != null);
    
    // Verify shape consistency (all should have valid view IDs)
    const step1_node = ctx.graph.getNode(step1).?;
    const step2_node = ctx.graph.getNode(step2).?;
    const step3_node = ctx.graph.getNode(step3).?;
    
    try testing.expect(step1_node.output_view_id != types.ViewId.invalid);
    try testing.expect(step2_node.output_view_id != types.ViewId.invalid);
    try testing.expect(step3_node.output_view_id != types.ViewId.invalid);
}