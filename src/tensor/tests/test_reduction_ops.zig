// Test suite for tensor layer reduction operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const tensor_types = tensor.types;
const NodeId = tensor_types.NodeId;
const reduction = tensor.reduction;
const creation = tensor.creation;

test "Tensor.reduction: sum operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor [3, 4]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const a = try creation.ones(core_instance, &shape_dims);
    
    // Test sum all dimensions
    const sum_all = try reduction.sum(core_instance, a, null, false);
    try testing.expect(sum_all.isValid());
    
    // Test sum along dimension 0 (keepdim=false)
    const dims0 = [_]i32{0};
    const sum_dim0 = try reduction.sum(core_instance, a, &dims0, false);
    try testing.expect(sum_dim0.isValid());
    
    // Test sum along dimension 1 (keepdim=true)
    const dims1 = [_]i32{1};
    const sum_dim1_keepdim = try reduction.sum(core_instance, a, &dims1, true);
    try testing.expect(sum_dim1_keepdim.isValid());
}

test "Tensor.reduction: max operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 5.0, 3.0, 2.0 });
    
    // Test max all dimensions
    const max_all = try reduction.max(core_instance, a, null, false);
    try testing.expect(max_all.isValid());
    
    // Create 2D tensor for dimension testing
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(2),
    };
    const b = try creation.full(core_instance, &shape_dims, 1.0);
    
    // Test max along dimension 0
    const dims0 = [_]i32{0};
    const max_dim0 = try reduction.max(core_instance, b, &dims0, false);
    try testing.expect(max_dim0.isValid());
}

test "Tensor.reduction: min operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 5.0, 3.0, 2.0 });
    
    // Test min all dimensions
    const min_all = try reduction.min(core_instance, a, null, false);
    try testing.expect(min_all.isValid());
    
    // Verify min is implemented as -max(-a)
    const min_node = core_instance.graph.getNode(min_all);
    try testing.expect(min_node != null);
    try testing.expectEqual(min_node.?.op, .multiply); // Final negation
}

test "Tensor.reduction: mean operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor with known values
    const a = try creation.constant(core_instance, &[_]f32{ 2.0, 4.0, 6.0, 8.0 });
    
    // Test mean all dimensions
    const mean_all = try reduction.mean(core_instance, a, null, false);
    try testing.expect(mean_all.isValid());
    
    // Verify mean is implemented as sum/count
    const mean_node = core_instance.graph.getNode(mean_all);
    try testing.expect(mean_node != null);
    try testing.expectEqual(mean_node.?.op, .multiply); // Division as multiplication by reciprocal
}

test "Tensor.reduction: prod operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensor
    const a = try creation.constant(core_instance, &[_]f32{ 2.0, 3.0, 4.0, 5.0 });
    
    // Test product all dimensions
    const prod_all = try reduction.prod(core_instance, a, null, false);
    try testing.expect(prod_all.isValid());
    
    // Verify prod is implemented as exp(sum(log(x)))
    const prod_node = core_instance.graph.getNode(prod_all);
    try testing.expect(prod_node != null);
    try testing.expectEqual(prod_node.?.op, .exp2); // Final exp operation
}

test "Tensor.reduction: multi-dimensional reductions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 3D tensor [2, 3, 4]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const a = try creation.ones(core_instance, &shape_dims);
    
    // Test reduction along multiple dimensions
    const dims = [_]i32{ 0, 2 };
    const sum_multi = try reduction.sum(core_instance, a, &dims, false);
    try testing.expect(sum_multi.isValid());
}

test "Tensor.reduction: keepdim functionality" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2D tensor [3, 4]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const a = try creation.ones(core_instance, &shape_dims);
    
    // Test sum with keepdim=true
    const dims0 = [_]i32{0};
    const sum_keepdim = try reduction.sum(core_instance, a, &dims0, true);
    
    // Verify output shape has dimension 1 where reduced
    const result_node = core_instance.graph.getNode(sum_keepdim);
    try testing.expect(result_node != null);
    
    // The output should have shape [1, 4] when reducing dimension 0 with keepdim
    const view = core_instance.shape.getView(result_node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expect(shape.dims.len == 2);
}

test "Tensor.reduction: negative dimension indexing" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2D tensor [3, 4]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const a = try creation.ones(core_instance, &shape_dims);
    
    // Test sum with negative dimension index
    const dims_neg = [_]i32{-1}; // Last dimension
    const sum_neg_dim = try reduction.sum(core_instance, a, &dims_neg, false);
    try testing.expect(sum_neg_dim.isValid());
}

test "Tensor.reduction: symbolic dimensions error" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create tensor with symbolic dimension
    const symbol = try core_instance.symbolic.newSymbolExpr("n");
    const shape_dims = [_]*types.Expr{
        symbol,
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const a = try creation.zeros(core_instance, &shape_dims);
    
    // Test mean with symbolic dimension now works with pure expression system
    const mean_result = try reduction.mean(core_instance, a, null, false);
    
    // Verify the result has the expected shape (mean over all axes → scalar)
    const result_node = core_instance.graph.getNode(mean_result).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    try testing.expectEqual(@as(usize, 1), result_shape.dims.len);
}