const std = @import("std");
const core = @import("core");
const tensor = @import("tensor");

test "basic tensor creation and operations" {
    // Initialize core
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Test object-oriented API
    {
        const t1 = try tensor.Tensor.constant(&ctx, &[_]f32{ 1.0, 2.0, 3.0 });
        const t2 = try tensor.Tensor.constant(&ctx, &[_]f32{ 4.0, 5.0, 6.0 });
        
        const t3 = try t1.add(t2);
        const t4 = try t3.mul(t2);
        const result = try t4.sum(null, false);
        
        std.debug.print("Object-oriented API test passed\n", .{});
    }
    
    // Test functional API
    {
        const t1 = try tensor.creation.constant(&ctx, &[_]f32{ 1.0, 2.0, 3.0 });
        const t2 = try tensor.creation.constant(&ctx, &[_]f32{ 4.0, 5.0, 6.0 });
        
        const t3 = try tensor.pointwise.add(&ctx, t1, t2);
        const t4 = try tensor.pointwise.mul(&ctx, t3, t2);
        const result = try tensor.reduction.sum(&ctx, t4, null, false);
        
        std.debug.print("Functional API test passed\n", .{});
    }
    
    // Test shape manipulation
    {
        const t1 = try tensor.Tensor.constant(&ctx, &[_][2]f32{
            .{ 1.0, 2.0 },
            .{ 3.0, 4.0 },
        });
        
        const reshaped = try t1.reshape(&[_]i64{4});
        const transposed = try t1.transpose(null, null);
        
        std.debug.print("Shape manipulation test passed\n", .{});
    }
    
    // Test error handling with TensorBuilder
    {
        const t1 = try tensor.Tensor.constant(&ctx, &[_]f32{ 1.0, 2.0, 3.0 });
        
        const result = t1.builder()
            .add(t1)
            .mul(t1)
            .sub(t1)
            .div(t1)
            .get();
        
        if (result) |t| {
            std.debug.print("TensorBuilder test passed\n", .{});
        } else |err| {
            std.debug.print("TensorBuilder error: {}\n", .{err});
        }
    }
}

test "matrix operations" {
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    
    var ctx = try core.Core.init(arena.allocator());
    defer ctx.deinit();
    
    // Test matrix multiplication
    {
        const a = try tensor.Tensor.constant(&ctx, &[_][3]f32{
            .{ 1.0, 2.0, 3.0 },
            .{ 4.0, 5.0, 6.0 },
        });
        
        const b = try tensor.Tensor.constant(&ctx, &[_][2]f32{
            .{ 7.0, 8.0 },
            .{ 9.0, 10.0 },
            .{ 11.0, 12.0 },
        });
        
        const c = try a.matmul(b);
        std.debug.print("Matrix multiplication test passed\n", .{});
    }
}