// Basic manipulation operations tests - only operations that actually work
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const tensor_types = tensor.types;
const NodeId = tensor_types.NodeId;
const creation = tensor.creation;
const manipulation = tensor.manipulation;

// Helper to get shape dimensions as values
fn getShapeDims(core_instance: *Core, node_id: NodeId) ![]i64 {
    const node = core_instance.graph.getNode(node_id).?;
    const view = core_instance.shape.getView(node.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    
    const dims = try core_instance.arena.allocator().alloc(i64, shape.dims.len);
    for (shape.dims, 0..) |dim_expr, i| {
        dims[i] = try core_instance.symbolic.evaluate(dim_expr, null);
    }
    return dims;
}

test "Manipulation: reshape operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims_2d);
    
    // Test reshape to 1D
    const reshaped_1d = try manipulation.reshape(core_instance, tensor_2x3, &[_]i64{6});
    const shape_1d = try getShapeDims(core_instance, reshaped_1d);
    try testing.expectEqual(@as(usize, 1), shape_1d.len);
    try testing.expectEqual(@as(i64, 6), shape_1d[0]);
    
    // Test flatten
    const flattened = try manipulation.flatten(core_instance, tensor_2x3);
    const shape_flat = try getShapeDims(core_instance, flattened);
    try testing.expectEqual(@as(usize, 1), shape_flat.len);
    try testing.expectEqual(@as(i64, 6), shape_flat[0]);
}

test "Manipulation: transpose operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims_2d);
    
    // Test transpose (swap dimensions)
    const transposed = try manipulation.transpose(core_instance, tensor_2x3, null, null);
    const shape_transposed = try getShapeDims(core_instance, transposed);
    try testing.expectEqual(@as(usize, 2), shape_transposed.len);
    try testing.expectEqual(@as(i64, 3), shape_transposed[0]);
    try testing.expectEqual(@as(i64, 2), shape_transposed[1]);
}

test "Manipulation: slice operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 4x5 tensor  
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
        try core_instance.symbolic.newIntegerExpr(5),
    };
    const tensor_4x5 = try creation.zeros(core_instance, &dims_2d);
    
    // Test slice [1:3, 2:4] -> 2x2
    const sliced = try manipulation.slice(core_instance, tensor_4x5, &[_]i64{1, 2}, &[_]i64{3, 4});
    const shape_sliced = try getShapeDims(core_instance, sliced);
    try testing.expectEqual(@as(usize, 2), shape_sliced.len);
    try testing.expectEqual(@as(i64, 2), shape_sliced[0]);
    try testing.expectEqual(@as(i64, 2), shape_sliced[1]);
}