// Tests to verify Luminal parity features in the tensor module
//
// This test file specifically validates the Luminal-style features we've implemented:
// 1. as_strided - Low-level view manipulation
// 2. expand with multiple dimensions
// 3. broadcastTo as a view operation
// 4. View chain optimization
// 5. Composed operations vs direct implementations

const std = @import("std");
const core = @import("core");
const tensor = @import("tensor");
const Core = core.Core;
const manipulation = tensor.manipulation;
const utils = tensor.utils;
const pointwise = tensor.pointwise;
const linalg = tensor.linalg;

// Main test function
pub fn main() !void {
    var arena = std.heap.ArenaAllocator.init(std.testing.allocator);
    defer arena.deinit();
    
    var core_ctx = try Core.init(arena.allocator());
    defer core_ctx.deinit();
    
    std.debug.print("\n== Testing Luminal Parity Features ==\n", .{});
    
    try testAsStrided(&core_ctx);
    try testExpandMultipleDimensions(&core_ctx);
    try testBroadcastToAsView(&core_ctx);
    try testOptimizeViews(&core_ctx);
    try testMatmulComposition(&core_ctx);
    try testDotProductComposition(&core_ctx);
    try testComplexViewChain(&core_ctx);
    
    std.debug.print("\n== All Luminal Parity Tests Passed ==\n", .{});
}

// Test the as_strided operation
fn testAsStrided(ctx: *Core) !void {
    std.debug.print("\nTesting as_strided functionality...\n", .{});
    
    // Create a 2x3 tensor with values 0-5
    const data = [_]f32{ 0, 1, 2, 3, 4, 5 };
    const a = try tensor.constant(ctx, &data, &[_]i64{2, 3});
    
    // Regular strides for a 2x3 tensor would be [3, 1]
    // Let's use as_strided to create a transposed view with strides [1, 2]
    const b = try manipulation.as_strided(ctx, a, &[_]i64{3, 2}, &[_]i64{1, 2}, 0);
    
    // When we materialize the tensor, it should be transposed
    const b_materialized = try manipulation.contiguous(ctx, b);
    
    // Make sure b and a reference the same node (view-only operation)
    std.debug.assert(
        a ==
        b
    );
    
    // But after materialization, it should be a different node
    std.debug.assert(
        a != 
        b_materialized
    );
    
    std.debug.print("as_strided test passed!\n", .{});
}

// Test expand with multiple dimensions
fn testExpandMultipleDimensions(ctx: *Core) !void {
    std.debug.print("\nTesting expand with multiple dimensions...\n", .{});
    
    // Create a 1x1x3 tensor
    const data = [_]f32{ 1, 2, 3 };
    const a = try tensor.constant(ctx, &data, &[_]i64{1, 1, 3});
    
    // Expand to 2x3x3 - expanding two dimensions simultaneously
    const b = try manipulation.expand(ctx, a, &[_]i64{2, 3, 3});
    
    // Make sure it's a view-only operation
    std.debug.assert(
        a ==
        b
    );
    
    std.debug.print("expand test passed!\n", .{});
}

// Test broadcastTo as a view operation
fn testBroadcastToAsView(ctx: *Core) !void {
    std.debug.print("\nTesting broadcastTo as view operation...\n", .{});
    
    // Create a 1x3 tensor
    const data = [_]f32{ 1, 2, 3 };
    const a = try tensor.constant(ctx, &data, &[_]i64{1, 3});
    
    // Broadcast to 4x3
    const b = try manipulation.broadcastTo(ctx, a, &[_]i64{4, 3});
    
    // Make sure it's a view-only operation
    std.debug.assert(
        a ==
        b
    );
    
    std.debug.print("broadcastTo test passed!\n", .{});
}

// Test view chain optimization
fn testOptimizeViews(ctx: *Core) !void {
    std.debug.print("\nTesting view chain optimization...\n", .{});
    
    // Create a 2x3 tensor
    const data = [_]f32{ 0, 1, 2, 3, 4, 5 };
    const a = try tensor.constant(ctx, &data, &[_]i64{2, 3});
    
    // Apply a chain of view operations:
    // 1. Reshape to 6x1
    // 2. Transpose to 1x6
    // 3. Reshape to 2x3 (same as original)
    const reshape1 = try manipulation.reshape(ctx, a, &[_]i64{6, 1});
    const transpose = try manipulation.transpose(ctx, reshape1, null, null);
    const reshape2 = try manipulation.reshape(ctx, transpose, &[_]i64{2, 3});
    
    // Get the view IDs before optimization
    const reshape2_node = ctx.graph.getNode(reshape2) orelse unreachable;
    const pre_optimize_view_id = reshape2_node.output_view_id;
    
    // Optimize the chain of view operations
    const optimized = try manipulation.optimizeViews(ctx, reshape2);
    
    // Get the view ID after optimization
    const optimized_node = ctx.graph.getNode(optimized) orelse unreachable;
    const post_optimize_view_id = optimized_node.output_view_id;
    
    // The optimized view should be different from the original chain of views
    std.debug.assert(pre_optimize_view_id != post_optimize_view_id);
    
    std.debug.print("optimizeViews test passed!\n", .{});
}

// Test matmul using composition
fn testMatmulComposition(ctx: *Core) !void {
    std.debug.print("\nTesting matmul composition...\n", .{});
    
    // Create two 2x2 matrices
    const a_data = [_]f32{ 1, 2, 3, 4 };
    const b_data = [_]f32{ 5, 6, 7, 8 };
    
    const a = try tensor.constant(ctx, &a_data, &[_]i64{2, 2});
    const b = try tensor.constant(ctx, &b_data, &[_]i64{2, 2});
    
    // Compute matmul using the composed implementation
    const result = try linalg.matmul(ctx, a, b);
    
    // Verify matmul creates a computational node (not view-only)
    std.debug.assert(
        a != 
        result
    );
    
    std.debug.print("matmul composition test passed!\n", .{});
}

// Test dot product using composition
fn testDotProductComposition(ctx: *Core) !void {
    std.debug.print("\nTesting dot product composition...\n", .{});
    
    // Create two 1D vectors
    const a_data = [_]f32{ 1, 2, 3 };
    const b_data = [_]f32{ 4, 5, 6 };
    
    const a = try tensor.constant(ctx, &a_data, &[_]i64{3});
    const b = try tensor.constant(ctx, &b_data, &[_]i64{3});
    
    // Compute dot product 
    const result = try linalg.dot(ctx, a, b);
    
    // Verify result creates a computational node (not view-only)
    std.debug.assert(
        a != 
        result
    );
    
    std.debug.print("dot product composition test passed!\n", .{});
}

// Test complex view chain with computation
fn testComplexViewChain(ctx: *Core) !void {
    std.debug.print("\nTesting complex view chain with computation...\n", .{});
    
    // Create a 2x3 tensor
    const data = [_]f32{ 1, 2, 3, 4, 5, 6 };
    const a = try tensor.constant(ctx, &data, &[_]i64{2, 3});
    
    // Apply a chain of operations:
    // 1. Reshape to 3x2
    const reshaped = try manipulation.reshape(ctx, a, &[_]i64{3, 2});
    
    // 2. Transpose to 2x3 (view operation)
    const transposed = try manipulation.transpose(ctx, reshaped, null, null);
    
    // 3. Add 10 to all elements (computational operation)
    const ten = try tensor.constant(ctx, &[_]f32{10}, &[_]i64{1});
    const added = try pointwise.add(ctx, transposed, ten);
    
    // 4. Slice to get a 2x2 portion (view operation)
    const sliced = try manipulation.slice(ctx, added, &[_]i64{0, 0}, &[_]i64{2, 2});
    
    // 5. Optimize view chain then materialize
    const optimized = try manipulation.optimizeViews(ctx, sliced);
    const result = try manipulation.contiguous(ctx, optimized);
    
    // Verify we get a new node after contiguous operation
    std.debug.assert(
        optimized != 
        result
    );
    
    std.debug.print("complex view chain test passed!\n", .{});
}