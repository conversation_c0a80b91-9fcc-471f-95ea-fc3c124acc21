const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const creation = tensor.creation;
const pointwise = tensor.pointwise;
const tensor_types = tensor.types;

test "debug broadcasting in multiplication" {
    const allocator = std.testing.allocator;
    const core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create [2,1,3] tensor
    const a_shape = [_]types.Dim{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 3 },
    };
    const a = try creation.ones(core_instance, &a_shape);
    std.debug.print("\nA shape: [2,1,3]\n", .{});
    
    // Create [1,4,3] tensor
    const b_shape = [_]types.Dim{
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 4 },
        types.Dim{ .concrete = 3 },
    };
    const b = try creation.ones(core_instance, &b_shape);
    std.debug.print("B shape: [1,4,3]\n", .{});
    
    // Multiply should broadcast to [2,4,3]
    std.debug.print("\nMultiplying...\n", .{});
    const mul = try pointwise.mul(core_instance, a, b);
    
    // Check result shape
    const mul_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(mul)).?;
    const mul_view = core_instance.shape.getView(@intFromEnum(mul_node.output_view_id));
    const mul_shape = core_instance.shape.getShape(mul_view.shape_id);
    
    std.debug.print("Result shape: [", .{});
    for (mul_shape.dims, 0..) |dim, i| {
        if (i > 0) std.debug.print(",", .{});
        switch (dim) {
            .concrete => |size| std.debug.print("{}", .{size}),
            .symbolic => std.debug.print("?", .{}),
        }
    }
    std.debug.print("]\n", .{});
    std.debug.print("Expected: [2,4,3]\n", .{});
    
    try testing.expectEqual(@as(usize, 3), mul_shape.dims.len);
    try testing.expectEqual(@as(usize, 2), mul_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), mul_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 3), mul_shape.dims[2].concrete);
}