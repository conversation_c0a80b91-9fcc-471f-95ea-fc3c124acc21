// Comprehensive symbolic tensor operations tests
// Tests integration between symbolic engine and tensor operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const tensor_types = tensor.types;
const NodeId = tensor_types.NodeId;
const creation = tensor.creation;
const pointwise = tensor.pointwise;
const reduction = tensor.reduction;
const linalg = tensor.linalg;
const manipulation = tensor.manipulation;

// Helper to check if expression is symbolic
fn isSymbolic(expr: *types.Expr) bool {
    return expr.tag == .symbol;
}

// Helper to evaluate if concrete, or return -1 if symbolic
fn tryEvaluate(core_instance: *Core, expr: *types.Expr) i64 {
    return core_instance.symbolic.evaluate(expr, null) catch -1;
}

test "Symbolic Tensor: creation with symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create symbolic dimensions
    const batch_size = try core_instance.symbolic.newSymbolExpr("batch");
    const seq_len = try core_instance.symbolic.newSymbolExpr("seq_len");
    const hidden_dim = try core_instance.symbolic.newIntegerExpr(768);
    
    // Create tensor with mixed symbolic/concrete dimensions
    const dims = [_]*types.Expr{ batch_size, seq_len, hidden_dim };
    const symbolic_tensor = try creation.zeros(core_instance, &dims);
    
    // Verify shape
    const node = core_instance.graph.getNode(symbolic_tensor).?;
    const view = core_instance.shape.getView(node.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    try testing.expect(isSymbolic(shape.dims[0])); // batch_size
    try testing.expect(isSymbolic(shape.dims[1])); // seq_len  
    try testing.expectEqual(@as(i64, 768), tryEvaluate(core_instance, shape.dims[2])); // hidden_dim
}

test "Symbolic Tensor: placeholder creation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create placeholder with symbolic batch dimension
    const batch_sym = try core_instance.symbolic.newSymbolExpr("batch");
    const seq_len = try core_instance.symbolic.newIntegerExpr(512);
    
    const dims = [_]*types.Expr{ batch_sym, seq_len };
    const placeholder = try creation.placeholder(core_instance, &dims, .f32);
    
    // Verify shape preservation
    const node = core_instance.graph.getNode(placeholder).?;
    const view = core_instance.shape.getView(node.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    
    try testing.expect(isSymbolic(shape.dims[0])); // batch dimension
    try testing.expectEqual(@as(i64, 512), tryEvaluate(core_instance, shape.dims[1]));
}

test "Symbolic Tensor: pointwise operations preserve symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create two tensors with symbolic dimensions
    const batch = try core_instance.symbolic.newSymbolExpr("N");
    const features = try core_instance.symbolic.newIntegerExpr(128);
    
    const dims = [_]*types.Expr{ batch, features };
    const tensor_a = try creation.zeros(core_instance, &dims);
    const tensor_b = try creation.ones(core_instance, &dims);
    
    // Test addition preserves symbolic dimensions
    const sum_result = try pointwise.add(core_instance, tensor_a, tensor_b);
    
    const result_node = core_instance.graph.getNode(sum_result).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    try testing.expect(isSymbolic(result_shape.dims[0])); // batch preserved
    try testing.expectEqual(@as(i64, 128), tryEvaluate(core_instance, result_shape.dims[1]));
    
    // Test multiplication also preserves dimensions  
    const mul_result = try pointwise.mul(core_instance, tensor_a, tensor_b);
    
    const mul_node = core_instance.graph.getNode(mul_result).?;
    const mul_view = core_instance.shape.getView(mul_node.output_view_id);
    const mul_shape = core_instance.shape.getShape(mul_view.shape_id);
    
    try testing.expect(isSymbolic(mul_shape.dims[0])); // batch preserved
    try testing.expectEqual(@as(i64, 128), tryEvaluate(core_instance, mul_shape.dims[1]));
}

test "Symbolic Tensor: reduction operations with symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create tensor with symbolic batch and concrete feature dims
    const batch = try core_instance.symbolic.newSymbolExpr("batch_size");
    const seq_len = try core_instance.symbolic.newIntegerExpr(100);
    const features = try core_instance.symbolic.newIntegerExpr(64);
    
    const dims = [_]*types.Expr{ batch, seq_len, features };
    const tensor_3d = try creation.zeros(core_instance, &dims);
    
    // Test mean reduction over last dimension (features)
    const mean_result = try reduction.mean(core_instance, tensor_3d, &[_]i32{2}, false);
    
    const result_node = core_instance.graph.getNode(mean_result).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    // Should be [batch_size, 100] after reducing features dimension
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    
    try testing.expect(isSymbolic(result_shape.dims[0])); // batch_size preserved
    try testing.expectEqual(@as(i64, 100), tryEvaluate(core_instance, result_shape.dims[1])); // seq_len preserved
    
    // Test sum reduction over sequence dimension
    const sum_result = try reduction.sum(core_instance, tensor_3d, &[_]i32{1}, false);
    
    const sum_node = core_instance.graph.getNode(sum_result).?;
    const sum_view = core_instance.shape.getView(sum_node.output_view_id);
    const sum_shape = core_instance.shape.getShape(sum_view.shape_id);
    
    // Should be [batch_size, 64] after reducing seq_len dimension  
    try testing.expectEqual(@as(usize, 2), sum_shape.dims.len);
    try testing.expect(isSymbolic(sum_shape.dims[0])); // batch_size preserved
    try testing.expectEqual(@as(i64, 64), tryEvaluate(core_instance, sum_shape.dims[1])); // features preserved
}

test "Symbolic Tensor: broadcasting with symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create tensors with compatible symbolic dimensions for broadcasting
    const batch = try core_instance.symbolic.newSymbolExpr("N");
    const features = try core_instance.symbolic.newIntegerExpr(256);
    
    // Tensor A: [N, 256]
    const dims_a = [_]*types.Expr{ batch, features };
    const tensor_a = try creation.zeros(core_instance, &dims_a);
    
    // Tensor B: [1, 256] (broadcastable to [N, 256])
    const one = try core_instance.symbolic.newIntegerExpr(1);
    const dims_b = [_]*types.Expr{ one, features };
    const tensor_b = try creation.ones(core_instance, &dims_b);
    
    // Test broadcasting in addition
    const broadcast_result = try pointwise.add(core_instance, tensor_a, tensor_b);
    
    const result_node = core_instance.graph.getNode(broadcast_result).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    // Result should be [N, 256] - symbolic dimension preserved
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    try testing.expect(isSymbolic(result_shape.dims[0])); // N preserved from broadcasting
    try testing.expectEqual(@as(i64, 256), tryEvaluate(core_instance, result_shape.dims[1]));
}

test "Symbolic Tensor: reshape with symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create tensor with symbolic batch dimension
    const batch = try core_instance.symbolic.newSymbolExpr("batch");
    const height = try core_instance.symbolic.newIntegerExpr(28);
    const width = try core_instance.symbolic.newIntegerExpr(28);
    
    const dims_2d = [_]*types.Expr{ batch, height, width }; // [batch, 28, 28]
    const tensor_3d = try creation.zeros(core_instance, &dims_2d);
    
    // Reshape to flatten spatial dimensions: [32, 784] (explicit instead of -1)
    const reshaped = try manipulation.reshape(core_instance, tensor_3d, &[_]i64{32, 784});
    
    const result_node = core_instance.graph.getNode(reshaped).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    // Should preserve symbolic batch dimension and concrete flattened dimension
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    // Note: -1 in reshape might be handled differently, checking if first dim is preserved
    try testing.expectEqual(@as(i64, 784), tryEvaluate(core_instance, result_shape.dims[1]));
}

test "Symbolic Tensor: transpose preserves symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create tensor with symbolic dimensions
    const batch = try core_instance.symbolic.newSymbolExpr("B");
    const seq_len = try core_instance.symbolic.newSymbolExpr("S");
    const hidden = try core_instance.symbolic.newIntegerExpr(512);
    
    const dims = [_]*types.Expr{ batch, seq_len, hidden }; // [B, S, 512]
    const tensor_3d = try creation.zeros(core_instance, &dims);
    
    // Transpose to [B, 512, S]
    const transposed = try manipulation.permute(core_instance, tensor_3d, &[_]i32{0, 2, 1});
    
    const result_node = core_instance.graph.getNode(transposed).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    // Verify dimension order after transpose
    try testing.expectEqual(@as(usize, 3), result_shape.dims.len);
    try testing.expect(isSymbolic(result_shape.dims[0])); // B preserved in position 0
    try testing.expectEqual(@as(i64, 512), tryEvaluate(core_instance, result_shape.dims[1])); // 512 moved to position 1
    try testing.expect(isSymbolic(result_shape.dims[2])); // S moved to position 2
}

test "Symbolic Tensor: expression equality and compatibility" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create same symbolic expression multiple times
    const batch1 = try core_instance.symbolic.newSymbolExpr("batch_size");
    const batch2 = try core_instance.symbolic.newSymbolExpr("batch_size");
    const different_batch = try core_instance.symbolic.newSymbolExpr("other_batch");
    
    // Test expression equality (should be same pointer due to caching)
    try testing.expectEqual(batch1, batch2);
    try testing.expect(batch1 != different_batch);
    
    // Test compatibility checking
    try testing.expect(try core_instance.symbolic.checkExprCompatibility(batch1, batch2));
    try testing.expect(!try core_instance.symbolic.checkExprCompatibility(batch1, different_batch));
    
    // Create tensors with compatible and incompatible shapes
    const dims_compatible = [_]*types.Expr{batch1};
    const dims_same = [_]*types.Expr{batch2}; // Same as batch1
    const dims_different = [_]*types.Expr{different_batch};
    
    const tensor_a = try creation.zeros(core_instance, &dims_compatible);
    const tensor_b = try creation.ones(core_instance, &dims_same);
    const tensor_c = try creation.zeros(core_instance, &dims_different);
    
    // Operations with compatible symbolic dimensions should work
    const compatible_result = try pointwise.add(core_instance, tensor_a, tensor_b);
    _ = compatible_result; // Should succeed
    
    // Operations with incompatible symbolic dimensions should be detected
    // Note: This might not fail at graph construction time but would fail at runtime
    const incompatible_result = pointwise.add(core_instance, tensor_a, tensor_c);
    _ = incompatible_result catch {}; // May succeed at graph level, would fail at execution
}