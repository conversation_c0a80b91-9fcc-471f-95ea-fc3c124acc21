// Test suite for tensor creation operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const creation = tensor.creation;
const tensor_types = tensor.types;

test "Tensor.creation: constant from scalar" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test scalar constant
    const scalar = try creation.constant(core_instance, 42.0);
    
    // Verify it creates a constant node
    const node = core_instance.graph.getNode(scalar);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .constant);
    
    // Verify shape is scalar (0-dimensional)
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 0);
}

test "Tensor.creation: constant from array" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test 1D array constant
    const array_1d = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0, 4.0 });
    
    // Verify shape
    const node = core_instance.graph.getNode(array_1d);
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 1);
    const dim0_val = try core_instance.symbolic.evaluate(shape.dims[0], null);
    try testing.expectEqual(dim0_val, 4);
    
    // Test 2D array constant
    const array_2d = try creation.constant(core_instance, &[_][3]f32{
        .{ 1.0, 2.0, 3.0 },
        .{ 4.0, 5.0, 6.0 },
    });
    
    // Verify 2D shape
    const node_2d = core_instance.graph.getNode(array_2d);
    const view_2d = core_instance.shape.getView(node_2d.?.output_view_id);
    const shape_2d = core_instance.shape.getShape(view_2d.shape_id);
    try testing.expectEqual(shape_2d.dims.len, 2); // Fixed: properly recognized as 2D array
    const dim0_val2 = try core_instance.symbolic.evaluate(shape_2d.dims[0], null);
    try testing.expectEqual(dim0_val2, 2); // 2 rows
    const dim1_val = try core_instance.symbolic.evaluate(shape_2d.dims[1], null);
    try testing.expectEqual(dim1_val, 3); // 3 columns
}

test "Tensor.creation: zeros" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create zeros tensor with shape [3, 4]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const zeros_tensor = try creation.zeros(core_instance, &shape_dims);
    
    // Verify it creates a constant node
    const node = core_instance.graph.getNode(zeros_tensor);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .constant);
    
    // Verify shape
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 2);
    const dim0_val = try core_instance.symbolic.evaluate(shape.dims[0], null);
    try testing.expectEqual(dim0_val, 3);
    const dim1_val = try core_instance.symbolic.evaluate(shape.dims[1], null);
    try testing.expectEqual(dim1_val, 4);
}

test "Tensor.creation: ones" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create ones tensor with shape [5]
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(5),
    };
    const ones_tensor = try creation.ones(core_instance, &shape_dims);
    
    // Verify it creates a constant node
    const node = core_instance.graph.getNode(ones_tensor);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .constant);
}

test "Tensor.creation: full" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create tensor filled with specific value
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const full_tensor = try creation.full(core_instance, &shape_dims, 3.14);
    
    // Verify it creates a constant node
    const node = core_instance.graph.getNode(full_tensor);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .constant);
}

test "Tensor.creation: eye" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create identity matrix
    const eye_tensor = try creation.eye(core_instance, 4, .f32);
    
    // Verify shape is square
    const node = core_instance.graph.getNode(eye_tensor);
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 2);
    const dim0_val = try core_instance.symbolic.evaluate(shape.dims[0], null);
    try testing.expectEqual(dim0_val, 4);
    const dim1_val = try core_instance.symbolic.evaluate(shape.dims[1], null);
    try testing.expectEqual(dim1_val, 4);
}

test "Tensor.creation: linspace" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create linearly spaced values
    const linspace_tensor = try creation.linspace(core_instance, 0.0, 10.0, 11);
    
    // Verify shape
    const node = core_instance.graph.getNode(linspace_tensor);
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 1);
    const dim0_val = try core_instance.symbolic.evaluate(shape.dims[0], null);
    try testing.expectEqual(dim0_val, 11);
}

test "Tensor.creation: arange" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create range of values
    const arange_tensor = try creation.arange(core_instance, 0.0, 10.0, 2.0);
    
    // Verify shape (should have 5 elements: 0, 2, 4, 6, 8)
    const node = core_instance.graph.getNode(arange_tensor);
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 1);
    const dim0_val = try core_instance.symbolic.evaluate(shape.dims[0], null);
    try testing.expectEqual(dim0_val, 5);
}

test "Tensor.creation: variable" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create trainable variable
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const variable_tensor = try creation.variable(core_instance, &shape_dims, .f32);
    
    // Verify it creates a variable node (not constant)
    const node = core_instance.graph.getNode(variable_tensor);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .variable);
}

test "Tensor.creation: variableWithData" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create trainable variable with initial data
    const initial_data = &[_]f32{ 1.0, 2.0, 3.0 };
    const variable_tensor = try creation.variableWithData(core_instance, initial_data);
    
    // Verify it creates a variable node
    const node = core_instance.graph.getNode(variable_tensor);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .variable);
    
    // Verify shape matches data
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 1);
    const dim0_val = try core_instance.symbolic.evaluate(shape.dims[0], null);
    try testing.expectEqual(dim0_val, 3);
}

test "Tensor.creation: placeholder" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create input placeholder
    const shape_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(32), // batch size
        try core_instance.symbolic.newIntegerExpr(784), // input size
    };
    const placeholder_tensor = try creation.placeholder(core_instance, &shape_dims, .f32);
    
    // Verify it creates an input node
    const node = core_instance.graph.getNode(placeholder_tensor);
    try testing.expect(node != null);
    try testing.expectEqual(node.?.op, .input);
}

test "Tensor.creation: placeholderSymbolic" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create input placeholder with symbolic dimensions
    const shape_spec = [_]creation.DimSpec{
        .{ .size = null, .symbol = "batch" }, // symbolic batch size
        .{ .size = 784, .symbol = null }, // concrete input size
    };
    const placeholder_tensor = try creation.placeholderSymbolic(core_instance, &shape_spec, .f32);
    
    // Verify shape has one symbolic and one concrete dimension
    const node = core_instance.graph.getNode(placeholder_tensor);
    const view = core_instance.shape.getView(node.?.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    try testing.expectEqual(shape.dims.len, 2);
    // Check that the first dimension is symbolic (contains a symbol)
    try testing.expect(shape.dims[0].tag == .symbol);
    const dim1_val = try core_instance.symbolic.evaluate(shape.dims[1], null);
    try testing.expectEqual(dim1_val, 784);
}

test "Tensor.creation: data type inference" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test different data types
    const float32_tensor = try creation.constant(core_instance, @as(f32, 3.14));
    const float64_tensor = try creation.constant(core_instance, @as(f64, 3.14159));
    const int32_tensor = try creation.constant(core_instance, @as(i32, 42));
    const uint32_tensor = try creation.constant(core_instance, @as(u32, 42));
    
    // All should create valid constant nodes
    const f32_node = core_instance.graph.getNode(float32_tensor);
    const f64_node = core_instance.graph.getNode(float64_tensor);
    const i32_node = core_instance.graph.getNode(int32_tensor);
    const u32_node = core_instance.graph.getNode(uint32_tensor);
    
    try testing.expect(f32_node != null);
    try testing.expect(f64_node != null);
    try testing.expect(i32_node != null);
    try testing.expect(u32_node != null);
}