// Test suite for tensor linear algebra operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const tensor_types = tensor.types;
const NodeId = tensor_types.NodeId;
const linalg = tensor.linalg;
const creation = tensor.creation;

test "Tensor.linalg: matrix multiplication" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test matrices
    // A: [2, 3]
    const a_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const a = try creation.ones(core_instance, &a_shape);
    
    // B: [3, 4]
    const b_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const b = try creation.ones(core_instance, &b_shape);
    
    // Test matmul - should produce [2, 4]
    const result = try linalg.matmul(core_instance, a, b);
    
    // Verify result shape
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    
    // Check that the final operation is a sum reduction
    try testing.expectEqual(result_node.?.op, .reduce_sum);
}

test "Tensor.linalg: batch matrix multiplication" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test batch matrices
    // A: [5, 2, 3] (batch of 5 matrices, each 2x3)
    const a_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(5),
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const a = try creation.ones(core_instance, &a_shape);
    
    // B: [5, 3, 4] (batch of 5 matrices, each 3x4)
    const b_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(5),
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const b = try creation.ones(core_instance, &b_shape);
    
    // Test bmm - should produce [5, 2, 4]
    const result = try linalg.bmm(core_instance, a, b);
    
    // Verify the operation completed
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .reduce_sum);
}

test "Tensor.linalg: dot product" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test vectors
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 4.0, 5.0, 6.0 });
    
    // Test dot product
    const result = try linalg.dot(core_instance, a, b);
    
    // Verify result - should be a sum reduction over all dimensions
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
    try testing.expectEqual(result_node.?.op, .reduce_sum);
}

test "Tensor.linalg: transpose operation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test matrix [3, 4]
    const shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const a = try creation.ones(core_instance, &shape);
    
    // Test transpose - should swap last two dimensions to [4, 3]
    const result = try linalg.transpose(core_instance, a);
    
    // In Luminal-style, transpose returns the same node ID with updated view
    try testing.expectEqual(result, a);
    
    // Verify the shape is transposed
    const result_node = core_instance.graph.getNode(result).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    const dim0_val = try core_instance.symbolic.evaluate(result_shape.dims[0], null);
    const dim1_val = try core_instance.symbolic.evaluate(result_shape.dims[1], null);
    try testing.expectEqual(@as(i64, 4), dim0_val);
    try testing.expectEqual(@as(i64, 3), dim1_val);
}

test "Tensor.linalg: matmul dimension mismatch error" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create matrices with incompatible dimensions
    // A: [2, 3]
    const a_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const a = try creation.ones(core_instance, &a_shape);
    
    // B: [4, 2] - inner dimensions don't match
    const b_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
        try core_instance.symbolic.newIntegerExpr(2),
    };
    const b = try creation.ones(core_instance, &b_shape);
    
    // Test matmul - should fail
    const result = linalg.matmul(core_instance, a, b);
    try testing.expectError(error.IncompatibleDimensionsForMatmul, result);
}

test "Tensor.linalg: matmul non-2D tensor error" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create non-2D tensors
    const a = try creation.constant(core_instance, &[_]f32{ 1.0, 2.0, 3.0 });
    const b = try creation.constant(core_instance, &[_]f32{ 4.0, 5.0, 6.0 });
    
    // Test matmul - should fail for 1D tensors
    const result = linalg.matmul(core_instance, a, b);
    try testing.expectError(error.MatmulRequires2DTensors, result);
}

test "Tensor.linalg: bmm non-3D tensor error" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2D tensors
    const a_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const a = try creation.ones(core_instance, &a_shape);
    const b = try creation.ones(core_instance, &a_shape);
    
    // Test bmm - should fail for 2D tensors
    const result = linalg.bmm(core_instance, a, b);
    try testing.expectError(error.BmmRequires3DTensors, result);
}

test "Tensor.linalg: matmul with symbolic dimensions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create matrices with symbolic dimensions
    const n = try core_instance.symbolic.newSymbolExpr("n");
    const a_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        n,
    };
    const a = try creation.zeros(core_instance, &a_shape);
    
    const b_shape = [_]*types.Expr{
        n,
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const b = try creation.zeros(core_instance, &b_shape);
    
    // Test matmul - should now work with symbolic dimensions
    const result = try linalg.matmul(core_instance, a, b);
    
    // Verify result shape is [m, 3] where m is symbolic
    const result_node = core_instance.graph.getNode(result).?;
    const result_view = core_instance.shape.getView(result_node.output_view_id);
    const result_shape = core_instance.shape.getShape(result_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), result_shape.dims.len);
    // Matmul [2, n] × [n, 3] should result in [2, 3] - both concrete
    try testing.expectEqual(@as(i64, 2), core_instance.symbolic.evaluate(result_shape.dims[0], null) catch unreachable);
    try testing.expectEqual(@as(i64, 3), core_instance.symbolic.evaluate(result_shape.dims[1], null) catch unreachable);
}

test "Tensor.linalg: matrix chain multiplication" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create chain of matrices
    // A: [2, 3]
    const a_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const a = try creation.ones(core_instance, &a_shape);
    
    // B: [3, 4]
    const b_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const b = try creation.ones(core_instance, &b_shape);
    
    // C: [4, 5]
    const c_shape = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
        try core_instance.symbolic.newIntegerExpr(5),
    };
    const c = try creation.ones(core_instance, &c_shape);
    
    // Test chain multiplication: (A @ B) @ C
    const ab = try linalg.matmul(core_instance, a, b);
    
    // Verify ab result is 2D
    const ab_node = core_instance.graph.getNode(ab).?;
    const ab_view = core_instance.shape.getView(ab_node.output_view_id);
    const ab_shape = core_instance.shape.getShape(ab_view.shape_id);
    try testing.expectEqual(@as(usize, 2), ab_shape.dims.len);
    
    const result = try linalg.matmul(core_instance, ab, c);
    
    // Verify the operation completed
    const result_node = core_instance.graph.getNode(result);
    try testing.expect(result_node != null);
}