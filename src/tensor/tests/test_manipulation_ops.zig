// Comprehensive test suite for tensor manipulation operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const tensor_types = tensor.types;
const NodeId = tensor_types.NodeId;
const creation = tensor.creation;
const manipulation = tensor.manipulation;

// Helper to get shape dimensions as values
fn getShapeDims(core_instance: *Core, node_id: NodeId) ![]i64 {
    const node = core_instance.graph.getNode(node_id).?;
    const view = core_instance.shape.getView(node.output_view_id);
    const shape = core_instance.shape.getShape(view.shape_id);
    
    const dims = try core_instance.arena.allocator().alloc(i64, shape.dims.len);
    for (shape.dims, 0..) |dim_expr, i| {
        dims[i] = try core_instance.symbolic.evaluate(dim_expr, null);
    }
    return dims;
}

test "Manipulation: reshape operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims_2d);
    
    // Test reshape to 1D
    const reshaped_1d = try manipulation.reshape(core_instance, tensor_2x3, &[_]i64{6});
    const shape_1d = try getShapeDims(core_instance, reshaped_1d);
    try testing.expectEqual(@as(usize, 1), shape_1d.len);
    try testing.expectEqual(@as(i64, 6), shape_1d[0]);
    
    // Test reshape to 3x2
    const reshaped_3x2 = try manipulation.reshape(core_instance, tensor_2x3, &[_]i64{3, 2});
    const shape_3x2 = try getShapeDims(core_instance, reshaped_3x2);
    try testing.expectEqual(@as(usize, 2), shape_3x2.len);
    try testing.expectEqual(@as(i64, 3), shape_3x2[0]);
    try testing.expectEqual(@as(i64, 2), shape_3x2[1]);
    
    // Test flatten
    const flattened = try manipulation.flatten(core_instance, tensor_2x3);
    const shape_flat = try getShapeDims(core_instance, flattened);
    try testing.expectEqual(@as(usize, 1), shape_flat.len);
    try testing.expectEqual(@as(i64, 6), shape_flat[0]);
}

test "Manipulation: squeeze and unsqueeze operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 1x3x1 tensor
    const dims_3d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(1),
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(1),
    };
    const tensor_3d = try creation.zeros(core_instance, &dims_3d);
    
    // Test squeeze all singleton dimensions
    const squeezed = try manipulation.squeeze(core_instance, tensor_3d, null);
    const shape_squeezed = try getShapeDims(core_instance, squeezed);
    try testing.expectEqual(@as(usize, 1), shape_squeezed.len);
    try testing.expectEqual(@as(i64, 3), shape_squeezed[0]);
    
    // Test squeeze specific dimension
    const squeezed_dim0 = try manipulation.squeeze(core_instance, tensor_3d, &[_]i64{0});
    const shape_squeezed_dim0 = try getShapeDims(core_instance, squeezed_dim0);
    try testing.expectEqual(@as(usize, 2), shape_squeezed_dim0.len);
    try testing.expectEqual(@as(i64, 3), shape_squeezed_dim0[0]);
    try testing.expectEqual(@as(i64, 1), shape_squeezed_dim0[1]);
    
    // Test unsqueeze
    const unsqueezed = try manipulation.unsqueeze(core_instance, squeezed, 1);
    const shape_unsqueezed = try getShapeDims(core_instance, unsqueezed);
    try testing.expectEqual(@as(usize, 2), shape_unsqueezed.len);
    try testing.expectEqual(@as(i64, 3), shape_unsqueezed[0]);
    try testing.expectEqual(@as(i64, 1), shape_unsqueezed[1]);
}

test "Manipulation: transpose and permute operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims_2d);
    
    // Test transpose (swap dimensions)
    const transposed = try manipulation.transpose(core_instance, tensor_2x3, null, null);
    const shape_transposed = try getShapeDims(core_instance, transposed);
    try testing.expectEqual(@as(usize, 2), shape_transposed.len);
    try testing.expectEqual(@as(i64, 3), shape_transposed[0]);
    try testing.expectEqual(@as(i64, 2), shape_transposed[1]);
    
    // Create 3D tensor for permute test
    const dims_3d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const tensor_3d = try creation.zeros(core_instance, &dims_3d);
    
    // Test permute (2,0,1) -> (4,2,3)
    const permuted = try manipulation.permute(core_instance, tensor_3d, &[_]i32{2, 0, 1});
    const shape_permuted = try getShapeDims(core_instance, permuted);
    try testing.expectEqual(@as(usize, 3), shape_permuted.len);
    try testing.expectEqual(@as(i64, 4), shape_permuted[0]);
    try testing.expectEqual(@as(i64, 2), shape_permuted[1]);
    try testing.expectEqual(@as(i64, 3), shape_permuted[2]);
}

test "Manipulation: slice and select operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 4x5 tensor  
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
        try core_instance.symbolic.newIntegerExpr(5),
    };
    const tensor_4x5 = try creation.zeros(core_instance, &dims_2d);
    
    // Test slice [1:3, 2:4] -> 2x2
    const sliced = try manipulation.slice(core_instance, tensor_4x5, &[_]i64{1, 2}, &[_]i64{3, 4});
    const shape_sliced = try getShapeDims(core_instance, sliced);
    try testing.expectEqual(@as(usize, 2), shape_sliced.len);
    try testing.expectEqual(@as(i64, 2), shape_sliced[0]);
    try testing.expectEqual(@as(i64, 2), shape_sliced[1]);
    
    // Test select (take index 1 from dimension 0) -> 5
    const selected = try manipulation.select(core_instance, tensor_4x5, 0, 1);
    const shape_selected = try getShapeDims(core_instance, selected);
    try testing.expectEqual(@as(usize, 1), shape_selected.len);
    try testing.expectEqual(@as(i64, 5), shape_selected[0]);
}

test "Manipulation: expand and broadcast operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 1x3 tensor
    const dims_1x3 = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(1),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_1x3 = try creation.zeros(core_instance, &dims_1x3);
    
    // Test expand to 4x3
    const expanded = try manipulation.expand(core_instance, tensor_1x3, &[_]i64{4, 3});
    const shape_expanded = try getShapeDims(core_instance, expanded);
    try testing.expectEqual(@as(usize, 2), shape_expanded.len);
    try testing.expectEqual(@as(i64, 4), shape_expanded[0]);
    try testing.expectEqual(@as(i64, 3), shape_expanded[1]);
    
    // Test broadcastTo (same as expand but different API)
    const broadcasted = try manipulation.broadcastTo(core_instance, tensor_1x3, &[_]i64{4, 3});
    const shape_broadcasted = try getShapeDims(core_instance, broadcasted);
    try testing.expectEqual(@as(usize, 2), shape_broadcasted.len);
    try testing.expectEqual(@as(i64, 4), shape_broadcasted[0]);
    try testing.expectEqual(@as(i64, 3), shape_broadcasted[1]);
}

test "Manipulation: concatenation operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create two 2x3 tensors
    const dims_2x3 = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor1 = try creation.zeros(core_instance, &dims_2x3);
    const tensor2 = try creation.ones(core_instance, &dims_2x3);
    
    // Test concat along dimension 0 -> 4x3
    const concat_dim0 = try manipulation.concat(core_instance, &[_]NodeId{tensor1, tensor2}, 0);
    const shape_concat0 = try getShapeDims(core_instance, concat_dim0);
    try testing.expectEqual(@as(usize, 2), shape_concat0.len);
    try testing.expectEqual(@as(i64, 4), shape_concat0[0]);
    try testing.expectEqual(@as(i64, 3), shape_concat0[1]);
    
    // Test concat along dimension 1 -> 2x6
    const concat_dim1 = try manipulation.concat(core_instance, &[_]NodeId{tensor1, tensor2}, 1);
    const shape_concat1 = try getShapeDims(core_instance, concat_dim1);
    try testing.expectEqual(@as(usize, 2), shape_concat1.len);
    try testing.expectEqual(@as(i64, 2), shape_concat1[0]);
    try testing.expectEqual(@as(i64, 6), shape_concat1[1]);
}

test "Manipulation: stack operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create two 2x3 tensors
    const dims_2x3 = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor1 = try creation.zeros(core_instance, &dims_2x3);
    const tensor2 = try creation.ones(core_instance, &dims_2x3);
    
    // Test stack along dimension 0 -> 2x2x3
    const stacked_dim0 = try manipulation.stack(core_instance, &[_]NodeId{tensor1, tensor2}, 0);
    const shape_stack0 = try getShapeDims(core_instance, stacked_dim0);
    try testing.expectEqual(@as(usize, 3), shape_stack0.len);
    try testing.expectEqual(@as(i64, 2), shape_stack0[0]);
    try testing.expectEqual(@as(i64, 2), shape_stack0[1]);
    try testing.expectEqual(@as(i64, 3), shape_stack0[2]);
    
    // Test stack along dimension 2 -> 2x3x2
    const stacked_dim2 = try manipulation.stack(core_instance, &[_]NodeId{tensor1, tensor2}, 2);
    const shape_stack2 = try getShapeDims(core_instance, stacked_dim2);
    try testing.expectEqual(@as(usize, 3), shape_stack2.len);
    try testing.expectEqual(@as(i64, 2), shape_stack2[0]);
    try testing.expectEqual(@as(i64, 3), shape_stack2[1]);
    try testing.expectEqual(@as(i64, 2), shape_stack2[2]);
}

test "Manipulation: split operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 6x4 tensor
    const dims_6x4 = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(6),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const tensor_6x4 = try creation.zeros(core_instance, &dims_6x4);
    
    // Test split into chunks of size 2 along dimension 0 -> 3 tensors of 2x4
    const split_chunks = try manipulation.split(core_instance, tensor_6x4, 2, 0);
    try testing.expectEqual(@as(usize, 3), split_chunks.len);
    
    // Check first chunk shape
    const chunk0_shape = try getShapeDims(core_instance, split_chunks[0]);
    try testing.expectEqual(@as(usize, 2), chunk0_shape.len);
    try testing.expectEqual(@as(i64, 2), chunk0_shape[0]);
    try testing.expectEqual(@as(i64, 4), chunk0_shape[1]);
    
    // Test splitWithSizes with specific sizes [2, 2, 2]
    const split_sizes = try manipulation.splitWithSizes(core_instance, tensor_6x4, &[_]i64{2, 2, 2}, 0);
    try testing.expectEqual(@as(usize, 3), split_sizes.len);
    
    // Check that all splits have correct size
    for (split_sizes) |chunk| {
        const chunk_shape = try getShapeDims(core_instance, chunk);
        try testing.expectEqual(@as(i64, 2), chunk_shape[0]);
        try testing.expectEqual(@as(i64, 4), chunk_shape[1]);
    }
}

test "Manipulation: repeat and tile operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims_2x3 = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims_2x3);
    
    // Test repeat [2, 3] -> 4x9
    const repeated = try manipulation.repeat(core_instance, tensor_2x3, &[_]i64{2, 3});
    const shape_repeated = try getShapeDims(core_instance, repeated);
    try testing.expectEqual(@as(usize, 2), shape_repeated.len);
    try testing.expectEqual(@as(i64, 4), shape_repeated[0]);
    try testing.expectEqual(@as(i64, 9), shape_repeated[1]);
    
    // Test tile [2, 3] -> 4x9 (similar to repeat but different semantics)
    const tiled = try manipulation.tile(core_instance, tensor_2x3, &[_]i64{2, 3});
    const shape_tiled = try getShapeDims(core_instance, tiled);
    try testing.expectEqual(@as(usize, 2), shape_tiled.len);
    try testing.expectEqual(@as(i64, 4), shape_tiled[0]);
    try testing.expectEqual(@as(i64, 9), shape_tiled[1]);
}

test "Manipulation: contiguous operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2x3 tensor
    const dims_2x3 = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const tensor_2x3 = try creation.zeros(core_instance, &dims_2x3);
    
    // Transpose to make it non-contiguous
    const transposed = try manipulation.transpose(core_instance, tensor_2x3, null, null);
    
    // Make it contiguous
    const contiguous_tensor = try manipulation.contiguous(core_instance, transposed);
    const shape_contiguous = try getShapeDims(core_instance, contiguous_tensor);
    try testing.expectEqual(@as(usize, 2), shape_contiguous.len);
    try testing.expectEqual(@as(i64, 3), shape_contiguous[0]);
    try testing.expectEqual(@as(i64, 2), shape_contiguous[1]);
}

test "Manipulation: as_strided low-level operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 6 element tensor
    const dims_1d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(6),
    };
    const tensor_1d = try creation.zeros(core_instance, &dims_1d);
    
    // Use as_strided to create 2x3 view
    const strided = try manipulation.as_strided(core_instance, tensor_1d, &[_]i64{2, 3}, &[_]i64{3, 1}, 0);
    const shape_strided = try getShapeDims(core_instance, strided);
    try testing.expectEqual(@as(usize, 2), shape_strided.len);
    try testing.expectEqual(@as(i64, 2), shape_strided[0]);
    try testing.expectEqual(@as(i64, 3), shape_strided[1]);
}