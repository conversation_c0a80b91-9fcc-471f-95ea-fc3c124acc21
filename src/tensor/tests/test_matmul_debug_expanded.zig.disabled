const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const tensor = @import("tensor");
const creation = tensor.creation;
const linalg = tensor.linalg;
const tensor_types = tensor.types;

test "debug matmul steps" {
    const allocator = std.testing.allocator;
    const core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create test tensors
    const a_shape = [_]*types.Expr{
        try types.Expr.testDim(core_instance.arena.allocator(), 2),
        try types.Expr.testDim(core_instance.arena.allocator(), 3),
    };
    const a = try creation.ones(core_instance, &a_shape);
    std.debug.print("\nA shape: 2x3\n", .{});
    
    const b_shape = [_]*types.Expr{
        try types.Expr.testDim(core_instance.arena.allocator(), 3),
        try types.Expr.testDim(core_instance.arena.allocator(), 4),
    };
    const b = try creation.ones(core_instance, &b_shape);
    std.debug.print("B shape: 3x4\n", .{});
    
    // Let's trace through the matmul steps manually
    const a_raw = tensor_types.nodeIdToU32(a);
    const b_raw = tensor_types.nodeIdToU32(b);
    
    const a_node = core_instance.graph.getNode(a_raw).?;
    const b_node = core_instance.graph.getNode(b_raw).?;
    
    _ = a_node;
    _ = b_node;
    
    
    std.debug.print("\nStep 1: Transpose B\n", .{});
    const b_t = try linalg.transpose(core_instance, b);
    
    const b_t_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(b_t)).?;
    const b_t_view = core_instance.shape.getView(@intFromEnum(b_t_node.output_view_id));
    const b_t_shape = core_instance.shape.getShape(b_t_view.shape_id);
    
    std.debug.print("B_t shape: dims.len={}\n", .{b_t_shape.dims.len});
    for (b_t_shape.dims, 0..) |dim, i| {
        switch (dim) {
            .concrete => |size| std.debug.print("  Dim {}: {}\n", .{i, size}),
            .symbolic => std.debug.print("  Dim {}: symbolic\n", .{i}),
        }
    }
    
    std.debug.print("\nStep 2: Print B dims for debugging\n", .{});
    const b_node_detail = core_instance.graph.getNode(tensor_types.nodeIdToU32(b)).?;
    const b_view_detail = core_instance.shape.getView(@intFromEnum(b_node_detail.output_view_id));
    const b_shape_detail = core_instance.shape.getShape(b_view_detail.shape_id);
    
    std.debug.print("Original B shape: dims.len={}\n", .{b_shape_detail.dims.len});
    for (b_shape_detail.dims, 0..) |dim, i| {
        switch (dim) {
            .concrete => |size| std.debug.print("  Dim {}: {}\n", .{i, size}),
            .symbolic => std.debug.print("  Dim {}: symbolic\n", .{i}),
        }
    }

    // Skip actual matmul since we're debugging the issue
    std.debug.print("\nSkipping matmul for now\n", .{});
    // const ab = try linalg.matmul(core_instance, a, b);
    
    // Create a dummy node for testing
    const dummy_shape = [_]types.Dim{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 4 },
    };
    const ab = try creation.ones(core_instance, &dummy_shape);
    
    // Check ab result shape
    const ab_node = core_instance.graph.getNode(tensor_types.nodeIdToU32(ab)).?;
    const ab_view = core_instance.shape.getView(@intFromEnum(ab_node.output_view_id));
    const ab_shape = core_instance.shape.getShape(ab_view.shape_id);
    
    std.debug.print("AB shape dims length: {}\n", .{ab_shape.dims.len});
    for (ab_shape.dims, 0..) |dim, i| {
        switch (dim) {
            .concrete => |size| std.debug.print("  Dim {}: {}\n", .{i, size}),
            .symbolic => std.debug.print("  Dim {}: symbolic\n", .{i}),
        }
    }
}