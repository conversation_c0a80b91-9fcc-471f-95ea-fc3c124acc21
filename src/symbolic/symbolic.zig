/// Symbolic Module - Main API
///
/// This file provides the complete public API for the symbolic manipulation module.
/// It serves as the single entry point for all symbolic operations.
///
/// Other modules should import this file to access the symbolic functionality:
/// ```zig
/// const symbolic = @import("symbolic");
/// ```
///
/// Example usage:
///
/// ```zig
/// const symbolic = @import("symbolic");
///
/// // Create a context
/// const ctx = try symbolic.context(allocator);
/// defer symbolic.destroyContext(ctx);
///
/// // Create expressions and perform operations
/// const x = try symbolic.symbol(ctx, "x");
/// const one = try symbolic.integer(ctx, 1);
/// const expr = try symbolic.add(ctx, x, one);
/// const simplified = try symbolic.simplify(ctx, expr);
/// ```
///
/// This module implements the specifications from symbolic/README.md, including:
/// 1. Arena-based allocation with explicit ownership and clear deallocation patterns
/// 2. Immutable directed acyclic graphs (DAGs) for safe expression sharing
/// 3. Context-based resource management with unified cleanup and explicit lifetimes
/// 4. Sum-type representation for expressions with safe direct initialization
/// 5. Direct error propagation with precise error unions
const std = @import("std");

// Import common types shared with the shape module
const common_types = @import("common_types");

// Import types and error modules
const types_mod = @import("types.zig");
const errors_mod = @import("errors.zig");
const validate_mod = @import("validate.zig");

// Import context module for initialization
const init_mod = @import("context/init.zig");
const factory = @import("context/factory.zig");
const context_utils = @import("context/utils.zig");

// Import options module to get settings from build.zig
const options_mod = @import("options.zig");

// Set FFI flag based on build options
pub const enable_ffi = options_mod.symbolic_enable_ffi;

// Set zlib flag for tests
pub const enable_zlib = options_mod.enable_zlib;

//------------------------------------------------------------------------------
// Validation Exports
//------------------------------------------------------------------------------

// Re-export additional validation functions for cross-module use
pub const isValidPointer = validate_mod.isValidPointer;
pub const sentinel = validate_mod.createSentinel;

// Export validation functions with clear names
pub const validateExprBasic = validate_mod.validateExprBasic;
pub const validateExprSafe = validate_mod.validateExprSafe;
pub const validateContextSafe = validate_mod.validateContextSafe;
pub const validateContextAndExpr = validate_mod.validateContextAndExpr;
pub const validateContext = validate_mod.validateContext;
pub const validateExpr = validate_mod.validateExpr;
pub const validateExprRecursive = validate_mod.validateExprRecursive;
pub const validateAllExprs = validate_mod.validateAllExprs;
pub const forEachNode = validate_mod.forEachNode;

// Export basic helper constants
pub const MIN_VALID_PTR = types_mod.MIN_VALID_PTR;

//------------------------------------------------------------------------------
// Utilities
//------------------------------------------------------------------------------

// Export the uniqueExpr function for hash-consing
pub const uniqueExpr = @import("context/expr.zig").uniqueExpr;

// Export utility functions used by the shape module
pub const nodesEqual = @import("context/binary_utils.zig").nodesEqual;

// Conditionally import Egg integration module
const egg_mod = if (enable_ffi) @import("egraph/egg.zig") else undefined;

/// Check if an expression has a specified constraint (positive, negative, zero, nonzero)
/// Returns an optional boolean - true if the constraint is satisfied, false if not,
/// null if the constraint can't be determined
pub fn hasConstraint(ctx: *SymbolicContext, expr: SymExpr, constraint_type: enum { positive, negative, zero, nonzero }) SymbolicError!?bool {
    // Validate the context and expression
    try validate_mod.validateContextSafe(ctx);
    try validate_mod.validateExprSafe(expr);

    // For constants, we can just check the value directly
    if (expr.tag == .integer) {
        const value = expr.payload.integer;
        return switch (constraint_type) {
            .positive => value > 0,
            .negative => value < 0,
            .zero => value == 0,
            .nonzero => value != 0,
        };
    }

    // For other expressions, try to evaluate if possible
    const value = factory.evaluateToInteger(ctx, expr) catch return null;

    // Now check the constraint
    return switch (constraint_type) {
        .positive => value > 0,
        .negative => value < 0,
        .zero => value == 0,
        .nonzero => value != 0,
    };
}

//------------------------------------------------------------------------------
// Type Exports
//------------------------------------------------------------------------------

/// The main context type for managing symbolic expressions
/// Matches the common interface for cross-module compatibility
pub const SymbolicContext = types_mod.SymbolicContext;

/// The node representation for symbolic expression tree
/// Matches the common interface for cross-module compatibility
pub const Node = types_mod.Node;

/// A reference to a symbolic expression node
/// Matches the common interface for cross-module compatibility
pub const SymExpr = types_mod.SymExpr;

/// An enum representing the type of a symbolic node
pub const NodeTag = types_mod.NodeTag;

/// All possible errors that can occur during symbolic operations
pub const SymbolicError = errors_mod.SymbolicError;

/// A safe wrapper around symbolic expressions with validation and error handling
pub const ExpressionRef = @import("context/expr_ref.zig").ExpressionRef;

/// Error with additional context information
pub const ErrorWithContext = errors_mod.ErrorWithContext;

/// Common map type for symbol-to-value mappings
pub const StringHashMap = std.StringHashMapUnmanaged;

/// Create and initialize a StringHashMap
/// This helper function creates a managed StringHashMap for compatibility with code
/// that expects the managed version
pub fn createStringHashMap(allocator: std.mem.Allocator, comptime V: type) std.StringHashMap(V) {
    return std.StringHashMap(V).init(allocator);
}

/// Re-export constraint types
pub const Constraint = @import("constraint.zig").Constraint;
pub const ConstraintSystem = @import("constraint.zig").ConstraintSystem;
pub const DimPool = @import("constraint.zig").DimPool;
pub const ErrorPolicy = @import("constraint.zig").ErrorPolicy;

/// Options for symbolic context initialization
pub const SymbolicContextOptions = init_mod.SymbolicContextOptions;

//------------------------------------------------------------------------------
// Public API Functions
//------------------------------------------------------------------------------

// Context Management
pub const context = factory.context;
pub const contextWithOptions = factory.contextWithOptions;

/// Creates a new symbolic context
/// Legacy naming for compatibility with existing code
pub fn createContext(allocator: std.mem.Allocator) !*SymbolicContext {
    return context(allocator);
}

pub const destroyContext = init_mod.destroyContext;
pub const reset = factory.reset;
pub const setDebugMode = factory.setDebugMode;

// Expression Creation
pub const integer = factory.integer;
pub const boolean = factory.boolean;
pub const symbol = factory.symbol;

// Arithmetic Operations
pub const add = factory.add;
pub const sub = factory.sub;
pub const mul = factory.mul;
pub const div = factory.div;
pub const mod = factory.mod;

// Comparison Operations
pub const equal = factory.equal;
pub const lessThan = factory.lessThan;
pub const greaterEqual = factory.greaterEqual;
pub const greater = factory.greater;
pub const lessEqual = factory.lessEqual;

// Logical Operations
pub const logicalAnd = factory.logicalAnd;
pub const logicalOr = factory.logicalOr;
pub const logicalNot = factory.logicalNot;

// Min/Max Operations
pub const min = factory.min;
pub const max = factory.max;

// Bitwise Operations
pub const bitAnd = factory.bitAnd;
pub const bitOr = factory.bitOr;
pub const negate = factory.negate;

// Simplification and Evaluation
pub const simplify = factory.simplify;
pub const simplifyWithCse = factory.simplifyWithCse;
pub const eliminateCommonSubexpressions = factory.eliminateCommonSubexpressions;
pub const eval = factory.eval;
pub const evaluateToInteger = factory.evaluateToInteger;
pub const isConstant = factory.isConstant;
pub const constantValue = factory.constantValue;

/// Evaluate a symbolic expression with variable bindings
/// Returns the integer value, or error if evaluation fails
///
/// Args:
///   ctx: The symbolic context
///   expr: The expression to evaluate
///   var_map: Optional pointer to variable bindings map (either mutable or const)
///
/// Returns:
///   The evaluated integer value, or an error
pub fn evaluate(ctx: *SymbolicContext, expr: SymExpr, var_map: ?*const std.StringHashMap(i64)) SymbolicError!i64 {
    try validate_mod.validateContextSafe(ctx);
    try validate_mod.validateExprSafe(expr);

    // Handle null map case
    if (var_map == null) {
        var empty_map = createStringHashMap(ctx.allocator, i64);
        defer empty_map.deinit();
        return factory.eval(ctx, expr, &empty_map);
    }

    // Create a temporary copy since we can't modify const map
    var temp_map = createStringHashMap(ctx.allocator, i64);
    defer temp_map.deinit();

    // Copy entries
    var iter = var_map.?.iterator();
    while (iter.next()) |entry| {
        try temp_map.put(entry.key_ptr.*, entry.value_ptr.*);
    }

    return factory.eval(ctx, expr, &temp_map);
}

// Substitution
pub const substitute = factory.substitute;

// Formatting
pub const format = factory.format;

// Advanced Operations
pub const deepCopyToContext = context_utils.deepCopyToContext;
pub const copyExpression = context_utils.copyExpression;
pub const mergeExpressions = context_utils.mergeExpressions;
pub const exprEquivalent = context_utils.exprEquivalent;
pub const deepClone = context_utils.deepClone;

// Shape Module Integration
pub const build_product = factory.buildProduct;
pub const build_sum = factory.buildSum;
pub const size_as_dim = factory.dimFromSize;
pub const symbolic_dim = factory.symbolicDim;
pub const apply_broadcasting_rules = factory.applyBroadcastingRules;
pub const are_dims_broadcastable = factory.areDimsBroadcastable;

//------------------------------------------------------------------------------
// Error Handling Utilities
//------------------------------------------------------------------------------

/// Convert a SymbolicError to a human-readable string
pub fn errorString(err: SymbolicError) []const u8 {
    return errors_mod.errorMessage(err);
}

/// Create an error with additional context information
pub fn errorWithContext(err: SymbolicError, contextStr: []const u8, allocator: std.mem.Allocator) ![]const u8 {
    return errors_mod.errorWithContext(err, contextStr, allocator);
}

/// Wrap a SymbolicError with context information
pub fn withContext(err: SymbolicError, contextStr: []const u8) ErrorWithContext {
    return errors_mod.withContext(err, contextStr);
}

/// Helper for running operations with error context
pub fn tryWithContext(operation: anytype, contextStr: []const u8) !@TypeOf(operation catch unreachable) {
    return errors_mod.tryWithContext(operation, contextStr);
}

/// Helper for running operations with error context and logging
pub fn tryWithLogging(operation: anytype, contextStr: []const u8) !@TypeOf(operation catch unreachable) {
    return errors_mod.tryWithLogging(operation, contextStr);
}

//------------------------------------------------------------------------------
// Helper Functions
//------------------------------------------------------------------------------

/// Get the left operand of a binary operation
/// Returns null if the expression is not a binary operation or is invalid
pub fn binaryLeft(expr: SymExpr) ?SymExpr {
    // Basic validation - return null for invalid expressions
    if (!validateExpr(expr)) {
        return null;
    }

    // Only proceed if this is actually a binary operation
    if (!isBinaryOperation(expr.tag)) {
        return null;
    }

    return @import("context/helpers.zig").getBinaryLeft(expr);
}

/// Get the right operand of a binary operation
/// Returns null if the expression is not a binary operation or is invalid
pub fn binaryRight(expr: SymExpr) ?SymExpr {
    // Basic validation - return null for invalid expressions
    if (!validateExpr(expr)) {
        return null;
    }

    // Only proceed if this is actually a binary operation
    if (!isBinaryOperation(expr.tag)) {
        return null;
    }

    return @import("context/helpers.zig").getBinaryRight(expr);
}

/// Get the operand of a unary operation
/// Returns null if the expression is not a unary operation or is invalid
pub fn unaryOperand(expr: SymExpr) ?SymExpr {
    // Basic validation - return null for invalid expressions
    if (!validateExpr(expr)) {
        return null;
    }

    // Only proceed if this is actually a unary operation
    if (!isUnaryOperation(expr.tag)) {
        return null;
    }

    return @import("context/helpers.zig").getUnaryOperand(expr);
}

/// Check if a tag represents a binary operation
fn isBinaryOperation(tag: NodeTag) bool {
    return switch (tag) {
        .add, .sub, .mul, .div, .mod, .min, .max, .LessThan, .GreaterEqual, .equal, .LogicalAnd, .LogicalOr => true,
        else => false,
    };
}

/// Check if a tag represents a unary operation
fn isUnaryOperation(tag: NodeTag) bool {
    return switch (tag) {
        .LogicalNot => true,
        else => false,
    };
}

//------------------------------------------------------------------------------
// FFI Integration (conditionally compiled)
//------------------------------------------------------------------------------

/// Re-export simplified FFI interface (conditionally)
pub const Expr = if (enable_ffi) egg_mod.ffi.Expr else void;
pub const FFINode = if (enable_ffi) egg_mod.ffi.FFINode else void;
pub const FFISymbol = if (enable_ffi) egg_mod.ffi.FFISymbol else void;
pub const ExprError = if (enable_ffi) egg_mod.ffi.ExprError else void;
pub const INVALID_IDX = if (enable_ffi) egg_mod.ffi.INVALID_IDX else std.math.maxInt(u32);

/// Helper function to cast from internal node type to common interface node type
pub fn toCommonNode(node: *const Node) *const common_types.Node {
    return @as(*const common_types.Node, @ptrCast(@alignCast(node)));
}

/// Helper function to cast from common interface node type to internal node type
pub fn fromCommonNode(node: *const common_types.Node) *const Node {
    return @as(*const Node, @ptrCast(@alignCast(node)));
}

/// Helper function to cast from internal context type to common interface context type
pub fn toCommonContext(ctx: *SymbolicContext) *common_types.SymbolicContext {
    return @as(*common_types.SymbolicContext, @ptrCast(@alignCast(ctx)));
}

/// Helper function to cast from common interface context type to internal context type
pub fn fromCommonContext(ctx: *common_types.SymbolicContext) *SymbolicContext {
    return @as(*SymbolicContext, @ptrCast(@alignCast(ctx)));
}

/// Re-export the simplifyWithEgg function from egraph_main.zig for compatibility
/// Simplify an expression using the Egg-based implementation (via FFI)
///
/// NOTE: This is primarily used for testing and verification purposes.
/// The native implementation (simplify) is the preferred approach for
/// production code. This function will fall back to the native implementation
/// if FFI is disabled.
pub const simplifyWithEgg = if (enable_ffi)
    egg_mod.simplifyWithEgg
else
    simplify;

/// Helper function to compare results of native and FFI implementations
/// Returns true if both implementations produce the same result
pub fn compareNativeAndFfi(ctx: *SymbolicContext, expr: SymExpr, debug_enabled: bool) !bool {
    if (!enable_ffi) {
        // FFI not enabled, can't compare
        if (debug_enabled) {
            std.debug.print("FFI not enabled, skipping comparison\n", .{});
        }
        return true;
    }

    if (debug_enabled) {
        std.debug.print("Comparing Native and FFI implementations for {any}\n", .{expr});
        std.debug.print("  Running Native...\n", .{});
    }

    // Native Path
    const native_result = try simplify(ctx, expr);

    if (debug_enabled) {
        std.debug.print("  Native finished. Result: {any}\n", .{native_result});
        std.debug.print("  Running FFI...\n", .{});
    }

    // FFI Path
    const ffi_result = try simplifyWithEgg(ctx, expr);

    if (debug_enabled) {
        std.debug.print("  FFI Result: {any}\n", .{ffi_result});
        std.debug.print("  Comparing results...\n", .{});
    }

    // Comparison
    const are_equal = nodesEqual(ctx, native_result, ffi_result);

    if (debug_enabled) {
        std.debug.print("  Comparison finished. Equal: {any}\n", .{are_equal});
    }

    if (!are_equal) {
        // Always print discrepancies, even in non-debug mode
        std.debug.print("\nDiscrepancy between Native and FFI implementations:\n", .{});
        std.debug.print("Input:         {any}\n", .{expr});
        std.debug.print("Native result: {any}\n", .{native_result});
        std.debug.print("FFI result:    {any}\n", .{ffi_result});
    }

    return are_equal;
}

/// Verifies that the native Zig implementation matches egg's results.
/// This is used to ensure our native implementation is correct.
pub fn verifyNativeImplementation(
    ctx: *SymbolicContext,
    expr: SymExpr,
    comptime operation: enum { simplify, substitute },
    args: anytype,
) !bool {
    // If FFI is not enabled, just return true without attempting any FFI operations
    if (!enable_ffi) {
        return true;
    }

    // First get the native result
    const native_result = switch (operation) {
        .simplify => try simplify(ctx, expr),
        .substitute => try substitute(ctx, expr, args[0], args[1]),
    };

    // For substitute operations, return early since there's no egg implementation
    if (operation == .substitute) {
        return true;
    }

    // Only call FFI functions if enabled - using direct function approach
    const egg_result = try simplifyWithEgg(ctx, expr);

    // Check if both implementations produce equivalent results
    return nodesEqual(ctx, native_result, egg_result);
}

//------------------------------------------------------------------------------
// Testing
//------------------------------------------------------------------------------

// Import core tests from their proper locations
test "symbolic:modules" {
    // Import all core modules
    _ = @import("context/init.zig");
    _ = @import("context/expr.zig");
    _ = @import("context/eval.zig");
    _ = @import("context/simplify.zig");
    _ = @import("context/substitute.zig");
    _ = @import("context/format.zig");
    _ = @import("context/cse.zig");
    _ = @import("context/helpers.zig");
    _ = @import("context/utils.zig");

    // Test the factory module itself
    _ = @import("context/factory.zig");

    // Explicit imports of other module tests
    _ = @import("errors.zig");
    _ = @import("types.zig");
}

test "symbolic:context" {
    const testing = std.testing;
    const ctx = try context(testing.allocator);
    defer destroyContext(ctx);
    // A pointer to a struct should never be null in Zig
    // We're just testing that we got a valid context here
    try testing.expect(@TypeOf(ctx) == *SymbolicContext);
}

test "symbolic:basicOperations" {
    const testing = std.testing;
    const ctx = try context(testing.allocator);
    defer destroyContext(ctx);

    const x = try symbol(ctx, "x");
    const zero = try integer(ctx, 0);
    const x_plus_zero = try add(ctx, x, zero);
    const simplified = try simplify(ctx, x_plus_zero);

    try testing.expect(nodesEqual(ctx, simplified, x));
}

// This is only used for the standalone test executable
pub fn main() !void {
    std.debug.print("\n=== Symbolic Testing ===\n", .{});

    // Create a context and do some basic testing
    var arena = std.heap.ArenaAllocator.init(std.heap.page_allocator);
    defer arena.deinit();
    const allocator = arena.allocator();

    const ctx = try context(allocator);
    defer destroyContext(ctx);

    // Enable debug mode for verbose output
    setDebugMode(ctx, true);

    // Create some symbolic expressions
    const x = try symbol(ctx, "x");
    const zero = try integer(ctx, 0);

    // Test x + 0 = x identity
    const x_plus_zero = try add(ctx, x, zero);
    const simplified = try simplify(ctx, x_plus_zero);

    const equals_x = nodesEqual(ctx, simplified, x);
    std.debug.print("x + 0 simplifies to x: {}\n", .{equals_x});

    if (!equals_x) {
        std.debug.print("FAILED: x + 0 did not simplify to x\n", .{});
        return error.TestFailed;
    }

    std.debug.print("=== Native symbolic engine working correctly ===\n", .{});
}
