# Compiler and Backend Implementation Plan

This is the definitive design document for implementing <PERSON><PERSON>'s compiler and backend system. All design decisions are finalized here.

## Final Architecture Decisions

1. **Compiler location**: `src/core/compiler/` (inside Core)
   - Needs deep integration with Graph, Shape, and Symbolic engines
   - Part of the graph transformation pipeline
   
2. **Backend location**: `src/backends/` (same level as Core)
   - Clean separation between graph building and execution
   - Allows independent backend development
   
3. **Everything is a compiler pass**: Unified abstraction
   - Autodiff is a compiler pass
   - Optimizations are compiler passes
   - Pattern matching is a compiler pass
   
4. **DSL-driven design**: Declarative approaches
   - Pattern matching DSL for optimizations
   - Gradient rules table for autodiff

## Key Innovation: Simple Yet Powerful Pattern Matching

Following Luminal's proven approach, we implement a pragmatic pattern matching system that focuses on what actually delivers performance:

1. **Simple Pattern DSL**: Define patterns as data, not hand-coded logic
2. **Greedy Fusion**: Aggressively fuse elementwise operations to minimize memory traffic
3. **Runtime Kernel Generation**: Generate optimal kernels for fused operations
4. **Decomposition First**: High-level ops → primitives → patterns → optimized kernels

We avoid over-engineering by focusing only on patterns that matter:
- Matrix multiplication: `Mul->ReduceSum` → `MatMul`
- Elementwise chains: `Add->ReLU->Mul` → single fused kernel
- Common patterns: LayerNorm, Attention, Convolution

The real performance comes from:
- **Eliminating memory roundtrips** through fusion
- **Runtime-generated kernels** that do exactly what's needed
- **Simple pattern matching** that's fast and debuggable

## Why This Approach Works

Luminal's success shows that you don't need complex pattern matching algorithms. What you need is:

1. **Control the decomposition**: When you control how operations decompose into primitives, simple patterns emerge naturally
2. **Focus on memory bandwidth**: The biggest wins come from fusing elementwise operations to avoid memory traffic
3. **Runtime compilation**: Generate exactly the kernel you need for each specific fusion pattern
4. **Keep it simple**: A linear scan with function-based patterns is fast enough and easy to debug

Our pattern system is just 100 lines of code vs thousands for advanced approaches, yet achieves the same performance gains.

## Benefits of the Minimal DSL

The lightweight DSL approach provides the best of both worlds:

**Advantages over pure function-based:**
- **Declarative patterns**: See the pattern structure at a glance
- **Less boilerplate**: ~50% less code per pattern
- **Reusable logic**: Common constraints like `output_used_by` are built-in
- **Easier to add patterns**: Just specify ops and constraints
- **Type safety**: Compile-time checking of pattern structure

**Advantages over complex DSLs:**
- **Still simple**: The entire matcher is ~200 lines
- **Debuggable**: Step through pattern matching with a debugger
- **Flexible**: Fall back to custom functions when needed
- **No magic**: Clear execution flow, no hidden behavior
- **Fast compilation**: No complex pattern compilation step

**Example comparison:**
```zig
// Old function-based approach: ~50 lines for matmul pattern
fn matchMatMul(plan: *ExecutionPlan, start_idx: usize) ?PatternMatch { ... }
fn applyMatMul(engine: *CompilerEngine, plan: *ExecutionPlan, match: PatternMatch) !void { ... }

// New DSL approach: ~15 lines for the same pattern
pub const matmul = Pattern{
    .ops = &.{
        OpPattern{ .op_type = .multiply, .name = "mul", .constraints = &.{ .{ .rank = 3 } } },
        OpPattern{ .op_type = .reduce_sum, .name = "reduce", .constraints = &.{ .{ .reduce_axis = 2 } } },
    },
    .transform = .{ .replace_with = .{ .op_type = .matmul, .inputs = &.{ "mul.input[0]", "mul.input[1]" } } },
};
```

## Core Principles

1. **Integration First**: Compiler is part of Core, not a separate system
2. **Leverage Existing Infrastructure**: Reuse operators, memory pools, and engines
3. **Type-Safe IDs**: Use existing NodeId, ViewId system throughout
4. **Explicit Ownership**: Clear memory ownership with arena allocators
5. **Performance by Design**: Structure for cache efficiency and parallelism

## Architecture Overview

```
src/
├── core/                    # Core graph building infrastructure
│   ├── GraphEngine         # Existing
│   ├── ShapeEngine         # Existing  
│   ├── SymbolicEngine      # Existing
│   ├── DataStore           # Existing
│   └── compiler/           # New compiler module
│       └── CompilerEngine
│
├── backends/               # Execution backends (same level as core)
│   ├── cpu/               # Immediate
│   ├── cuda/              # Future
│   └── webgpu/            # Future
│
├── tensor/                # High-level tensor operations
└── nn/                    # Neural network modules
```

## Compiler Type Definitions

These types will be defined in the compiler module, not in core/types.zig:

```zig
// In src/core/compiler/types.zig

const std = @import("std");
const graph = @import("../graph/mod.zig");
const parent_types = @import("../types.zig");

const NodeId = parent_types.NodeId;
const DataType = parent_types.DataType;

/// Device where computations run
pub const Device = enum {
    cpu,
    cuda,
    metal,
    vulkan,
};

/// Backend capability flags for feature detection
pub const BackendCapability = packed struct {
    float16: bool = false,
    float64: bool = true,
    int8: bool = false,
    int16: bool = false,
    int32: bool = true,
    int64: bool = true,
    
    // Execution capabilities
    async_execution: bool = false,
    graph_optimization: bool = true,
    kernel_fusion: bool = false,
    memory_pooling: bool = true,
    
    // Advanced features
    custom_ops: bool = false,
    distributed: bool = false,
};

/// Execution plan generated by compiler
pub const ExecutionPlan = struct {
    /// Operations in execution order
    operations: []const PlannedOp,
    /// Memory allocation plan
    memory_plan: MemoryPlan,
    /// Fusion opportunities identified
    fusion_groups: []const FusionGroup,
    /// Backend-specific optimizations applied
    backend_hints: BackendHints,
};

pub const PlannedOp = struct {
    node_id: NodeId,
    op_type: graph.types.OpType,
    inputs: []const NodeId,
    output: NodeId,
    /// Memory locations for inputs/outputs
    input_buffers: []const BufferId,
    output_buffer: BufferId,
    /// Fused with next operations
    fused: bool = false,
};

pub const BufferId = enum(u32) { _ };

pub const MemoryPlan = struct {
    /// Total memory required
    total_bytes: usize,
    /// Buffer allocations
    buffers: []const BufferAllocation,
    /// Reuse opportunities
    reuse_map: std.AutoHashMapUnmanaged(BufferId, BufferId) = .{},
};

pub const BufferAllocation = struct {
    id: BufferId,
    size: usize,
    alignment: usize,
    lifetime_start: usize, // Operation index
    lifetime_end: usize,   // Operation index
};

pub const FusionGroup = struct {
    operations: []const NodeId,
    pattern: FusionPattern,
};

pub const FusionPattern = enum {
    pointwise_chain,    // add->relu->mul etc
    matmul_bias_activation,
    conv_bn_relu,
    attention_pattern,
};

pub const BackendHints = struct {
    preferred_tile_size: ?usize = null,
    vectorization_width: ?usize = null,
    cache_blocking: ?CacheBlocking = null,
};

pub const CacheBlocking = struct {
    l1_tile: usize,
    l2_tile: usize,
    l3_tile: usize,
};

/// Compiled graph ready for execution
pub const CompiledGraph = struct {
    /// Unique ID for caching
    id: CompiledGraphId,
    /// The execution plan
    plan: ExecutionPlan,
    /// Backend-specific compiled artifacts
    artifacts: *anyopaque,
    /// Stats for profiling
    stats: CompilationStats,
    
    pub const CompiledGraphId = enum(u32) { _ };
};

pub const CompilationStats = struct {
    compile_time_ns: u64,
    memory_footprint: usize,
    operation_count: usize,
    fusion_count: usize,
};

/// Runtime tensor data passed to backend execution
pub const TensorData = struct {
    /// Pointer to the data buffer
    data: *anyopaque,
    /// Shape of the tensor
    shape: []const i64,
    /// Data type
    dtype: DataType,
    /// Device where data resides
    device: Device,
};

/// The new backend interface
pub const Backend = struct {
    /// Backend name
    name: []const u8,
    
    /// Capabilities of this backend
    capabilities: BackendCapability,
    
    /// Initialize backend with Core reference
    initFn: *const fn (core: *Core) anyerror!*Backend,
    
    /// Compile an execution plan into backend-specific format
    compileFn: *const fn (
        self: *Backend,
        engine: *CompilerEngine,
        plan: ExecutionPlan,
    ) anyerror!CompiledGraph,
    
    /// Execute a compiled graph
    executeFn: *const fn (
        self: *Backend,
        compiled: *CompiledGraph,
        inputs: []const types.TensorData,
    ) anyerror![]types.TensorData,
    
    /// Get memory requirements for a compiled graph
    getMemoryRequirementsFn: *const fn (
        self: *Backend,
        compiled: *CompiledGraph,
    ) usize,
    
    /// Profile execution for optimization feedback
    profileFn: ?*const fn (
        self: *Backend,
        compiled: *CompiledGraph,
    ) anyerror!ProfilingData = null,
    
    /// Cleanup
    deinitFn: *const fn (self: *Backend) void,
    
    /// Implementation data
    impl: *anyopaque,
};

pub const ProfilingData = struct {
    kernel_times: []const KernelProfile,
    memory_bandwidth: f64,
    compute_utilization: f64,
};

pub const KernelProfile = struct {
    name: []const u8,
    time_ns: u64,
    flops: u64,
};
```

## Integrated Compiler Engine

The compiler is now part of Core and leverages existing infrastructure:

```zig
// In src/core/compiler/engine.zig

const std = @import("std");
const types = @import("types.zig");
const parent_types = @import("../types.zig");
const graph = @import("../graph/mod.zig");
const passes = @import("passes/mod.zig");
// Error handling uses direct error literals (idiomatic Zig 0.14)

// Forward declare Core to avoid circular dependency
const Core = @import("../core.zig").Core;

const Allocator = std.mem.Allocator;
const NodeId = parent_types.NodeId;
const PlannedOp = types.PlannedOp;
const ExecutionPlan = types.ExecutionPlan;
const CompiledGraph = types.CompiledGraph;
const CompilerPass = types.CompilerPass;
const ConsumerTracker = types.ConsumerTracker;
const CompilationCache = types.CompilationCache;

pub const CompilerEngine = struct {
    const Self = @This();
    
    /// Reference to parent Core
    core: *Core,
    /// Registered compiler passes (everything is a compiler pass)
    passes: std.ArrayListUnmanaged(CompilerPass) = .{},
    /// Consumer tracking for memory optimization
    consumer_tracker: ConsumerTracker,
    /// Cache of compiled graphs
    cache: CompilationCache,
    
    pub fn init(core: *Core) !Self {
        return .{
            .core = core,
            .consumer_tracker = try ConsumerTracker.init(core.allocator()),
            .cache = CompilationCache.init(core.allocator()),
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.passes.deinit(self.core.allocator());
        self.consumer_tracker.deinit();
        self.cache.deinit();
    }
    
    /// Register standard optimization passes
    pub fn registerStandardPasses(self: *Self) !void {
        const allocator = self.core.allocator();
        
        // Order matters - these build on each other
        try self.passes.append(allocator, CompilerPass.fromOptimization(
            "deadCodeElimination",
            passes.eliminateDeadCode,
        ));
        
        try self.passes.append(allocator, CompilerPass.fromOptimization(
            "commonSubexpressionElimination", 
            passes.eliminateCommonSubexpressions,
        ));
        
        try self.passes.append(allocator, CompilerPass.fromOptimization(
            "operationFusion",
            passes.fuseOperations,
        ));
        
        try self.passes.append(allocator, CompilerPass.fromOptimization(
            "memoryPlanning",
            passes.planMemoryUsage,
        ));
    }
    
    /// Register training-specific passes
    pub fn registerTrainingPasses(self: *Self) !void {
        const allocator = self.core.allocator();
        
        // Gradient checkpointing to reduce memory usage
        try self.passes.append(allocator, CompilerPass.fromOptimization(
            "gradientCheckpointing",
            passes.gradientCheckpointing,
        ));
    }
    
    /// Generic compilation with any compiler pass
    /// This is the core method that makes everything a compiler pass
    pub fn compile(
        self: *Self,
        comptime T: type,
        pass: CompilerPass,
        input: T,
    ) !T {
        // Execute the compiler pass
        const input_ptr = @ptrCast(*anyopaque, @constCast(&input));
        const output_ptr = try pass.compileFn(&pass, self, self.core.graph_engine, input_ptr);
        
        // Return the typed output
        return @ptrCast(*T, @alignCast(@alignOf(T), output_ptr)).*;
    }
    
    /// Compile execution plan for a specific backend
    pub fn compileForBackend(
        self: *Self,
        backend: *Backend,
    ) !CompiledGraph {
        // Check cache first
        const cache_key = try self.computeCacheKey();
        if (self.cache.get(cache_key)) |cached| {
            return cached;
        }
        
        const start_time = std.time.nanoTimestamp();
        
        // Build initial execution plan from graph
        var plan = try self.buildExecutionPlan();
        defer plan.deinit(self.core.allocator());
        
        // Run optimization passes on the plan
        for (self.passes.items) |pass| {
            _ = try self.compile(*ExecutionPlan, pass, &plan);
        }
        
        // Let backend compile the optimized plan
        const compiled = try backend.compileFn(backend, self, plan);
        
        // Update stats
        compiled.stats.compile_time_ns = @intCast(std.time.nanoTimestamp() - start_time);
        
        // Cache the result
        try self.cache.put(cache_key, compiled);
        
        return compiled;
    }
    
    fn buildExecutionPlan(self: *Self) !ExecutionPlan {
        const graph = self.core.graph_engine;
        const allocator = self.core.allocator();
        
        // Get execution order
        const order = try graph.getTopologicalOrder(allocator);
        defer allocator.free(order);
        
        // Track consumers for memory planning
        try self.consumer_tracker.analyze(graph);
        
        // Build planned operations
        var operations = std.ArrayListUnmanaged(PlannedOp){};
        var buffer_id: u32 = 0;
        
        for (order) |node_id| {
            const node = graph.getNode(node_id) orelse continue;
            
            // Skip dead code
            if (self.consumer_tracker.getConsumerCount(node_id) == 0 and
                !graph.isOutput(node_id)) {
                continue;
            }
            
            const planned_op = PlannedOp{
                .node_id = node_id,
                .op_type = node.operator.getType(),
                .inputs = try allocator.dupe(NodeId, node.inputs.items),
                .output = node_id,
                .input_buffers = try self.allocateInputBuffers(node),
                .output_buffer = @enumFromInt(buffer_id),
                .fused = false,
            };
            
            buffer_id += 1;
            try operations.append(allocator, planned_op);
        }
        
        return ExecutionPlan{
            .operations = try operations.toOwnedSlice(allocator),
            .memory_plan = try self.planMemory(operations.items),
            .fusion_groups = &.{}, // Populated by fusion pass
            .backend_hints = .{},   // Populated by backend-specific passes
        };
    }
};

/// Compiler pass that can transform graphs and return outputs
/// Following Luminal's design where everything is a compiler pass
pub const CompilerPass = struct {
    name: []const u8,
    
    /// The actual compilation function - can modify graph and return data
    compileFn: *const fn (
        self: *const CompilerPass,
        engine: *CompilerEngine, 
        graph: *GraphEngine,
        input: *anyopaque,
    ) anyerror!*anyopaque,
    
    /// Optional data for the pass
    data: ?*anyopaque = null,
    
    /// Convenience wrapper for passes that only transform without returning
    pub fn fromOptimization(
        name: []const u8,
        apply: *const fn (engine: *CompilerEngine, plan: *ExecutionPlan) anyerror!bool,
    ) CompilerPass {
        return .{
            .name = name,
            .compileFn = struct {
                fn compile(
                    self: *const CompilerPass,
                    engine: *CompilerEngine,
                    graph: *GraphEngine,
                    input: *anyopaque,
                ) !*anyopaque {
                    _ = self;
                    _ = graph;
                    const plan = @ptrCast(*ExecutionPlan, @alignCast(@alignOf(ExecutionPlan), input));
                    const changed = try apply(engine, plan);
                    return @intToPtr(*anyopaque, @boolToInt(changed));
                }
            }.compile,
        };
    }
};

/// Tracks tensor consumers for memory optimization
pub const ConsumerTracker = struct {
    consumer_counts: std.AutoHashMapUnmanaged(NodeId, u32) = .{},
    last_consumer: std.AutoHashMapUnmanaged(NodeId, NodeId) = .{},
    
    pub fn init(allocator: Allocator) !ConsumerTracker {
        _ = allocator;
        return .{};
    }
    
    pub fn deinit(self: *ConsumerTracker) void {
        self.consumer_counts.deinit();
        self.last_consumer.deinit();
    }
    
    pub fn analyze(self: *ConsumerTracker, graph: *graph.GraphEngine) !void {
        // Implementation details...
    }
};

/// Cache compiled graphs
pub const CompilationCache = struct {
    cache: std.StringHashMapUnmanaged(CompiledGraph) = .{},
    allocator: Allocator,
    
    pub fn init(allocator: Allocator) CompilationCache {
        return .{ .allocator = allocator };
    }
    
    pub fn deinit(self: *CompilationCache) void {
        self.cache.deinit(self.allocator);
    }
};
```

## Simple Pattern Matching System

Following Luminal's proven approach, we implement a focused pattern matching system that delivers performance without complexity:

### Core Pattern Infrastructure

```zig
// In src/core/compiler/pattern.zig

/// Minimal DSL for pattern specification
pub const Pattern = struct {
    /// Name for debugging
    name: []const u8,
    /// Sequence of operations to match
    ops: []const OpPattern,
    /// Transformation to apply
    transform: Transform,
    /// Priority (higher = applied first)
    priority: i32 = 0,
};

/// Pattern for a single operation
pub const OpPattern = struct {
    /// Operation type to match (null = any)
    op_type: ?graph.types.OpType = null,
    /// Name to bind this operation to
    name: []const u8,
    /// Constraints to check
    constraints: []const Constraint = &.{},
};

/// Simple constraints that cover 90% of use cases
pub const Constraint = union(enum) {
    /// Output flows to another operation
    output_used_by: []const u8,
    /// Has specific rank
    rank: usize,
    /// Reduce on specific axis
    reduce_axis: i64,
    /// Custom constraint function for complex cases
    custom: *const fn (op: PlannedOp, plan: *ExecutionPlan) bool,
};

/// Transformation to apply when pattern matches
pub const Transform = union(enum) {
    /// Replace with single operation
    replace_with: struct {
        op_type: graph.types.OpType,
        /// Map input names to new operation inputs
        inputs: []const []const u8,
    },
    /// Mark operations for fusion
    fuse: struct {
        /// Operations to fuse (by name)
        ops: []const []const u8,
    },
    /// Custom transformation for complex cases
    custom: *const fn (match: PatternMatch, engine: *CompilerEngine, plan: *ExecutionPlan) anyerror!void,
};

pub const PatternMatch = struct {
    pattern: *const Pattern,
    start_idx: usize,
    end_idx: usize,
    /// Bindings from pattern names to operation indices
    bindings: std.StringHashMapUnmanaged(usize),
};

/// Simple pattern matcher - no over-engineering
pub const PatternMatcher = struct {
    patterns: []const Pattern,
    
    pub fn init(patterns: []const Pattern) PatternMatcher {
        // Sort patterns by priority
        std.sort.sort(Pattern, patterns, {}, comparePriority);
        return .{ .patterns = patterns };
    }
    
    pub fn apply(self: *PatternMatcher, engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
        var changed = false;
        
        // Simple linear scan - good enough for our use case
        var i: usize = 0;
        while (i < plan.operations.len) {
            var matched = false;
            
            // Try patterns in priority order
            for (self.patterns) |*pattern| {
                if (try self.matchPattern(pattern, plan, i)) |match| {
                    try self.applyTransform(pattern, match, engine, plan);
                    i = match.end_idx; // Skip matched operations
                    matched = true;
                    changed = true;
                    break;
                }
            }
            
            if (!matched) i += 1;
        }
        
        return changed;
    }
    
    fn matchPattern(self: *PatternMatcher, pattern: *const Pattern, plan: *ExecutionPlan, start: usize) !?PatternMatch {
        _ = self;
        if (start + pattern.ops.len > plan.operations.len) return null;
        
        var match = PatternMatch{
            .pattern = pattern,
            .start_idx = start,
            .end_idx = start,
            .bindings = std.StringHashMapUnmanaged(usize){},
        };
        
        // Try to match each operation in the pattern
        for (pattern.ops, 0..) |op_pattern, offset| {
            const op_idx = start + offset;
            const op = plan.operations[op_idx];
            
            // Check operation type
            if (op_pattern.op_type) |expected_type| {
                if (op.op_type != expected_type) return null;
            }
            
            // Check constraints
            for (op_pattern.constraints) |constraint| {
                if (!self.checkConstraint(constraint, op, plan, &match.bindings)) return null;
            }
            
            // Add binding
            try match.bindings.put(plan.allocator, op_pattern.name, op_idx);
            match.end_idx = op_idx + 1;
        }
        
        return match;
    }
    
    fn checkConstraint(self: *PatternMatcher, constraint: Constraint, op: PlannedOp, plan: *ExecutionPlan, bindings: *std.StringHashMapUnmanaged(usize)) bool {
        _ = self;
        switch (constraint) {
            .output_used_by => |name| {
                // Check if this op's output is used by the named operation
                if (bindings.get(name)) |other_idx| {
                    const other_op = plan.operations[other_idx];
                    for (other_op.inputs) |input| {
                        if (input.eql(op.output)) return true;
                    }
                }
                return false;
            },
            .rank => |expected_rank| {
                const shape = plan.getShape(op.output);
                return shape.len == expected_rank;
            },
            .reduce_axis => |axis| {
                if (op.op_type != .reduce_sum) return false;
                return op.attributes.axis == axis;
            },
            .custom => |check_fn| {
                return check_fn(op, plan);
            },
        }
    }
    
    fn applyTransform(self: *PatternMatcher, pattern: *const Pattern, match: PatternMatch, engine: *CompilerEngine, plan: *ExecutionPlan) !void {
        _ = self;
        switch (pattern.transform) {
            .replace_with => |replacement| {
                // Simple single-operation replacement
                const first_idx = match.bindings.get(pattern.ops[0].name).?;
                
                // Gather inputs based on the mapping
                var inputs = std.ArrayListUnmanaged(NodeId){};
                for (replacement.inputs) |input_spec| {
                    // Parse "op_name.input[N]" or just "op_name"
                    if (std.mem.indexOf(u8, input_spec, ".input[")) |dot_pos| {
                        const op_name = input_spec[0..dot_pos];
                        const idx_str = input_spec[dot_pos + 7 .. input_spec.len - 1];
                        const input_idx = try std.fmt.parseInt(usize, idx_str, 10);
                        
                        const op_idx = match.bindings.get(op_name).?;
                        try inputs.append(engine.core.allocator(), plan.operations[op_idx].inputs[input_idx]);
                    } else {
                        // Use the operation's output
                        const op_idx = match.bindings.get(input_spec).?;
                        try inputs.append(engine.core.allocator(), plan.operations[op_idx].output);
                    }
                }
                
                // Replace first operation
                plan.operations[first_idx] = PlannedOp{
                    .node_id = plan.operations[first_idx].node_id,
                    .op_type = replacement.op_type,
                    .inputs = try inputs.toOwnedSlice(),
                    .output = plan.operations[match.end_idx - 1].output,
                    // ... other fields
                };
                
                // Mark others as noop
                for (first_idx + 1..match.end_idx) |i| {
                    plan.operations[i].op_type = .noop;
                }
            },
            .fuse => |fusion| {
                // Mark operations for fusion
                _ = fusion;
                // Implementation for fusion
            },
            .custom => |transform_fn| {
                try transform_fn(match, engine, plan);
            },
        }
    }
};

/// Common patterns that actually matter for performance
pub const Patterns = struct {
    /// Matrix multiplication: Mul -> ReduceSum pattern
    /// Note: matmul is NOT a primitive - this shows how patterns create higher-level ops
    pub const matmul = Pattern{
        .name = "matmul_2d",
        .ops = &.{
            OpPattern{ 
                .op_type = .multiply, 
                .name = "mul",
                .constraints = &.{ .{ .rank = 3 } },
            },
            OpPattern{ 
                .op_type = .reduce_sum, 
                .name = "reduce",
                .constraints = &.{
                    .{ .output_used_by = "mul" },
                    .{ .reduce_axis = 2 },
                },
            },
        },
        .transform = .{
            .replace_with = .{
                .op_type = .fused_matmul,  // A backend-specific fused operation
                .inputs = &.{ "mul.input[0]", "mul.input[1]" },
            },
        },
        .priority = 100,
    };
    
    /// Elementwise fusion pattern - still needs custom logic due to variable length
    pub const elementwise_fusion = Pattern{
        .name = "elementwise_fusion",
        .ops = &.{
            OpPattern{ 
                .name = "first",
                .constraints = &.{ .{ .custom = isElementwiseOp } },
            },
        },
        .transform = .{ .custom = fuseElementwiseChain },
        .priority = 90,
    };
    
    fn isElementwiseOp(op: PlannedOp, plan: *ExecutionPlan) bool {
        _ = plan;
        return isElementwise(op.op_type);
    }
    
    fn fuseElementwiseChain(match: PatternMatch, engine: *CompilerEngine, plan: *ExecutionPlan) !void {
        // This still needs custom logic because the chain length is variable
        const start_idx = match.start_idx;
        
        // Find full chain
        var end_idx = start_idx + 1;
        while (end_idx < plan.operations.len) {
            const op = plan.operations[end_idx];
            
            if (!isElementwise(op.op_type)) break;
            if (!op.inputs[0].eql(plan.operations[end_idx - 1].output)) break;
            
            // Check no external consumers of intermediate values
            if (hasExternalConsumers(plan, end_idx - 1, start_idx, end_idx)) break;
            
            end_idx += 1;
        }
        
        // Only fuse if we have multiple operations
        if (end_idx - start_idx < 2) return null;
        
        return PatternMatch{
            .pattern = &elementwise_fusion,
            .start_idx = start_idx,
            .end_idx = end_idx,
        };
    }
    
    fn applyElementwiseFusion(engine: *CompilerEngine, plan: *ExecutionPlan, match: PatternMatch) !void {
        const ops = plan.operations[match.start_idx..match.end_idx];
        
        // Collect operations to fuse
        var fused_ops = try engine.core.allocator().alloc(ElementwiseOp, ops.len);
        for (ops, 0..) |op, i| {
            fused_ops[i] = .{
                .op_type = op.op_type,
                .attributes = op.attributes,
            };
        }
        
        // Replace first op with fused version
        plan.operations[match.start_idx] = PlannedOp{
            .node_id = ops[0].node_id,
            .op_type = .fused_elementwise,
            .inputs = ops[0].inputs,
            .output = ops[ops.len - 1].output,
            .input_buffers = ops[0].input_buffers,
            .output_buffer = ops[ops.len - 1].output_buffer,
            .data = .{ .fused_ops = fused_ops },
        };
        
        // Mark others as noop
        for (match.start_idx + 1..match.end_idx) |i| {
            plan.operations[i].op_type = .noop;
        }
    }
    
    fn isElementwise(op_type: graph.types.OpType) bool {
        return switch (op_type) {
            // Core primitives that are elementwise
            .add, .multiply, .mod,
            .reciprocal, .sqrt, .sin, .log2, .exp2,
            .less_than,  // Comparison operations are elementwise
            // Note: higher-level ops like relu, sigmoid would be decomposed to primitives
            else => false,
        };
    }
    
    /// Softmax pattern - shows multi-op matching with primitives
    /// Softmax = exp(x - max) / sum(exp(x - max))
    pub const softmax = Pattern{
        .name = "softmax",
        .ops = &.{
            OpPattern{ .op_type = .reduce_max, .name = "max" },
            OpPattern{ 
                .op_type = .add,  // subtract is decomposed to add + neg
                .name = "centered",
                .constraints = &.{ .{ .custom = isNegatedSecondInput } },
            },
            OpPattern{ 
                .op_type = .exp2,  // exp is decomposed to exp2
                .name = "exp_vals",
                .constraints = &.{ .{ .output_used_by = "centered" } },
            },
            OpPattern{ 
                .op_type = .reduce_sum, 
                .name = "sum_exp",
                .constraints = &.{ .{ .output_used_by = "exp_vals" } },
            },
            OpPattern{ 
                .op_type = .multiply, 
                .name = "normalized",
                .constraints = &.{ 
                    .{ .output_used_by = "exp_vals" },
                    .{ .custom = isReciprocal },  // div is mul by reciprocal
                },
            },
        },
        .transform = .{
            .replace_with = .{
                .op_type = .fused_softmax,  // Backend-specific fused operation
                .inputs = &.{ "max.input[0]" },
            },
        },
        .priority = 95,
    };
    
    fn isNegatedSecondInput(op: PlannedOp, plan: *ExecutionPlan) bool {
        _ = plan;
        // Check if second input is negated (multiplied by -1)
        return op.attributes.negated_input == 1;
    }
    
    fn isReciprocal(op: PlannedOp, plan: *ExecutionPlan) bool {
        _ = plan;
        // Check if this is multiplication by reciprocal (division pattern)
        return op.attributes.is_reciprocal_mul;
    }
    
    /// Batch matmul pattern - shows how to extend for other patterns
    pub const batch_matmul = Pattern{
        .name = "batch_matmul",
        .ops = &.{
            OpPattern{ 
                .op_type = .multiply, 
                .name = "mul",
                .constraints = &.{ .{ .rank = 4 } }, // Batch dimension
            },
            OpPattern{ 
                .op_type = .reduce_sum, 
                .name = "reduce",
                .constraints = &.{
                    .{ .output_used_by = "mul" },
                    .{ .reduce_axis = 3 }, // Last axis for batch
                },
            },
        },
        .transform = .{
            .replace_with = .{
                .op_type = .batch_matmul,
                .inputs = &.{ "mul.input[0]", "mul.input[1]" },
            },
        },
        .priority = 105,
    };
};

/// Elementwise fusion data
pub const ElementwiseOp = struct {
    op_type: graph.types.OpType,
    attributes: Attributes,
};
```

### Pattern-Based Compiler Pass

```zig
// In src/core/compiler/passes/pattern.zig

pub fn applyPatternMatching(engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
    // Create pattern matcher with essential patterns
    const patterns = [_]Pattern{
        Patterns.matmul,
        Patterns.batch_matmul,
        Patterns.softmax,
        Patterns.elementwise_fusion, // Should be last due to greedy matching
    };
    
    var matcher = PatternMatcher.init(&patterns);
    return try matcher.apply(engine, plan);
}
```

### Integration with Backend Compilers

The simple pattern matching system integrates cleanly with backend-specific compilers:

```zig
// In src/core/backends/cpu/backend.zig

pub const CpuBackend = struct {
    core: *Core,
    
    pub fn init(core: *Core) !*Backend {
        const self = try core.allocator().create(CpuBackend);
        self.* = .{ .core = core };
        
        const backend = try core.allocator().create(Backend);
        backend.* = .{
            .name = "cpu",
            .capabilities = .{
                .float64 = true,
                .int32 = true,
                .int64 = true,
                .graph_optimization = true,
                .memory_pooling = true,
            },
            .initFn = init,
            .compileFn = compile,
            .executeFn = execute,
            .getMemoryRequirementsFn = getMemoryRequirements,
            .deinitFn = deinit,
            .impl = self,
        };
        
        return backend;
    }
    
    fn compile(
        backend: *Backend,
        engine: *CompilerEngine,
        plan: ExecutionPlan,
    ) !CompiledGraph {
        const self = @ptrCast(*CpuBackend, @alignCast(@alignOf(CpuBackend), backend.impl));
        _ = self;
        
        // Run pattern matching as a compiler pass
        _ = try applyPatternMatching(engine, &plan);
        
        // Additional CPU-specific optimizations
        try optimizeForCpu(&plan);
        
        // Generate CPU kernels
        return generateCpuKernels(plan);
    }
};

```

### CPU Backend Execution

```zig
// In src/core/backends/cpu/backend.zig

pub const CpuBackend = struct {
    core: *Core,
    
    pub fn init(core: *Core) !*Backend {
        const self = try core.allocator().create(CpuBackend);
        self.* = .{ .core = core };
        
        const backend = try core.allocator().create(Backend);
        backend.* = .{
            .name = "cpu",
            .capabilities = .{
                .float64 = true,
                .int32 = true,
                .int64 = true,
            },
            .initFn = initBackend,
            .compileFn = compile,
            .executeFn = execute,
            .deinitFn = deinit,
            .impl = self,
        };
        
        return backend;
    }
    
    fn compile(
        backend: *Backend,
        engine: *CompilerEngine,
        plan: ExecutionPlan,
    ) !CompiledGraph {
        // Run CPU-specific compiler passes
        var cpu_plan = plan;
        
        inline for (CpuCompiler.Passes) |Pass| {
            _ = try Pass.compile(engine, &cpu_plan);
        }
        
        // Convert to CPU operations
        const ops = try convertToCpuOps(engine.core.allocator(), cpu_plan);
        
        return CompiledGraph{
            .plan = cpu_plan,
            .artifacts = ops,
            .stats = .{},
        };
    }
};

/// Specialized CPU operations (following Luminal's approach)
pub const CpuOp = union(enum) {
    matmul: MatMul2D,
    fused_unary: FusedUnary,
    binary: BinaryOp,
    copy: Copy,
};

pub const MatMul2D = struct {
    pub fn execute(inputs: []const Tensor, output: *Tensor) !void {
        const a = inputs[0].data.asSlice(f32);
        const b = inputs[1].data.asSlice(f32);
        const c = output.data.asSliceMut(f32);
        
        const m = inputs[0].shape[0];
        const k = inputs[0].shape[1]; 
        const n = inputs[1].shape[1];
        
        // Use optimized BLAS-like operation
        // In Zig, we can use @Vector for small sizes or call BLAS
        if (m * n < 1024) {
            // Small matrices - use vectorized loops
            vectorizedMatMul(a, b, c, m, k, n);
        } else {
            // Large matrices - use cache-friendly blocking
            blockedMatMul(a, b, c, m, k, n);
        }
    }
};

pub const FusedUnary = struct {
    ops: []const UnaryFn,
    
    pub fn execute(input: Tensor, output: *Tensor) !void {
        const in_data = input.data.asSlice(f32);
        const out_data = output.data.asSliceMut(f32);
        
        // Apply all operations in sequence without intermediate writes
        const vec_size = 8;
        var i: usize = 0;
        
        // Vectorized loop
        while (i + vec_size <= in_data.len) : (i += vec_size) {
            var vec: @Vector(vec_size, f32) = in_data[i..][0..vec_size].*;
            
            for (self.ops) |op| {
                vec = op.applyVector(vec);
            }
            
            out_data[i..][0..vec_size].* = vec;
        }
        
        // Scalar cleanup
        while (i < in_data.len) : (i += 1) {
            var val = in_data[i];
            for (self.ops) |op| {
                val = op.applyScalar(val);
            }
            out_data[i] = val;
        }
    }
};

pub const UnaryFn = struct {
    op_type: UnaryOpType,
    
    pub fn applyScalar(self: UnaryFn, x: f32) f32 {
        return switch (self.op_type) {
            .exp2 => @exp2(x),
            .log2 => @log2(x),
            .recip => 1.0 / x,
            .sin => @sin(x),
            .cos => @cos(x),
            .relu => @max(0.0, x),
            .sigmoid => 1.0 / (1.0 + @exp(-x)),
        };
    }
    
    pub fn applyVector(self: UnaryFn, x: @Vector(8, f32)) @Vector(8, f32) {
        return switch (self.op_type) {
            .exp2 => @exp2(x),
            .log2 => @log2(x),
            .recip => @as(@Vector(8, f32), @splat(1.0)) / x,
            .sin => @sin(x),
            .cos => @cos(x),
            .relu => @max(@as(@Vector(8, f32), @splat(0.0)), x),
            .sigmoid => blk: {
                const ones = @as(@Vector(8, f32), @splat(1.0));
                break :blk ones / (ones + @exp(-x));
            },
        };
    }
};
    
    fn initBackend(core: *Core) !*Backend {
        const cpu = try CpuBackend.init(core);
        const backend = try core.allocator().create(Backend);
        backend.* = cpu.asBackend();
        return backend;
    }
    
    fn compile(
        backend: *Backend,
        engine: *CompilerEngine,
        plan: ExecutionPlan,
    ) !CompiledGraph {
        const self = @ptrCast(*CpuBackend, @alignCast(@alignOf(CpuBackend), backend.impl));
        
        // Create CPU-specific compiled artifacts
        const artifacts = try self.createCompiledArtifacts(plan);
        
        return CompiledGraph{
            .id = @enumFromInt(@as(u32, @truncate(@intFromPtr(artifacts)))),
            .plan = plan,
            .artifacts = artifacts,
            .stats = .{
                .compile_time_ns = 0, // Set by compiler
                .memory_footprint = try self.calculateMemoryFootprint(plan),
                .operation_count = plan.operations.len,
                .fusion_count = plan.fusion_groups.len,
            },
        };
    }
    
    fn execute(
        backend: *Backend,
        compiled: *CompiledGraph,
        inputs: []const types.TensorData,
    ) ![]types.TensorData {
        const self = @ptrCast(*CpuBackend, @alignCast(@alignOf(CpuBackend), backend.impl));
        const artifacts = @ptrCast(*CpuCompiledArtifacts, compiled.artifacts);
        
        // Allocate output array
        const output_count = countOutputs(compiled.plan);
        var outputs = try self.core.allocator().alloc(types.TensorData, output_count);
        
        // Execute using the artifacts
        try artifacts.execute(self, inputs, outputs);
        
        return outputs;
    }
    
    fn createCompiledArtifacts(self: *CpuBackend, plan: ExecutionPlan) !*CpuCompiledArtifacts {
        const allocator = self.core.allocator();
        const artifacts = try allocator.create(CpuCompiledArtifacts);
        
        // Convert planned ops to executable kernels
        var kernels = try allocator.alloc(CompiledKernel, plan.operations.len);
        
        for (plan.operations, 0..) |planned_op, i| {
            kernels[i] = try self.compileOperation(planned_op);
        }
        
        artifacts.* = .{
            .kernels = kernels,
            .memory_layout = plan.memory_plan,
            .fusion_kernels = try self.compileFusionGroups(plan.fusion_groups),
        };
        
        return artifacts;
    }
    
    fn compileOperation(self: *CpuBackend, op: PlannedOp) !CompiledKernel {
        const node = self.core.graph_engine.getNode(op.node_id) orelse 
            return error.NodeNotFound;
        
        // Get the operator from the node
        const operator = &node.operator;
        
        // Create kernel based on operator type
        return CompiledKernel{
            .operator = operator,
            .input_buffers = op.input_buffers,
            .output_buffer = op.output_buffer,
            .execution_fn = try self.kernels.getKernelFunction(operator.getType()),
        };
    }
};

/// CPU-specific compiled artifacts
pub const CpuCompiledArtifacts = struct {
    kernels: []CompiledKernel,
    memory_layout: MemoryPlan,
    fusion_kernels: []FusionKernel,
    
    pub fn execute(
        self: *CpuCompiledArtifacts,
        backend: *CpuBackend,
        inputs: []const types.TensorData,
        outputs: []types.TensorData,
    ) !void {
        // Allocate working memory based on memory plan
        const memory = try backend.memory_pool.alloc(self.memory_layout.total_bytes);
        defer backend.memory_pool.free(memory);
        
        // Create buffer views into the memory
        var buffers = try self.createBufferViews(memory);
        defer backend.core.allocator().free(buffers);
        
        // Copy inputs to appropriate buffers
        try self.copyInputs(inputs, buffers);
        
        // Execute kernels (including fused ones)
        for (self.kernels) |kernel| {
            if (kernel.fused) continue; // Skip if part of fusion group
            
            try kernel.execute(backend, buffers);
        }
        
        // Execute fusion kernels
        for (self.fusion_kernels) |fusion| {
            try fusion.execute(backend, buffers);
        }
        
        // Copy outputs from buffers
        try self.copyOutputs(buffers, outputs);
    }
};

/// Compiled kernel ready for execution
pub const CompiledKernel = struct {
    operator: *operator_mod.Operator,
    input_buffers: []const BufferId,
    output_buffer: BufferId,
    execution_fn: KernelFunction,
    fused: bool = false,
    
    pub fn execute(self: CompiledKernel, backend: *CpuBackend, buffers: []Buffer) !void {
        // Gather inputs
        var inputs = try backend.core.allocator().alloc(types.TensorData, self.input_buffers.len);
        defer backend.core.allocator().free(inputs);
        
        for (self.input_buffers, 0..) |buffer_id, i| {
            inputs[i] = buffers[@intFromEnum(buffer_id)].asOperatorInput();
        }
        
        // Execute
        const output = try self.execution_fn(backend, self.operator, inputs);
        
        // Store output
        buffers[@intFromEnum(self.output_buffer)] = Buffer.fromOperatorOutput(output);
    }
};

/// Kernel function type
pub const KernelFunction = *const fn (
    backend: *CpuBackend,
    operator: *operator_mod.Operator,
    inputs: []const types.TensorData,
) anyerror!types.TensorData;

/// CPU kernel implementations
pub const CpuKernels = struct {
    core: *Core,
    
    pub fn init(core: *Core) CpuKernels {
        return .{ .core = core };
    }
    
    pub fn getKernelFunction(self: CpuKernels, op_type: graph.types.OpType) !KernelFunction {
        _ = self;
        return switch (op_type) {
            .add => executeAdd,
            .multiply => executeMultiply,
            .matmul => executeMatMul,
            .relu => executeRelu,
            .reduce_sum => executeReduceSum,
            .reshape => executeReshape,
            .transpose => executeTranspose,
            else => error.UnsupportedOperation,
        };
    }
    
    // Vectorized add implementation
    fn executeAdd(
        backend: *CpuBackend,
        operator: *operator_mod.Operator,
        inputs: []const types.TensorData,
    ) !types.TensorData {
        const a = inputs[0].asTensor();
        const b = inputs[1].asTensor();
        
        // Validate shapes match (or are broadcastable)
        const output_shape = try operator.inferShape(inputs);
        
        // Allocate output
        const output_size = calculateTensorSize(output_shape);
        const output_data = try backend.memory_pool.alloc(output_size * @sizeOf(f32));
        
        const a_data = a.data.asSlice(f32);
        const b_data = b.data.asSlice(f32);
        const out_data = @ptrCast([*]f32, output_data);
        
        // Parallel vectorized execution
        const chunk_size = 1024;
        var wg = std.Thread.WaitGroup{};
        
        var i: usize = 0;
        while (i < output_size) : (i += chunk_size) {
            const end = @min(i + chunk_size, output_size);
            backend.thread_pool.spawnWg(&wg, addChunk, .{
                a_data[i..end],
                b_data[i..end],
                out_data[i..end],
            });
        }
        
        backend.thread_pool.waitAndWork(&wg);
        
        return types.TensorData{
            .tensor = .{
                .shape = output_shape,
                .data = .{ .f32 = out_data[0..output_size] },
            },
        };
    }
    
    fn addChunk(a: []const f32, b: []const f32, out: []f32) void {
        const vec_size = 8;
        var i: usize = 0;
        
        // SIMD vectorized loop
        while (i + vec_size <= a.len) : (i += vec_size) {
            const a_vec: @Vector(vec_size, f32) = a[i..][0..vec_size].*;
            const b_vec: @Vector(vec_size, f32) = b[i..][0..vec_size].*;
            out[i..][0..vec_size].* = a_vec + b_vec;
        }
        
        // Scalar cleanup
        while (i < a.len) : (i += 1) {
            out[i] = a[i] + b[i];
        }
    }
    
    // Other kernel implementations follow similar patterns...
};

/// Fused kernel for executing multiple operations together
pub const FusionKernel = struct {
    operations: []const NodeId,
    pattern: FusionPattern,
    execute_fn: *const fn (backend: *CpuBackend, buffers: []Buffer) anyerror!void,
    
    pub fn execute(self: FusionKernel, backend: *CpuBackend, buffers: []Buffer) !void {
        try self.execute_fn(backend, buffers);
    }
};
```

## Integration with Core

## Integration with Core

The compiler will be built in `src/core/compiler/` and integrated with Core through a clean interface:

```zig
// In src/core/compiler/mod.zig - Export the compiler module
pub const engine = @import("engine.zig");
pub const passes = @import("passes/mod.zig");
pub const types = @import("types.zig");

pub const CompilerEngine = engine.CompilerEngine;
pub const CompilerPass = types.CompilerPass;
pub const CompiledGraph = types.CompiledGraph;
```

Then Core can optionally use the compiler:

```zig
// In src/core/core.zig - Minimal changes to support compilation
const compiler = @import("compiler/mod.zig");

pub const Core = struct {
    // ... existing fields remain unchanged ...
    
    /// Create a compiler engine for this core instance
    pub fn createCompiler(self: *Core) !*compiler.CompilerEngine {
        const engine = try self.arena.allocator().create(compiler.CompilerEngine);
        engine.* = try compiler.CompilerEngine.init(self);
        return engine;
    }
};
```

## Key Design Decisions

1. **No Backward Compatibility**: We completely replaced the old Backend struct with a comprehensive interface designed for performance and extensibility.

2. **Integrated Architecture**: The compiler is now part of Core and works seamlessly with existing engines (Graph, Shape, Symbolic).

3. **Reuse Existing Infrastructure**: 
   - Uses existing Operator system for kernel execution
   - Leverages existing memory pools and arena allocators
   - Integrates with existing type-safe ID system

4. **Performance-Oriented Design**:
   - Compilation caching built-in
   - Memory planning for optimal buffer reuse
   - Operation fusion for reduced memory traffic
   - SIMD vectorization using Zig's @Vector

5. **Clean Separation of Concerns**:
   - CompilerEngine handles optimization and planning
   - Backends handle execution details
   - Core orchestrates the overall system

## Advanced Performance Features

To match and exceed Luminal's performance, we need runtime kernel generation and advanced optimization:

### CUDA Backend Following Luminal's Strategy

The CUDA backend's power comes from runtime kernel generation and aggressive fusion:

```zig
// In src/core/backends/cuda/compiler.zig

pub const CudaCompiler = struct {
    // Ordered passes following Luminal
    pub const Passes = .{
        PrimitiveCompiler,      // Replace ops with CUDA variants
        SpecialOpsCompiler,     // MatMul, Softmax, etc.
        CopyCompiler,           // Handle CPU<->GPU transfers
        ElementwiseFusionCompiler, // The magic happens here
    };
};

/// Replace generic ops with CUDA-specific variants that support fusion
pub const PrimitiveCompiler = struct {
    pub fn compile(engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
        var changed = false;
        
        for (plan.operations) |*op| {
            // Mark elementwise operations with their CUDA expression
            if (isElementwise(op.op_type)) {
                op.backend_data = try generateElementwiseExpression(op.op_type);
                changed = true;
            }
        }
        
        return changed;
    }
    
    fn generateElementwiseExpression(op_type: graph.types.OpType) ![]const u8 {
        return switch (op_type) {
            .add => "a + b",
            .multiply => "a * b",
            .relu => "fmaxf(a, 0.0f)",
            .sigmoid => "1.0f / (1.0f + expf(-a))",
            .exp => "expf(a)",
            .log => "logf(a)",
            else => error.NotElementwise,
        };
    }
};

/// The core of CUDA performance - fuse elementwise operations
pub const ElementwiseFusionCompiler = struct {
    device: *cuda.Device,
    kernel_cache: std.StringHashMapUnmanaged(CudaKernel),
    
    pub fn compile(engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
        var changed = false;
        var i: usize = 0;
        
        while (i < plan.operations.len) {
            if (!hasElementwiseExpression(plan.operations[i])) {
                i += 1;
                continue;
            }
            
            // Find fusion candidates
            var fusion_group = std.ArrayListUnmanaged(usize){};
            try fusion_group.append(engine.core.allocator(), i);
            
            // Greedy fusion - keep adding while beneficial
            var j = i + 1;
            while (j < plan.operations.len) {
                const op = plan.operations[j];
                
                // Can only fuse if:
                // 1. It's elementwise
                // 2. All inputs come from our fusion group
                // 3. No external consumers of intermediate values
                if (!hasElementwiseExpression(op)) break;
                if (!allInputsFromGroup(op, fusion_group.items)) break;
                if (hasExternalConsumers(plan, j, fusion_group.items)) break;
                
                try fusion_group.append(engine.core.allocator(), j);
                j += 1;
            }
            
            if (fusion_group.items.len > 1) {
                // Generate fused kernel
                const kernel = try self.generateFusedKernel(
                    plan.operations[fusion_group.items[0]..fusion_group.items[fusion_group.items.len-1]+1]
                );
                
                // Replace with single fused operation
                plan.operations[i] = PlannedOp{
                    .op_type = .fused_cuda_kernel,
                    .backend_data = kernel,
                    .inputs = try collectExternalInputs(plan, fusion_group.items),
                    .output = plan.operations[fusion_group.items[fusion_group.items.len-1]].output,
                };
                
                // Mark others for removal
                for (fusion_group.items[1..]) |idx| {
                    plan.operations[idx].op_type = .noop;
                }
                
                changed = true;
            }
            
            i = j;
        }
        
        if (changed) {
            plan.operations = try removeNoops(plan.operations);
        }
        
        return changed;
    }
    
    fn generateFusedKernel(self: *ElementwiseFusionCompiler, ops: []const PlannedOp) !CudaKernel {
        // Generate cache key
        var hasher = std.hash.Wyhash.init(0);
        for (ops) |op| {
            hasher.update(std.mem.asBytes(&op.op_type));
        }
        const cache_key = hasher.final();
        
        // Check cache
        if (self.kernel_cache.get(cache_key)) |kernel| {
            return kernel;
        }
        
        // Generate CUDA kernel code
        const kernel_code = try self.generateKernelCode(ops);
        
        // Compile with NVRTC
        const kernel = try self.compileKernel(kernel_code);
        try self.kernel_cache.put(cache_key, kernel);
        
        return kernel;
    }
    
    fn generateKernelCode(self: *ElementwiseFusionCompiler, ops: []const PlannedOp) ![]const u8 {
        var code = std.ArrayListUnmanaged(u8){};
        
        // Kernel header
        try code.appendSlice(self.device.allocator,
            \\#include <cuda_fp16.h>
            \\extern "C" __global__ void kernel(
        );
        
        // Parameters - outputs first, then inputs
        try code.appendSlice(self.device.allocator, "float* out");
        
        // Add input parameters
        var input_count: usize = 0;
        for (ops[0].inputs) |_| {
            try code.writer().print(", const float* input{}", .{input_count});
            input_count += 1;
        }
        
        // Add dimension parameters for dynamic shapes
        try code.appendSlice(self.device.allocator, ", int numel");
        
        try code.appendSlice(self.device.allocator,
            \\) {
            \\    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
            \\    if (idx >= numel) return;
            \\
        );
        
        // Generate fused computation
        try self.generateFusedComputation(&code, ops);
        
        try code.appendSlice(self.device.allocator,
            \\    out[idx] = result;
            \\}
        );
        
        return code.toOwnedSlice(self.device.allocator);
    }
    
    fn generateFusedComputation(self: *ElementwiseFusionCompiler, code: *std.ArrayListUnmanaged(u8), ops: []const PlannedOp) !void {
        // Build expression tree
        var intermediates = std.StringHashMapUnmanaged([]const u8){};
        
        for (ops, 0..) |op, i| {
            const expr = op.backend_data.?; // The elementwise expression
            
            // Replace input references
            var processed_expr = try self.processExpression(expr, &intermediates, op.inputs);
            
            if (i < ops.len - 1) {
                // Intermediate value
                try code.writer().print("    float intermediate{} = {};\n", .{i, processed_expr});
                try intermediates.put(op.output, try std.fmt.allocPrint(self.device.allocator, "intermediate{}", .{i}));
            } else {
                // Final result
                try code.writer().print("    float result = {};\n", .{processed_expr});
            }
        }
    }
};

/// Convert symbolic expressions to CUDA code (following Luminal)
pub fn exprToCuda(expr: *const Expression) ![]const u8 {
    var cuda_code = std.ArrayList(u8).init(expr.allocator);
    
    // Stack-based evaluation to CUDA infix
    var stack = std.ArrayList([]const u8).init(expr.allocator);
    
    for (expr.terms) |term| {
        switch (term) {
            .num => |n| try stack.append(try std.fmt.allocPrint(expr.allocator, "{}", .{n})),
            .var => |v| {
                if (v == 'z') {
                    try stack.append("idx");
                } else {
                    try stack.append(try std.fmt.allocPrint(expr.allocator, "{c}", .{v}));
                }
            },
            .add => {
                const b = stack.pop();
                const a = stack.pop();
                try stack.append(try std.fmt.allocPrint(expr.allocator, "({s} + {s})", .{a, b}));
            },
            .mul => {
                const b = stack.pop();
                const a = stack.pop();
                try stack.append(try std.fmt.allocPrint(expr.allocator, "({s} * {s})", .{a, b}));
            },
            .max => {
                const b = stack.pop();
                const a = stack.pop();
                try stack.append(try std.fmt.allocPrint(expr.allocator, "max({s}, {s})", .{a, b}));
            },
            .min => {
                const b = stack.pop();
                const a = stack.pop();
                try stack.append(try std.fmt.allocPrint(expr.allocator, "min({s}, {s})", .{a, b}));
            },
        }
    }
    
    return stack.items[0];
}

/// Compile CUDA kernel using NVRTC
pub fn compileCudaKernel(code: []const u8, device: *cuda.Device) !CudaKernel {
    const kernel_name = try std.fmt.allocPrint(device.allocator, "kernel_{}", .{std.hash.Wyhash.hash(0, code)});
    
    // Check if already compiled
    if (device.hasKernel(kernel_name)) {
        return device.getKernel(kernel_name);
    }
    
    // Compile with NVRTC
    const ptx = try cuda.nvrtc.compileToPtx(.{
        .source = code,
        .name = kernel_name,
        .options = &.{
            "-arch=sm_75",
            "-use_fast_math",
            "-O3",
        },
    });
    
    // Load and return
    try device.loadPtx(ptx, kernel_name);
    return device.getKernel(kernel_name);
}
```

### Automatic Differentiation Using Gradient Rules Table

We use a simple gradient rules table for autodiff, keeping gradient rules clean and declarative.

**Important**: The graph engine only supports Luminal's primitive operations:
- Binary: `add`, `multiply`, `mod`, `less_than`
- Unary: `reciprocal`, `sqrt`, `sin`, `log2`, `exp2`
- Reduction: `reduce_sum`, `reduce_max`
- Memory: `contiguous`

All higher-level operations (subtract, divide, relu, sigmoid, matmul, etc.) are decomposed
to these primitives in the tensor layer before reaching the compiler.

```zig
// In src/core/compiler/passes/autodiff.zig

/// Gradient rule - just an operation type and its backward function
pub const GradientRule = struct {
    op_type: graph.types.OpType,
    backward: *const fn (
        op: PlannedOp,
        grad_output: NodeId,
        graph: *GraphEngine,
    ) anyerror![]const NodeId,
};

/// All gradient rules for Luminal primitive operations only
/// This is a simple data table, not a DSL - following idiomatic Zig practices
pub const gradient_rules = [_]GradientRule{
    // Addition: both inputs get the same gradient
    .{ .op_type = .add, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            _ = op; _ = graph;
            return &[_]NodeId{ grad_output, grad_output };
        }
    }.backward },
    
    // Multiplication: grad_a = grad_out * b, grad_b = grad_out * a  
    .{ .op_type = .multiply, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            const grad_a = try graph.multiply(grad_output, op.inputs[1]);
            const grad_b = try graph.multiply(grad_output, op.inputs[0]);
            return &[_]NodeId{ grad_a, grad_b };
        }
    }.backward },
    
    // Reciprocal: grad = -grad_out / (x^2)
    .{ .op_type = .reciprocal, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            const x_squared = try graph.multiply(op.inputs[0], op.inputs[0]);
            const neg_grad = try graph.multiply(grad_output, try graph.constant(-1.0, .{}));
            const grad_input = try graph.multiply(neg_grad, try graph.reciprocal(x_squared));
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Sqrt: grad = grad_out / (2 * sqrt(x))
    .{ .op_type = .sqrt, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            const two = try graph.constant(2.0, .{});
            const sqrt_x = try graph.sqrt(op.inputs[0]);
            const two_sqrt_x = try graph.multiply(two, sqrt_x);
            const grad_input = try graph.multiply(grad_output, try graph.reciprocal(two_sqrt_x));
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Sin: grad = grad_out * cos(x)
    .{ .op_type = .sin, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            // Note: cos would be decomposed to sin(x + pi/2) in the tensor layer
            const pi_over_2 = try graph.constant(1.5707963267948966, .{});
            const x_shifted = try graph.add(op.inputs[0], pi_over_2);
            const cos_x = try graph.sin(x_shifted);
            const grad_input = try graph.multiply(grad_output, cos_x);
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Log2: grad = grad_out / (x * ln(2))
    .{ .op_type = .log2, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            const ln2 = try graph.constant(0.693147180559945309417, .{});
            const x_ln2 = try graph.multiply(op.inputs[0], ln2);
            const grad_input = try graph.multiply(grad_output, try graph.reciprocal(x_ln2));
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Exp2: grad = grad_out * exp2(x) * ln(2)
    .{ .op_type = .exp2, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            const ln2 = try graph.constant(0.693147180559945309417, .{});
            const exp2_x = try graph.exp2(op.inputs[0]);
            const exp2_x_ln2 = try graph.multiply(exp2_x, ln2);
            const grad_input = try graph.multiply(grad_output, exp2_x_ln2);
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Reduce sum: broadcast gradient back
    .{ .op_type = .reduce_sum, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            // Note: broadcast is handled at tensor layer by expanding/repeating
            const metadata = op.metadata.?.reduction;
            const grad_input = try graph.broadcast(grad_output, metadata.original_shape);
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Reduce max: gradient flows to the max element(s)
    .{ .op_type = .reduce_max, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            // Create mask where input == max value
            const metadata = op.metadata.?.reduction;
            const max_broadcasted = try graph.broadcast(op.output, metadata.original_shape);
            const is_max = try graph.equal(op.inputs[0], max_broadcasted);
            const grad_input = try graph.multiply(grad_output, is_max);
            return &[_]NodeId{ grad_input };
        }
    }.backward },
    
    // Less than: no gradient (discrete operation)
    .{ .op_type = .less_than, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            _ = op; _ = grad_output; _ = graph;
            // Comparison operations have zero gradient
            const zero_a = try graph.zerosLike(op.inputs[0]);
            const zero_b = try graph.zerosLike(op.inputs[1]);
            return &[_]NodeId{ zero_a, zero_b };
        }
    }.backward },
    
    // Mod: gradient flows through first input only
    .{ .op_type = .mod, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            _ = op; _ = graph;
            const zero_b = try graph.zerosLike(op.inputs[1]);
            return &[_]NodeId{ grad_output, zero_b };
        }
    }.backward },
    
    // Contiguous: gradient passes through unchanged
    .{ .op_type = .contiguous, .backward = struct {
        fn backward(op: PlannedOp, grad_output: NodeId, graph: *GraphEngine) ![]const NodeId {
            _ = op; _ = graph;
            return &[_]NodeId{ grad_output };
        }
    }.backward },
};

/// Input to the Autograd compiler pass
pub const AutogradInput = struct {
    params: []const NodeId,
    loss: NodeId,
};

/// Output from the Autograd compiler pass
pub const AutogradOutput = struct {
    /// Gradient for each parameter (same order as input params)
    gradients: []const struct { param: NodeId, grad: NodeId },
};

/// Autograd as a proper compiler pass (following Luminal's design)
pub const Autograd = struct {
    params: []const NodeId,
    loss: NodeId,
    
    pub fn init(params: []const NodeId, loss: NodeId) Autograd {
        return .{ .params = params, .loss = loss };
    }
    
    /// Create a compiler pass for this autograd computation
    pub fn toCompilerPass(self: *const Autograd) CompilerPass {
        return .{
            .name = "autograd",
            .compileFn = compile,
            .data = @ptrCast(*anyopaque, @constCast(self)),
        };
    }
    
    fn compile(
        pass: *const CompilerPass,
        engine: *CompilerEngine,
        graph: *GraphEngine,
        input: *anyopaque,
    ) !*anyopaque {
        _ = input; // Autograd doesn't need additional input
        const self = @ptrCast(*const Autograd, @alignCast(@alignOf(Autograd), pass.data.?));
        
        // Initialize gradient rule map
        var rule_map = std.AutoHashMapUnmanaged(graph.types.OpType, GradientRule){};
        defer rule_map.deinit();
        
        for (gradient_rules) |rule| {
            try rule_map.put(engine.core.allocator(), rule.op_type, rule);
        }
        
        // Build backward graph
        const gradients = try buildBackwardGraph(
            engine,
            graph,
            self.loss,
            self.params,
            &rule_map,
        );
        defer gradients.deinit();
        
        // Create output
        const allocator = engine.core.allocator();
        const output = try allocator.create(AutogradOutput);
        output.* = .{
            .gradients = try allocator.alloc(
                struct { param: NodeId, grad: NodeId },
                self.params.len,
            ),
        };
        
        for (self.params, 0..) |param, i| {
            const grad_info = gradients.get(param) orelse {
                // No gradient - create zero gradient
                const zero = try graph.zerosLike(param);
                output.gradients[i] = .{ .param = param, .grad = zero };
                continue;
            };
            output.gradients[i] = .{ .param = param, .grad = grad_info.id };
        }
        
        return @ptrCast(*anyopaque, output);
    }
    
    fn buildBackwardGraph(
        engine: *CompilerEngine,
        graph: *GraphEngine, 
        loss: NodeId,
        params: []const NodeId,
        rule_map: *std.AutoHashMapUnmanaged(graph.types.OpType, GradientRule),
    ) !FxHashMap(NodeId, struct { id: NodeId, shape: ShapeTracker }) {
        // Build valid set (nodes that are both reachable from params and can reach loss)
        const forward_set = try buildReachableSet(params, graph, .forward);
        const backward_set = try buildReachableSet(&[_]NodeId{loss}, graph, .backward);
        defer forward_set.deinit();
        defer backward_set.deinit();
        
        var valid_set = FxHashSet(NodeId).init(graph.allocator());
        defer valid_set.deinit();
        
        // Intersection of forward and backward sets
        var iter = forward_set.iterator();
        while (iter.next()) |entry| {
            if (backward_set.contains(entry.key_ptr.*)) {
                try valid_set.put(entry.key_ptr.*);
            }
        }
        
        // Track gradients for each node (with shape info)
        var gradients = FxHashMap(NodeId, struct { id: NodeId, shape: ShapeTracker }).init(graph.allocator());
        
        // Start with gradient of loss = 1.0
        const ones = try graph.constant(1.0, .{});
        const loss_shape = try graph.getShapeTracker(loss);
        try gradients.put(loss, .{ .id = ones, .shape = loss_shape });
        
        // Get nodes in reverse topological order
        const order = try graph.getTopologicalOrder(graph.allocator());
        defer graph.allocator().free(order);
        
        // Process backwards
        var i = order.len;
        while (i > 0) {
            i -= 1;
            const node_id = order[i];
            
            // Skip if not in valid set
            if (!valid_set.contains(node_id)) continue;
            
            // Skip if no gradient flowing through this node
            const grad_info = gradients.get(node_id) orelse continue;
            const grad_output = grad_info.id;
            
            // Get the operation
            const node = graph.getNode(node_id) orelse continue;
            const op_type = node.operator.getType();
            
            // Skip undifferentiable operations for params
            if (op_type == .mod or op_type == .less_than) {
                // Assert this isn't a parameter
                for (params) |p| {
                    std.debug.assert(p != node_id, "Parameter {d} has undifferentiable op {}", .{node_id, op_type});
                }
                continue;
            }
            
            // Look up gradient rule
            const rule = rule_map.get(op_type) orelse {
                std.debug.print("Warning: No gradient rule for {}\n", .{op_type});
                continue;
            };
            
            // Apply the backward function
            const op = PlannedOp{
                .node_id = node_id,
                .op_type = op_type,
                .inputs = node.inputs.items,
                .output = node_id,
                .metadata = node.metadata,
            };
            
            const grad_inputs = try rule.backward(op, grad_output, graph);
            
            // Accumulate gradients for each input with shape handling
            for (node.inputs.items, grad_inputs) |input_id, grad_input_id| {
                if (!valid_set.contains(input_id)) continue;
                
                // Handle shape transformations (Luminal's add_grad logic)
                var grad = try graph.getGraphTensor(grad_input_id);
                const fwd = try graph.getGraphTensor(input_id);
                
                // Reshape gradient to match input shape
                grad = try reshapeGradient(graph, grad, fwd);
                
                if (gradients.get(input_id)) |existing| {
                    // Accumulate gradients
                    const sum = try graph.add(existing.id, grad.id);
                    const sum_shape = try graph.getShapeTracker(sum);
                    try gradients.put(input_id, .{ .id = sum, .shape = sum_shape });
                } else {
                    const grad_shape = try graph.getShapeTracker(grad.id);
                    try gradients.put(input_id, .{ .id = grad.id, .shape = grad_shape });
                }
            }
        }
        
        return gradients;
    }
    
    fn reshapeGradient(graph: *GraphEngine, grad: GraphTensor, fwd: GraphTensor) !GraphTensor {
        var result = grad;
        
        // Undo permutes (match Luminal lines 194-199)
        if (!fwd.shape.indexesMatch(grad.shape)) {
            var new_indexes = try graph.allocator().alloc(usize, fwd.shape.len());
            for (0..fwd.shape.len()) |i| {
                new_indexes[fwd.shape.indexes[i]] = grad.shape.indexes[i];
            }
            result.shape.indexes = new_indexes;
        }
        
        // Undo expands by sum reducing fake dimensions (match Luminal lines 202-211)
        var i = fwd.shape.indexes.len;
        while (i > 0) {
            i -= 1;
            const idx = fwd.shape.indexes[i];
            if (fwd.shape.fake[idx]) {
                result = try graph.sumReduce(result, idx);
            }
        }
        
        // Ensure contiguous if shapes don't match (match Luminal lines 214-226)
        const pre_fwd_shape = try graph.getPreOpShape(fwd.id);
        if (!result.shape.dimsEqual(pre_fwd_shape)) {
            if (!result.shape.isContiguous()) {
                result = try graph.contiguous(result);
            }
            result.shape = pre_fwd_shape.contiguous();
        }
        
        return result;
    }
    
    fn buildReachableSet(
        start_nodes: []const NodeId,
        graph: *GraphEngine,
        direction: enum { forward, backward },
    ) !FxHashSet(NodeId) {
        var set = FxHashSet(NodeId).init(graph.allocator());
        var stack = std.ArrayList(NodeId).init(graph.allocator());
        defer stack.deinit();
        
        try stack.appendSlice(start_nodes);
        
        while (stack.popOrNull()) |node| {
            if (set.contains(node)) continue;
            try set.put(node);
            
            const edges = switch (direction) {
                .forward => try graph.getConsumers(node),
                .backward => try graph.getInputs(node),
            };
            
            for (edges) |edge| {
                if (!edge.is_schedule) { // Skip schedule edges
                    try stack.append(edge.node);
                }
            }
        }
        
        return set;
    }
};

/// Gradient checkpointing to reduce memory usage during training
pub const GradientCheckpointing = struct {
    pub fn gradientCheckpointing(
        engine: *CompilerEngine,
        plan: *ExecutionPlan,
    ) !bool {
        // Identify checkpointing opportunities
        const checkpoint_ops = try findCheckpointCandidates(plan);
        if (checkpoint_ops.len == 0) return false;
        
        // Mark operations for recomputation during backward pass
        for (checkpoint_ops) |op_idx| {
            plan.operations[op_idx].checkpoint = true;
        }
        
        // Update memory plan to account for checkpointing
        try updateMemoryPlanForCheckpoints(plan);
        
        return true;
    }
    
    fn findCheckpointCandidates(plan: *ExecutionPlan) ![]usize {
        // Heuristic: checkpoint at transformer layer boundaries
        // or after every N operations to balance memory vs compute
        var candidates = std.ArrayList(usize).init(engine.core.allocator());
        
        const checkpoint_interval = 50; // Operations between checkpoints
        var op_count: usize = 0;
        
        for (plan.operations, 0..) |op, i| {
            op_count += 1;
            
            // Checkpoint at regular intervals
            if (op_count >= checkpoint_interval) {
                try candidates.append(i);
                op_count = 0;
            }
            
            // Also checkpoint before memory-intensive operations
            if (isMemoryIntensive(op)) {
                try candidates.append(i);
                op_count = 0;
            }
        }
        
        return candidates.toOwnedSlice();
    }
};
```

### Key Improvements from Luminal's Autodiff

Our implementation incorporates Luminal's key insights while maintaining simplicity:

1. **Valid Set Computation**: Only compute gradients for nodes that both:
   - Are reachable from parameters (forward pass)
   - Can reach the loss (backward pass)
   
2. **Shape Handling**: Properly handle gradient reshaping:
   - Undo permutations from transposes
   - Sum-reduce gradients for broadcast dimensions
   - Ensure contiguous layout when needed

3. **Gradient Accumulation**: Multiple paths to the same node accumulate gradients

4. **Undifferentiable Ops**: Properly handle mod and comparison operations

5. **Data-Driven Design**: Clean separation between gradient rules table (data) and execution (logic)

Usage example:
```zig
// Define parameters and compute loss
const model_params = try params(&model);
const loss = try cross_entropy(output, target);

// Create autograd compiler pass
const autograd = Autograd.init(model_params, loss.id);
const autograd_pass = autograd.toCompilerPass();

// Compute gradients using the compiler pass system
const grad_output = try compiler.compile(AutogradOutput, autograd_pass, {});

// Apply gradients with optimizer
for (grad_output.gradients) |grad_info| {
    try optimizer.applyGradient(grad_info.param, grad_info.grad);
}

// Can also chain with other passes
const optimized_grads = try compiler.compile(
    AutogradOutput,
    CompilerPass.chain(&.{
        autograd_pass,
        GenericOptimizer.toCompilerPass(),  // Optimize the gradient graph
    }),
    {},
);
```

### Aggressive Fusion Optimizer

```zig
// In src/core/compiler/passes/fusion.zig

pub const FusionOptimizer = struct {
    /// Learned patterns from profiling
    hot_patterns: PatternDatabase,
    /// Fusion decisions based on measured performance
    fusion_policy: FusionPolicy,
    
    pub fn fuseOperations(
        engine: *CompilerEngine,
        plan: *ExecutionPlan,
    ) !bool {
        const fusion_groups = try findFusionOpportunities(plan);
        if (fusion_groups.len == 0) return false;
        
        // Replace operation sequences with fusion groups
        for (fusion_groups) |group| {
            try replacaWithFusion(plan, group);
        }
        
        return true;
    }
    
    fn findFusionOpportunities(plan: *ExecutionPlan) ![]FusionGroup {
        var groups = std.ArrayList(FusionGroup).init(engine.core.allocator());
        
        // Scan for fusable sequences
        var i: usize = 0;
        while (i < plan.operations.len) {
            const start = i;
            
            // Greedy fusion - keep adding ops while beneficial
            while (i < plan.operations.len and shouldFuse(plan.operations[start..i+1])) {
                i += 1;
            }
            
            if (i > start + 1) {
                try groups.append(.{
                    .operations = try allocator.dupe(NodeId, getNodeIds(plan.operations[start..i])),
                    .pattern = identifyPattern(plan.operations[start..i]),
                    .estimated_speedup = estimateBenefit(plan.operations[start..i]),
                });
            } else {
                i += 1;
            }
        }
        
        return groups.toOwnedSlice();
    }
};
```

### Shape-Specialized Kernels

```zig
// Generate specialized kernels for specific shapes
pub const ShapeSpecializer = struct {
    pub fn specializeForShape(
        kernel: *CompiledKernel,
        concrete_shape: []const i64,
    ) !void {
        // Determine specialization strategy
        if (isSmallShape(concrete_shape)) {
            kernel.variant = .fully_unrolled;
            kernel.unroll_factor = @intCast(concrete_shape[concrete_shape.len - 1]);
        } else if (isPowerOfTwo(concrete_shape)) {
            kernel.variant = .power_of_two_optimized;
        } else if (hasContiguousAccess(concrete_shape)) {
            kernel.variant = .vectorized_contiguous;
            kernel.vector_width = 8; // AVX width
        }
    }
};
```

### Adaptive Compilation Strategy

```zig
pub const AdaptiveBackend = struct {
    /// Base backend functionality
    base: Backend,
    /// Runtime kernel generator
    kernel_generator: ?*KernelGenerator = null,
    /// Execution statistics
    stats: ExecutionStats,
    
    pub fn executeAdaptive(
        self: *AdaptiveBackend,
        compiled: *CompiledGraph,
        inputs: []const types.TensorData,
    ) ![]types.TensorData {
        // Track execution count
        compiled.stats.execution_count += 1;
        
        // Use static kernels initially
        if (compiled.stats.execution_count < 10) {
            return self.base.executeFn(&self.base, compiled, inputs);
        }
        
        // Profile and optimize hot paths
        if (compiled.stats.execution_count == 10) {
            const profile = try self.profileExecution(compiled, inputs);
            try self.optimizeHotPaths(compiled, profile);
        }
        
        // Use optimized kernels
        return self.executeOptimized(compiled, inputs);
    }
    
    fn optimizeHotPaths(
        self: *AdaptiveBackend,
        compiled: *CompiledGraph,
        profile: ProfilingData,
    ) !void {
        // Initialize kernel generator if needed
        if (self.kernel_generator == null) {
            self.kernel_generator = try KernelGenerator.init(self.base.core);
        }
        
        // Find bottlenecks
        const bottlenecks = profile.getBottlenecks(0.1); // 10% threshold
        
        for (bottlenecks) |bottleneck| {
            switch (bottleneck.type) {
                .memory_bound => {
                    // Generate fused kernel
                    const fused = try self.kernel_generator.?.generateFusedKernel(
                        bottleneck.operations,
                        bottleneck.shapes,
                    );
                    try self.replacaWithFused(compiled, bottleneck, fused);
                },
                .compute_bound => {
                    // Generate specialized variant
                    try self.generateSpecialized(compiled, bottleneck);
                },
            }
        }
    }
};
```

## Implementation Phases

### Phase 1: Foundation (Weeks 1-2)
- Implement basic backend interface with static kernels
- Get CPU backend working with vectorization
- Basic graph compilation without fusion

### Phase 2: Runtime Generation (Weeks 3-4)
- Add CUDA kernel generator for elementwise ops
- Implement kernel caching system
- Basic fusion for elementwise operations

### Phase 3: Advanced Optimization (Weeks 5-6)
- Pattern-based fusion optimizer
- Shape specialization
- Memory access optimization

### Phase 4: Adaptive Performance (Weeks 7-8)
- Profile-guided optimization
- Hot path detection and optimization
- Benchmark and tune against Luminal

## Performance Strategy

1. **Start Simple**: Pre-compiled kernels get us 70-80% of Luminal's speed
2. **Add Fusion**: Runtime fusion gets us to 90-95%
3. **Profile & Specialize**: Adaptive optimization gets us to 100-110%
4. **Zig Advantages**: Better memory control and CPU performance push us beyond

## Simplified Multi-Backend Support

For super performance with simple, maintainable code, here's the minimal approach:

### Phase 1: Single Backend Focus (Immediate)

```zig
// Start with explicit backend selection - no auto-detection yet
pub const Core = struct {
    // ... existing fields ...
    
    pub fn initWithBackend(allocator: Allocator, backend_name: []const u8) !*Core {
        const core = try Core.init(allocator);
        
        // Simple explicit backend creation
        const backend = switch (backend_name) {
            "cpu" => try CpuBackend.init(core),
            "cuda" => try CudaBackend.init(core),
            else => return error.UnknownBackend,
        };
        
        try core.registerBackend(backend);
        return core;
    }
};

// Usage - explicit and simple
const core = try Core.initWithBackend(allocator, "cuda");
```

### Phase 2: Backend Interface (Keep it Minimal)

The backend interface we already have is good - just ensure implementations stay focused:

```zig
// Each backend just needs to implement the core interface
// No complex abstractions or registries yet

pub const CudaBackend = struct {
    core: *Core,
    device: cuda.Device,
    kernel_cache: KernelCache,
    
    pub fn init(core: *Core) !*Backend {
        const self = try core.allocator().create(CudaBackend);
        
        // Simple initialization
        self.* = .{
            .core = core,
            .device = try cuda.Device.init(0),
            .kernel_cache = KernelCache.init(core.allocator()),
        };
        
        // Return the interface
        return &self.base;
    }
};
```

### Phase 3: Future Multi-Backend (When Needed)

Keep the door open but don't implement yet:

```zig
// Future extension point - not implemented now
pub const Core = struct {
    // Single backend for now
    backend: *Backend,
    
    // Future: Could become backends: []const *Backend
    // Future: Could add backend_selector: BackendSelector
};
```

## Focus Areas for Performance

Instead of complex multi-backend infrastructure, focus on:

### 1. **Kernel Generation Excellence**
```zig
// This is where performance comes from
pub const KernelGenerator = struct {
    // Generate the fastest possible kernels
    pub fn generateFusedKernel(self: *KernelGenerator, ops: []const PlannedOp) !CudaKernel {
        // Smart fusion strategies
        // Optimal memory access patterns
        // Register optimization
        // Shared memory usage
    }
};
```

### 2. **Memory Management**
```zig
// Pre-allocate and reuse everything
pub const MemoryPool = struct {
    // Fast allocation strategies
    // Buffer reuse
    // Minimal allocations during execution
};
```

### 3. **Compilation Caching**
```zig
// Never compile the same thing twice
pub const CompilationCache = struct {
    // Aggressive caching
    // Fast lookup
    // Persistent cache to disk
};
```

## Benefits of This Approach

1. **Immediate Performance**: Focus on making one backend super fast
2. **Simple Code**: No complex abstractions to maintain
3. **Easy Debugging**: Single code path to trace
4. **Future Ready**: Clean interface allows multi-backend later
5. **Faster Development**: Ship working code sooner

## When to Add Complexity

Add multi-backend features only when:
- You have CPU and CUDA backends both highly optimized
- Users specifically request backend selection
- You need to support heterogeneous execution
- Auto-detection becomes a real user pain point

## Implementation Roadmap

Here's the clear path to build the compiler infrastructure in `src/core/compiler/`:

### File Structure
```
src/
├── core/
│   ├── compiler/
│   │   ├── mod.zig           # Module exports
│   │   ├── types.zig         # Compiler-specific types
│   │   ├── engine.zig        # Main CompilerEngine
│   │   ├── pattern.zig       # Pattern matching DSL
│   │   └── passes/
│   │       ├── mod.zig       # Pass exports
│   │       ├── dead_code.zig
│   │       ├── cse.zig       
│   │       ├── fusion.zig
│   │       ├── memory.zig
│   │       └── autodiff.zig
│   └── ... (existing core modules)
│
├── backends/         # Backends at same level as core
│   ├── cpu/
│   │   ├── mod.zig
│   │   ├── backend.zig   # Implements enhanced Backend interface
│   │   └── kernels.zig   # CPU-specific kernel implementations
│   └── cuda/
│       ├── mod.zig
│       ├── backend.zig   # Implements enhanced Backend interface
│       └── kernel_gen.zig # Runtime kernel generation
│
├── tensor/           # Existing tensor ops (already at this level)
├── nn/               # Neural network modules
└── zing.zig          # Main entry point
```

### Step 1: Create Compiler Types
```zig
// In src/core/compiler/types.zig
const std = @import("std");
const graph = @import("../graph/mod.zig");
const NodeId = @import("../types.zig").NodeId;

// All the compiler types (BackendCapability, PlannedOp, ExecutionPlan, etc.)
```

### Step 2: Basic Optimization Passes
```zig
// Create src/core/compiler/passes/
// Start with these essential passes:

// dead_code.zig
pub fn eliminateDeadCode(engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
    // Remove operations with no consumers
}

// cse.zig  
pub fn eliminateCommonSubexpressions(engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
    // Find and merge duplicate operations
}

// fusion.zig
pub fn fuseOperations(engine: *CompilerEngine, plan: *ExecutionPlan) !bool {
    // Start simple: fuse consecutive elementwise ops
}
```

### Step 3: CPU Backend Implementation
```zig
// 1. Create src/core/backends/cpu/backend.zig
// 2. Implement the CpuBackend struct
// 3. Add kernel implementations:

// src/core/backends/cpu/kernels/add.zig
pub fn executeAdd(
    backend: *CpuBackend,
    operator: *operator_mod.Operator,
    inputs: []const types.TensorData,
) !types.TensorData {
    // Vectorized implementation using @Vector
}

// Repeat for other operations
```

### Step 4: Wire Everything Together
```zig
// In your main code:
const core = try Core.initWithBackend(allocator, "cpu");
const graph = core.graph_engine;

// Build graph
const a = try graph.constant(1.0, .{});
const b = try graph.constant(2.0, .{});
const c = try graph.add(a, b);

// Compile
const compiler = try core.getCompiler();
try compiler.registerStandardPasses();
const compiled = try compiler.compile(core.backend);

// Execute
const result = try core.execute(compiled, &.{});
```

### Step 5: Add CUDA Backend (For Performance)
```zig
// 1. Create src/core/backends/cuda/backend.zig
// 2. Implement kernel generator:

// src/core/backends/cuda/kernel_generator.zig
pub fn generateFusedKernel(ops: []const PlannedOp) ![]const u8 {
    // Generate CUDA code string
    // Compile with NVRTC
    // Return compiled kernel
}
```

### Step 6: Performance Optimization
1. **Profile First**: Identify bottlenecks
2. **Optimize Kernel Generation**: Better fusion, shared memory usage
3. **Memory Pool Tuning**: Reduce allocations
4. **Cache Everything**: Compiled kernels, shapes, expressions

## Testing Strategy

### Unit Tests
```zig
// src/core/compiler/tests/test_compiler.zig
test "basic compilation" {
    const core = try Core.initWithBackend(testing.allocator, "cpu");
    // Test compilation works
}

// src/core/backends/cpu/tests/test_kernels.zig
test "vectorized add" {
    // Test individual kernels
}
```

### Integration Tests
```zig
test "end to end execution" {
    // Build graph, compile, execute, verify results
}
```

### Performance Benchmarks
```zig
// benchmarks/matmul_benchmark.zig
// Compare against baseline/Luminal
```

## Common Pitfalls to Avoid

1. **Don't optimize prematurely** - Get it working first
2. **Don't over-abstract** - Keep backends simple until you need flexibility  
3. **Don't forget caching** - Compilation should happen once
4. **Don't neglect memory** - Pool and reuse aggressively

## Key Simplifications from Original Design

Based on lessons learned (see COMPILER_COMPARISON.md for details):

1. **Simple Backend Interface**: Function pointers instead of complex vtables
2. **No Separate Pass System**: Everything uses the unified CompilerPass
3. **Standard Thread Pool**: Use std.Thread.Pool instead of custom implementation
4. **Unified Kernel Execution**: One parallel+vectorized implementation per op
5. **Simple Memory Planning**: Let arena allocator handle most complexity

## Design Summary

Our compiler design integrates with Core while maintaining clean separation:

### Architecture Layers
1. **Core** (`src/core/`): Graph building infrastructure
   - Symbolic, Shape, Graph, Data engines
   - Compiler module for graph optimization
   
2. **Backends** (`src/backends/`): Execution targets (same level as core)
   - CPU, CUDA, WebGPU implementations
   - Use Core's compiler for optimization
   - Implement the enhanced Backend interface
   
3. **Tensor/NN** (`src/tensor/`, `src/nn/`): High-level APIs
   - Use Core for graph building
   - Backend-agnostic operations

### Key Design Principles
1. **Everything is a Compiler Pass**: Unified abstraction for all transformations
2. **Declarative Design**: Pattern matching uses a DSL, autodiff uses a gradient rules table
3. **Clean Separation**: Core remains focused on graph building; compiler handles optimization
4. **Type Safety**: Uses existing type-safe IDs (NodeId, ViewId, etc.)
5. **Error Handling**: Uses direct error literals (idiomatic Zig 0.14) following core module pattern

### Integration Points
- Core creates graphs using its existing engines
- Compiler takes a Core instance and optimizes its graph
- Backends execute the optimized graphs

This design allows the compiler to be developed independently while leveraging all of Core's infrastructure.

### Compiler-Specific Errors to Add

The following errors are used by the compiler and should be added to `src/core/errors.zig`:
```zig
// Compiler errors (add to ZingError enum)
UnsupportedOperation,  // Operation not supported by backend
NotElementwise,        // Operation is not elementwise
UnknownBackend,        // Backend name not recognized

// Add to describe() function
error.UnsupportedOperation => "Operation not supported by backend",
error.NotElementwise => "Operation is not elementwise",  
error.UnknownBackend => "Unknown backend specified",

// Add translateCompilerError() function similar to other modules
pub fn translateCompilerError(err: anytype) ZingError {
    return switch (err) {
        error.OutOfMemory => error.OutOfMemory,
        error.UnsupportedOperation => error.UnsupportedOperation,
        error.NotElementwise => error.NotElementwise,
        error.UnknownBackend => error.UnknownBackend,
        error.CompilationFailed => error.CompilationFailed,
        error.NodeNotFound => error.NodeNotFound,
        else => error.InternalError,
    };
}
```

## Implementation Checklist

### Phase 1: Core Compiler Infrastructure
- [ ] Create `src/core/compiler/types.zig` with all compiler types
- [ ] Create `src/core/compiler/engine.zig` with CompilerEngine
- [ ] Create `src/core/compiler/pattern.zig` with Pattern DSL
- [ ] Add `createCompiler()` method to Core
- [ ] Create basic pass structure in `src/core/compiler/passes/`

### Phase 2: Essential Compiler Passes  
- [ ] Implement dead code elimination pass
- [ ] Implement CSE (common subexpression elimination) pass
- [ ] Implement pattern matching pass using the DSL
- [ ] Implement memory planning pass
- [ ] Test passes with simple graphs

### Phase 3: CPU Backend
- [ ] Create `src/backends/cpu/backend.zig` 
- [ ] Implement vectorized kernels for all primitive ops
- [ ] Add pattern-based fusion (matmul, softmax, etc.)
- [ ] Benchmark against baseline

### Phase 4: Autodiff
- [ ] Implement autodiff as a compiler pass
- [ ] Add gradient rules for all primitive ops
- [ ] Test with simple neural networks
- [ ] Add gradient checkpointing pass

### Phase 5: CUDA Backend (Future)
- [ ] Create `src/backends/cuda/backend.zig`
- [ ] Implement runtime kernel generation
- [ ] Add aggressive elementwise fusion
- [ ] Benchmark against Luminal

This design provides a clear, implementable path that:
- Integrates cleanly with existing Core infrastructure
- Uses proven patterns from Luminal
- Maintains simplicity while enabling high performance
- Allows incremental development and testing