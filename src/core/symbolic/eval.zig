// Expression evaluation for shape calculations
const std = @import("std");
const types = @import("../types.zig");

pub const Evaluator = struct {
    pub fn init() Evaluator {
        return .{};
    }
    
    pub fn evaluate(self: *const Evaluator, expr: *types.Expr, bindings: ?std.StringHashMap(i64)) !i64 {
        
        switch (expr.tag) {
            .integer => return expr.data.integer,
            
            .symbol => {
                if (bindings) |b| {
                    if (b.get(expr.data.symbol.name)) |value| {
                        return value;
                    }
                }
                return error.UndefinedSymbol;
            },
            
            .add => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return lhs + rhs;
            },
            
            .multiply => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return lhs * rhs;
            },
            
            .max => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return @max(lhs, rhs);
            },
            
            .min => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return @min(lhs, rhs);
            },
            
            .equal => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return if (lhs == rhs) 1 else 0;
            },
            
            .subtract => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return lhs - rhs;
            },
            
            .divide => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                if (rhs == 0) return error.DivisionByZero;
                return @divFloor(lhs, rhs);
            },
            
            .mod => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                if (rhs == 0) return error.DivisionByZero;
                return @mod(lhs, rhs);
            },
            
            .less_than => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return if (lhs < rhs) 1 else 0;
            },
            
            .greater_than => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return if (lhs > rhs) 1 else 0;
            },
            
            .less_equal => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return if (lhs <= rhs) 1 else 0;
            },
            
            .greater_equal => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return if (lhs >= rhs) 1 else 0;
            },
            
            .not_equal => {
                const lhs = try self.evaluate(expr.data.binary.left, bindings);
                const rhs = try self.evaluate(expr.data.binary.right, bindings);
                return if (lhs != rhs) 1 else 0;
            },
        }
    }
};