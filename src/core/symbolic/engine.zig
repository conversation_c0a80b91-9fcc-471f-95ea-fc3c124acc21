// Symbolic engine for shape arithmetic
const std = @import("std");
const types = @import("../types.zig");

// Import sub-modules
const ExprCache = @import("cache.zig").ExprCache;
const Simplifier = @import("simplify.zig").Simplifier;
const Evaluator = @import("eval.zig").Evaluator;

// Import split modules for organization
const solver = @import("solver.zig");
const manipulation = @import("manipulation.zig");

/// SymbolicEngine manages symbolic expressions for shape arithmetic
pub const SymbolicEngine = struct {
    const Self = @This();
    
    allocator: std.mem.Allocator,
    
    // Expression management  
    expr_cache: ExprCache,      // From cache.zig
    simplifier: Simplifier,     // From simplify.zig
    evaluator: Evaluator,       // From eval.zig
    
    // Statistics tracking
    stats: SymbolicStats = .{},
    
    pub const SymbolicStats = struct {
        expr_count: usize = 0,
        cache_hits: usize = 0,
        cache_misses: usize = 0,
        simplifications: usize = 0,
    };
    
    pub fn init(allocator: std.mem.Allocator) !Self {
        const simplifier = try Simplifier.init(allocator);
        
        return Self{
            .allocator = allocator,
            .expr_cache = ExprCache.init(allocator),
            .simplifier = simplifier,
            .evaluator = Evaluator.init(),
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.expr_cache.deinit();
        self.simplifier.deinit();
    }
    
    pub fn clearCache(self: *Self) void {
        self.expr_cache.clear();
    }
    
    /// Reset the engine to initial state
    pub fn reset(self: *Self) void {
        self.expr_cache.clear();
        self.stats = .{};
    }
    
    /// Get engine statistics
    pub fn getStats(self: *const Self) SymbolicStats {
        return self.stats;
    }
    
    
    // Public API methods for tensor shape arithmetic only
    pub fn newSymbolExpr(self: *Self, name: []const u8) !*types.Expr {
        if (self.expr_cache.getSymbol(name)) |cached| {
            self.stats.cache_hits += 1;
            return cached;
        }
        self.stats.cache_misses += 1;
        
        const expr = try self.allocator.create(types.Expr);
        const symbol = try types.Symbol.intern(self.allocator, name);
        expr.* = .{
            .tag = .symbol,
            .data = .{ .symbol = symbol },
        };
        
        try self.expr_cache.putSymbol(name, expr);
        self.stats.expr_count += 1;
        return expr;
    }
    
    pub fn newIntegerExpr(self: *Self, value: i64) !*types.Expr {
        if (self.expr_cache.getInteger(value)) |cached| {
            self.stats.cache_hits += 1;
            return cached;
        }
        self.stats.cache_misses += 1;
        
        const expr = try self.allocator.create(types.Expr);
        expr.* = .{
            .tag = .integer,
            .data = .{ .integer = value },
        };
        
        try self.expr_cache.putInteger(value, expr);
        self.stats.expr_count += 1;
        return expr;
    }
    
    pub fn newBinaryExpr(self: *Self, op: types.BinaryOp, lhs: *types.Expr, rhs: *types.Expr) !*types.Expr {
        // Check cache first
        if (self.expr_cache.getBinary(op, lhs, rhs)) |cached| {
            self.stats.cache_hits += 1;
            return cached;
        }
        self.stats.cache_misses += 1;
        
        const tag: types.Expr.Tag = switch (op) {
            .add => .add,
            .multiply => .multiply,
            .max => .max,
            .min => .min,
            .equal => .equal,
            .subtract => .subtract,
            .divide => .divide,
            .mod => .mod,
            .less_than => .less_than,
            .greater_than => .greater_than,
            .less_equal => .less_equal,
            .greater_equal => .greater_equal,
            .not_equal => .not_equal,
            else => return error.UnsupportedOperation,
        };
        
        const expr = try self.allocator.create(types.Expr);
        expr.* = .{
            .tag = tag,
            .data = .{ .binary = .{ .left = lhs, .right = rhs } },
        };
        
        // Simplify the expression
        const simplified = try self.simplify(expr);
        try self.expr_cache.putBinary(op, lhs, rhs, simplified);
        self.stats.expr_count += 1;
        return simplified;
    }
    
    pub fn simplify(self: *Self, expr: *types.Expr) !*types.Expr {
        // Native simplification only - no egg FFI
        self.stats.simplifications += 1;
        return self.simplifier.simplify(expr);
    }
    
    pub fn evaluate(self: *Self, expr: *types.Expr, bindings: ?std.StringHashMap(i64)) !i64 {
        // Direct evaluation for shape calculations
        return self.evaluator.evaluate(expr, bindings);
    }
    
    /// Evaluate with validation (enhanced version of basic evaluate)
    pub fn evaluateWithValidation(self: *Self, expr: *types.Expr, bindings: ?std.StringHashMap(i64)) !i64 {
        try self.validateExpr(expr);
        return self.evaluator.evaluate(expr, bindings);
    }
    
    
    pub fn checkExprCompatibility(self: *Self, expr_a: *types.Expr, expr_b: *types.Expr) !bool {
        // Check if two expressions are compatible for operations
        // Try to simplify equality check
        const equal_expr = try self.newBinaryExpr(.equal, expr_a, expr_b);
        const simplified = try self.simplify(equal_expr);
        
        // If simplifies to a constant, we can determine compatibility
        if (simplified.tag == .integer) {
            return simplified.data.integer != 0;
        }
        
        // Otherwise, they're compatible if they're the same expression
        return self.exprEquals(expr_a, expr_b);
    }
    
    // Simple shape inference operations (no constraint solver needed)
    
    /// Infer unknown dimension in reshape operation (like -1 in PyTorch/NumPy)
    pub fn inferReshapeDimension(
        self: *Self,
        input_size: *types.Expr,
        known_dims: []const *types.Expr,
        unknown_index: usize,
        total_dims: usize,
    ) !*types.Expr {
        _ = unknown_index; // Not needed for the calculation
        _ = total_dims;
        
        // Calculate product of known dimensions
        var known_product = try self.newIntegerExpr(1);
        for (known_dims) |dim| {
            known_product = try self.newBinaryExpr(.multiply, known_product, dim);
        }
        
        // Unknown dimension = input_size / known_product  
        return self.newBinaryExpr(.divide, input_size, known_product);
    }
    
    /// Check if two shapes can be broadcast together (NumPy broadcasting rules)
    pub fn canBroadcast(self: *Self, shape1: []const *types.Expr, shape2: []const *types.Expr) !bool {
        const max_dims = @max(shape1.len, shape2.len);
        
        // Check compatibility from right to left (NumPy style)
        var i: usize = 0;
        while (i < max_dims) : (i += 1) {
            const idx1 = if (shape1.len >= i + 1) shape1.len - 1 - i else null;
            const idx2 = if (shape2.len >= i + 1) shape2.len - 1 - i else null;
            
            const dim1 = if (idx1) |idx| shape1[idx] else null;
            const dim2 = if (idx2) |idx| shape2[idx] else null;
            
            if (!try self.dimensionsCompatible(dim1, dim2)) {
                return false;
            }
        }
        return true;
    }
    
    fn dimensionsCompatible(self: *Self, dim1: ?*types.Expr, dim2: ?*types.Expr) !bool {
        // Missing dimensions are treated as 1
        if (dim1 == null or dim2 == null) return true;
        
        // Check if either dimension is 1 (broadcasts)
        if (try self.isOne(dim1.?) or try self.isOne(dim2.?)) return true;
        
        // Check if dimensions are symbolically equal
        return dim1.?.eql(dim2.?);
    }
    
    /// Check if expression evaluates to 1
    fn isOne(self: *Self, expr: *types.Expr) !bool {
        if (expr.tag == .integer) {
            return expr.data.integer == 1;
        }
        
        // For symbolic expressions, try to simplify
        const simplified = try self.simplify(expr);
        if (simplified.tag == .integer) {
            return simplified.data.integer == 1;
        }
        
        // Can't determine - be conservative and return false
        return false;
    }
    
    /// Compute convolution output dimension using standard formula
    pub fn computeConvOutputDim(
        self: *Self,
        input_dim: *types.Expr,
        kernel_size: *types.Expr,
        stride: *types.Expr,
        padding: *types.Expr,
    ) !*types.Expr {
        // Standard conv formula: floor((input + 2*padding - kernel) / stride) + 1
        
        const two = try self.newIntegerExpr(2);
        const one = try self.newIntegerExpr(1);
        
        // 2 * padding
        const double_padding = try self.newBinaryExpr(.multiply, two, padding);
        
        // input + 2*padding  
        const padded_input = try self.newBinaryExpr(.add, input_dim, double_padding);
        
        // (input + 2*padding - kernel)
        const numerator = try self.newBinaryExpr(.subtract, padded_input, kernel_size);
        
        // floor((input + 2*padding - kernel) / stride)
        const quotient = try self.newBinaryExpr(.divide, numerator, stride);
        
        // floor(...) + 1
        return self.newBinaryExpr(.add, quotient, one);
    }
    
    
    pub fn substituteSymbol(self: *Self, expr: *types.Expr, symbol: types.Symbol, value: i64) !*types.Expr {
        // Replace symbol with concrete value in expression
        switch (expr.tag) {
            .integer => return expr,
            .symbol => {
                if (std.mem.eql(u8, expr.data.symbol.name, symbol.name)) {
                    return self.newIntegerExpr(value);
                }
                return expr;
            },
            .add, .multiply, .max, .min, .equal,
            .subtract, .divide, .mod,
            .less_than, .greater_than, .less_equal, .greater_equal, .not_equal => {
                const left = try self.substituteSymbol(expr.data.binary.left, symbol, value);
                const right = try self.substituteSymbol(expr.data.binary.right, symbol, value);
                
                const op: types.BinaryOp = switch (expr.tag) {
                    .add => .add,
                    .multiply => .multiply,
                    .max => .max,
                    .min => .min,
                    .equal => .equal,
                    .subtract => .subtract,
                    .divide => .divide,
                    .mod => .mod,
                    .less_than => .less_than,
                    .greater_than => .greater_than,
                    .less_equal => .less_equal,
                    .greater_equal => .greater_equal,
                    .not_equal => .not_equal,
                    else => unreachable,
                };
                
                return self.newBinaryExpr(op, left, right);
            },
        }
    }
    
    
    fn exprEqual(self: *const Self, a: *const types.Expr, b: *const types.Expr) bool {
        if (a.tag != b.tag) return false;
        
        return switch (a.tag) {
            .integer => a.data.integer == b.data.integer,
            .symbol => std.mem.eql(u8, a.data.symbol.name, b.data.symbol.name),
            .add, .multiply, .max, .min, .equal,
            .subtract, .divide, .mod,
            .less_than, .greater_than, .less_equal, .greater_equal, .not_equal => {
                return self.exprEqual(a.data.binary.left, b.data.binary.left) and
                       self.exprEqual(a.data.binary.right, b.data.binary.right);
            },
        };
    }
    
    // Validation and evaluation
    pub fn validateExpr(self: *Self, expr: *types.Expr) !void {
        switch (expr.tag) {
            .integer => {
                // Validate integer is positive for dimensions
                if (expr.data.integer < 0) return error.InvalidDimension;
            },
            .symbol => {
                // Symbol names should be non-empty
                if (expr.data.symbol.name.len == 0) return error.InvalidSymbol;
            },
            .add, .multiply, .max, .min, .equal,
            .subtract, .divide, .mod,
            .less_than, .greater_than, .less_equal, .greater_equal, .not_equal => {
                const binary = expr.data.binary;
                try self.validateExpr(binary.left);
                try self.validateExpr(binary.right);
            },
        }
    }
    
    
    // CSE and optimization
    pub fn eliminateCommonSubexpressions(self: *Self, expr: *types.Expr) !*types.Expr {
        // Simple CSE implementation
        var seen = std.AutoHashMap(*types.Expr, *types.Expr).init(self.allocator);
        defer seen.deinit();
        return self.performCSE(expr, &seen);
    }
    
    fn performCSE(self: *Self, expr: *types.Expr, seen: *std.AutoHashMap(*types.Expr, *types.Expr)) !*types.Expr {
        if (seen.get(expr)) |cached| {
            return cached;
        }
        
        const result = switch (expr.tag) {
            .integer, .symbol => expr,
            .add, .multiply, .max, .min, .equal,
            .subtract, .divide, .mod,
            .less_than, .greater_than, .less_equal, .greater_equal, .not_equal => blk: {
                const binary = expr.data.binary;
                const left = try self.performCSE(binary.left, seen);
                const right = try self.performCSE(binary.right, seen);
                
                if (left == binary.left and right == binary.right) {
                    break :blk expr;
                }
                
                const op: types.BinaryOp = switch (expr.tag) {
                    .add => .add,
                    .multiply => .multiply,
                    .max => .max,
                    .min => .min,
                    .equal => .equal,
                    .subtract => .subtract,
                    .divide => .divide,
                    .mod => .mod,
                    .less_than => .less_than,
                    .greater_than => .greater_than,
                    .less_equal => .less_equal,
                    .greater_equal => .greater_equal,
                    .not_equal => .not_equal,
                    else => unreachable,
                };
                
                break :blk try self.newBinaryExpr(op, left, right);
            },
        };
        
        try seen.put(expr, result);
        return result;
    }
    
    pub fn exprEquals(self: *const Self, a: *types.Expr, b: *types.Expr) bool {
        // Use safe structural equality instead of unsafe pointer comparison
        _ = self;
        return a.eql(b);
    }
    

    // ============================================================================
    // CONSTRAINT SOLVING - Phase 1.1 Implementation
    // Based on algorithms from SymEngine's solve.cpp (solve_poly_linear function)
    // ============================================================================

    /// Solve equation for the specified variable
    /// 
    /// Uses comprehensive algebraic techniques including equality normalization,
    /// rational expressions, multiplication factoring, and polynomial solving.
    /// 
    /// Returns null if no solution exists, otherwise returns the solution expression.
    /// 
    /// Errors:
    /// - `ComplexLinearExpression`: Expression too complex for current implementation
    /// - `UnsupportedLinearOperation`: Operation not supported by solver
    /// - `NonLinearExpression`: Expression contains non-linear terms
    /// - Memory allocation errors from arena allocator
    pub fn solveForVariable(self: *Self, equation: *types.Expr, variable: types.Symbol) !?*types.Expr {
        // Safety guard: limit recursion depth to prevent stack overflow
        const max_depth = 10;
        return self.solveComplexEquationDepth(equation, variable, 0, max_depth);
    }

    /// Extract linear coefficients from expression in the form: a*variable + b
    /// Returns {coefficient: a, constant: b} where a might be null if variable doesn't appear
    /// 
    /// Errors:
    /// - `NonLinearExpression`: Expression contains non-linear terms (x^2, x*y, etc.)
    /// - `ComplexLinearExpression`: Expression structure too complex for current implementation
    /// - `UnsupportedLinearOperation`: Contains operations not supported by linear analysis
    /// - Memory allocation errors from arena allocator
    pub fn extractLinearCoefficients(self: *Self, expr: *types.Expr, variable: types.Symbol) !LinearCoefficients {
        switch (expr.tag) {
            .integer => {
                // Constant term only
                return LinearCoefficients{
                    .coefficient = null,
                    .constant = expr,
                };
            },
            .symbol => {
                if (std.mem.eql(u8, expr.data.symbol.name, variable.name)) {
                    // This is our variable: 1*variable + 0
                    return LinearCoefficients{
                        .coefficient = try self.newIntegerExpr(1),
                        .constant = try self.newIntegerExpr(0),
                    };
                } else {
                    // Different symbol: 0*variable + symbol
                    return LinearCoefficients{
                        .coefficient = null,
                        .constant = expr,
                    };
                }
            },
            .add => {
                // Recursively extract from both sides and combine
                const left_coeffs = try self.extractLinearCoefficients(expr.data.binary.left, variable);
                const right_coeffs = try self.extractLinearCoefficients(expr.data.binary.right, variable);
                
                // Combine coefficients and constants
                const combined_coeff = if (left_coeffs.coefficient != null and right_coeffs.coefficient != null)
                    try self.newBinaryExpr(.add, left_coeffs.coefficient.?, right_coeffs.coefficient.?)
                else if (left_coeffs.coefficient != null)
                    left_coeffs.coefficient
                else if (right_coeffs.coefficient != null)
                    right_coeffs.coefficient
                else
                    null;
                
                const combined_constant = try self.newBinaryExpr(.add, left_coeffs.constant, right_coeffs.constant);
                
                return LinearCoefficients{
                    .coefficient = combined_coeff,
                    .constant = combined_constant,
                };
            },
            .subtract => {
                // Similar to add but subtract the right side
                const left_coeffs = try self.extractLinearCoefficients(expr.data.binary.left, variable);
                const right_coeffs = try self.extractLinearCoefficients(expr.data.binary.right, variable);
                
                // Subtract right coefficients from left
                const combined_coeff = if (left_coeffs.coefficient != null and right_coeffs.coefficient != null)
                    try self.newBinaryExpr(.subtract, left_coeffs.coefficient.?, right_coeffs.coefficient.?)
                else if (left_coeffs.coefficient != null and right_coeffs.coefficient == null)
                    left_coeffs.coefficient
                else if (left_coeffs.coefficient == null and right_coeffs.coefficient != null)
                    try self.negate(right_coeffs.coefficient.?)
                else
                    null;
                
                const combined_constant = try self.newBinaryExpr(.subtract, left_coeffs.constant, right_coeffs.constant);
                
                return LinearCoefficients{
                    .coefficient = combined_coeff,
                    .constant = combined_constant,
                };
            },
            .multiply => {
                // Check if one side is variable and other is coefficient
                const left = expr.data.binary.left;
                const right = expr.data.binary.right;
                
                const left_has_var = try self.containsVariable(left, variable);
                const right_has_var = try self.containsVariable(right, variable);
                
                if (left_has_var and right_has_var) {
                    // Both sides contain variable - not linear
                    return error.NonLinearExpression;
                } else if (left_has_var) {
                    // right * variable pattern
                    if (try self.isSymbol(left, variable)) {
                        return LinearCoefficients{
                            .coefficient = right,
                            .constant = try self.newIntegerExpr(0),
                        };
                    } else {
                        // Complex expression involving variable - not supported yet
                        return error.ComplexLinearExpression;
                    }
                } else if (right_has_var) {
                    // left * variable pattern  
                    if (try self.isSymbol(right, variable)) {
                        return LinearCoefficients{
                            .coefficient = left,
                            .constant = try self.newIntegerExpr(0),
                        };
                    } else {
                        // Complex expression involving variable - not supported yet
                        return error.ComplexLinearExpression;
                    }
                } else {
                    // Neither side contains variable - this is just a constant
                    return LinearCoefficients{
                        .coefficient = null,
                        .constant = expr,
                    };
                }
            },
            .divide => {
                // Support division patterns: variable/constant and (a*x + b)/c
                const left = expr.data.binary.left;
                const right = expr.data.binary.right;
                
                if (try self.containsVariable(right, variable)) {
                    // Division by variable - not linear
                    return error.NonLinearExpression;
                }
                
                if (try self.isSymbol(left, variable)) {
                    // variable / constant = (1/constant) * variable
                    const one = try self.newIntegerExpr(1);
                    const inv_right = try self.newBinaryExpr(.divide, one, right);
                    return LinearCoefficients{
                        .coefficient = inv_right,
                        .constant = try self.newIntegerExpr(0),
                    };
                } else if (try self.containsVariable(left, variable)) {
                    // Enhanced: Handle (a*x + b) / c patterns
                    // Extract coefficients from numerator, then divide by denominator
                    const num_coeffs = self.extractLinearCoeffsSimple(left, variable) catch {
                        return error.UnsupportedLinearOperation;
                    };
                    
                    // Divide coefficients by denominator: (a*x + b) / c → (a/c)*x + (b/c)
                    const new_coeff = if (num_coeffs.coefficient) |coeff|
                        try self.newBinaryExpr(.divide, coeff, right)
                    else
                        null;
                        
                    const new_constant = try self.newBinaryExpr(.divide, num_coeffs.constant, right);
                    
                    return LinearCoefficients{
                        .coefficient = new_coeff,
                        .constant = new_constant,
                    };
                } else {
                    // Left side doesn't contain variable - treat as constant
                    return LinearCoefficients{
                        .coefficient = null,
                        .constant = expr,
                    };
                }
            },
            else => {
                // Other operations not supported for linear extraction
                return error.UnsupportedLinearOperation;
            },
        }
    }

    /// Simple coefficient extraction for basic patterns (no recursion)
    /// Used internally to avoid recursion issues in division handling
    fn extractLinearCoeffsSimple(self: *Self, expr: *types.Expr, variable: types.Symbol) !LinearCoefficients {
        switch (expr.tag) {
            .integer => {
                return LinearCoefficients{
                    .coefficient = null,
                    .constant = expr,
                };
            },
            .symbol => {
                if (std.mem.eql(u8, expr.data.symbol.name, variable.name)) {
                    return LinearCoefficients{
                        .coefficient = try self.newIntegerExpr(1),
                        .constant = try self.newIntegerExpr(0),
                    };
                } else {
                    return LinearCoefficients{
                        .coefficient = null,
                        .constant = expr,
                    };
                }
            },
            .multiply => {
                const left = expr.data.binary.left;
                const right = expr.data.binary.right;
                
                const left_has_var = try self.containsVariable(left, variable);
                const right_has_var = try self.containsVariable(right, variable);
                
                if (left_has_var and right_has_var) {
                    return error.NonLinearExpression;
                } else if (left_has_var and try self.isSymbol(left, variable)) {
                    return LinearCoefficients{
                        .coefficient = right,
                        .constant = try self.newIntegerExpr(0),
                    };
                } else if (right_has_var and try self.isSymbol(right, variable)) {
                    return LinearCoefficients{
                        .coefficient = left,
                        .constant = try self.newIntegerExpr(0),
                    };
                } else {
                    return LinearCoefficients{
                        .coefficient = null,
                        .constant = expr,
                    };
                }
            },
            else => {
                // For other operations, fall back to unsupported for safety
                return error.UnsupportedLinearOperation;
            },
        }
    }

    const LinearCoefficients = struct {
        coefficient: ?*types.Expr, // Coefficient of the variable (null if variable doesn't appear)
        constant: *types.Expr,     // Constant term
    };

    /// Check if expression contains the given variable
    pub fn containsVariable(self: *Self, expr: *types.Expr, variable: types.Symbol) !bool {
        switch (expr.tag) {
            .integer => return false,
            .symbol => return std.mem.eql(u8, expr.data.symbol.name, variable.name),
            .add, .subtract, .multiply, .divide, .mod, 
            .equal, .not_equal, .less_than, .greater_than, .less_equal, .greater_equal,
            .max, .min => {
                const left_has = try self.containsVariable(expr.data.binary.left, variable);
                const right_has = try self.containsVariable(expr.data.binary.right, variable);
                return left_has or right_has;
            },
        }
    }

    /// Check if expression is exactly the given symbol
    fn isSymbol(self: *Self, expr: *types.Expr, symbol: types.Symbol) !bool {
        _ = self;
        return expr.tag == .symbol and std.mem.eql(u8, expr.data.symbol.name, symbol.name);
    }

    /// Check if expression evaluates to zero
    pub fn isZero(self: *Self, expr: *types.Expr) !bool {
        if (expr.tag == .integer) {
            return expr.data.integer == 0;
        }
        
        // Try to simplify and check again
        const simplified = try self.simplify(expr);
        if (simplified.tag == .integer) {
            return simplified.data.integer == 0;
        }
        
        // Conservative: can't determine
        return false;
    }

    /// Create negation of expression: -expr
    pub fn negate(self: *Self, expr: *types.Expr) !*types.Expr {
        const zero = try self.newIntegerExpr(0);
        return self.newBinaryExpr(.subtract, zero, expr);
    }

    /// Solve system of linear constraints for automatic shape inference
    /// This is particularly useful for transformer attention mechanisms
    /// where multiple dimension constraints must be satisfied simultaneously
    pub fn solveConstraintSystem(
        self: *Self,
        constraints: []const *types.Expr, // List of equations = 0
        variables: []const types.Symbol,   // Variables to solve for
    ) !?std.StringHashMapUnmanaged(*types.Expr) {
        if (constraints.len == 0 or variables.len == 0) {
            return null;
        }

        var solution = std.StringHashMapUnmanaged(*types.Expr){};
        var solved_vars = std.StringHashMapUnmanaged(void){};
        defer solved_vars.deinit(self.allocator);

        // Simple greedy approach: try to solve each constraint for each unsolved variable
        for (constraints) |constraint| {
            for (variables) |variable| {
                // Skip if we already solved this variable
                if (solved_vars.contains(variable.name)) continue;

                // Try to solve this constraint for this variable
                if (try self.solveForVariable(constraint, variable)) |solution_expr| {
                    // Store the solution using duped string key to handle the CString->[]const u8 conversion
                    const key = try self.allocator.dupe(u8, variable.name);
                    try solution.put(self.allocator, key, solution_expr);
                    try solved_vars.put(self.allocator, variable.name, {});
                    break; // Move to next constraint
                }
            }
        }

        if (solution.count() == 0) {
            solution.deinit(self.allocator);
            return null;
        }

        return solution;
    }

    // ============================================================================
    // POLYNOMIAL EXPANSION - Phase 1.2 Implementation  
    // Based on algorithms from SymEngine's expand.cpp (expand_power, expand_add_mul functions)
    // ============================================================================

    /// Expand polynomial expressions for better simplification
    /// Essential for complex tensor operations like attention mechanisms
    pub fn expandExpression(self: *Self, expr: *types.Expr) anyerror!*types.Expr {
        switch (expr.tag) {
            .integer, .symbol => return expr, // Already expanded
            
            .add => {
                // Recursively expand both sides
                const left = try self.expandExpression(expr.data.binary.left);
                const right = try self.expandExpression(expr.data.binary.right);
                return self.newBinaryExpr(.add, left, right);
            },
            
            .subtract => {
                // Recursively expand both sides
                const left = try self.expandExpression(expr.data.binary.left);
                const right = try self.expandExpression(expr.data.binary.right);
                return self.newBinaryExpr(.subtract, left, right);
            },
            
            .multiply => {
                // This is where the real expansion happens
                return self.expandMultiplication(expr.data.binary.left, expr.data.binary.right);
            },
            
            .divide => {
                // Expand numerator but leave denominator factored for now
                const numerator = try self.expandExpression(expr.data.binary.left);
                const denominator = expr.data.binary.right; // Keep factored
                return self.newBinaryExpr(.divide, numerator, denominator);
            },
            
            else => {
                // For other operations (comparisons, etc.), recursively expand operands
                const left = try self.expandExpression(expr.data.binary.left);
                const right = try self.expandExpression(expr.data.binary.right);
                const op: types.BinaryOp = switch (expr.tag) {
                    .equal => .equal,
                    .not_equal => .not_equal,
                    .less_than => .less_than,
                    .greater_than => .greater_than,
                    .less_equal => .less_equal,
                    .greater_equal => .greater_equal,
                    .max => .max,
                    .min => .min,
                    .mod => .mod,
                    else => return error.UnsupportedExpansionOperation,
                };
                return self.newBinaryExpr(op, left, right);
            },
        }
    }

    /// Expand multiplication: (a + b) * (c + d) = ac + ad + bc + bd
    /// This is the core of polynomial expansion, critical for ML calculations
    fn expandMultiplication(self: *Self, left: *types.Expr, right: *types.Expr) anyerror!*types.Expr {
        // First recursively expand both operands
        const expanded_left = try self.expandExpression(left);
        const expanded_right = try self.expandExpression(right);
        
        // Now distribute: left * right
        return self.distributeMultiplication(expanded_left, expanded_right);
    }

    /// Distribute multiplication over addition: A * (B + C) = A*B + A*C
    fn distributeMultiplication(self: *Self, left: *types.Expr, right: *types.Expr) anyerror!*types.Expr {
        // Case 1: Right side is addition - distribute left over it
        if (right.tag == .add) {
            // left * (b + c) = (left * b) + (left * c)
            const left_times_b = try self.distributeMultiplication(left, right.data.binary.left);
            const left_times_c = try self.distributeMultiplication(left, right.data.binary.right);
            return self.newBinaryExpr(.add, left_times_b, left_times_c);
        }
        
        // Case 2: Right side is subtraction - distribute left over it
        if (right.tag == .subtract) {
            // left * (b - c) = (left * b) - (left * c)
            const left_times_b = try self.distributeMultiplication(left, right.data.binary.left);
            const left_times_c = try self.distributeMultiplication(left, right.data.binary.right);
            return self.newBinaryExpr(.subtract, left_times_b, left_times_c);
        }
        
        // Case 3: Left side is addition - distribute right over it
        if (left.tag == .add) {
            // (a + b) * right = (a * right) + (b * right)
            const a_times_right = try self.distributeMultiplication(left.data.binary.left, right);
            const b_times_right = try self.distributeMultiplication(left.data.binary.right, right);
            return self.newBinaryExpr(.add, a_times_right, b_times_right);
        }
        
        // Case 4: Left side is subtraction - distribute right over it
        if (left.tag == .subtract) {
            // (a - b) * right = (a * right) - (b * right)
            const a_times_right = try self.distributeMultiplication(left.data.binary.left, right);
            const b_times_right = try self.distributeMultiplication(left.data.binary.right, right);
            return self.newBinaryExpr(.subtract, a_times_right, b_times_right);
        }
        
        // Case 5: Neither side is addition/subtraction - just multiply
        return self.newBinaryExpr(.multiply, left, right);
    }

    /// Substitute one expression for another in a larger expression
    /// Useful for applying solved constraint values to other expressions
    pub fn substituteExpression(self: *Self, expr: *types.Expr, target: *types.Expr, replacement: *types.Expr) anyerror!*types.Expr {
        // Check if this expression is exactly the target
        if (self.exprEquals(expr, target)) {
            return replacement;
        }
        
        switch (expr.tag) {
            .integer, .symbol => return expr, // Base cases
            
            .add, .subtract, .multiply, .divide, .mod,
            .equal, .not_equal, .less_than, .greater_than, .less_equal, .greater_equal,
            .max, .min => {
                // Recursively substitute in both operands
                const left = try self.substituteExpression(expr.data.binary.left, target, replacement);
                const right = try self.substituteExpression(expr.data.binary.right, target, replacement);
                
                const op: types.BinaryOp = switch (expr.tag) {
                    .add => .add,
                    .subtract => .subtract,
                    .multiply => .multiply,
                    .divide => .divide,
                    .mod => .mod,
                    .equal => .equal,
                    .not_equal => .not_equal,
                    .less_than => .less_than,
                    .greater_than => .greater_than,
                    .less_equal => .less_equal,
                    .greater_equal => .greater_equal,
                    .max => .max,
                    .min => .min,
                    else => unreachable,
                };
                
                return self.newBinaryExpr(op, left, right);
            },
        }
    }

    /// Collect like terms in an expanded expression: 2*x + 3*x = 5*x
    /// This is essential for keeping expanded expressions manageable in ML contexts
    pub fn collectLikeTerms(self: *Self, expr: *types.Expr) anyerror!*types.Expr {
        // For now, implement a simple version that handles basic cases
        // A full implementation would use a term collection algorithm
        
        switch (expr.tag) {
            .integer, .symbol => return expr,
            
            .add => {
                // Recursively collect in both sides first
                const left = try self.collectLikeTerms(expr.data.binary.left);
                const right = try self.collectLikeTerms(expr.data.binary.right);
                
                // Try to combine if they're like terms
                return self.tryCombineLikeTerms(left, right, .add);
            },
            
            .subtract => {
                const left = try self.collectLikeTerms(expr.data.binary.left);
                const right = try self.collectLikeTerms(expr.data.binary.right);
                
                return self.tryCombineLikeTerms(left, right, .subtract);
            },
            
            else => {
                // For other operations, recursively collect in operands
                const left = try self.collectLikeTerms(expr.data.binary.left);
                const right = try self.collectLikeTerms(expr.data.binary.right);
                
                const op: types.BinaryOp = switch (expr.tag) {
                    .multiply => .multiply,
                    .divide => .divide,
                    .mod => .mod,
                    .equal => .equal,
                    .not_equal => .not_equal,
                    .less_than => .less_than,
                    .greater_than => .greater_than,
                    .less_equal => .less_equal,
                    .greater_equal => .greater_equal,
                    .max => .max,
                    .min => .min,
                    else => return error.UnsupportedCollectionOperation,
                };
                
                return self.newBinaryExpr(op, left, right);
            },
        }
    }

    /// Try to combine like terms: 2*x + 3*x = 5*x
    fn tryCombineLikeTerms(self: *Self, left: *types.Expr, right: *types.Expr, operation: types.BinaryOp) anyerror!*types.Expr {
        // Simple case: two integers
        if (left.tag == .integer and right.tag == .integer) {
            const result = switch (operation) {
                .add => left.data.integer + right.data.integer,
                .subtract => left.data.integer - right.data.integer,
                else => {
                    // Not a combinable operation, return as-is
                    return self.newBinaryExpr(operation, left, right);
                },
            };
            return self.newIntegerExpr(result);
        }
        
        // For now, just return the operation as-is
        // A full implementation would check for patterns like a*x ± b*x
        return self.newBinaryExpr(operation, left, right);
    }

    /// Factorize expression (opposite of expansion) for more compact representation
    /// Useful for final simplified forms in ML dimension calculations
    pub fn factorizeExpression(self: *Self, expr: *types.Expr) anyerror!*types.Expr {
        // This is a complex operation - for now, implement basic common factor extraction
        switch (expr.tag) {
            .integer, .symbol => return expr,
            
            .add => {
                // Look for common factors in addition: ax + bx = x(a + b)
                return self.extractCommonFactors(expr);
            },
            
            else => {
                // For other operations, recursively factorize operands
                const left = try self.factorizeExpression(expr.data.binary.left);
                const right = try self.factorizeExpression(expr.data.binary.right);
                
                const op: types.BinaryOp = switch (expr.tag) {
                    .subtract => .subtract,
                    .multiply => .multiply,
                    .divide => .divide,
                    .mod => .mod,
                    .equal => .equal,
                    .not_equal => .not_equal,
                    .less_than => .less_than,
                    .greater_than => .greater_than,
                    .less_equal => .less_equal,
                    .greater_equal => .greater_equal,
                    .max => .max,
                    .min => .min,
                    else => return error.UnsupportedFactorizationOperation,
                };
                
                return self.newBinaryExpr(op, left, right);
            },
        }
    }

    /// Extract common factors from addition: ax + bx = x(a + b)
    fn extractCommonFactors(self: *Self, expr: *types.Expr) anyerror!*types.Expr {
        // For now, return the expression as-is
        // A full implementation would analyze the terms and extract common factors
        _ = self; // Mark self as intentionally unused for now
        return expr;
    }

    /// Full polynomial expansion with collection: expand and then collect like terms
    /// This is the main entry point for polynomial operations in ML contexts

    // ============================================================================
    // ENHANCED ALGEBRAIC MANIPULATION - Phase 1.2b Implementation
    // Based on algorithms from SymEngine's solve.cpp (isolate_variable, solve_poly functions)
    // ============================================================================

    /// Enhanced equation isolation - move terms step by step
    /// Algorithm adapted from SymEngine's isolate_variable() in symengine/solve.cpp
    pub fn isolateVariable(self: *Self, equation: *types.Expr, variable: types.Symbol) anyerror!?*types.Expr {
        // Step 1: Move all terms with variable to left, constants to right
        const rearranged = try self.rearrangeEquation(equation, variable);
        
        // Step 2: Try to isolate by clearing nested structures
        const cleared = try self.clearNestedStructures(rearranged, variable);
        
        // Step 3: Apply basic linear solving to the cleared equation
        return self.solveForVariable(cleared, variable);
    }

    /// Rearrange equation to collect variable terms
    /// Example: a = (b + x) / c → a*c = b + x → a*c - b = x
    pub fn rearrangeEquation(self: *Self, equation: *types.Expr, variable: types.Symbol) anyerror!*types.Expr {
        if (equation.tag != .equal) return equation; // Not an equation, return as-is
        
        const left = equation.data.binary.left;
        const right = equation.data.binary.right;
        
        // Determine which side has the variable
        const left_has_var = try self.containsVariable(left, variable);
        const right_has_var = try self.containsVariable(right, variable);
        
        if (left_has_var and right_has_var) {
            // Both sides have variable - move right side to left
            // a*x + b = c*x + d → a*x - c*x = d - b → (a-c)*x = d - b
            const moved_term = try self.negate(right);
            const new_left = try self.newBinaryExpr(.add, left, moved_term);
            const zero = try self.newIntegerExpr(0);
            return self.newBinaryExpr(.equal, new_left, zero);
        } else if (right_has_var) {
            // Variable on right - swap sides
            return self.newBinaryExpr(.equal, right, left);
        } else {
            // Variable on left (or neither) - already good
            return equation;
        }
    }

    /// Clear nested structures like divisions to simplify solving
    /// Example: (a + x) / b = c → a + x = c * b
    pub fn clearNestedStructures(self: *Self, equation: *types.Expr, variable: types.Symbol) anyerror!*types.Expr {
        if (equation.tag != .equal) return equation;
        
        const left = equation.data.binary.left;
        const right = equation.data.binary.right;
        
        // Check if left side has a division containing the variable
        if (left.tag == .divide) {
            const numerator = left.data.binary.left;
            const denominator = left.data.binary.right;
            
            if (try self.containsVariable(numerator, variable)) {
                // (a + x) / b = c → a + x = c * b
                const new_right = try self.newBinaryExpr(.multiply, right, denominator);
                return self.newBinaryExpr(.equal, numerator, new_right);
            }
        }
        
        // Check if we need to clear additions/subtractions around divisions
        if (left.tag == .add or left.tag == .subtract) {
            return self.clearArithmeticNesting(equation, variable);
        }
        
        return equation;
    }

    /// Clear arithmetic nesting around variables
    /// Example: (a + x) / b + c = d → (a + x) / b = d - c → a + x = (d - c) * b
    pub fn clearArithmeticNesting(self: *Self, equation: *types.Expr, variable: types.Symbol) anyerror!*types.Expr {
        const left = equation.data.binary.left;
        const right = equation.data.binary.right;
        
        // Look for patterns like: (expr_with_var / denom) + constant = result
        if (left.tag == .add) {
            const add_left = left.data.binary.left;
            const add_right = left.data.binary.right;
            
            // Check if one side is a division with the variable
            if (add_left.tag == .divide and try self.containsVariable(add_left.data.binary.left, variable)) {
                // (expr / denom) + constant = result → expr / denom = result - constant
                const new_right = try self.newBinaryExpr(.subtract, right, add_right);
                return self.newBinaryExpr(.equal, add_left, new_right);
            }
            
            if (add_right.tag == .divide and try self.containsVariable(add_right.data.binary.left, variable)) {
                // constant + (expr / denom) = result → expr / denom = result - constant
                const new_right = try self.newBinaryExpr(.subtract, right, add_left);
                return self.newBinaryExpr(.equal, add_right, new_right);
            }
        }
        
        return equation;
    }



    /// Apply different algebraic manipulation strategies
    pub fn applyManipulationStrategy(self: *Self, equation: *types.Expr, variable: types.Symbol, strategy: u32) anyerror!*types.Expr {
        _ = variable; // May be used in future strategies
        
        return switch (strategy % 3) {
            0 => try self.expandExpression(equation),      // Try expansion
            1 => try self.factorizeExpression(equation),   // Try factorization  
            2 => try self.simplify(equation),              // Try simplification
            else => equation,
        };
    }

    
    /// Internal recursive solver with depth limiting
    fn solveComplexEquationDepth(self: *Self, equation: *types.Expr, variable: types.Symbol, depth: u32, max_depth: u32) anyerror!?*types.Expr {
        // Check recursion depth
        if (depth >= max_depth) {
            return null; // Too deep - give up to prevent stack overflow
        }
        
        // Step 1: Handle equality by converting a = b to solve(a - b = 0, var)
        if (equation.tag == .equal) {
            const left = equation.data.binary.left;
            const right = equation.data.binary.right;
            
            // Check if already normalized (right side is zero)
            if (right.tag == .integer and right.data.integer == 0) {
                // Already in form f = 0, continue with f
                const f = left;
                
                // Step 2: Check if variable is present
                if (!try self.containsVariable(f, variable)) {
                    return null; // No solution if variable not present
                }
                
                // Continue with the rest of the algorithm using f
                return self.solveSingleExpression(f, variable, depth, max_depth);
            } else {
                // Convert a = b to (a - b) = 0
                const diff = try self.newBinaryExpr(.subtract, left, right);
                const zero_expr = try self.newIntegerExpr(0);
                const normalized = try self.newBinaryExpr(.equal, diff, zero_expr);
                return self.solveComplexEquationDepth(normalized, variable, depth + 1, max_depth);
            }
        }
        
        // If not an equality, treat the expression as f = 0
        // This handles cases where we get a bare expression that should equal zero
        const f = equation;
        
        // Check if variable is present
        if (!try self.containsVariable(f, variable)) {
            return null; // No solution if variable not present
        }
        
        // Continue with solving f = 0
        return self.solveSingleExpression(f, variable, depth, max_depth);
    }
    
    /// Handle solving a single expression f = 0 for the given variable
    fn solveSingleExpression(self: *Self, f: *types.Expr, variable: types.Symbol, depth: u32, max_depth: u32) anyerror!?*types.Expr {
        // Step 3: Handle multiplication: solve(a*b, var) → solve(a, var) ∪ solve(b, var)
        if (f.tag == .multiply) {
            const left_factor = f.data.binary.left;
            const right_factor = f.data.binary.right;
            
            // Try solving each factor (with depth limiting)
            const zero = try self.newIntegerExpr(0);
            const left_eq = try self.newBinaryExpr(.equal, left_factor, zero);
            const right_eq = try self.newBinaryExpr(.equal, right_factor, zero);
            
            if (self.solveComplexEquationDepth(left_eq, variable, depth + 1, max_depth) catch null) |sol| {
                return sol;
            }
            if (self.solveComplexEquationDepth(right_eq, variable, depth + 1, max_depth) catch null) |sol| {
                return sol;
            }
            return null;
        }
        
        // Step 4: Delegate to rational solver (SymEngine: solve_rational())
        return self.solveRational(f, variable);
    }
    
    /// Rational expression solver following SymEngine's solve_rational() algorithm
    /// Algorithm adapted from SymEngine's solve_rational() in symengine/solve.cpp
    fn solveRational(self: *Self, f: *types.Expr, variable: types.Symbol) anyerror!?*types.Expr {
        // Step 1: Separate numerator and denominator (SymEngine: as_numer_denom())
        const numer_denom = try self.asNumeratorDenominator(f);
        const numerator = numer_denom.numerator;
        const denominator = numer_denom.denominator;
        
        // Step 2: Check if variable is in denominator
        if (try self.containsVariable(denominator, variable)) {
            // Variable in denominator - cannot solve with simple linear methods
            // SymEngine would compute set_complement(solve(num), solve(den))
            // For now, we return null to indicate unsolvable with current methods
            return null;
        }
        
        // Step 3: Variable only in numerator - delegate to polynomial solver
        return self.solvePolynomial(numerator, variable);
    }
    
    /// Separate expression into numerator and denominator (simplified approach)
    /// Algorithm inspired by SymEngine's as_numer_denom() function
    fn asNumeratorDenominator(self: *Self, expr: *types.Expr) anyerror!struct { numerator: *types.Expr, denominator: *types.Expr } {
        return switch (expr.tag) {
            .divide => .{
                .numerator = expr.data.binary.left,
                .denominator = expr.data.binary.right,
            },
            else => {
                // For simplicity, treat all non-division expressions as numerator with denominator = 1
                // This avoids complex recursion and handles the most common cases
                const one = try self.newIntegerExpr(1);
                return .{ .numerator = expr, .denominator = one };
            },
        };
    }
    
    /// Polynomial solver following SymEngine's solve_poly() approach
    /// Algorithm adapted from SymEngine's solve_poly() in symengine/solve.cpp
    fn solvePolynomial(self: *Self, f: *types.Expr, variable: types.Symbol) anyerror!?*types.Expr {
        // Extract polynomial coefficients (SymEngine: extract_coeffs())
        const coeffs = try self.extractPolynomialCoefficients(f, variable);
        
        // Check for empty coefficients (should not happen, but safety check)
        if (coeffs.len == 0) {
            return null;
        }
        
        // Delegate to specialized polynomial solvers based on degree
        const degree = coeffs.len - 1;
        
        return switch (degree) {
            0 => {
                // Constant equation: c = 0
                // If c ≠ 0, no solution; if c = 0, any value works (but we return null for "no constraint")
                return null;
            },
            1 => self.solveLinearPolynomial(coeffs),
            2 => self.solveQuadraticPolynomial(coeffs),
            else => {
                // Higher degree polynomials not implemented yet
                return null;
            },
        };
    }
    
    /// Extract polynomial coefficients from expression (simplified approach)
    /// Returns coefficients array where index i contains coefficient of variable^i
    /// Uses idiomatic Zig error handling and arena allocation
    fn extractPolynomialCoefficients(self: *Self, expr: *types.Expr, variable: types.Symbol) anyerror![]const *types.Expr {
        // Use existing linear coefficient extraction as foundation
        // This is safer and follows our existing patterns
        const linear_coeffs = self.extractLinearCoefficients(expr, variable) catch |err| switch (err) {
            error.NonLinearExpression, error.ComplexLinearExpression, error.UnsupportedLinearOperation => {
                // For complex expressions, propagate the error instead of returning empty array
                return err;
            },
            else => return err,
        };
        
        // Convert LinearCoefficients to polynomial coefficient array
        var coeffs = try self.allocator.alloc(*types.Expr, 2);
        coeffs[0] = linear_coeffs.constant; // x^0 coefficient
        coeffs[1] = linear_coeffs.coefficient orelse try self.newIntegerExpr(0); // x^1 coefficient
        
        return coeffs;
    }
    
    
    /// Helper function to simplify addition during coefficient extraction
    fn simplifyAddition(self: *Self, left: *types.Expr, right: *types.Expr) anyerror!*types.Expr {
        const sum = try self.newBinaryExpr(.add, left, right);
        return self.simplify(sum);
    }
    
    /// Helper function to simplify subtraction during coefficient extraction
    fn simplifySubtraction(self: *Self, left: *types.Expr, right: *types.Expr) anyerror!*types.Expr {
        const diff = try self.newBinaryExpr(.subtract, left, right);
        return self.simplify(diff);
    }
    
    /// Solve linear polynomial: ax + b = 0 → x = -b/a
    /// Following SymEngine's solve_poly_linear() algorithm
    fn solveLinearPolynomial(self: *Self, coeffs: []const *types.Expr) anyerror!?*types.Expr {
        if (coeffs.len != 2) return null;
        
        const constant = coeffs[0]; // b
        const linear = coeffs[1];   // a
        
        // Check if linear coefficient is zero (would mean no variable term)
        if (try self.isZero(linear)) return null;
        
        // Solve ax + b = 0 → x = -b/a
        const neg_constant = try self.newBinaryExpr(.subtract, try self.newIntegerExpr(0), constant);
        const solution = try self.newBinaryExpr(.divide, neg_constant, linear);
        
        return self.simplify(solution);
    }
    
    /// Solve quadratic polynomial: ax² + bx + c = 0
    /// Following SymEngine's solve_poly_quadratic() algorithm  
    fn solveQuadraticPolynomial(self: *Self, coeffs: []const *types.Expr) anyerror!?*types.Expr {
        // Quadratic solving is complex - for now return null
        // This would need discriminant calculation and square root handling
        _ = self;
        _ = coeffs;
        return null;
    }

    /// Check if an equation can be solved (without actually solving)
    pub fn canSolveEquation(self: *Self, equation: *types.Expr, variable: types.Symbol) anyerror!bool {
        // Quick check: is variable present?
        if (!try self.containsVariable(equation, variable)) {
            return false;
        }
        
        // Try enhanced solving
        const solution = try self.solveForVariable(equation, variable);
        return solution != null;
    }

};