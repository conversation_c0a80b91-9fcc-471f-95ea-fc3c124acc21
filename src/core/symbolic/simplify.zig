// Native simplification rules for shape arithmetic
const std = @import("std");
const types = @import("../types.zig");

pub const Simplifier = struct {
    allocator: std.mem.Allocator,
    
    pub fn init(allocator: std.mem.Allocator) !Simplifier {
        return .{ 
            .allocator = allocator,
        };
    }
    
    pub fn deinit(self: *Simplifier) void {
        _ = self;
    }
    
    pub fn simplify(self: *Simplifier, expr: *types.Expr) !*types.Expr {
        switch (expr.tag) {
            .integer, .symbol => return expr,
            
            .add => return self.simplifyBinaryOp(expr, SimplifyAdd),
            .multiply => return self.simplifyBinaryOp(expr, SimplifyMultiply),
            .max => return self.simplifyBinaryOp(expr, SimplifyMax),
            .min => return self.simplifyBinaryOp(expr, SimplifyMin),
            .equal => return self.simplifyBinaryOp(expr, SimplifyEqual),
            .subtract => return self.simplifyBinaryOp(expr, SimplifySubtract),
            .divide => return self.simplifyBinaryOp(expr, SimplifyDivide),
            .mod => return self.simplifyBinaryOp(expr, SimplifyMod),
            .less_than => return self.simplifyComparison(expr, .less_than),
            .greater_than => return self.simplifyComparison(expr, .greater_than),
            .less_equal => return self.simplifyComparison(expr, .less_equal),
            .greater_equal => return self.simplifyComparison(expr, .greater_equal),
            .not_equal => return self.simplifyComparison(expr, .not_equal),
        }
    }
    
    // Generic binary operation simplifier
    fn simplifyBinaryOp(self: *Simplifier, expr: *types.Expr, comptime SimplifierType: type) !*types.Expr {
        const lhs = expr.data.binary.left;
        const rhs = expr.data.binary.right;
        
        // Apply operation-specific rules
        if (try SimplifierType.simplifySpecific(self, lhs, rhs)) |result| {
            return result;
        }
        
        // Common rules for all binary operations
        
        // If both sides are the same expression
        if (exprEquals(lhs, rhs)) {
            if (try SimplifierType.simplifySelfOp(self, lhs)) |result| {
                return result;
            }
        }
        
        // Constant folding
        if (lhs.tag == .integer and rhs.tag == .integer) {
            const result = try self.allocator.create(types.Expr);
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = try SimplifierType.computeConstant(lhs.data.integer, rhs.data.integer) },
            };
            return result;
        }
        
        return expr;
    }
    
    // Generic comparison simplifier
    fn simplifyComparison(self: *Simplifier, expr: *types.Expr, op: types.Expr.Tag) !*types.Expr {
        const lhs = expr.data.binary.left;
        const rhs = expr.data.binary.right;
        
        // If both sides are the same expression
        if (exprEquals(lhs, rhs)) {
            const result = try self.allocator.create(types.Expr);
            const value: i64 = switch (op) {
                .less_than, .greater_than, .not_equal => 0,
                .less_equal, .greater_equal, .equal => 1,
                else => unreachable,
            };
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = value },
            };
            return result;
        }
        
        // Constant comparison
        if (lhs.tag == .integer and rhs.tag == .integer) {
            const result = try self.allocator.create(types.Expr);
            const value: i64 = switch (op) {
                .less_than => if (lhs.data.integer < rhs.data.integer) 1 else 0,
                .greater_than => if (lhs.data.integer > rhs.data.integer) 1 else 0,
                .less_equal => if (lhs.data.integer <= rhs.data.integer) 1 else 0,
                .greater_equal => if (lhs.data.integer >= rhs.data.integer) 1 else 0,
                .not_equal => if (lhs.data.integer != rhs.data.integer) 1 else 0,
                .equal => if (lhs.data.integer == rhs.data.integer) 1 else 0,
                else => unreachable,
            };
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = value },
            };
            return result;
        }
        
        return expr;
    }
    
    fn exprEquals(a: *types.Expr, b: *types.Expr) bool {
        // Use safe structural equality instead of unsafe pointer comparison
        return a.eql(b);
    }
    
    // Simplifier for addition
    const SimplifyAdd = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            // 0 + x = x
            if (lhs.tag == .integer and lhs.data.integer == 0) return rhs;
            // x + 0 = x
            if (rhs.tag == .integer and rhs.data.integer == 0) return lhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = self;
            _ = expr;
            // x + x could be simplified to 2*x, but we don't have that pattern yet
            return null;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            return lhs + rhs;
        }
    };
    
    // Simplifier for multiplication
    const SimplifyMultiply = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            // 0 * x = 0
            if (lhs.tag == .integer and lhs.data.integer == 0) return lhs;
            // x * 0 = 0
            if (rhs.tag == .integer and rhs.data.integer == 0) return rhs;
            // 1 * x = x
            if (lhs.tag == .integer and lhs.data.integer == 1) return rhs;
            // x * 1 = x
            if (rhs.tag == .integer and rhs.data.integer == 1) return lhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = self;
            _ = expr;
            // x * x could be simplified to x^2, but we don't have power operation
            return null;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            return lhs * rhs;
        }
    };
    
    // Simplifier for max
    const SimplifyMax = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            // max(x, 1) where x > 1 = x (for broadcasting)
            if (rhs.tag == .integer and rhs.data.integer == 1) {
                if (lhs.tag == .integer and lhs.data.integer > 1) return lhs;
            }
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = self;
            // max(x, x) = x
            return expr;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            return @max(lhs, rhs);
        }
    };
    
    // Simplifier for min
    const SimplifyMin = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            _ = lhs;
            _ = rhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = self;
            // min(x, x) = x
            return expr;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            return @min(lhs, rhs);
        }
    };
    
    // Simplifier for equality
    const SimplifyEqual = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            _ = lhs;
            _ = rhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            // x == x => 1 (true)
            _ = expr;
            const result = try self.allocator.create(types.Expr);
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = 1 },
            };
            return result;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            return if (lhs == rhs) 1 else 0;
        }
    };
    
    // Simplifier for subtraction
    const SimplifySubtract = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            // x - 0 = x
            if (rhs.tag == .integer and rhs.data.integer == 0) return lhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = expr;
            // x - x = 0
            const result = try self.allocator.create(types.Expr);
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = 0 },
            };
            return result;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            return lhs - rhs;
        }
    };
    
    // Simplifier for division
    const SimplifyDivide = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            _ = self;
            // x / 1 = x
            if (rhs.tag == .integer and rhs.data.integer == 1) return lhs;
            // 0 / x = 0 (for any non-zero x)
            if (lhs.tag == .integer and lhs.data.integer == 0) return lhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = expr;
            // x / x = 1 (where x != 0)
            const result = try self.allocator.create(types.Expr);
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = 1 },
            };
            return result;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            if (rhs == 0) return error.DivisionByZero;
            return @divFloor(lhs, rhs);
        }
    };
    
    // Simplifier for modulo
    const SimplifyMod = struct {
        fn simplifySpecific(self: *Simplifier, lhs: *types.Expr, rhs: *types.Expr) !?*types.Expr {
            // x % 1 = 0
            if (rhs.tag == .integer and rhs.data.integer == 1) {
                const result = try self.allocator.create(types.Expr);
                result.* = .{
                    .tag = .integer,
                    .data = .{ .integer = 0 },
                };
                return result;
            }
            // 0 % x = 0 (for any non-zero x)
            if (lhs.tag == .integer and lhs.data.integer == 0) return lhs;
            return null;
        }
        
        fn simplifySelfOp(self: *Simplifier, expr: *types.Expr) !?*types.Expr {
            _ = expr;
            // x % x = 0 (where x != 0)
            const result = try self.allocator.create(types.Expr);
            result.* = .{
                .tag = .integer,
                .data = .{ .integer = 0 },
            };
            return result;
        }
        
        fn computeConstant(lhs: i64, rhs: i64) !i64 {
            if (rhs == 0) return error.DivisionByZero;
            return @mod(lhs, rhs);
        }
    };
};