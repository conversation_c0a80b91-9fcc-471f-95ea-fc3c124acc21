const std = @import("std");
const types = @import("../types.zig");

/// Expression manipulation operations for SymbolicEngine
/// This module handles algebraic manipulation: expansion, collection, factorization, substitution

pub fn expandExpression(self: anytype, expr: *types.Expr) anyerror!*types.Expr {
    self.stats.expansions_performed += 1;
    
    return switch (expr.*) {
        .binary => |b| switch (b.op) {
            .mul => {
                // Expand multiplication: a * (b + c) = a*b + a*c
                const left_expanded = try self.expandExpression(b.left);
                const right_expanded = try self.expandExpression(b.right);
                
                // Check if either side is addition/subtraction
                if (right_expanded.* == .binary and 
                    (right_expanded.binary.op == .add or right_expanded.binary.op == .sub)) {
                    // Distribute left over right
                    const right_bin = right_expanded.binary;
                    const dist_left = try self.newBinaryExpr(.mul, left_expanded, right_bin.left);
                    const dist_right = try self.newBinaryExpr(.mul, left_expanded, right_bin.right);
                    return self.newBinaryExpr(right_bin.op, dist_left, dist_right);
                } else if (left_expanded.* == .binary and 
                          (left_expanded.binary.op == .add or left_expanded.binary.op == .sub)) {
                    // Distribute right over left
                    const left_bin = left_expanded.binary;
                    const dist_left = try self.newBinaryExpr(.mul, left_bin.left, right_expanded);
                    const dist_right = try self.newBinaryExpr(.mul, left_bin.right, right_expanded);
                    return self.newBinaryExpr(left_bin.op, dist_left, dist_right);
                } else {
                    // No expansion needed
                    return self.newBinaryExpr(.mul, left_expanded, right_expanded);
                }
            },
            .add, .sub => {
                // Recursively expand operands
                const left_expanded = try self.expandExpression(b.left);
                const right_expanded = try self.expandExpression(b.right);
                return self.newBinaryExpr(b.op, left_expanded, right_expanded);
            },
            else => expr, // Other operations don't expand
        },
        else => expr, // Symbols and constants don't expand
    };
}

pub fn collectLikeTerms(self: anytype, expr: *types.Expr) anyerror!*types.Expr {
    // For now, implement a simple version that collects constants
    return switch (expr.*) {
        .binary => |b| switch (b.op) {
            .add, .sub => {
                // Recursively collect in operands
                const left_collected = try self.collectLikeTerms(b.left);
                const right_collected = try self.collectLikeTerms(b.right);
                
                // If both are constants, combine them
                if (left_collected.* == .constant and right_collected.* == .constant) {
                    const result = if (b.op == .add)
                        left_collected.constant + right_collected.constant
                    else
                        left_collected.constant - right_collected.constant;
                    return self.newIntegerExpr(@intFromFloat(result));
                }
                
                // Check for x + x = 2*x pattern
                if (b.op == .add and self.exprsEqual(left_collected, right_collected)) {
                    const two = try self.newIntegerExpr(2);
                    return self.newBinaryExpr(.mul, two, left_collected);
                }
                
                // Check for x - x = 0 pattern
                if (b.op == .sub and self.exprsEqual(left_collected, right_collected)) {
                    return self.newIntegerExpr(0);
                }
                
                return self.newBinaryExpr(b.op, left_collected, right_collected);
            },
            .mul => {
                const left_collected = try self.collectLikeTerms(b.left);
                const right_collected = try self.collectLikeTerms(b.right);
                
                // Collect constant factors
                if (left_collected.* == .constant and right_collected.* == .constant) {
                    const result = left_collected.constant * right_collected.constant;
                    return self.newIntegerExpr(@intFromFloat(result));
                }
                
                return self.newBinaryExpr(.mul, left_collected, right_collected);
            },
            else => expr,
        },
        else => expr,
    };
}

pub fn factorizeExpression(self: anytype, expr: *types.Expr) anyerror!*types.Expr {
    return switch (expr.*) {
        .binary => |b| switch (b.op) {
            .add, .sub => {
                // Look for common factors
                const common_factor = try self.findCommonFactor(b.left, b.right);
                if (common_factor) |factor| {
                    // Factor out: ax + bx = x(a + b)
                    const left_quotient = try self.divideExpression(b.left, factor);
                    const right_quotient = try self.divideExpression(b.right, factor);
                    const sum = try self.newBinaryExpr(b.op, left_quotient, right_quotient);
                    return self.newBinaryExpr(.mul, factor, sum);
                }
                
                // No common factor found
                return expr;
            },
            else => expr,
        },
        else => expr,
    };
}

pub fn substituteExpression(
    self: anytype,
    expr: *types.Expr,
    target: *types.Expr,
    replacement: *types.Expr,
) anyerror!*types.Expr {
    // If this expression matches target, return replacement
    if (self.exprsEqual(expr, target)) {
        return replacement;
    }
    
    // Otherwise, recursively substitute in subexpressions
    return switch (expr.*) {
        .binary => |b| {
            const new_left = try self.substituteExpression(b.left, target, replacement);
            const new_right = try self.substituteExpression(b.right, target, replacement);
            
            // Only create new expression if something changed
            if (new_left != b.left or new_right != b.right) {
                return self.newBinaryExpr(b.op, new_left, new_right);
            }
            return expr;
        },
        else => expr, // Symbols and constants have no subexpressions
    };
}

pub fn substituteSymbol(
    self: anytype,
    expr: *types.Expr,
    symbol: types.Symbol,
    value: i64,
) !*types.Expr {
    return switch (expr.*) {
        .symbol => |s| {
            if (std.mem.eql(u8, s.name, symbol.name)) {
                return self.newIntegerExpr(value);
            }
            return expr;
        },
        .constant => expr,
        .binary => |b| {
            const new_left = try self.substituteSymbol(b.left, symbol, value);
            const new_right = try self.substituteSymbol(b.right, symbol, value);
            
            // Create new expression with substituted values
            const new_expr = try self.newBinaryExpr(b.op, new_left, new_right);
            
            // Try to simplify if both operands are now constants
            return self.simplify(new_expr);
        },
    };
}

pub fn containsVariable(self: anytype, expr: *types.Expr, variable: types.Symbol) !bool {
    return switch (expr.*) {
        .symbol => |s| std.mem.eql(u8, s.name, variable.name),
        .constant => false,
        .binary => |b| {
            const left_contains = try self.containsVariable(b.left, variable);
            if (left_contains) return true;
            return self.containsVariable(b.right, variable);
        },
    };
}

pub fn isZero(self: anytype, expr: *types.Expr) !bool {
    _ = self;
    return switch (expr.*) {
        .constant => |c| c == 0.0,
        .binary => false,
        .symbol => false,
    };
}

pub fn isOne(self: anytype, expr: *types.Expr) !bool {
    _ = self;
    return switch (expr.*) {
        .constant => |c| c == 1.0,
        .binary => false,
        .symbol => false,
    };
}

pub fn negate(self: anytype, expr: *types.Expr) !*types.Expr {
    // Optimize negation of constants
    if (expr.* == .constant) {
        return self.newIntegerExpr(-@as(i64, @intFromFloat(expr.constant)));
    }
    
    // -(-x) = x
    if (expr.* == .binary and expr.binary.op == .mul) {
        if (expr.binary.left.* == .constant and expr.binary.left.constant == -1) {
            return expr.binary.right;
        }
    }
    
    // General case: -x = -1 * x
    const neg_one = try self.newIntegerExpr(-1);
    return self.newBinaryExpr(.mul, neg_one, expr);
}

// Helper functions

fn findCommonFactor(self: anytype, a: *types.Expr, b: *types.Expr) !?*types.Expr {
    // Simple implementation: check if both are products with a common factor
    const a_bin = switch (a.*) {
        .binary => |bin| if (bin.op == .mul) bin else return null,
        else => return null,
    };
    
    const b_bin = switch (b.*) {
        .binary => |bin| if (bin.op == .mul) bin else return null,
        else => return null,
    };
    
    // Check all combinations of factors
    if (self.exprsEqual(a_bin.left, b_bin.left)) return a_bin.left;
    if (self.exprsEqual(a_bin.left, b_bin.right)) return a_bin.left;
    if (self.exprsEqual(a_bin.right, b_bin.left)) return a_bin.right;
    if (self.exprsEqual(a_bin.right, b_bin.right)) return a_bin.right;
    
    return null;
}

fn divideExpression(self: anytype, expr: *types.Expr, divisor: *types.Expr) !*types.Expr {
    // Special case: dividing by itself gives 1
    if (self.exprsEqual(expr, divisor)) {
        return self.newIntegerExpr(1);
    }
    
    // If expr is a product and one factor is divisor, return the other factor
    if (expr.* == .binary and expr.binary.op == .mul) {
        if (self.exprsEqual(expr.binary.left, divisor)) {
            return expr.binary.right;
        }
        if (self.exprsEqual(expr.binary.right, divisor)) {
            return expr.binary.left;
        }
    }
    
    // General case: create division expression
    return self.newBinaryExpr(.div, expr, divisor);
}

/// Extract all terms from an addition/subtraction expression
pub fn extractTerms(self: anytype, expr: *types.Expr) !std.ArrayList(*types.Expr) {
    var terms = std.ArrayList(*types.Expr).init(self.pool.arena.allocator());
    try extractTermsRecursive(self, expr, &terms, true);
    return terms;
}

fn extractTermsRecursive(
    self: anytype,
    expr: *types.Expr,
    terms: *std.ArrayList(*types.Expr),
    positive: bool,
) !void {
    switch (expr.*) {
        .binary => |b| switch (b.op) {
            .add => {
                try extractTermsRecursive(self, b.left, terms, positive);
                try extractTermsRecursive(self, b.right, terms, positive);
            },
            .sub => {
                try extractTermsRecursive(self, b.left, terms, positive);
                try extractTermsRecursive(self, b.right, terms, !positive);
            },
            else => {
                // Not an add/sub, treat as single term
                if (positive) {
                    try terms.append(expr);
                } else {
                    try terms.append(try self.negate(expr));
                }
            },
        },
        else => {
            // Symbol or constant, treat as single term
            if (positive) {
                try terms.append(expr);
            } else {
                try terms.append(try self.negate(expr));
            }
        },
    }
}

/// Combine terms back into an expression
pub fn combineTerms(self: anytype, terms: []const *types.Expr) !*types.Expr {
    if (terms.len == 0) {
        return self.newIntegerExpr(0);
    }
    
    var result = terms[0];
    for (terms[1..]) |term| {
        // Check if term is negative (starts with -1 multiplication)
        if (term.* == .binary and term.binary.op == .mul and
            term.binary.left.* == .constant and term.binary.left.constant == -1) {
            // Subtract the positive part
            result = try self.newBinaryExpr(.sub, result, term.binary.right);
        } else {
            // Add the term
            result = try self.newBinaryExpr(.add, result, term);
        }
    }
    
    return result;
}