const std = @import("std");
const core = @import("core");
const tensor_ops = @import("../../tensor/pointwise.zig");

test "primitive operations decomposition" {
    const allocator = std.testing.allocator;
    var ctx = try core.Core.init(allocator);
    defer ctx.deinit();
    
    // Create a test constant
    const a = try ctx.graph.newNodeConstantTyped(0, .f32);
    
    // Test neg operation (should decompose to -1 * a)
    const neg_a = try tensor_ops.neg(ctx, a);
    _ = neg_a;
    
    // Test exp operation (should use exp2)
    const exp_a = try tensor_ops.exp(ctx, a);
    _ = exp_a;
    
    // Test log operation (should use log2)
    const log_a = try tensor_ops.log(ctx, a);
    _ = log_a;
    
    // Test div operation (should use reciprocal)
    const b = try ctx.graph.newNodeConstantTyped(1, .f32);
    const div_result = try tensor_ops.div(ctx, a, b);
    _ = div_result;
}

test "activation functions decomposition" {
    const allocator = std.testing.allocator;
    var ctx = try core.Core.init(allocator);
    defer ctx.deinit();
    
    // Create a test constant
    const a = try ctx.graph.newNodeConstantTyped(0, .f32);
    
    // Test relu operation
    const relu_a = try tensor_ops.relu(ctx, a);
    _ = relu_a;
    
    // Test sigmoid operation
    const sigmoid_a = try tensor_ops.sigmoid(ctx, a);
    _ = sigmoid_a;
    
    // Test tanh operation
    const tanh_a = try tensor_ops.tanh(ctx, a);
    _ = tanh_a;
}