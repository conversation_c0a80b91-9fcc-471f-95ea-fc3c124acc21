const std = @import("std");
const testing = std.testing;
const core = @import("core");

test "shape reshape dimension inference" {
    var core_instance = try core.Core.init(testing.allocator);
    defer core_instance.deinit();
    
    const shape_engine = &core_instance.shape;
    
    // Test case: input tensor with size 24, reshape to [2, ?, 3]
    // Unknown dimension should be 4
    const total_elements: usize = 24;
    const known_dims = [_]usize{ 2, 3 };
    
    const unknown_dim = try shape_engine.inferReshapeDimension(
        total_elements,
        &known_dims,
        1, // unknown is at index 1 (not used in simplified version)
    );
    
    try testing.expectEqual(@as(usize, 4), unknown_dim);
    
    // Test case 2: tensor with size 100, reshape to [?, 5, 5]
    const total_elements2: usize = 100;
    const known_dims2 = [_]usize{ 5, 5 };
    
    const unknown_dim2 = try shape_engine.inferReshapeDimension(
        total_elements2,
        &known_dims2,
        0,
    );
    
    try testing.expectEqual(@as(usize, 4), unknown_dim2);
}

test "shape broadcasting compatibility" {
    // Simple dimension broadcasting check
    try testing.expect(core.shape.engine.ShapeEngine.canBroadcast(2, 2));
    try testing.expect(core.shape.engine.ShapeEngine.canBroadcast(2, 1));
    try testing.expect(core.shape.engine.ShapeEngine.canBroadcast(1, 3));
    try testing.expect(!core.shape.engine.ShapeEngine.canBroadcast(2, 3));
    
    // Shape-level broadcasting
    {
        const shape1 = [_]usize{ 2, 3, 4 };
        const shape2 = [_]usize{ 1, 3, 4 };
        try testing.expect(core.shape.engine.ShapeEngine.canBroadcastShapeArrays(&shape1, &shape2));
    }
    
    {
        const shape1 = [_]usize{ 2, 3, 4 };
        const shape2 = [_]usize{ 3, 1 };
        try testing.expect(core.shape.engine.ShapeEngine.canBroadcastShapeArrays(&shape1, &shape2));
    }
    
    {
        const shape1 = [_]usize{ 2, 3, 4 };
        const shape2 = [_]usize{ 2, 4, 4 }; // incompatible in middle dim
        try testing.expect(!core.shape.engine.ShapeEngine.canBroadcastShapeArrays(&shape1, &shape2));
    }
}

test "shape convolution output dimensions" {
    // Test conv2d output calculation
    // Input: 28x28, Kernel: 3x3, Stride: 1, Padding: 1
    // Output should be 28x28
    const input_size: usize = 28;
    const kernel_size: usize = 3;
    const stride: usize = 1;
    const padding: usize = 1;
    
    const output_size = core.shape.engine.ShapeEngine.convOutputSize(input_size, kernel_size, stride, padding);
    
    // ((28 + 2*1 - 3) / 1) + 1 = (27 / 1) + 1 = 28
    try testing.expectEqual(@as(usize, 28), output_size);
    
    // Test case 2: No padding
    const output_size2 = core.shape.engine.ShapeEngine.convOutputSize(28, 3, 1, 0);
    // ((28 + 0 - 3) / 1) + 1 = 25 + 1 = 26  
    try testing.expectEqual(@as(usize, 26), output_size2);
    
    // Test case 3: Stride 2
    const output_size3 = core.shape.engine.ShapeEngine.convOutputSize(28, 3, 2, 1);
    // ((28 + 2 - 3) / 2) + 1 = 13 + 1 = 14
    try testing.expectEqual(@as(usize, 14), output_size3);
}

test "broadcasted shape computation" {
    var core_instance = try core.Core.init(testing.allocator);
    defer core_instance.deinit();
    
    const shape_engine = &core_instance.shape;
    
    // Test case 1: [2, 3, 4] and [1, 3, 4] -> [2, 3, 4]
    {
        const shape1 = [_]usize{ 2, 3, 4 };
        const shape2 = [_]usize{ 1, 3, 4 };
        
        const result = try shape_engine.broadcastedShape(&shape1, &shape2);
        try testing.expectEqualSlices(usize, &[_]usize{ 2, 3, 4 }, result);
    }
    
    // Test case 2: [3, 4] and [2, 3, 4] -> [2, 3, 4]
    {
        const shape1 = [_]usize{ 3, 4 };
        const shape2 = [_]usize{ 2, 3, 4 };
        
        const result = try shape_engine.broadcastedShape(&shape1, &shape2);
        try testing.expectEqualSlices(usize, &[_]usize{ 2, 3, 4 }, result);
    }
    
    // Test case 3: [1] and [2, 3, 4] -> [2, 3, 4]
    {
        const shape1 = [_]usize{ 1 };
        const shape2 = [_]usize{ 2, 3, 4 };
        
        const result = try shape_engine.broadcastedShape(&shape1, &shape2);
        try testing.expectEqualSlices(usize, &[_]usize{ 2, 3, 4 }, result);
    }
    
    // Test case 4: Incompatible shapes should error
    {
        const shape1 = [_]usize{ 2, 3 };
        const shape2 = [_]usize{ 2, 4 };
        
        const result = shape_engine.broadcastedShape(&shape1, &shape2);
        try testing.expectError(error.IncompatibleShapes, result);
    }
}