// Test that memory management improvements compile correctly
const std = @import("std");
const core = @import("core");
const memory = @import("../memory.zig");
// Access types through core module exports

test "ObjectPool with statistics" {
    const allocator = std.testing.allocator;
    
    const TestItem = struct { value: i32 };
    var test_pool = memory.ObjectPool(TestItem).init(allocator);
    defer test_pool.deinit();
    
    // Acquire and check stats
    const item1 = try test_pool.acquire();
    const item2 = try test_pool.acquire();
    _ = item2; // Use to avoid unused variable error
    
    var stats = test_pool.getStats();
    try std.testing.expectEqual(@as(usize, 2), stats.acquires);
    try std.testing.expectEqual(@as(usize, 2), stats.peak_usage);
    
    // Release and check stats
    try test_pool.release(item1);
    stats = test_pool.getStats();
    try std.testing.expectEqual(@as(usize, 1), stats.releases);
}

test "IdPool with statistics" {
    const allocator = std.testing.allocator;
    
    var id_pool = memory.IdPool(i32).init(allocator);
    defer id_pool.deinit();
    
    // Add items and check stats
    const id1 = try id_pool.add(42);
    const id2 = try id_pool.add(100);
    _ = id2; // Use to avoid unused variable error
    
    var stats = id_pool.getStats();
    try std.testing.expectEqual(@as(usize, 2), stats.adds);
    try std.testing.expectEqual(@as(usize, 2), stats.peak_count);
    
    // Remove and check stats
    _ = id_pool.remove(id1);
    stats = id_pool.getStats();
    try std.testing.expectEqual(@as(usize, 1), stats.removes);
}

test "Symbolic engine manages expressions" {
    var test_core = try core.Core.init(std.testing.allocator);
    defer test_core.deinit();
    
    // The symbolic engine should manage expressions via pool
    const engine = &test_core.symbolic;
    
    // Test creating expressions
    const expr1 = try engine.newIntegerExpr(42);
    const expr2 = try engine.newSymbolExpr("x");
    _ = expr1;
    _ = expr2;
    
    // Verify stats are being tracked
    try std.testing.expect(engine.stats.expr_count > 0);
}

test "Unmanaged containers in symbolic engine" {
    var test_core = try core.Core.init(std.testing.allocator);
    defer test_core.deinit();
    
    // Test that constraints are now unmanaged
    const engine = &test_core.symbolic;
    
    // Test expression creation is working
    const left_expr = try engine.newIntegerExpr(1);
    const right_expr = try engine.newIntegerExpr(2);
    const result_expr = try engine.newBinaryExpr(.multiply, left_expr, right_expr);
    
    // The expression is used in creating the binary expr
    _ = result_expr;
    
    // Check that expression count increased
    try std.testing.expect(engine.stats.expr_count >= 3);
}

test "Memory pools no longer have unused types" {
    // This test verifies the specialized pools were reduced
    // Only ExprPool should exist now
    _ = memory.ExprPool;
    
    // These should no longer compile if properly removed:
    // _ = pool.ShapePool;    // Should error
    // _ = pool.ViewDescPool; // Should error
    // _ = pool.NodePool;     // Should error
}