// Integration test for all V2 Core Engines
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const OpType = core.graph.types.OpType;

test "Core integration: all engines working together" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test 1: Create symbolic dimensions
    const batch_sym = try core_instance.symbolic.newSymbolExpr("N");
    const seq_sym = try core_instance.symbolic.newSymbolExpr("T");
    _ = try core_instance.symbolic.newIntegerExpr(128);
    
    // Test 2: Create shapes with symbolic dimensions
    const dim_128 = try core_instance.symbolic.newIntegerExpr(128);
    const dims = [_]*types.Expr{
        batch_sym,
        seq_sym,
        dim_128,
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    // Test 3: Create graph nodes with dynamic shapes
    const view_id_typed = @as(types.ViewId, view_id);
    const input_node = try core_instance.graph.newNodeConstant(view_id_typed);
    const dim_128_2 = try core_instance.symbolic.newIntegerExpr(128);
    const dim_64 = try core_instance.symbolic.newIntegerExpr(64);
    const weight_shape_id = try core_instance.shape.newShape(&[_]*types.Expr{
        dim_128_2,
        dim_64,
    });
    const weight_view_id = try core_instance.shape.newDefaultView(weight_shape_id);
    const weight_view_id_typed = @as(types.ViewId, weight_view_id);
    const weight_node = try core_instance.graph.newNodeConstant(weight_view_id_typed);
    _ = weight_node; // Will be used in future operations
    
    // Test 4: Operations that change shapes
    // Reduce sum over sequence dimension
    const axes = [_]i32{1};
    const dim_128_3 = try core_instance.symbolic.newIntegerExpr(128);
    const reduced_shape_id = try core_instance.shape.newShape(&[_]*types.Expr{
        batch_sym,
        dim_128_3,
    });
    const reduced_view_id = try core_instance.shape.newDefaultView(reduced_shape_id);
    const reduced_view_id_typed = @as(types.ViewId, reduced_view_id);
    const sum_node = try core_instance.graph.newNodeReduceSum(input_node, &axes, reduced_view_id_typed);
    
    // Test 5: Shape operations (reshape is shape-only in Luminal-style)
    const dim_64_2 = try core_instance.symbolic.newIntegerExpr(64);
    const dim_2 = try core_instance.symbolic.newIntegerExpr(2);
    const reshaped_shape_id = try core_instance.shape.newShape(&[_]*types.Expr{
        batch_sym,
        dim_64_2,
        dim_2,
    });
    _ = try core_instance.shape.newDefaultView(reshaped_shape_id);
    // Reshape is handled by shape engine, no graph node created
    
    // Test 6: Verify sum node graph structure
    const sum_node_data = core_instance.graph.getNode(sum_node).?;
    try testing.expectEqual(OpType.reduce_sum, sum_node_data.op);
    
    // Test 7: Create symbolic expressions
    const zero_expr = try core_instance.symbolic.newIntegerExpr(0);
    const one_expr = try core_instance.symbolic.newIntegerExpr(1);
    const gt_expr = try core_instance.symbolic.newBinaryExpr(.add, batch_sym, one_expr);
    
    // Verify expressions were created (they're pointers, not optionals)
    _ = zero_expr;
    _ = gt_expr;
    
    // Test 8: Evaluate symbolic expressions
    var test_bindings = std.StringHashMap(i64).init(allocator);
    defer test_bindings.deinit();
    try test_bindings.put("N", 32);
    try test_bindings.put("T", 100);
    
    const batch_value = try core_instance.symbolic.evaluate(batch_sym, test_bindings);
    try testing.expectEqual(@as(i64, 32), batch_value);
    
    // Test 9: Check shape engine caching
    const shape_info = core_instance.shape.getShape(shape_id);
    try testing.expectEqual(@as(usize, 3), shape_info.dims.len);
    
    // Test 10: Graph node counting (3 nodes: input, weight, sum)
    try testing.expectEqual(@as(u32, 3), core_instance.graph.stats.node_count);
}

test "Core integration: all reduction operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 3D shape for comprehensive reduction testing
    const dim_4 = try core_instance.symbolic.newIntegerExpr(4);
    const dim_6 = try core_instance.symbolic.newIntegerExpr(6);
    const dim_8 = try core_instance.symbolic.newIntegerExpr(8);
    const dims = [_]*types.Expr{
        dim_4,
        dim_6,
        dim_8,
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    // Create input node
    const view_id_typed = @as(types.ViewId, view_id);
    const input_node = try core_instance.graph.newNodeConstant(view_id_typed);
    
    // Test reduction operations - simplified direct tests
    const reduce_axes_1 = &[_]i32{1};
    const reduce_axes_0_2 = &[_]i32{0, 2};
    
    // Test reduce_sum over axis 1: [4,6,8] -> [4,8]
    const sum_output_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
        try core_instance.symbolic.newIntegerExpr(8),
    };
    const sum_output_shape_id = try core_instance.shape.newShape(&sum_output_dims);
    const sum_output_view_id = try core_instance.shape.newDefaultView(sum_output_shape_id);
    const sum_output_view_id_typed = @as(types.ViewId, sum_output_view_id);
    const sum_node_id = try core_instance.graph.newNodeReduceSum(input_node, reduce_axes_1, sum_output_view_id_typed);
    const sum_node = core_instance.graph.getNode(sum_node_id).?;
    try testing.expectEqual(OpType.reduce_sum, sum_node.op);
    
    // Test reduce_max over axes 0,2: [4,6,8] -> [6]
    const max_output_dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(6),
    };
    const max_output_shape_id = try core_instance.shape.newShape(&max_output_dims);
    const max_output_view_id = try core_instance.shape.newDefaultView(max_output_shape_id);
    const max_output_view_id_typed = @as(types.ViewId, max_output_view_id);
    const max_node_id = try core_instance.graph.newNodeReduceMax(input_node, reduce_axes_0_2, max_output_view_id_typed);
    const max_node = core_instance.graph.getNode(max_node_id).?;
    try testing.expectEqual(OpType.reduce_max, max_node.op);
}

test "Core integration: engine interactions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create a computation with symbolic dimensions and shape operations
    const batch_id = try core_instance.symbolic.newSymbolExpr("batch");
    const channels_expr = try core_instance.symbolic.newBinaryExpr(
        .multiply,
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(32)
    );
    
    // Create shape with mix of symbolic and computed dimensions
    const dim_224_1 = try core_instance.symbolic.newIntegerExpr(224);
    const dim_224_2 = try core_instance.symbolic.newIntegerExpr(224);
    const dims = [_]*types.Expr{
        batch_id,
        dim_224_1, // height
        dim_224_2, // width
        channels_expr, // 3 * 32 = 96 channels
    };
    const input_shape_id = try core_instance.shape.newShape(&dims);
    const input_view_id = try core_instance.shape.newDefaultView(input_shape_id);
    
    // Create computation graph
    const input_view_id_typed = @as(types.ViewId, input_view_id);
    const img_node = try core_instance.graph.newNodeConstant(input_view_id_typed);
    
    // Sum pooling - reduce over spatial dimensions (using sum instead of mean, as mean is not primitive)
    const spatial_axes = [_]i32{1, 2};
    const gap_dims = [_]*types.Expr{
        batch_id,
        channels_expr,
    };
    const gap_shape_id = try core_instance.shape.newShape(&gap_dims);
    const gap_view_id = try core_instance.shape.newDefaultView(gap_shape_id);
    const gap_view_id_typed = @as(types.ViewId, gap_view_id);
    const gap_node = try core_instance.graph.newNodeReduceSum(img_node, &spatial_axes, gap_view_id_typed);
    
    // Check result
    const gap_node_data = core_instance.graph.getNode(gap_node).?;
    try testing.expectEqual(OpType.reduce_sum, gap_node_data.op);
    
    // Evaluate symbolic dimension
    var test_bindings = std.StringHashMap(i64).init(allocator);
    defer test_bindings.deinit();
    try test_bindings.put("batch", 16);
    
    const batch_value = try core_instance.symbolic.evaluate(batch_id, test_bindings);
    try testing.expectEqual(@as(i64, 16), batch_value);
    
    const channels_value = try core_instance.symbolic.evaluate(channels_expr, test_bindings);
    try testing.expectEqual(@as(i64, 96), channels_value);
}