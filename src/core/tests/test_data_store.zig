const std = @import("std");
const core = @import("core");
const Core = core.Core;
const types = core.types;
const graph_types = core.graph.types;
const data_module = core.data;
const tensor = @import("tensor");
const creation = tensor.creation;

test "DataStore - constant patterns" {
    const allocator = std.testing.allocator;
    
    // Initialize core
    const zing_core = try Core.init(allocator);
    defer zing_core.deinit();
    
    // Test creating constants with patterns
    const dim_3 = try zing_core.symbolic.newIntegerExpr(3);
    const dim_4 = try zing_core.symbolic.newIntegerExpr(4);
    const zeros = try creation.zeros(zing_core, &[_]*types.Expr{
        dim_3,
        dim_4,
    });
    
    const dim_2 = try zing_core.symbolic.newIntegerExpr(2);
    const ones = try creation.ones(zing_core, &[_]*types.Expr{
        dim_2,
        dim_2,
    });
    
    const dim_5 = try zing_core.symbolic.newIntegerExpr(5);
    const full = try creation.full(zing_core, &[_]*types.Expr{
        dim_5,
    }, 3.14);
    
    const eye = try creation.eye(zing_core, 3, .f32);
    
    // Check node types
    const graph = &zing_core.graph;
    const data_store = &zing_core.data;
    
    // Verify nodes are created correctly
    const zeros_node = graph.getNode(zeros).?;
    try std.testing.expectEqual(graph_types.OpType.constant, zeros_node.op);
    try std.testing.expect(zeros_node.isConstant());
    
    // Verify patterns are stored
    try std.testing.expect(data_store.hasConstantData(zeros));
    try std.testing.expect(data_store.hasConstantData(ones));
    try std.testing.expect(data_store.hasConstantData(full));
    try std.testing.expect(data_store.hasConstantData(eye));
    
    // Check patterns
    const zeros_pattern = try data_store.getConstantPattern(zeros);
    try std.testing.expectEqual(data_module.DataStore.Pattern.zeros, zeros_pattern);
    
    const ones_pattern = try data_store.getConstantPattern(ones);
    try std.testing.expectEqual(data_module.DataStore.Pattern.ones, ones_pattern);
    
    const eye_pattern = try data_store.getConstantPattern(eye);
    try std.testing.expectEqual(data_module.DataStore.Pattern.identity, eye_pattern);
}

test "DataStore - constant data" {
    const allocator = std.testing.allocator;
    
    // Initialize core
    const zing_core = try Core.init(allocator);
    defer zing_core.deinit();
    
    // Test creating constants with actual data
    const scalar = try creation.constant(zing_core, @as(f32, 42.0));
    
    const array_data = [_]f32{ 1.0, 2.0, 3.0, 4.0 };
    const array = try creation.constant(zing_core, array_data);
    
    const slice_data = &[_]f32{ 5.0, 6.0, 7.0 };
    const slice = try creation.constant(zing_core, slice_data);
    
    const data_store = &zing_core.data;
    
    // Verify data is stored
    try std.testing.expect(data_store.hasConstantData(scalar));
    try std.testing.expect(data_store.hasConstantData(array));
    try std.testing.expect(data_store.hasConstantData(slice));
    
    // Test getting actual data
    const scalar_data = try data_store.getConstantData(f32, scalar);
    try std.testing.expectEqual(@as(usize, 1), scalar_data.len);
    try std.testing.expectEqual(@as(f32, 42.0), scalar_data[0]);
    
    const array_retrieved = try data_store.getConstantData(f32, array);
    try std.testing.expectEqual(@as(usize, 4), array_retrieved.len);
    for (array_retrieved, 0..) |val, i| {
        try std.testing.expectEqual(array_data[i], val);
    }
    
    const slice_retrieved = try data_store.getConstantData(f32, slice);
    try std.testing.expectEqual(@as(usize, 3), slice_retrieved.len);
    for (slice_retrieved, 0..) |val, i| {
        try std.testing.expectEqual(slice_data[i], val);
    }
}

test "DataStore - linspace and arange" {
    const allocator = std.testing.allocator;
    
    // Initialize core
    const zing_core = try Core.init(allocator);
    defer zing_core.deinit();
    
    // Test linspace
    const linspace = try creation.linspace(zing_core, 0.0, 10.0, 5);
    const data_store = &zing_core.data;
    
    const linspace_data = try data_store.getConstantData(f32, linspace);
    try std.testing.expectEqual(@as(usize, 5), linspace_data.len);
    try std.testing.expectApproxEqAbs(@as(f32, 0.0), linspace_data[0], 0.0001);
    try std.testing.expectApproxEqAbs(@as(f32, 2.5), linspace_data[1], 0.0001);
    try std.testing.expectApproxEqAbs(@as(f32, 5.0), linspace_data[2], 0.0001);
    try std.testing.expectApproxEqAbs(@as(f32, 7.5), linspace_data[3], 0.0001);
    try std.testing.expectApproxEqAbs(@as(f32, 10.0), linspace_data[4], 0.0001);
    
    // Test arange
    const arange = try creation.arange(zing_core, 0.0, 10.0, 2.0);
    const arange_data = try data_store.getConstantData(f32, arange);
    try std.testing.expectEqual(@as(usize, 5), arange_data.len);
    try std.testing.expectEqual(@as(f32, 0.0), arange_data[0]);
    try std.testing.expectEqual(@as(f32, 2.0), arange_data[1]);
    try std.testing.expectEqual(@as(f32, 4.0), arange_data[2]);
    try std.testing.expectEqual(@as(f32, 6.0), arange_data[3]);
    try std.testing.expectEqual(@as(f32, 8.0), arange_data[4]);
}

test "DataStore - variables and parameters" {
    const allocator = std.testing.allocator;
    
    // Initialize core
    const zing_core = try Core.init(allocator);
    defer zing_core.deinit();
    
    // Create variables
    const dim_3_b = try zing_core.symbolic.newIntegerExpr(3);
    const weights = try creation.variable(zing_core, &[_]*types.Expr{
        dim_3_b,
        dim_3_b,
    }, .f32);
    
    const init_data = [_]f32{ 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0 };
    const weights_init = try creation.variableWithData(zing_core, init_data);
    
    const graph = &zing_core.graph;
    const data_store = &zing_core.data;
    
    // Check node types
    const weights_node = graph.getNode(weights).?;
    try std.testing.expectEqual(graph_types.OpType.variable, weights_node.op);
    try std.testing.expect(weights_node.isVariable());
    
    // Check parameters are registered
    try std.testing.expect(data_store.isParameter(weights));
    try std.testing.expect(data_store.isParameter(weights_init));
    
    // Check initialized data
    const weights_init_data = try data_store.getConstantData(f32, weights_init);
    try std.testing.expectEqual(@as(usize, 9), weights_init_data.len);
    for (weights_init_data, 0..) |val, i| {
        try std.testing.expectEqual(init_data[i], val);
    }
}

test "DataStore - input placeholders" {
    const allocator = std.testing.allocator;
    
    // Initialize core
    const zing_core = try Core.init(allocator);
    defer zing_core.deinit();
    
    // Create placeholders
    const dim_10 = try zing_core.symbolic.newIntegerExpr(10);
    const dim_784 = try zing_core.symbolic.newIntegerExpr(784);
    const input = try creation.placeholder(zing_core, &[_]*types.Expr{
        dim_10,
        dim_784,
    }, .f32);
    
    const batch_input = try creation.placeholderSymbolic(zing_core, &.{
        .{ .size = null, .symbol = "batch" },
        .{ .size = 784, .symbol = null },
    }, .f32);
    
    const graph = &zing_core.graph;
    const data_store = &zing_core.data;
    
    // Check node types
    const input_node = graph.getNode(input).?;
    try std.testing.expectEqual(graph_types.OpType.input, input_node.op);
    try std.testing.expect(input_node.isInput());
    
    const batch_node = graph.getNode(batch_input).?;
    try std.testing.expectEqual(graph_types.OpType.input, batch_node.op);
    try std.testing.expect(batch_node.isInput());
    
    // Check inputs are registered
    try std.testing.expect(data_store.isInput(input));
    try std.testing.expect(data_store.isInput(batch_input));
    
    // Check shape is symbolic for batch dimension
    const view_raw = batch_node.output_view_id;
    const batch_view = zing_core.shape.getView(view_raw);
    const batch_shape = zing_core.shape.getShape(batch_view.shape_id);
    try std.testing.expect(batch_shape.dims[0].tag == .symbol);
    try std.testing.expect(batch_shape.dims[1].tag == .integer);
    const dim1_val = try zing_core.symbolic.evaluate(batch_shape.dims[1], null);
    try std.testing.expectEqual(@as(i64, 784), dim1_val);
}

test "DataStore - type inference" {
    const allocator = std.testing.allocator;
    
    // Initialize core
    const zing_core = try Core.init(allocator);
    defer zing_core.deinit();
    
    // Test various types
    const int_data = @as(i32, 42);
    const int_tensor = try creation.constant(zing_core, int_data);
    
    const float_data = @as(f64, 3.14);
    const float_tensor = try creation.constant(zing_core, float_data);
    
    const bool_data = true;
    const bool_tensor = try creation.constant(zing_core, bool_data);
    
    const graph = &zing_core.graph;
    
    // Check inferred types
    const int_node = graph.getNode(int_tensor).?;
    try std.testing.expectEqual(graph_types.DataType.i32, int_node.dtype);
    
    const float_node = graph.getNode(float_tensor).?;
    try std.testing.expectEqual(graph_types.DataType.f64, float_node.dtype);
    
    const bool_node = graph.getNode(bool_tensor).?;
    try std.testing.expectEqual(graph_types.DataType.bool, bool_node.dtype);
}