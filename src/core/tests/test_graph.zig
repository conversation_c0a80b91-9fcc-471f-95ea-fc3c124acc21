// Test suite for V2 GraphEngine
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;

// Helper function to convert raw view IDs to u32 for legacy interfaces
inline fn viewIdToU32(id: types.ViewId) u32 {
    return id;
}

test "GraphEngine: create and manage nodes" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create shapes for our operations
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    // Create a constant node (note: data is managed separately in V2)
    const const_node_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Get the node and check its properties
    const const_node = core_instance.graph.getNode(const_node_id);
    try testing.expect(const_node != null);
    try testing.expectEqual(const_node.?.op, .constant);
    try testing.expectEqual(const_node.?.dtype, .f32);
    try testing.expectEqual(const_node.?.output_view_id, view_id);
}

test "GraphEngine: binary operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create shapes
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(2),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    // Create two constant nodes
    const node1_id = try core_instance.graph.newNodeConstant(view_id);
    const node2_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Create add operation
    const add_inputs = [_]types.NodeId{node1_id, node2_id};
    const add_id = try core_instance.graph.newNodeAdd(&add_inputs, view_id);
    const add_node = core_instance.graph.getNode(add_id);
    
    try testing.expect(add_node != null);
    try testing.expectEqual(add_node.?.op, .add);
    try testing.expectEqual(add_node.?.inputs.len, 2);
    try testing.expectEqual(add_node.?.inputs[0], node1_id);
    try testing.expectEqual(add_node.?.inputs[1], node2_id);
    
    // Create multiply operation
    const mul_inputs = [_]types.NodeId{add_id, node1_id};
    const mul_id = try core_instance.graph.newNodeMultiply(&mul_inputs, view_id);
    const mul_node = core_instance.graph.getNode(mul_id);
    
    try testing.expect(mul_node != null);
    try testing.expectEqual(mul_node.?.op, .multiply);
    try testing.expectEqual(mul_node.?.inputs.len, 2);
    try testing.expectEqual(mul_node.?.inputs[0], add_id);
    try testing.expectEqual(mul_node.?.inputs[1], node1_id);
}

test "GraphEngine: primitive unary operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create shape
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    // Create constant node
    const const_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Test reciprocal
    const recip_id = try core_instance.graph.newNodeReciprocal(const_id, view_id);
    const recip_node = core_instance.graph.getNode(recip_id);
    try testing.expect(recip_node != null);
    try testing.expectEqual(recip_node.?.op, .reciprocal);
    try testing.expectEqual(recip_node.?.inputs[0], const_id);
    
    // Test sqrt
    const sqrt_id = try core_instance.graph.newNodeSqrt(const_id, view_id);
    const sqrt_node = core_instance.graph.getNode(sqrt_id);
    try testing.expect(sqrt_node != null);
    try testing.expectEqual(sqrt_node.?.op, .sqrt);
    try testing.expectEqual(sqrt_node.?.inputs[0], const_id);
    
    // Test sin
    const sin_id = try core_instance.graph.newNodeSin(const_id, view_id);
    const sin_node = core_instance.graph.getNode(sin_id);
    try testing.expect(sin_node != null);
    try testing.expectEqual(sin_node.?.op, .sin);
    try testing.expectEqual(sin_node.?.inputs[0], const_id);
    
    // Test log2
    const log2_id = try core_instance.graph.newNodeLog2(const_id, view_id);
    const log2_node = core_instance.graph.getNode(log2_id);
    try testing.expect(log2_node != null);
    try testing.expectEqual(log2_node.?.op, .log2);
    try testing.expectEqual(log2_node.?.inputs[0], const_id);
    
    // Test exp2
    const exp2_id = try core_instance.graph.newNodeExp2(const_id, view_id);
    const exp2_node = core_instance.graph.getNode(exp2_id);
    try testing.expect(exp2_node != null);
    try testing.expectEqual(exp2_node.?.op, .exp2);
    try testing.expectEqual(exp2_node.?.inputs[0], const_id);
}

test "GraphEngine: reduction operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create 2D shape (3x4)
    const dims_2d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const shape_2d_id = try core_instance.shape.newShape(&dims_2d);
    const view_2d_id = try core_instance.shape.newDefaultView(shape_2d_id);
    
    // Create 1D shape for axis 1 reduction result (3,)
    const dims_1d = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const shape_1d_id = try core_instance.shape.newShape(&dims_1d);
    const view_1d_id = try core_instance.shape.newDefaultView(shape_1d_id);
    
    // Create constant node
    const const_id = try core_instance.graph.newNodeConstant(view_2d_id);
    
    // Test reduce_sum
    const axes = [_]i32{1}; // Reduce along axis 1
    const sum_id = try core_instance.graph.newNodeReduceSum(const_id, &axes, view_1d_id);
    const sum_node = core_instance.graph.getNode(sum_id);
    
    try testing.expect(sum_node != null);
    try testing.expectEqual(sum_node.?.op, .reduce_sum);
    try testing.expectEqual(sum_node.?.inputs[0], const_id);
    try testing.expectEqual(sum_node.?.output_view_id, view_1d_id);
    
    // Check metadata
    try testing.expect(sum_node.?.metadata != null);
    try testing.expectEqual(sum_node.?.metadata.?.reduction.axes.len, 1);
    try testing.expectEqual(sum_node.?.metadata.?.reduction.axes[0], 1);
    
    // Test reduce_max
    const max_id = try core_instance.graph.newNodeReduceMax(const_id, &axes, view_1d_id);
    const max_node = core_instance.graph.getNode(max_id);
    
    try testing.expect(max_node != null);
    try testing.expectEqual(max_node.?.op, .reduce_max);
    try testing.expectEqual(max_node.?.inputs[0], const_id);
    try testing.expectEqual(max_node.?.output_view_id, view_1d_id);
    
    
}


test "GraphEngine: graph utilities" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create a simple computation graph
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    const a_id = try core_instance.graph.newNodeConstant(view_id);
    const b_id = try core_instance.graph.newNodeConstant(view_id);
    const inputs = [_]types.NodeId{a_id, b_id};
    const add_id = try core_instance.graph.newNodeAdd(&inputs, view_id);
    
    // Test node count via stats
    try testing.expectEqual(core_instance.graph.stats.node_count, 3);
    
    // Test node retrieval
    const add_node = core_instance.graph.getNode(add_id);
    try testing.expect(add_node != null);
    try testing.expectEqual(add_node.?.op, .add);
    
    // Test input tracking (inputs are stored directly on the node)
    const add_inputs = add_node.?.inputs;
    try testing.expectEqual(add_inputs.len, 2);
    try testing.expectEqual(add_inputs[0], a_id);
    try testing.expectEqual(add_inputs[1], b_id);
    
    // Test consumer tracking
    const a_consumers = core_instance.graph.getNodeConsumers(a_id);
    try testing.expectEqual(a_consumers.len, 1);
    try testing.expectEqual(a_consumers[0], @intFromEnum(add_id));
}

test "GraphEngine: memory management" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create many nodes to test memory pooling
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(10),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    
    var node_ids = std.ArrayList(types.NodeId).init(allocator);
    defer node_ids.deinit();
    
    // Create 100 constant nodes
    for (0..100) |_| {
        const view_id = try core_instance.shape.newDefaultView(shape_id);
        const node_id = try core_instance.graph.newNodeConstant(view_id);
        try node_ids.append(node_id);
    }
    
    // Create operations between nodes
    for (0..50) |i| {
        const view_id = try core_instance.shape.newDefaultView(shape_id);
        const inputs = [_]types.NodeId{
            node_ids.items[i * 2], 
            node_ids.items[i * 2 + 1]
        };
        const add_id = try core_instance.graph.newNodeAdd(&inputs, view_id);
        try node_ids.append(add_id);
    }
    
    // Check node count
    try testing.expectEqual(core_instance.graph.stats.node_count, 150);
    
    // Reset graph
    core_instance.graph.reset();
    try testing.expectEqual(core_instance.graph.stats.node_count, 0);
    
    // Should be able to create new nodes after reset
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    const new_node_id = try core_instance.graph.newNodeConstant(view_id);
    try testing.expectEqual(new_node_id, @as(types.NodeId, @enumFromInt(1))); // Should start from 1 after reset (0 is reserved for invalid)
}

test "GraphEngine: complex graph construction" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Build a complex graph using only primitives: (a + b) * (c + (-1 * d))
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(3),
        try core_instance.symbolic.newIntegerExpr(3),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    const a_id = try core_instance.graph.newNodeConstant(view_id);
    const b_id = try core_instance.graph.newNodeConstant(view_id);
    const c_id = try core_instance.graph.newNodeConstant(view_id);
    const d_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Create constant -1 for subtraction decomposition
    const neg_one_id = try core_instance.graph.newNodeConstant(view_id);
    
    const add_inputs = [_]types.NodeId{a_id, b_id};
    const add_id = try core_instance.graph.newNodeAdd(&add_inputs, view_id);
    
    // Implement subtraction as c + (-1 * d)
    const neg_d_inputs = [_]types.NodeId{neg_one_id, d_id};
    const neg_d_id = try core_instance.graph.newNodeMultiply(&neg_d_inputs, view_id);
    
    const sub_inputs = [_]types.NodeId{c_id, neg_d_id};
    const sub_result_id = try core_instance.graph.newNodeAdd(&sub_inputs, view_id);
    
    const mul_inputs = [_]types.NodeId{add_id, sub_result_id};
    const mul_id = try core_instance.graph.newNodeMultiply(&mul_inputs, view_id);
    
    // Check the final node
    const mul_node = core_instance.graph.getNode(mul_id);
    try testing.expect(mul_node != null);
    try testing.expectEqual(mul_node.?.op, .multiply);
    try testing.expectEqual(mul_node.?.inputs[0], add_id);
    try testing.expectEqual(mul_node.?.inputs[1], sub_result_id);
    
    // Check consumer relationships
    const add_consumers = core_instance.graph.getNodeConsumers(add_id);
    try testing.expectEqual(add_consumers.len, 1);
    try testing.expectEqual(add_consumers[0], @intFromEnum(mul_id));
}

test "GraphEngine: edge cases and error handling" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test empty reduction axes (should be valid)
    const dims = [_]*types.Expr{
        try core_instance.symbolic.newIntegerExpr(4),
    };
    const shape_id = try core_instance.shape.newShape(&dims);
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    const node_id = try core_instance.graph.newNodeConstant(view_id);
    
    // Scalar shape for full reduction
    const scalar_dims = [_]*types.Expr{};
    const scalar_shape_id = try core_instance.shape.newShape(&scalar_dims);
    const scalar_view_id = try core_instance.shape.newDefaultView(scalar_shape_id);
    
    // Reduce with all axes
    const axes = [_]i32{0};
    const reduce_id = try core_instance.graph.newNodeReduceSum(node_id, &axes, scalar_view_id);
    const reduce_node = core_instance.graph.getNode(reduce_id);
    try testing.expect(reduce_node != null);
    try testing.expectEqual(reduce_node.?.output_view_id, scalar_view_id);
    
    // Test invalid input count for binary operations
    const single_input = [_]types.NodeId{node_id};
    const result = core_instance.graph.newNodeAdd(&single_input, view_id);
    try testing.expectError(error.InvalidArgumentCount, result);
}