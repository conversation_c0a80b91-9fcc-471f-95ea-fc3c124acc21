const std = @import("std");
const testing = std.testing;
const core = @import("core");
const types = core.types;

test "shape engine - all V1 operations preserved" {
    const allocator = testing.allocator;
    
    var c = try core.Core.init(allocator);
    defer c.deinit();
    
    // Test with both concrete and symbolic dimensions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const seq_len = try c.symbolic.newSymbolExpr("seq_len");
    
    // Create shapes with mixed dimensions using proper symbolic engine
    const expr_10 = try c.symbolic.newIntegerExpr(10);
    const expr_768_1 = try c.symbolic.newIntegerExpr(768);
    const shape1 = try c.shape.newShape(&.{ batch, expr_10, expr_768_1 });
    
    const expr_768_2 = try c.symbolic.newIntegerExpr(768);
    const shape2 = try c.shape.newShape(&.{ batch, seq_len, expr_768_2 });
    
    // Test 1: Broadcast operations
    {
        const view1 = try c.shape.newDefaultView(shape1);
        const expr_768_3 = try c.symbolic.newIntegerExpr(768);
        const broadcast_shape = try c.shape.newShape(&.{ batch, seq_len, expr_768_3 });
        
        // This should work with symbolic dimensions
        const broadcast_view = try c.shape.newBroadcastView(view1, broadcast_shape);
        try testing.expect(broadcast_view != view1);
    }
    
    // Test 2: Expand operations
    {
        const view2 = try c.shape.newDefaultView(shape1);
        const expanded_view = try c.shape.newExpandedView(
            view2, 
            1, // Insert at axis 1
            seq_len
        );
        
        const expanded_shape = c.shape.getShape(c.shape.getView(expanded_view).shape_id);
        try testing.expectEqual(@as(usize, 4), expanded_shape.dims.len);
    }
    
    // Test 3: Squeeze operations
    {
        const expr_1 = try c.symbolic.newIntegerExpr(1);
        const expr_768_4 = try c.symbolic.newIntegerExpr(768);
        const squeeze_shape = try c.shape.newShape(&.{ batch, expr_1, expr_768_4 });
        const view3 = try c.shape.newDefaultView(squeeze_shape);
        
        // Squeeze the singleton dimension
        const squeezed_view = try c.shape.newSqueezeView(view3, &.{1});
        const squeezed_shape = c.shape.getShape(c.shape.getView(squeezed_view).shape_id);
        try testing.expectEqual(@as(usize, 2), squeezed_shape.dims.len);
    }
    
    // Test 4: Concat operations
    {
        const view_a = try c.shape.newDefaultView(shape1);
        const view_b = try c.shape.newDefaultView(shape1);
        
        // Concat along axis 1 (the concrete dimension)
        const concat_view = try c.shape.newConcatView(&.{view_a, view_b}, 1);
        const concat_shape = c.shape.getShape(c.shape.getView(concat_view).shape_id);
        
        // Should have doubled the size of dimension 1
        const eval_concat_dim = try c.symbolic.evaluate(concat_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 20), eval_concat_dim);
    }
    
    // Test 5: Reshape with -1 inference
    {
        const expr_2 = try c.symbolic.newIntegerExpr(2);
        const expr_3 = try c.symbolic.newIntegerExpr(3);
        const expr_4 = try c.symbolic.newIntegerExpr(4);
        const concrete_shape = try c.shape.newShape(&.{ expr_2, expr_3, expr_4 });
        
        // Reshape to [6, -1] - should infer -1 as 4
        const expr_6 = try c.symbolic.newIntegerExpr(6);
        const expr_max = try c.symbolic.newIntegerExpr(-1); // Using -1 for auto-infer
        const new_dims = [_]*types.Expr{ expr_6, expr_max };
        
        const inferred_shape_id = try c.shape.inferReshapeShape(concrete_shape, &new_dims);
        const inferred_shape = c.shape.getShape(inferred_shape_id);
        
        const eval_inferred = try c.symbolic.evaluate(inferred_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 4), eval_inferred);
    }
    
    // Test 6: Slicing with symbolic dimensions
    {
        const view4 = try c.shape.newDefaultView(shape2);
        const ranges = [_]types.SliceRange{
            .{ .start = 0, .end = -1, .step = 1 }, // Full slice on batch
            .{ .start = 0, .end = 10, .step = 1 }, // Partial slice on seq_len (step=1 for symbolic)
            .{ .start = 0, .end = 768, .step = 1 }, // Full slice on features
        };
        
        // This should not error with symbolic dimensions
        const sliced_view = try c.shape.newSlicedView(view4, &ranges);
        try testing.expect(sliced_view != view4);
    }
    
    // Test 7: Broadcasting compatibility check
    {
        const can_broadcast = c.shape.canBroadcastShapes(shape1, shape2);
        try testing.expect(can_broadcast);
        
        // Test incompatible shapes
        const expr_20 = try c.symbolic.newIntegerExpr(20);
        const expr_768_5 = try c.symbolic.newIntegerExpr(768);
        const incompatible_shape = try c.shape.newShape(&.{ batch, expr_20, expr_768_5 });
        
        const cannot_broadcast = !c.shape.canBroadcastShapes(shape1, incompatible_shape);
        try testing.expect(cannot_broadcast);
    }
    
    // Test 8: Dimension validation
    {
        // Valid dimensions (including symbolic)
        const expr_10_valid = try c.symbolic.newIntegerExpr(10);
        const valid_dims = [_]*types.Expr{ batch, expr_10_valid };
        try c.shape.validateDims(&valid_dims);
        
        // Invalid dimension (zero)
        const expr_0 = try c.symbolic.newIntegerExpr(0);
        const invalid_dims = [_]*types.Expr{ expr_0 };
        const result = c.shape.validateDims(&invalid_dims);
        try testing.expectError(error.InvalidDimension, result);
    }
}

test "shape engine - symbolic dimension preservation" {
    const allocator = testing.allocator;
    
    var c = try core.Core.init(allocator);
    defer c.deinit();
    
    // Create symbolic expressions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const height = try c.symbolic.newSymbolExpr("height");
    const width = try c.symbolic.newSymbolExpr("width");
    
    // Test that all operations preserve symbolic dimensions
    const expr_3 = try c.symbolic.newIntegerExpr(3);
    const symbolic_shape = try c.shape.newShape(&.{ batch, expr_3, height, width });
    
    const view = try c.shape.newDefaultView(symbolic_shape);
    
    // Permute axes - should preserve symbolic dims
    const permuted_view = try c.shape.newPermutedView(view, &.{0, 2, 3, 1});
    const permuted_shape = c.shape.getShape(c.shape.getView(permuted_view).shape_id);
    
    // Check that symbolic dimensions were preserved in correct positions
    try testing.expect(permuted_shape.dims[0] == batch);
    try testing.expect(permuted_shape.dims[1] == height);
}

test "shape engine - padding operations" {
    const allocator = testing.allocator;
    
    var c = try core.Core.init(allocator);
    defer c.deinit();
    
    // Test 1: Concrete padding
    {
        const expr_32 = try c.symbolic.newIntegerExpr(32);
        const expr_224_1 = try c.symbolic.newIntegerExpr(224);
        const expr_224_2 = try c.symbolic.newIntegerExpr(224);
        const expr_3_pad = try c.symbolic.newIntegerExpr(3);
        const shape = try c.shape.newShape(&.{ expr_32, expr_224_1, expr_224_2, expr_3_pad });
        
        const view = try c.shape.newDefaultView(shape);
        
        // Pad with concrete values
        const expr_0_1 = try c.symbolic.newIntegerExpr(0);
        const expr_0_2 = try c.symbolic.newIntegerExpr(0);
        const expr_2_1 = try c.symbolic.newIntegerExpr(2);
        const expr_2_2 = try c.symbolic.newIntegerExpr(2);
        const expr_2_3 = try c.symbolic.newIntegerExpr(2);
        const expr_2_4 = try c.symbolic.newIntegerExpr(2);
        const expr_0_3 = try c.symbolic.newIntegerExpr(0);
        const expr_0_4 = try c.symbolic.newIntegerExpr(0);
        const padding = [_][2]*types.Expr{
            [2]*types.Expr{ expr_0_1, expr_0_2 },    // No padding on batch
            [2]*types.Expr{ expr_2_1, expr_2_2 },    // Pad 2 on each side of height
            [2]*types.Expr{ expr_2_3, expr_2_4 },    // Pad 2 on each side of width
            [2]*types.Expr{ expr_0_3, expr_0_4 },    // No padding on channels
        };
        
        const padded_view = try c.shape.newPaddedView(view, &padding);
        const padded_shape = c.shape.getShape(c.shape.getView(padded_view).shape_id);
        
        const eval_batch_pad = try c.symbolic.evaluate(padded_shape.dims[0], null);
        const eval_height_pad = try c.symbolic.evaluate(padded_shape.dims[1], null);
        const eval_width_pad = try c.symbolic.evaluate(padded_shape.dims[2], null);
        const eval_channels_pad = try c.symbolic.evaluate(padded_shape.dims[3], null);
        try testing.expectEqual(@as(i64, 32), eval_batch_pad);
        try testing.expectEqual(@as(i64, 228), eval_height_pad); // 224 + 2 + 2
        try testing.expectEqual(@as(i64, 228), eval_width_pad); // 224 + 2 + 2
        try testing.expectEqual(@as(i64, 3), eval_channels_pad);
    }
    
    // Test 2: Symbolic padding
    {
        const batch = try c.symbolic.newSymbolExpr("batch");
        const height = try c.symbolic.newSymbolExpr("height");
        const width = try c.symbolic.newSymbolExpr("width");
        const pad_amount = try c.symbolic.newSymbolExpr("pad");
        
        const expr_3_sym = try c.symbolic.newIntegerExpr(3);
        const shape = try c.shape.newShape(&.{ batch, height, width, expr_3_sym });
        
        const view = try c.shape.newDefaultView(shape);
        
        // Pad with symbolic values
        const expr_0_sym1 = try c.symbolic.newIntegerExpr(0);
        const expr_0_sym2 = try c.symbolic.newIntegerExpr(0);
        const expr_1_sym1 = try c.symbolic.newIntegerExpr(1);
        const expr_1_sym2 = try c.symbolic.newIntegerExpr(1);
        const expr_0_sym3 = try c.symbolic.newIntegerExpr(0);
        const expr_0_sym4 = try c.symbolic.newIntegerExpr(0);
        const padding = [_][2]*types.Expr{
            [2]*types.Expr{ expr_0_sym1, expr_0_sym2 },          // No padding on batch
            [2]*types.Expr{ pad_amount, pad_amount },            // Symbolic padding on height
            [2]*types.Expr{ expr_1_sym1, expr_1_sym2 },          // Concrete padding on width
            [2]*types.Expr{ expr_0_sym3, expr_0_sym4 },          // No padding on channels
        };
        
        const padded_view = try c.shape.newPaddedView(view, &padding);
        const padded_shape = c.shape.getShape(c.shape.getView(padded_view).shape_id);
        
        // Check batch dimension is unchanged
        try testing.expect(padded_shape.dims[0] == batch);
        
        // Check height dimension is height + pad + pad
        // This should be an add expression
        try testing.expectEqual(types.Expr.Tag.add, padded_shape.dims[1].tag);
        
        // Check width dimension is width + 2
        try testing.expectEqual(types.Expr.Tag.add, padded_shape.dims[2].tag);
        
        // Check channels dimension is unchanged
        const eval_channels_sym = try c.symbolic.evaluate(padded_shape.dims[3], null);
        try testing.expectEqual(@as(i64, 3), eval_channels_sym);
    }
    
    // Test 3: Mixed padding (concrete and symbolic)
    {
        const batch = try c.symbolic.newSymbolExpr("batch");
        const pad_h = try c.symbolic.newSymbolExpr("pad_h");
        
        const expr_100_1 = try c.symbolic.newIntegerExpr(100);
        const expr_100_2 = try c.symbolic.newIntegerExpr(100);
        const shape = try c.shape.newShape(&.{ batch, expr_100_1, expr_100_2 });
        
        const view = try c.shape.newDefaultView(shape);
        
        // Mixed padding
        const expr_0_mix1 = try c.symbolic.newIntegerExpr(0);
        const expr_0_mix2 = try c.symbolic.newIntegerExpr(0);
        const expr_5_mix = try c.symbolic.newIntegerExpr(5);
        const expr_3_mix1 = try c.symbolic.newIntegerExpr(3);
        const expr_3_mix2 = try c.symbolic.newIntegerExpr(3);
        const padding = [_][2]*types.Expr{
            [2]*types.Expr{ expr_0_mix1, expr_0_mix2 },     // No padding on batch
            [2]*types.Expr{ pad_h, expr_5_mix },            // Mixed padding on height
            [2]*types.Expr{ expr_3_mix1, expr_3_mix2 },     // Concrete padding on width
        };
        
        const padded_view = try c.shape.newPaddedView(view, &padding);
        const padded_shape = c.shape.getShape(c.shape.getView(padded_view).shape_id);
        
        // Check height dimension is 100 + pad_h + 5
        try testing.expectEqual(types.Expr.Tag.add, padded_shape.dims[1].tag);
        
        // Check width dimension is 100 + 3 + 3 = 106
        const eval_width_mix = try c.symbolic.evaluate(padded_shape.dims[2], null);
        try testing.expectEqual(@as(i64, 106), eval_width_mix);
    }
}