// Comprehensive tests for polynomial expansion functionality
// Tests the algorithms adapted from SymEngine's expand.cpp

const std = @import("std");
const testing = std.testing;
const expect = testing.expect;
const expectEqual = testing.expectEqual;
const expectError = testing.expectError;

const core_module = @import("core");
const Core = core_module.Core;
const types = core_module.types;
const Symbol = types.Symbol;

test "polynomial expansion: basic multiplication expansion" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test expanding: x * (y + z) = x*y + x*z
    const x = try core.symbolic.newSymbolExpr("x");
    const y = try core.symbolic.newSymbolExpr("y");
    const z = try core.symbolic.newSymbolExpr("z");
    
    const y_plus_z = try core.symbolic.newBinaryExpr(.add, y, z);
    const x_times_sum = try core.symbolic.newBinaryExpr(.multiply, x, y_plus_z);
    
    const expanded = try core.symbolic.expandExpression(x_times_sum);
    
    // Should be x*y + x*z
    try expect(expanded.tag == .add);
    try expect(expanded.data.binary.left.tag == .multiply);
    try expect(expanded.data.binary.right.tag == .multiply);
}

test "polynomial expansion: distributive property left side" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test expanding: (a + b) * c = a*c + b*c
    const a = try core.symbolic.newSymbolExpr("a");
    const b = try core.symbolic.newSymbolExpr("b");
    const c = try core.symbolic.newSymbolExpr("c");
    
    const a_plus_b = try core.symbolic.newBinaryExpr(.add, a, b);
    const sum_times_c = try core.symbolic.newBinaryExpr(.multiply, a_plus_b, c);
    
    const expanded = try core.symbolic.expandExpression(sum_times_c);
    
    // Should be a*c + b*c
    try expect(expanded.tag == .add);
    try expect(expanded.data.binary.left.tag == .multiply);
    try expect(expanded.data.binary.right.tag == .multiply);
}

test "polynomial expansion: double distribution" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test expanding: (a + b) * (c + d) = a*c + a*d + b*c + b*d
    const a = try core.symbolic.newSymbolExpr("a");
    const b = try core.symbolic.newSymbolExpr("b");
    const c = try core.symbolic.newSymbolExpr("c");
    const d = try core.symbolic.newSymbolExpr("d");
    
    const a_plus_b = try core.symbolic.newBinaryExpr(.add, a, b);
    const c_plus_d = try core.symbolic.newBinaryExpr(.add, c, d);
    const product = try core.symbolic.newBinaryExpr(.multiply, a_plus_b, c_plus_d);
    
    const expanded = try core.symbolic.expandExpression(product);
    
    // Result should be a sum (the exact structure depends on how we distribute)
    try expect(expanded.tag == .add);
}

test "polynomial expansion: subtraction handling" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test expanding: x * (y - z) = x*y - x*z
    const x = try core.symbolic.newSymbolExpr("x");
    const y = try core.symbolic.newSymbolExpr("y");
    const z = try core.symbolic.newSymbolExpr("z");
    
    const y_minus_z = try core.symbolic.newBinaryExpr(.subtract, y, z);
    const x_times_diff = try core.symbolic.newBinaryExpr(.multiply, x, y_minus_z);
    
    const expanded = try core.symbolic.expandExpression(x_times_diff);
    
    // Should be x*y - x*z
    try expect(expanded.tag == .subtract);
    try expect(expanded.data.binary.left.tag == .multiply);
    try expect(expanded.data.binary.right.tag == .multiply);
}

test "polynomial expansion: nested expressions" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test expanding: 2 * (x + (y * 3))
    const two = try core.symbolic.newIntegerExpr(2);
    const three = try core.symbolic.newIntegerExpr(3);
    const x = try core.symbolic.newSymbolExpr("x");
    const y = try core.symbolic.newSymbolExpr("y");
    
    const y_times_three = try core.symbolic.newBinaryExpr(.multiply, y, three);
    const x_plus_inner = try core.symbolic.newBinaryExpr(.add, x, y_times_three);
    const full_expr = try core.symbolic.newBinaryExpr(.multiply, two, x_plus_inner);
    
    const expanded = try core.symbolic.expandExpression(full_expr);
    
    // Should be distributed: 2*x + 2*(y*3)
    try expect(expanded.tag == .add);
}

test "polynomial expansion: collect like terms - integers" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test collecting: 5 + 3 = 8
    const five = try core.symbolic.newIntegerExpr(5);
    const three = try core.symbolic.newIntegerExpr(3);
    const sum = try core.symbolic.newBinaryExpr(.add, five, three);
    
    const collected = try core.symbolic.collectLikeTerms(sum);
    
    // Should simplify to 8
    try expect(collected.tag == .integer);
    try expectEqual(@as(i64, 8), collected.data.integer);
}

test "polynomial expansion: collect like terms - integer subtraction" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test collecting: 10 - 3 = 7
    const ten = try core.symbolic.newIntegerExpr(10);
    const three = try core.symbolic.newIntegerExpr(3);
    const diff = try core.symbolic.newBinaryExpr(.subtract, ten, three);
    
    const collected = try core.symbolic.collectLikeTerms(diff);
    
    // Should simplify to 7
    try expect(collected.tag == .integer);
    try expectEqual(@as(i64, 7), collected.data.integer);
}

test "polynomial expansion: expression substitution" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test substituting x with 5 in expression: x + y
    const x = try core.symbolic.newSymbolExpr("x");
    const y = try core.symbolic.newSymbolExpr("y");
    const five = try core.symbolic.newIntegerExpr(5);
    
    const x_plus_y = try core.symbolic.newBinaryExpr(.add, x, y);
    const substituted = try core.symbolic.substituteExpression(x_plus_y, x, five);
    
    // Should be 5 + y
    try expect(substituted.tag == .add);
    try expect(substituted.data.binary.left.tag == .integer);
    try expectEqual(@as(i64, 5), substituted.data.binary.left.data.integer);
    try expect(substituted.data.binary.right.tag == .symbol);
}

test "polynomial expansion: complete substitution" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test substituting entire expression
    const x = try core.symbolic.newSymbolExpr("x");
    const y = try core.symbolic.newSymbolExpr("y");
    const replacement = try core.symbolic.newIntegerExpr(42);
    
    const target = try core.symbolic.newBinaryExpr(.multiply, x, y);
    const substituted = try core.symbolic.substituteExpression(target, target, replacement);
    
    // Should be replaced entirely with 42
    try expect(substituted.tag == .integer);
    try expectEqual(@as(i64, 42), substituted.data.integer);
}

test "polynomial expansion: expand and collect workflow" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test the main workflow: expand (2+3)*(4+5) and collect
    const two = try core.symbolic.newIntegerExpr(2);
    const three = try core.symbolic.newIntegerExpr(3);
    const four = try core.symbolic.newIntegerExpr(4);
    const five = try core.symbolic.newIntegerExpr(5);
    
    const left_sum = try core.symbolic.newBinaryExpr(.add, two, three);
    const right_sum = try core.symbolic.newBinaryExpr(.add, four, five);
    const product = try core.symbolic.newBinaryExpr(.multiply, left_sum, right_sum);
    
    // Expand and collect like terms in one operation
    const expanded = try core.symbolic.expandExpression(product);
    const result = try core.symbolic.collectLikeTerms(expanded);
    
    // After expansion and collection, this should be computed
    // (2+3)*(4+5) = 5*9 = 45 eventually, but intermediate may be more complex
    try expect(result.tag == .add or result.tag == .integer);
}

test "polynomial expansion: factorization - basic" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Test basic factorization (for now just returns the expression)
    const x = try core.symbolic.newSymbolExpr("x");
    const y = try core.symbolic.newSymbolExpr("y");
    
    const x_plus_y = try core.symbolic.newBinaryExpr(.add, x, y);
    const factorized = try core.symbolic.factorizeExpression(x_plus_y);
    
    // For now, should return the same expression
    try expect(factorized.tag == .add);
}

test "polynomial expansion: real world transformer example" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // Realistic ML example: expand (batch_size + 1) * (seq_len + embedding_dim)
    // This type of expansion happens in transformer attention calculations
    
    const batch_size = try core.symbolic.newSymbolExpr("batch_size");
    const seq_len = try core.symbolic.newSymbolExpr("seq_len");
    const embedding_dim = try core.symbolic.newSymbolExpr("embedding_dim");
    const one = try core.symbolic.newIntegerExpr(1);
    
    const batch_plus_one = try core.symbolic.newBinaryExpr(.add, batch_size, one);
    const seq_plus_embed = try core.symbolic.newBinaryExpr(.add, seq_len, embedding_dim);
    const product = try core.symbolic.newBinaryExpr(.multiply, batch_plus_one, seq_plus_embed);
    
    const expanded = try core.symbolic.expandExpression(product);
    
    // Should expand to: batch_size*seq_len + batch_size*embedding_dim + seq_len + embedding_dim
    try expect(expanded.tag == .add);
    
    // Verify it's more complex than the original (expanded form)
    try expect(expanded != product);
}

test "polynomial expansion: convolution dimension expansion" {
    var core = try Core.init(testing.allocator);
    defer core.deinit();

    // ML example: expand convolution output calculation
    // (input_h + 2*pad_h - kernel_h) * (input_w + 2*pad_w - kernel_w)
    
    const input_h = try core.symbolic.newSymbolExpr("input_h");
    const input_w = try core.symbolic.newSymbolExpr("input_w");
    const pad_h = try core.symbolic.newSymbolExpr("pad_h");
    const pad_w = try core.symbolic.newSymbolExpr("pad_w");
    const kernel_h = try core.symbolic.newSymbolExpr("kernel_h");
    const kernel_w = try core.symbolic.newSymbolExpr("kernel_w");
    const two = try core.symbolic.newIntegerExpr(2);
    
    // Build height calculation: input_h + 2*pad_h - kernel_h
    const two_pad_h = try core.symbolic.newBinaryExpr(.multiply, two, pad_h);
    const input_plus_pad_h = try core.symbolic.newBinaryExpr(.add, input_h, two_pad_h);
    const height_calc = try core.symbolic.newBinaryExpr(.subtract, input_plus_pad_h, kernel_h);
    
    // Build width calculation: input_w + 2*pad_w - kernel_w  
    const two_pad_w = try core.symbolic.newBinaryExpr(.multiply, two, pad_w);
    const input_plus_pad_w = try core.symbolic.newBinaryExpr(.add, input_w, two_pad_w);
    const width_calc = try core.symbolic.newBinaryExpr(.subtract, input_plus_pad_w, kernel_w);
    
    // Multiply them together
    const area_product = try core.symbolic.newBinaryExpr(.multiply, height_calc, width_calc);
    
    const expanded = try core.symbolic.expandExpression(area_product);
    
    // Should expand to a complex sum of products
    try expect(expanded.tag == .add or expanded.tag == .subtract);
    
    // The expanded form should be different from the original
    try expect(expanded != area_product);
}