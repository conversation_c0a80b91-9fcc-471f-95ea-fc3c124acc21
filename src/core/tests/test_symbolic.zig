// Test suite for V2 SymbolicEngine
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;

test "SymbolicEngine: create and retrieve integer expressions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test integer creation and caching
    const expr1 = try core_instance.symbolic.newIntegerExpr(42);
    const expr2 = try core_instance.symbolic.newIntegerExpr(42);
    const expr3 = try core_instance.symbolic.newIntegerExpr(100);
    
    // Should return same pointer for same value (cached)
    try testing.expectEqual(expr1, expr2);
    // Different value should be different pointer
    try testing.expect(expr1 != expr3);
    
    // Verify values
    try testing.expectEqual(expr1.tag, .integer);
    try testing.expectEqual(expr1.data.integer, 42);
    try testing.expectEqual(expr3.data.integer, 100);
}

test "SymbolicEngine: create and retrieve symbol expressions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test symbol creation and caching
    const batch_sym = try core_instance.symbolic.newSymbolExpr("batch_size");
    const seq_sym = try core_instance.symbolic.newSymbolExpr("seq_len");
    const batch_sym2 = try core_instance.symbolic.newSymbolExpr("batch_size");
    
    // Same symbol name should return same pointer
    try testing.expectEqual(batch_sym, batch_sym2);
    // Different symbols should be different
    try testing.expect(batch_sym != seq_sym);
    
    // Verify symbol names
    try testing.expectEqual(batch_sym.tag, .symbol);
    try testing.expectEqualStrings(batch_sym.data.symbol.name, "batch_size");
    try testing.expectEqualStrings(seq_sym.data.symbol.name, "seq_len");
}

test "SymbolicEngine: create binary expressions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    const a = try core_instance.symbolic.newIntegerExpr(10);
    const b = try core_instance.symbolic.newIntegerExpr(20);
    
    // Test addition (will be simplified to 30)
    const sum = try core_instance.symbolic.newBinaryExpr(.add, a, b);
    try testing.expectEqual(sum.tag, .integer);  // Constant folding
    try testing.expectEqual(sum.data.integer, 30);
    
    // Test multiplication (will be simplified to 200)
    const product = try core_instance.symbolic.newBinaryExpr(.multiply, a, b);
    try testing.expectEqual(product.tag, .integer);  // Constant folding
    try testing.expectEqual(product.data.integer, 200);
    
    // Test with symbols (no simplification)
    const x = try core_instance.symbolic.newSymbolExpr("x");
    const y = try core_instance.symbolic.newSymbolExpr("y");
    const sum_sym = try core_instance.symbolic.newBinaryExpr(.add, x, y);
    try testing.expectEqual(sum_sym.tag, .add);
    try testing.expectEqual(sum_sym.data.binary.left, x);
    try testing.expectEqual(sum_sym.data.binary.right, y);
}

test "SymbolicEngine: evaluate expressions" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create expression: batch_size * seq_len + hidden_dim
    const batch_size = try core_instance.symbolic.newSymbolExpr("batch_size");
    const seq_len = try core_instance.symbolic.newSymbolExpr("seq_len");
    const hidden_dim = try core_instance.symbolic.newIntegerExpr(768);
    
    const batch_seq = try core_instance.symbolic.newBinaryExpr(.multiply, batch_size, seq_len);
    const total = try core_instance.symbolic.newBinaryExpr(.add, batch_seq, hidden_dim);
    
    // Create bindings
    var bindings = std.StringHashMap(i64).init(allocator);
    defer bindings.deinit();
    try bindings.put("batch_size", 32);
    try bindings.put("seq_len", 128);
    
    // Evaluate
    const result = try core_instance.symbolic.evaluate(total, bindings);
    try testing.expectEqual(result, 32 * 128 + 768);
}

test "SymbolicEngine: dimension compatibility checking" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Test concrete dimensions
    const expr_5 = try core_instance.symbolic.newIntegerExpr(5);
    const expr_5_again = try core_instance.symbolic.newIntegerExpr(5);
    const expr_10 = try core_instance.symbolic.newIntegerExpr(10);
    
    try testing.expect(try core_instance.symbolic.checkExprCompatibility(expr_5, expr_5_again));
    try testing.expect(!try core_instance.symbolic.checkExprCompatibility(expr_5, expr_10));
    
    // Test symbolic dimensions
    const batch_sym = try core_instance.symbolic.newSymbolExpr("batch");
    _ = try core_instance.symbolic.newSymbolExpr("batch"); // Test repeated symbol creation
    
    const seq_sym = try core_instance.symbolic.newSymbolExpr("seq");
    
    try testing.expect(try core_instance.symbolic.checkExprCompatibility(batch_sym, batch_sym));
    try testing.expect(!try core_instance.symbolic.checkExprCompatibility(batch_sym, seq_sym));
    
    // Test mixed (concrete vs symbolic) - should be incompatible
    try testing.expect(!try core_instance.symbolic.checkExprCompatibility(expr_5, batch_sym));
}

test "SymbolicEngine: symbol substitution" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create expression: batch_size * 2 + 10
    const batch_size = try core_instance.symbolic.newSymbolExpr("batch_size");
    const two = try core_instance.symbolic.newIntegerExpr(2);
    const ten = try core_instance.symbolic.newIntegerExpr(10);
    
    const batch_times_2 = try core_instance.symbolic.newBinaryExpr(.multiply, batch_size, two);
    const expr = try core_instance.symbolic.newBinaryExpr(.add, batch_times_2, ten);
    
    // Substitute batch_size with 32
    const symbol = types.Symbol{ .name = "batch_size" };
    const substituted = try core_instance.symbolic.substituteSymbol(expr, symbol, 32);
    
    // Evaluate the substituted expression
    const result = try core_instance.symbolic.evaluate(substituted, null);
    try testing.expectEqual(result, 32 * 2 + 10);
}

test "SymbolicEngine: constraint tracking" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create symbolic dimensions
    const dim_a = try core_instance.symbolic.newSymbolExpr("dim_a");
    const dim_b = try core_instance.symbolic.newSymbolExpr("dim_b");
    
    // Create equality expression instead of constraint
    const eq_expr = try core_instance.symbolic.newBinaryExpr(.equal, dim_a, dim_b);
    
    // Test expression evaluation with bindings
    var bindings = std.StringHashMap(i64).init(allocator);
    defer bindings.deinit();
    try bindings.put("dim_a", 10);
    try bindings.put("dim_b", 10);
    
    const result = try core_instance.symbolic.evaluate(eq_expr, bindings);
    try testing.expectEqual(@as(i64, 1), result); // true as integer
}

test "SymbolicEngine: expression validation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Valid expressions
    const valid_int = try core_instance.symbolic.newIntegerExpr(100);
    try core_instance.symbolic.validateExpr(valid_int);
    
    const valid_sym = try core_instance.symbolic.newSymbolExpr("valid_name");
    try core_instance.symbolic.validateExpr(valid_sym);
    
    // Invalid expression (negative dimension)
    const invalid_int = try core_instance.symbolic.newIntegerExpr(-5);
    try testing.expectError(error.InvalidDimension, core_instance.symbolic.validateExpr(invalid_int));
}

test "SymbolicEngine: max and min operations" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    const a = try core_instance.symbolic.newIntegerExpr(10);
    const b = try core_instance.symbolic.newIntegerExpr(20);
    
    // Test max
    const max_expr = try core_instance.symbolic.newBinaryExpr(.max, a, b);
    const max_result = try core_instance.symbolic.evaluate(max_expr, null);
    try testing.expectEqual(max_result, 20);
    
    // Test min
    const min_expr = try core_instance.symbolic.newBinaryExpr(.min, a, b);
    const min_result = try core_instance.symbolic.evaluate(min_expr, null);
    try testing.expectEqual(min_result, 10);
}

test "SymbolicEngine: complex expression evaluation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create expression: max(batch_size * seq_len, min_size)
    const batch_size = try core_instance.symbolic.newSymbolExpr("batch_size");
    const seq_len = try core_instance.symbolic.newSymbolExpr("seq_len");
    const min_size = try core_instance.symbolic.newIntegerExpr(1000);
    
    const product = try core_instance.symbolic.newBinaryExpr(.multiply, batch_size, seq_len);
    const result_expr = try core_instance.symbolic.newBinaryExpr(.max, product, min_size);
    
    // Evaluate with small values (should get min_size)
    var bindings1 = std.StringHashMap(i64).init(allocator);
    defer bindings1.deinit();
    try bindings1.put("batch_size", 10);
    try bindings1.put("seq_len", 20);
    
    const result1 = try core_instance.symbolic.evaluate(result_expr, bindings1);
    try testing.expectEqual(result1, 1000); // max(10*20, 1000) = 1000
    
    // Evaluate with large values
    var bindings2 = std.StringHashMap(i64).init(allocator);
    defer bindings2.deinit();
    try bindings2.put("batch_size", 50);
    try bindings2.put("seq_len", 100);
    
    const result2 = try core_instance.symbolic.evaluate(result_expr, bindings2);
    try testing.expectEqual(result2, 5000); // max(50*100, 1000) = 5000
}

test "SymbolicEngine: memory management and arena allocation" {
    const allocator = testing.allocator;
    var core_instance = try Core.init(allocator);
    defer core_instance.deinit();
    
    // Create many expressions to test arena allocation
    var expressions = std.ArrayList(*types.Expr).init(allocator);
    defer expressions.deinit();
    
    // Create 100 expressions
    for (0..100) |i| {
        const int_expr = try core_instance.symbolic.newIntegerExpr(@intCast(i));
        try expressions.append(int_expr);
    }
    
    // Reset graph resources (this should clear the arena)
    core_instance.resetGraphResources();
    
    // Verify we can still create expressions after reset
    const new_expr = try core_instance.symbolic.newIntegerExpr(42);
    try testing.expectEqual(new_expr.tag, .integer);
    try testing.expectEqual(new_expr.data.integer, 42);
}