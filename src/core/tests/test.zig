// Basic test for the V2 architecture
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
// ops not needed for these basic tests

test "create core and engines" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Verify engines are initialized
    // Engines use ID pools which don't expose next_id
}

test "symbolic expression creation" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Create symbolic dimension
    const batch_sym = try core_instance.symbolic.newSymbolExpr("batch_size");
    try testing.expect(batch_sym.tag == .symbol);
    try testing.expectEqualStrings(batch_sym.data.symbol.name, "batch_size");
    
    // Create integer expression
    const ten = try core_instance.symbolic.newIntegerExpr(10);
    try testing.expect(ten.tag == .integer);
    try testing.expectEqual(ten.data.integer, 10);
}

test "shape creation and broadcasting" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Create shapes
    const expr_1 = try core_instance.symbolic.newIntegerExpr(1);
    const expr_3 = try core_instance.symbolic.newIntegerExpr(3);
    const shape_a = try core_instance.shape.newShape(&.{ expr_1, expr_3 });
    
    const expr_2 = try core_instance.symbolic.newIntegerExpr(2);
    const expr_1_b = try core_instance.symbolic.newIntegerExpr(1);
    const shape_b = try core_instance.shape.newShape(&.{ expr_2, expr_1_b });
    
    // Test broadcasting
    const broadcast_shape = try core_instance.shape.inferBroadcastShape(shape_a, shape_b);
    const result = core_instance.shape.getShape(broadcast_shape);
    
    try testing.expectEqual(result.dims.len, 2);
    const dim0_value = try core_instance.symbolic.evaluate(result.dims[0], null);
    const dim1_value = try core_instance.symbolic.evaluate(result.dims[1], null);
    try testing.expectEqual(dim0_value, 2);
    try testing.expectEqual(dim1_value, 3);
}

test "graph construction" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Create input shapes
    const expr_32 = try core_instance.symbolic.newIntegerExpr(32);
    const expr_10 = try core_instance.symbolic.newIntegerExpr(10);
    const input_shape = try core_instance.shape.newShape(&.{ expr_32, expr_10 });
    
    // Create input node
    const input_view = try core_instance.shape.newDefaultView(input_shape);
    const input_node = try core_instance.graph.newNodeConstant(input_view);
    const node = core_instance.graph.getNode(input_node).?;
    
    try testing.expectEqual(node.op, .constant);
    try testing.expectEqual(node.inputs.len, 0);
}

// Test the enhanced shape system features
test "shape system enhancements" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    // Manually run the tests that would be in test_dimension_collapsing.zig
    // Create Core instance
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Create a 3D shape
    const expr_2_c = try core_instance.symbolic.newIntegerExpr(2);  // dim 0
    const expr_3_c = try core_instance.symbolic.newIntegerExpr(3);  // dim 1
    const expr_4_c = try core_instance.symbolic.newIntegerExpr(4);  // dim 2
    const dims = [_]*types.Expr{ expr_2_c, expr_3_c, expr_4_c };
    
    const shape_id = try core_instance.shape.newShape(&dims);
    
    // Create a view with default strides
    const view_id = try core_instance.shape.newDefaultView(shape_id);
    
    // Basic verification that the view was created correctly
    const view = core_instance.shape.getView(view_id);
    try testing.expect(view.strides.len == 3);
}

