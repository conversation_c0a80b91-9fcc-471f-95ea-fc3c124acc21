// Tests for enhanced symbolic engine with new operations
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const BinaryOp = core.types.BinaryOp;

test "symbolic subtract operations" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Test x - 0 = x
    const x = try core_instance.symbolic.newSymbolExpr("x");
    const zero = try core_instance.symbolic.newIntegerExpr(0);
    const result1 = try core_instance.symbolic.newBinaryExpr(.subtract, x, zero);
    try testing.expectEqual(result1, x);
    
    // Test x - x = 0
    const result2 = try core_instance.symbolic.newBinaryExpr(.subtract, x, x);
    try testing.expectEqual(result2.tag, .integer);
    try testing.expectEqual(result2.data.integer, 0);
    
    // Test constant folding: 10 - 3 = 7
    const ten = try core_instance.symbolic.newIntegerExpr(10);
    const three = try core_instance.symbolic.newIntegerExpr(3);
    const result3 = try core_instance.symbolic.newBinaryExpr(.subtract, ten, three);
    try testing.expectEqual(result3.tag, .integer);
    try testing.expectEqual(result3.data.integer, 7);
}

test "symbolic divide operations" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Test x / 1 = x
    const x = try core_instance.symbolic.newSymbolExpr("x");
    const one = try core_instance.symbolic.newIntegerExpr(1);
    const result1 = try core_instance.symbolic.newBinaryExpr(.divide, x, one);
    try testing.expectEqual(result1, x);
    
    // Test 0 / x = 0
    const zero = try core_instance.symbolic.newIntegerExpr(0);
    const result2 = try core_instance.symbolic.newBinaryExpr(.divide, zero, x);
    try testing.expectEqual(result2.tag, .integer);
    try testing.expectEqual(result2.data.integer, 0);
    
    // Test x / x = 1
    const result3 = try core_instance.symbolic.newBinaryExpr(.divide, x, x);
    try testing.expectEqual(result3.tag, .integer);
    try testing.expectEqual(result3.data.integer, 1);
    
    // Test constant folding: 20 / 4 = 5
    const twenty = try core_instance.symbolic.newIntegerExpr(20);
    const four = try core_instance.symbolic.newIntegerExpr(4);
    const result4 = try core_instance.symbolic.newBinaryExpr(.divide, twenty, four);
    try testing.expectEqual(result4.tag, .integer);
    try testing.expectEqual(result4.data.integer, 5);
}

test "symbolic mod operations" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Test x % 1 = 0
    const x = try core_instance.symbolic.newSymbolExpr("x");
    const one = try core_instance.symbolic.newIntegerExpr(1);
    const result1 = try core_instance.symbolic.newBinaryExpr(.mod, x, one);
    try testing.expectEqual(result1.tag, .integer);
    try testing.expectEqual(result1.data.integer, 0);
    
    // Test constant folding: 10 % 3 = 1
    const ten = try core_instance.symbolic.newIntegerExpr(10);
    const three = try core_instance.symbolic.newIntegerExpr(3);
    const result2 = try core_instance.symbolic.newBinaryExpr(.mod, ten, three);
    try testing.expectEqual(result2.tag, .integer);
    try testing.expectEqual(result2.data.integer, 1);
    
    // Test 20 % 5 = 0
    const twenty = try core_instance.symbolic.newIntegerExpr(20);
    const five = try core_instance.symbolic.newIntegerExpr(5);
    const result3 = try core_instance.symbolic.newBinaryExpr(.mod, twenty, five);
    try testing.expectEqual(result3.tag, .integer);
    try testing.expectEqual(result3.data.integer, 0);
}

test "symbolic comparison operations" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Test x < x => false (0)
    const x = try core_instance.symbolic.newSymbolExpr("x");
    const result1 = try core_instance.symbolic.newBinaryExpr(.less_than, x, x);
    try testing.expectEqual(result1.tag, .integer);
    try testing.expectEqual(result1.data.integer, 0);
    
    // Test x > x => false (0)
    const result2 = try core_instance.symbolic.newBinaryExpr(.greater_than, x, x);
    try testing.expectEqual(result2.tag, .integer);
    try testing.expectEqual(result2.data.integer, 0);
    
    // Test x <= x => true (1)
    const result3 = try core_instance.symbolic.newBinaryExpr(.less_equal, x, x);
    try testing.expectEqual(result3.tag, .integer);
    try testing.expectEqual(result3.data.integer, 1);
    
    // Test x >= x => true (1)
    const result4 = try core_instance.symbolic.newBinaryExpr(.greater_equal, x, x);
    try testing.expectEqual(result4.tag, .integer);
    try testing.expectEqual(result4.data.integer, 1);
    
    // Test x != x => false (0)
    const result5 = try core_instance.symbolic.newBinaryExpr(.not_equal, x, x);
    try testing.expectEqual(result5.tag, .integer);
    try testing.expectEqual(result5.data.integer, 0);
}

test "symbolic comparison with constants" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    const five = try core_instance.symbolic.newIntegerExpr(5);
    const ten = try core_instance.symbolic.newIntegerExpr(10);
    
    // Test 5 < 10 => true (1)
    const result1 = try core_instance.symbolic.newBinaryExpr(.less_than, five, ten);
    try testing.expectEqual(result1.tag, .integer);
    try testing.expectEqual(result1.data.integer, 1);
    
    // Test 10 < 5 => false (0)
    const result2 = try core_instance.symbolic.newBinaryExpr(.less_than, ten, five);
    try testing.expectEqual(result2.tag, .integer);
    try testing.expectEqual(result2.data.integer, 0);
    
    // Test 5 > 10 => false (0)
    const result3 = try core_instance.symbolic.newBinaryExpr(.greater_than, five, ten);
    try testing.expectEqual(result3.tag, .integer);
    try testing.expectEqual(result3.data.integer, 0);
    
    // Test 10 > 5 => true (1)
    const result4 = try core_instance.symbolic.newBinaryExpr(.greater_than, ten, five);
    try testing.expectEqual(result4.tag, .integer);
    try testing.expectEqual(result4.data.integer, 1);
    
    // Test 5 != 10 => true (1)
    const result5 = try core_instance.symbolic.newBinaryExpr(.not_equal, five, ten);
    try testing.expectEqual(result5.tag, .integer);
    try testing.expectEqual(result5.data.integer, 1);
    
    // Test 5 == 5 => true (1)
    const result6 = try core_instance.symbolic.newBinaryExpr(.equal, five, five);
    try testing.expectEqual(result6.tag, .integer);
    try testing.expectEqual(result6.data.integer, 1);
}

test "complex expression evaluation" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    const core_instance = try Core.init(gpa.allocator());
    defer core_instance.deinit();
    
    // Test (10 + 5) / 3 = 5
    const ten = try core_instance.symbolic.newIntegerExpr(10);
    const five = try core_instance.symbolic.newIntegerExpr(5);
    const three = try core_instance.symbolic.newIntegerExpr(3);
    
    const add_result = try core_instance.symbolic.newBinaryExpr(.add, ten, five);
    const div_result = try core_instance.symbolic.newBinaryExpr(.divide, add_result, three);
    
    try testing.expectEqual(div_result.tag, .integer);
    try testing.expectEqual(div_result.data.integer, 5);
    
    // Test (20 - 5) % 4 = 3
    const twenty = try core_instance.symbolic.newIntegerExpr(20);
    const four = try core_instance.symbolic.newIntegerExpr(4);
    
    const sub_result = try core_instance.symbolic.newBinaryExpr(.subtract, twenty, five);
    const mod_result = try core_instance.symbolic.newBinaryExpr(.mod, sub_result, four);
    
    try testing.expectEqual(mod_result.tag, .integer);
    try testing.expectEqual(mod_result.data.integer, 3);
}