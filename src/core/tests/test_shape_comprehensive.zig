const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
// Fixed import - access shape types through core module
const SliceRange = core.types.SliceRange;

// Test all ShapeEngine operations comprehensively

test "ShapeEngine: basic shape creation and retrieval" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Test 1: Basic shape creation  
    const expr_2 = try core_instance.symbolic.newIntegerExpr(2);
    const expr_3 = try core_instance.symbolic.newIntegerExpr(3);
    const expr_4 = try core_instance.symbolic.newIntegerExpr(4);
    const dims = [_]*types.Expr{ expr_2, expr_3, expr_4 };
    const shape_id = try c.shape.newShape(&dims);
    
    // Test 2: Shape retrieval
    const shape = c.shape.getShape(shape_id);
    try testing.expectEqual(@as(usize, 3), shape.dims.len);
    // Evaluate expressions to check values
    const eval_0 = try c.symbolic.evaluate(shape.dims[0], null);
    const eval_1 = try c.symbolic.evaluate(shape.dims[1], null);
    const eval_2 = try c.symbolic.evaluate(shape.dims[2], null);
    try testing.expectEqual(@as(i64, 2), eval_0);
    try testing.expectEqual(@as(i64, 3), eval_1);
    try testing.expectEqual(@as(i64, 4), eval_2);
    
    // Test 3: Shape caching (same dims should return same ID)
    const shape_id_2 = try c.shape.newShape(&dims);
    try testing.expectEqual(shape_id, shape_id_2);
    
    // Test 4: Different shape gets different ID
    const expr_4_new = try core_instance.symbolic.newIntegerExpr(4);
    const expr_5 = try core_instance.symbolic.newIntegerExpr(5);
    const dims_2 = [_]*types.Expr{ expr_4_new, expr_5 };
    const shape_id_3 = try c.shape.newShape(&dims_2);
    try testing.expect(shape_id != shape_id_3);
}

test "ShapeEngine: view creation and management" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    const expr_10 = try core_instance.symbolic.newIntegerExpr(10);
    const expr_20 = try core_instance.symbolic.newIntegerExpr(20);
    const expr_30 = try core_instance.symbolic.newIntegerExpr(30);
    const shape_id = try c.shape.newShape(&.{ expr_10, expr_20, expr_30 });
    
    // Test default view creation
    const view_id = try c.shape.newDefaultView(shape_id);
    const view = c.shape.getView(view_id);
    
    try testing.expectEqual(shape_id, view.shape_id);
    try testing.expectEqual(@as(usize, 0), view.offset_elements);
    
    // Test strides are correct for row-major layout
    try testing.expectEqual(@as(i64, 600), view.strides[0]); // 20 * 30
    try testing.expectEqual(@as(i64, 30), view.strides[1]);  // 30
    try testing.expectEqual(@as(i64, 1), view.strides[2]);   // 1
}

test "ShapeEngine: reshape operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Create original shape [2, 3, 4]
    const expr_2 = try core_instance.symbolic.newIntegerExpr(2);
    const expr_3 = try core_instance.symbolic.newIntegerExpr(3);
    const expr_4 = try core_instance.symbolic.newIntegerExpr(4);
    const orig_shape_id = try c.shape.newShape(&.{ expr_2, expr_3, expr_4 });
    const orig_view_id = try c.shape.newDefaultView(orig_shape_id);
    
    // Test 1: Valid reshape to [6, 4]
    const expr_6 = try core_instance.symbolic.newIntegerExpr(6);
    const expr_4_new = try core_instance.symbolic.newIntegerExpr(4);
    const new_shape_id_1 = try c.shape.newShape(&.{ expr_6, expr_4_new });
    const reshaped_view_1 = try c.shape.newReshapedView(orig_view_id, new_shape_id_1);
    const reshaped_shape_1 = c.shape.getShape(c.shape.getView(reshaped_view_1).shape_id);
    try testing.expectEqual(@as(usize, 2), reshaped_shape_1.dims.len);
    // Evaluate expressions to check values
    const eval_0 = try c.symbolic.evaluate(reshaped_shape_1.dims[0], null);
    const eval_1 = try c.symbolic.evaluate(reshaped_shape_1.dims[1], null);
    try testing.expectEqual(@as(i64, 6), eval_0);
    try testing.expectEqual(@as(i64, 4), eval_1);
    
    // Test 2: Invalid reshape (different element count)
    const expr_5_1 = try core_instance.symbolic.newIntegerExpr(5);
    const expr_5_2 = try core_instance.symbolic.newIntegerExpr(5);
    const bad_shape_id = try c.shape.newShape(&.{ expr_5_1, expr_5_2 });
    const bad_result = c.shape.newReshapedView(orig_view_id, bad_shape_id);
    try testing.expectError(error.IncompatibleShapes, bad_result);
    
    // Test 3: Reshape with symbolic dimensions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const expr_24 = try core_instance.symbolic.newIntegerExpr(24);
    const sym_shape_id = try c.shape.newShape(&.{ batch, expr_24 });
    const sym_view_id = try c.shape.newDefaultView(sym_shape_id);
    
    const expr_4_sym = try core_instance.symbolic.newIntegerExpr(4);
    const expr_6_sym = try core_instance.symbolic.newIntegerExpr(6);
    const sym_new_shape_id = try c.shape.newShape(&.{ batch, expr_4_sym, expr_6_sym });
    // Should succeed with symbolic dimensions
    const sym_reshaped = try c.shape.newReshapedView(sym_view_id, sym_new_shape_id);
    try testing.expect(sym_reshaped != sym_view_id);
}

test "ShapeEngine: permute operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Create shape [2, 3, 4]
    const shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Test 1: Valid permutation [2, 0, 1] -> [4, 2, 3]
    const axes = [_]u32{ 2, 0, 1 };
    const permuted_view_id = try c.shape.newPermutedView(view_id, &axes);
    const permuted_view = c.shape.getView(permuted_view_id);
    const permuted_shape = c.shape.getShape(permuted_view.shape_id);
    
    try testing.expectEqual(@as(usize, 4), permuted_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 2), permuted_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 3), permuted_shape.dims[2].concrete);
    
    // Check strides were permuted correctly
    const orig_view = c.shape.getView(view_id);
    try testing.expectEqual(orig_view.strides[2], permuted_view.strides[0]);
    try testing.expectEqual(orig_view.strides[0], permuted_view.strides[1]);
    try testing.expectEqual(orig_view.strides[1], permuted_view.strides[2]);
    
    // Test 2: Invalid permutation (duplicate axes)
    const bad_axes = [_]u32{ 0, 0, 1 };
    const bad_result = c.shape.newPermutedView(view_id, &bad_axes);
    try testing.expectError(error.InvalidAxes, bad_result);
    
    // Test 3: Invalid permutation (out of bounds)
    const oob_axes = [_]u32{ 0, 1, 3 };
    const oob_result = c.shape.newPermutedView(view_id, &oob_axes);
    try testing.expectError(error.InvalidAxes, oob_result);
}

test "ShapeEngine: slice operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Create shape [10, 20, 30]
    const shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 10 },
        types.Dim{ .concrete = 20 },
        types.Dim{ .concrete = 30 },
    });
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Test 1: Basic slice [2:8, 5:15, :] -> [6, 10, 30]
    const ranges = [_]SliceRange{
        .{ .start = 2, .end = 8, .step = 1 },
        .{ .start = 5, .end = 15, .step = 1 },
        .{ .start = 0, .end = 30, .step = 1 },
    };
    const sliced_view_id = try c.shape.newSlicedView(view_id, &ranges);
    const sliced_view = c.shape.getView(sliced_view_id);
    const sliced_shape = c.shape.getShape(sliced_view.shape_id);
    
    try testing.expectEqual(@as(usize, 6), sliced_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 10), sliced_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 30), sliced_shape.dims[2].concrete);
    
    // Check offset was updated correctly
    const orig_view = c.shape.getView(view_id);
    const expected_offset = 2 * @as(usize, @intCast(@abs(orig_view.strides[0]))) + 
                           5 * @as(usize, @intCast(@abs(orig_view.strides[1])));
    try testing.expectEqual(expected_offset, sliced_view.offset_elements);
    
    // Test 2: Slice with step
    const step_ranges = [_]SliceRange{
        .{ .start = 0, .end = 10, .step = 2 },  // [0, 2, 4, 6, 8] -> 5 elements
        .{ .start = 0, .end = 20, .step = 3 },  // [0, 3, 6, ..., 18] -> 7 elements
        .{ .start = 0, .end = 30, .step = 1 },
    };
    const step_view_id = try c.shape.newSlicedView(view_id, &step_ranges);
    const step_shape = c.shape.getShape(c.shape.getView(step_view_id).shape_id);
    
    try testing.expectEqual(@as(usize, 5), step_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 7), step_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 30), step_shape.dims[2].concrete);
    
    // Test 3: Negative indices
    const neg_ranges = [_]SliceRange{
        .{ .start = -8, .end = -2, .step = 1 },  // [2:8] -> 6 elements
        .{ .start = 0, .end = -5, .step = 1 },   // [0:15] -> 15 elements
        .{ .start = -30, .end = -1, .step = 1 }, // [0:29] -> 29 elements
    };
    const neg_view_id = try c.shape.newSlicedView(view_id, &neg_ranges);
    const neg_shape = c.shape.getShape(c.shape.getView(neg_view_id).shape_id);
    
    try testing.expectEqual(@as(usize, 6), neg_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 15), neg_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 29), neg_shape.dims[2].concrete);
    
    // Test 4: Slice with symbolic dimensions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const sym_shape_id = try c.shape.newShape(&.{
        types.Dim{ .symbolic = batch },
        types.Dim{ .concrete = 20 },
        types.Dim{ .concrete = 30 },
    });
    const sym_view_id = try c.shape.newDefaultView(sym_shape_id);
    
    const sym_ranges = [_]SliceRange{
        .{ .start = 0, .end = -1, .step = 1 }, // Full slice on symbolic dim
        .{ .start = 5, .end = 15, .step = 1 },
        .{ .start = 0, .end = 30, .step = 1 },
    };
    const sym_sliced_id = try c.shape.newSlicedView(sym_view_id, &sym_ranges);
    const sym_sliced_shape = c.shape.getShape(c.shape.getView(sym_sliced_id).shape_id);
    
    // Symbolic dimension should be preserved
    try testing.expect(sym_sliced_shape.dims[0].symbolic == batch);
    try testing.expectEqual(@as(usize, 10), sym_sliced_shape.dims[1].concrete);
}

test "ShapeEngine: broadcast operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Test 1: Basic broadcasting [3, 1] -> [2, 3, 4]
    const input_shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 1 },
    });
    const input_view_id = try c.shape.newDefaultView(input_shape_id);
    
    const target_shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    
    const broadcast_view_id = try c.shape.newBroadcastView(input_view_id, target_shape_id);
    const broadcast_view = c.shape.getView(broadcast_view_id);
    
    // Check shape is correct
    try testing.expectEqual(target_shape_id, broadcast_view.shape_id);
    
    // Check strides are set correctly for broadcasting
    try testing.expectEqual(@as(i64, 0), broadcast_view.strides[0]); // New dimension
    try testing.expectEqual(@as(i64, 1), broadcast_view.strides[1]); // Original stride
    try testing.expectEqual(@as(i64, 0), broadcast_view.strides[2]); // Broadcast dimension
    
    // Test 2: Invalid broadcast (incompatible shapes)
    const incompat_shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 5 }, // Incompatible with 3
        types.Dim{ .concrete = 4 },
    });
    const bad_result = c.shape.newBroadcastView(input_view_id, incompat_shape_id);
    try testing.expectError(types.ShapeError.IncompatibleShapes, bad_result);
    
    // Test 3: Broadcasting with symbolic dimensions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const sym_input_id = try c.shape.newShape(&.{
        types.Dim{ .symbolic = batch },
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 768 },
    });
    const sym_input_view = try c.shape.newDefaultView(sym_input_id);
    
    const sym_target_id = try c.shape.newShape(&.{
        types.Dim{ .symbolic = batch },
        types.Dim{ .concrete = 10 },
        types.Dim{ .concrete = 768 },
    });
    
    const sym_broadcast_id = try c.shape.newBroadcastView(sym_input_view, sym_target_id);
    const sym_broadcast_view = c.shape.getView(sym_broadcast_id);
    
    // Middle dimension should have stride 0 (broadcast)
    try testing.expectEqual(@as(i64, 0), sym_broadcast_view.strides[1]);
}

test "ShapeEngine: expand operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Start with shape [3, 4]
    const shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Test 1: Expand at beginning -> [1, 3, 4]
    const expanded_1 = try c.shape.newExpandedView(view_id, 0, types.Dim{ .concrete = 1 });
    const expanded_shape_1 = c.shape.getShape(c.shape.getView(expanded_1).shape_id);
    
    try testing.expectEqual(@as(usize, 3), expanded_shape_1.dims.len);
    try testing.expectEqual(@as(usize, 1), expanded_shape_1.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), expanded_shape_1.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), expanded_shape_1.dims[2].concrete);
    
    // New dimension should have stride 0
    const expanded_view_1 = c.shape.getView(expanded_1);
    try testing.expectEqual(@as(i64, 0), expanded_view_1.strides[0]);
    
    // Test 2: Expand in middle -> [3, 1, 4]
    const expanded_2 = try c.shape.newExpandedView(view_id, 1, types.Dim{ .concrete = 1 });
    const expanded_shape_2 = c.shape.getShape(c.shape.getView(expanded_2).shape_id);
    
    try testing.expectEqual(@as(usize, 3), expanded_shape_2.dims.len);
    try testing.expectEqual(@as(usize, 3), expanded_shape_2.dims[0].concrete);
    try testing.expectEqual(@as(usize, 1), expanded_shape_2.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), expanded_shape_2.dims[2].concrete);
    
    // Test 3: Expand with symbolic dimension
    const seq_len = try c.symbolic.newSymbolExpr("seq_len");
    const expanded_3 = try c.shape.newExpandedView(view_id, 1, types.Dim{ .symbolic = seq_len });
    const expanded_shape_3 = c.shape.getShape(c.shape.getView(expanded_3).shape_id);
    
    try testing.expectEqual(@as(usize, 3), expanded_shape_3.dims.len);
    try testing.expect(expanded_shape_3.dims[1].symbolic == seq_len);
}

test "ShapeEngine: squeeze operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Start with shape [2, 1, 3, 1, 4]
    const shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 4 },
    });
    const view_id = try c.shape.newDefaultView(shape_id);
    
    // Test 1: Squeeze specific dimensions
    const squeezed_1 = try c.shape.newSqueezeView(view_id, &.{ 1, 3 });
    const squeezed_shape_1 = c.shape.getShape(c.shape.getView(squeezed_1).shape_id);
    
    try testing.expectEqual(@as(usize, 3), squeezed_shape_1.dims.len);
    try testing.expectEqual(@as(usize, 2), squeezed_shape_1.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), squeezed_shape_1.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), squeezed_shape_1.dims[2].concrete);
    
    // Test 2: Squeeze all singleton dimensions (empty array)
    const squeezed_2 = try c.shape.newSqueezeView(view_id, &.{});
    const squeezed_shape_2 = c.shape.getShape(c.shape.getView(squeezed_2).shape_id);
    
    try testing.expectEqual(@as(usize, 3), squeezed_shape_2.dims.len);
    try testing.expectEqual(@as(usize, 2), squeezed_shape_2.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), squeezed_shape_2.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), squeezed_shape_2.dims[2].concrete);
    
    // Test 3: Invalid squeeze (non-singleton dimension)
    const bad_result = c.shape.newSqueezeView(view_id, &.{0}); // Dim 0 is 2, not 1
    try testing.expectError(error.InvalidAxes, bad_result);
}

test "ShapeEngine: concat operation" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Create shapes to concatenate
    const shape_1_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    const view_1_id = try c.shape.newDefaultView(shape_1_id);
    
    const shape_2_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 5 }, // Different size on axis 1
        types.Dim{ .concrete = 4 },
    });
    const view_2_id = try c.shape.newDefaultView(shape_2_id);
    
    // Test 1: Concatenate along axis 1
    const concat_1 = try c.shape.newConcatView(&.{ view_1_id, view_2_id }, 1);
    const concat_shape_1 = c.shape.getShape(c.shape.getView(concat_1).shape_id);
    
    try testing.expectEqual(@as(usize, 3), concat_shape_1.dims.len);
    try testing.expectEqual(@as(usize, 2), concat_shape_1.dims[0].concrete);
    try testing.expectEqual(@as(usize, 8), concat_shape_1.dims[1].concrete); // 3 + 5
    try testing.expectEqual(@as(usize, 4), concat_shape_1.dims[2].concrete);
    
    // Test 2: Incompatible shapes
    const shape_3_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 3 }, // Different size on axis 0
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    const view_3_id = try c.shape.newDefaultView(shape_3_id);
    
    const bad_result = c.shape.newConcatView(&.{ view_1_id, view_3_id }, 1);
    try testing.expectError(error.IncompatibleShapes, bad_result);
    
    // Test 3: Empty array
    const empty_result = c.shape.newConcatView(&.{}, 0);
    try testing.expectError(types.ShapeError.InvalidArgumentCount, empty_result);
}

test "ShapeEngine: broadcast shape inference" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Test 1: Basic broadcast inference
    const shape_a_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 1 },
    });
    
    const shape_b_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 5 },
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 4 },
    });
    
    const broadcast_shape_id = try c.shape.inferBroadcastShape(shape_a_id, shape_b_id);
    const broadcast_shape = c.shape.getShape(broadcast_shape_id);
    
    try testing.expectEqual(@as(usize, 3), broadcast_shape.dims.len);
    try testing.expectEqual(@as(usize, 5), broadcast_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), broadcast_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), broadcast_shape.dims[2].concrete);
    
    // Test 2: Different ranks
    const shape_c_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 1 },
    });
    
    const broadcast_shape_2_id = try c.shape.inferBroadcastShape(shape_c_id, shape_b_id);
    const broadcast_shape_2 = c.shape.getShape(broadcast_shape_2_id);
    
    try testing.expectEqual(@as(usize, 3), broadcast_shape_2.dims.len);
    try testing.expectEqual(@as(usize, 5), broadcast_shape_2.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), broadcast_shape_2.dims[1].concrete);
    try testing.expectEqual(@as(usize, 4), broadcast_shape_2.dims[2].concrete);
    
    // Test 3: Symbolic dimensions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const shape_d_id = try c.shape.newShape(&.{
        types.Dim{ .symbolic = batch },
        types.Dim{ .concrete = 1 },
        types.Dim{ .concrete = 768 },
    });
    
    const shape_e_id = try c.shape.newShape(&.{
        types.Dim{ .symbolic = batch },
        types.Dim{ .concrete = 10 },
        types.Dim{ .concrete = 768 },
    });
    
    const broadcast_shape_3_id = try c.shape.inferBroadcastShape(shape_d_id, shape_e_id);
    const broadcast_shape_3 = c.shape.getShape(broadcast_shape_3_id);
    
    try testing.expectEqual(@as(usize, 3), broadcast_shape_3.dims.len);
    try testing.expect(broadcast_shape_3.dims[0].symbolic == batch);
    try testing.expectEqual(@as(usize, 10), broadcast_shape_3.dims[1].concrete);
    try testing.expectEqual(@as(usize, 768), broadcast_shape_3.dims[2].concrete);
}

test "ShapeEngine: reshape with -1 inference" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Create shape [2, 3, 4] = 24 elements
    const shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    
    // Test 1: Infer single dimension
    const new_dims_1 = [_]types.Dim{
        types.Dim{ .concrete = 6 },
        types.Dim{ .concrete = std.math.maxInt(usize) }, // -1
    };
    
    const inferred_1_id = try c.shape.inferReshapeShape(shape_id, &new_dims_1);
    const inferred_1 = c.shape.getShape(inferred_1_id);
    
    try testing.expectEqual(@as(usize, 2), inferred_1.dims.len);
    try testing.expectEqual(@as(usize, 6), inferred_1.dims[0].concrete);
    try testing.expectEqual(@as(usize, 4), inferred_1.dims[1].concrete); // Inferred
    
    // Test 2: Invalid inference (not divisible)
    const new_dims_2 = [_]types.Dim{
        types.Dim{ .concrete = 5 },
        types.Dim{ .concrete = std.math.maxInt(usize) }, // -1
    };
    
    const bad_result = c.shape.inferReshapeShape(shape_id, &new_dims_2);
    try testing.expectError(error.IncompatibleShapes, bad_result);
    
    // Test 3: Multiple -1 (invalid)
    const new_dims_3 = [_]types.Dim{
        types.Dim{ .concrete = std.math.maxInt(usize) }, // -1
        types.Dim{ .concrete = std.math.maxInt(usize) }, // -1
    };
    
    const bad_result_2 = c.shape.inferReshapeShape(shape_id, &new_dims_3);
    try testing.expectError(error.InvalidShape, bad_result_2);
}

test "ShapeEngine: layout calculations" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Test 1: Row-major strides
    const shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 2 },
        types.Dim{ .concrete = 3 },
        types.Dim{ .concrete = 4 },
    });
    
    const strides = try c.shape.calculateStrides(shape_id);
    try testing.expectEqual(@as(i64, 12), strides[0]); // 3 * 4
    try testing.expectEqual(@as(i64, 4), strides[1]);  // 4
    try testing.expectEqual(@as(i64, 1), strides[2]);  // 1
    
    // Test 2: Contiguity check
    const view_id = try c.shape.newDefaultView(shape_id);
    try testing.expect(c.shape.isContiguous(view_id));
    
    // After permutation, should not be contiguous
    const perm_view_id = try c.shape.newPermutedView(view_id, &.{ 2, 0, 1 });
    try testing.expect(!c.shape.isContiguous(perm_view_id));
    
    // Test 3: Symbolic dimensions
    const batch = try c.symbolic.newSymbolExpr("batch");
    const sym_shape_id = try c.shape.newShape(&.{
        types.Dim{ .symbolic = batch },
        types.Dim{ .concrete = 10 },
        types.Dim{ .concrete = 20 },
    });
    
    const sym_strides = try c.shape.calculateStrides(sym_shape_id);
    try testing.expectEqual(@as(i64, 1), sym_strides[0]); // Symbolic gets stride 1
    try testing.expectEqual(@as(i64, 20), sym_strides[1]);
    try testing.expectEqual(@as(i64, 1), sym_strides[2]);
}

test "ShapeEngine: ML-specific helpers" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Test image shape creation
    const image_shape_id = try c.shape.newImageShape(32, 3, 224, 224);
    const image_shape = c.shape.getShape(image_shape_id);
    
    try testing.expectEqual(@as(usize, 4), image_shape.dims.len);
    try testing.expectEqual(@as(usize, 32), image_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 3), image_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 224), image_shape.dims[2].concrete);
    try testing.expectEqual(@as(usize, 224), image_shape.dims[3].concrete);
    
    // Test sequence shape creation
    const seq_shape_id = try c.shape.newSequenceShape(16, 128, 768);
    const seq_shape = c.shape.getShape(seq_shape_id);
    
    try testing.expectEqual(@as(usize, 3), seq_shape.dims.len);
    try testing.expectEqual(@as(usize, 16), seq_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 128), seq_shape.dims[1].concrete);
    try testing.expectEqual(@as(usize, 768), seq_shape.dims[2].concrete);
}

test "ShapeEngine: edge cases" {
    const allocator = testing.allocator;
    var c = try Core.init(allocator);
    defer c.deinit();
    
    // Test 1: Empty shape (scalar)
    const scalar_shape_id = try c.shape.newShape(&.{});
    const scalar_shape = c.shape.getShape(scalar_shape_id);
    try testing.expectEqual(@as(usize, 0), scalar_shape.dims.len);
    
    // Test 2: Zero dimension validation
    const zero_dims = [_]types.Dim{
        types.Dim{ .concrete = 0 },
    };
    const result = c.shape.validateDims(&zero_dims);
    try testing.expectError(error.InvalidDimension, result);
    
    // Test 3: Very large shape
    const large_shape_id = try c.shape.newShape(&.{
        types.Dim{ .concrete = 1000 },
        types.Dim{ .concrete = 1000 },
        types.Dim{ .concrete = 1000 },
    });
    const large_strides = try c.shape.calculateStrides(large_shape_id);
    try testing.expectEqual(@as(i64, 1000000), large_strides[0]);
}