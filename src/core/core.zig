// Core engine coordination and memory management
const std = @import("std");
pub const types = @import("types.zig");
pub const errors = @import("errors.zig");
pub const graph = struct {
    pub const types = @import("graph/types.zig");
    pub const engine = @import("graph/engine.zig");
};
pub const shape = struct {
    pub const engine = @import("shape/engine.zig");
    pub const symbolic_utils = @import("shape/symbolic_utils.zig");
};
pub const symbolic = struct {
    pub const engine = @import("symbolic/engine.zig");
};
pub const data = @import("data.zig");

// Import core engines (from within core/)
const SymbolicEngine = @import("symbolic/engine.zig").SymbolicEngine;
const ShapeEngine = @import("shape/engine.zig").ShapeEngine;
const GraphEngine = @import("graph/engine.zig").GraphEngine;
const DataStore = @import("data.zig").DataStore;
const CompilerEngine = @import("compiler/engine.zig").CompilerEngine;

pub const Core = struct {
    allocator: std.mem.Allocator,
    arena: std.heap.ArenaAllocator,
    
    // Core engines
    symbolic: SymbolicEngine,
    shape: ShapeEngine,  
    graph: GraphEngine,
    data: DataStore,
    
    // Compiler engine (lazy initialization)
    compiler: ?*CompilerEngine = null,
    
    // Note: All allocations use the arena allocator directly
    // No need for separate memory pools since we have a single arena
    
    pub fn init(allocator: std.mem.Allocator) !*Core {
        // Allocate core on heap to ensure stable address
        var core = try allocator.create(Core);
        errdefer allocator.destroy(core);
        
        // Initialize arena first
        core.arena = std.heap.ArenaAllocator.init(allocator);
        errdefer core.arena.deinit();
        
        // Initialize the rest of core struct
        core.allocator = allocator;
        
        // Initialize engines with their dependencies in the correct order
        // Note: ShapeEngine depends on SymbolicEngine, so symbolic must be initialized first
        core.symbolic = try SymbolicEngine.init(core.arena.allocator());
        core.shape = try ShapeEngine.init(core);
        core.graph = try GraphEngine.init(core);
        core.data = try DataStore.init(core.arena.allocator());
        
        return core;
    }
    
    pub fn getCompiler(self: *Core) !*CompilerEngine {
        if (self.compiler == null) {
            self.compiler = try self.arena.allocator().create(CompilerEngine);
            self.compiler.?.* = try CompilerEngine.init(self);
        }
        return self.compiler.?;
    }
    
    pub fn deinit(self: *Core) void {
        // Clean up compiler if initialized
        if (self.compiler) |compiler| {
            compiler.deinit();
        }
        
        // Engines clean up their own resources
        self.symbolic.deinit();
        self.shape.deinit();
        self.graph.deinit();
        self.data.deinit();
        
        // Arena cleanup handles all allocations
        self.arena.deinit();
        
        // Free core structure itself
        const allocator = self.allocator;
        allocator.destroy(self);
    }
    
    pub fn resetGraphResources(self: *Core) void {
        // Deinit compiler if initialized
        if (self.compiler) |compiler| {
            compiler.deinit();
            self.compiler = null;
        }
        
        // Deinit engines before arena reset
        self.symbolic.deinit();
        self.shape.deinit();
        self.graph.deinit();
        self.data.deinit();
        
        // Reset arena to clear all allocations
        _ = self.arena.reset(.retain_capacity);
        
        // Re-init engines with their dependencies
        self.symbolic = SymbolicEngine.init(self.arena.allocator()) catch unreachable;
        self.shape = ShapeEngine.init(self) catch unreachable;
        self.graph = GraphEngine.init(self) catch unreachable;  
        self.data = DataStore.init(self.arena.allocator()) catch unreachable;
    }
};