const std = @import("std");
const types = @import("types.zig");
const graph_types = @import("graph/types.zig");
// Shape types are now in core/types.zig

// ============================================================================
// Object Pools
// ============================================================================

/// Object pool for efficient allocation/deallocation of fixed-size objects
pub fn ObjectPool(comptime T: type) type {
    return struct {
        const Self = @This();
        
        allocator: std.mem.Allocator,
        items: std.ArrayList(T),
        free_list: std.ArrayList(usize),
        stats: PoolStats = .{},
        
        pub const PoolStats = struct {
            acquires: usize = 0,
            releases: usize = 0,
            peak_usage: usize = 0,
        };
        
        pub fn init(allocator: std.mem.Allocator) Self {
            return .{
                .allocator = allocator,
                .items = std.ArrayList(T).init(allocator),
                .free_list = std.ArrayList(usize).init(allocator),
                .stats = .{},
            };
        }
        
        pub fn deinit(self: *Self) void {
            self.items.deinit();
            self.free_list.deinit();
        }
        
        /// Get an item from the pool
        pub fn acquire(self: *Self) !*T {
            self.stats.acquires += 1;
            
            if (self.free_list.items.len > 0) {
                const index = self.free_list.pop() orelse unreachable;
                return &self.items.items[index];
            }
            
            // Allocate new item
            try self.items.append(undefined);
            const new_item = &self.items.items[self.items.items.len - 1];
            
            // Update peak usage
            const current_usage = self.items.items.len - self.free_list.items.len;
            self.stats.peak_usage = @max(self.stats.peak_usage, current_usage);
            
            return new_item;
        }
        
        /// Return an item to the pool
        pub fn release(self: *Self, item: *T) !void {
            self.stats.releases += 1;
            
            // Find the index of this item
            const item_ptr = @intFromPtr(item);
            const items_start = @intFromPtr(self.items.items.ptr);
            const items_end = items_start + self.items.items.len * @sizeOf(T);
            
            if (item_ptr < items_start or item_ptr >= items_end) {
                return error.InvalidPointer;
            }
            
            const index = (item_ptr - items_start) / @sizeOf(T);
            if (index >= self.items.items.len) {
                return error.InvalidPointer;
            }
            
            // Add to free list
            try self.free_list.append(index);
        }
        
        /// Get pool statistics
        pub fn getStats(self: *const Self) PoolStats {
            return self.stats;
        }
        
        /// Reset the pool (clear free list but keep allocated memory)
        pub fn reset(self: *Self) void {
            self.free_list.clearRetainingCapacity();
            // Mark all items as free
            for (0..self.items.items.len) |i| {
                self.free_list.append(i) catch {};
            }
        }
    };
}

/// ID-based pool for managing objects with stable identifiers
pub fn IdPool(comptime T: type) type {
    return struct {
        const Self = @This();
        
        allocator: std.mem.Allocator,
        items: std.AutoHashMapUnmanaged(u32, T) = .{},
        next_id: u32 = 1, // Start at 1 since 0 is reserved for invalid ID
        free_ids: std.ArrayListUnmanaged(u32) = .{},
        stats: IdPoolStats = .{},
        
        pub const IdPoolStats = struct {
            adds: usize = 0,
            removes: usize = 0,
            peak_count: usize = 0,
        };
        
        pub fn init(allocator: std.mem.Allocator) Self {
            return .{
                .allocator = allocator,
            };
        }
        
        pub fn deinit(self: *Self) void {
            self.items.deinit(self.allocator);
            self.free_ids.deinit(self.allocator);
        }
        
        /// Add an item to the pool, returning its ID
        pub fn add(self: *Self, item: T) !u32 {
            self.stats.adds += 1;
            
            const id = if (self.free_ids.items.len > 0)
                self.free_ids.orderedRemove(self.free_ids.items.len - 1)
            else blk: {
                const new_id = self.next_id;
                self.next_id += 1;
                break :blk new_id;
            };
            
            try self.items.put(self.allocator, id, item);
            
            // Update peak count
            self.stats.peak_count = @max(self.stats.peak_count, self.items.count());
            
            return id;
        }
        
        /// Get an item by ID (raw u32)
        pub fn get(self: *const Self, id: u32) ?*const T {
            return self.items.getPtr(id);
        }
        
        /// Get a mutable item by ID (raw u32)
        pub fn getMut(self: *Self, id: u32) ?*T {
            return self.items.getPtr(id);
        }
        
        /// Remove an item by ID (raw u32)
        pub fn remove(self: *Self, id: u32) bool {
            if (self.items.remove(id)) {
                self.stats.removes += 1;
                self.free_ids.append(self.allocator, id) catch {
                    // If we can't add to free list, just let the ID be lost
                };
                return true;
            }
            return false;
        }
        
        /// Get pool statistics
        pub fn getStats(self: *const Self) IdPoolStats {
            return self.stats;
        }
    };
}

/// Specialized pool for expressions
pub const ExprPool = ObjectPool(types.Expr);

// ============================================================================
// Caching
// ============================================================================

/// Generic cache key with pre-computed hash
pub fn CacheKey(comptime T: type) type {
    return struct {
        hash_value: u64,
        data: T,
        
        pub fn init(data: T) @This() {
            var hasher = std.hash.Wyhash.init(0);
            hashType(&hasher, T, data);
            return .{
                .hash_value = hasher.final(),
                .data = data,
            };
        }
        
        pub fn hash(self: @This()) u64 {
            return self.hash_value;
        }
        
        pub fn eql(self: @This(), other: @This()) bool {
            return deepEql(T, self.data, other.data);
        }
    };
}

/// Content-addressable cache for any type
pub fn ContentCache(comptime K: type, comptime V: type) type {
    return struct {
        const Self = @This();
        const KeyType = CacheKey(K);
        
        allocator: std.mem.Allocator,
        map: std.HashMapUnmanaged(KeyType, V, Key.HashContext, 80) = .{},
        stats: CacheStats = .{},
        pub const CacheStats = struct {
            hits: usize = 0,
            misses: usize = 0,
            insertions: usize = 0,
            evictions: usize = 0,
        };
        
        pub fn init(allocator: std.mem.Allocator) Self {
            return .{
                .allocator = allocator,
            };
        }
        
        pub fn deinit(self: *Self) void {
            self.map.deinit(self.allocator);
        }
        
        /// Get or create a value in the cache
        pub fn getOrPut(self: *Self, key_data: K, create_fn: anytype) !V {
            const key = KeyType.init(key_data);
            
            // Check if already cached
            if (self.map.get(key)) |value| {
                self.stats.hits += 1;
                return value;
            }
            
            self.stats.misses += 1;
            
            // Create new value
            const value = try create_fn(key_data);
            
            // Store in cache
            try self.map.put(self.allocator, key, value);
            self.stats.insertions += 1;
            
            return value;
        }
        
        /// Get a value from the cache
        pub fn get(self: *Self, key_data: K) ?V {
            const key = KeyType.init(key_data);
            if (self.map.get(key)) |value| {
                self.stats.hits += 1;
                return value;
            }
            self.stats.misses += 1;
            return null;
        }
        
        /// Put a value directly into the cache
        pub fn put(self: *Self, key_data: K, value: V) !void {
            const key = KeyType.init(key_data);
            try self.map.put(self.allocator, key, value);
            self.stats.insertions += 1;
        }
        
        /// Clear the cache
        pub fn clear(self: *Self) void {
            self.map.clearRetainingCapacity();
            // Don't reset stats - they're cumulative
        }
        
        /// Get cache statistics
        pub fn getStats(self: *const Self) CacheStats {
            return self.stats;
        }
    };
}

// Helper function for hashing types
fn hashType(hasher: anytype, comptime T: type, value: T) void {
    const type_info = @typeInfo(T);
    switch (type_info) {
        .@"struct" => |s| {
            inline for (s.fields) |field| {
                hashType(hasher, field.type, @field(value, field.name));
            }
        },
        .array => |a| {
            for (value) |item| {
                hashType(hasher, a.child, item);
            }
        },
        .pointer => |p| {
            if (p.size == .slice) {
                const slice = value;
                hasher.update(std.mem.asBytes(&slice.len));
                for (slice) |item| {
                    hashType(hasher, p.child, item);
                }
            } else {
                hasher.update(std.mem.asBytes(&value));
            }
        },
        .optional => {
            if (value) |v| {
                hasher.update("\x01");
                hashType(hasher, type_info.optional.child, v);
            } else {
                hasher.update("\x00");
            }
        },
        .@"union" => |u| {
            if (u.tag_type) |_| {
                const tag = std.meta.activeTag(value);
                hasher.update(std.mem.asBytes(&tag));
                inline for (u.fields) |field| {
                    if (tag == @field(std.meta.Tag(T), field.name)) {
                        hashType(hasher, field.type, @field(value, field.name));
                    }
                }
            } else {
                @compileError("Cannot hash untagged union");
            }
        },
        .@"enum" => {
            hasher.update(std.mem.asBytes(&value));
        },
        .int, .float, .bool => {
            hasher.update(std.mem.asBytes(&value));
        },
        else => @compileError("Cannot hash type: " ++ @typeName(T)),
    }
}

// Helper function for deep equality comparison
fn deepEql(comptime T: type, a: T, b: T) bool {
    const type_info = @typeInfo(T);
    switch (type_info) {
        .@"struct" => |s| {
            inline for (s.fields) |field| {
                if (!deepEql(field.type, @field(a, field.name), @field(b, field.name))) {
                    return false;
                }
            }
            return true;
        },
        .array => {
            for (a, b) |item_a, item_b| {
                if (!deepEql(@TypeOf(item_a), item_a, item_b)) {
                    return false;
                }
            }
            return true;
        },
        .pointer => |p| {
            if (p.size == .slice) {
                if (a.len != b.len) return false;
                for (a, b) |item_a, item_b| {
                    if (!deepEql(p.child, item_a, item_b)) {
                        return false;
                    }
                }
                return true;
            } else {
                return a == b;
            }
        },
        .optional => {
            if (a == null and b == null) return true;
            if (a == null or b == null) return false;
            return deepEql(type_info.optional.child, a.?, b.?);
        },
        .@"union" => |u| {
            if (u.tag_type) |_| {
                const tag_a = std.meta.activeTag(a);
                const tag_b = std.meta.activeTag(b);
                if (tag_a != tag_b) return false;
                
                inline for (u.fields) |field| {
                    if (tag_a == @field(std.meta.Tag(T), field.name)) {
                        return deepEql(field.type, @field(a, field.name), @field(b, field.name));
                    }
                }
                return false;
            } else {
                return std.meta.eql(a, b);
            }
        },
        else => return std.meta.eql(a, b),
    }
}

// Namespace containing the context for the cache keys
const Key = struct {
    pub const HashContext = struct {
        pub fn hash(_: @This(), key: anytype) u64 {
            return key.hash();
        }
        
        pub fn eql(_: @This(), a: anytype, b: anytype) bool {
            return a.eql(b);
        }
    };
};