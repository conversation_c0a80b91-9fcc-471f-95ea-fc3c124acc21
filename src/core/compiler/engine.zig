// Core compiler engine that integrates with existing Core architecture
const std = @import("std");
const Core = @import("../core.zig").Core;
const graph_mod = @import("../graph/engine.zig");
const graph_types = @import("../graph/types.zig");
const types = @import("../types.zig");

const Allocator = std.mem.Allocator;
const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ShapeId = types.ShapeId;

/// Compiler engine that integrates with existing Core architecture
pub const CompilerEngine = struct {
    const Self = @This();

    /// Reference to parent Core - uses existing infrastructure
    core: *Core,
    
    /// Optimization passes in execution order
    passes: std.ArrayListUnmanaged(OptimizationPass) = .{},
    
    /// Consumer tracking for memory optimization
    consumer_tracker: ConsumerTracker,
    
    /// Compiler diagnostics
    diagnostics: std.ArrayListUnmanaged(CompilerDiagnostic) = .{},
    
    pub fn init(core: *Core) !Self {
        const allocator = core.arena.allocator();
        
        var self = Self{
            .core = core,
            .consumer_tracker = ConsumerTracker.init(allocator),
        };
        
        // Register default passes matching Luminal's GenericCompiler
        try self.registerDefaultPasses();
        
        return self;
    }
    
    pub fn deinit(self: *Self) void {
        // Note: Using arena allocator, so individual deinit calls are not needed
        // The arena will clean up all allocations when it's deinit'd
        // Calling deinit on collections allocated from arena can cause double-free issues
        _ = self;
    }
    
    /// Main compilation entry point
    pub fn compile(self: *Self, backend_name: []const u8) !CompiledGraph {
        _ = backend_name; // TODO: Use backend for compilation
        
        // Phase 1: Run optimization passes
        try self.runOptimizationPasses();
        
        // Phase 2: Analyze consumer counts
        try self.consumer_tracker.analyze(&self.core.graph);
        
        // Phase 3: Create execution plan
        const plan = try self.createExecutionPlan();
        
        // Phase 4: Return compiled graph (backend compilation will be added later)
        return CompiledGraph{
            .plan = plan,
            .allocator = self.core.arena.allocator(),
        };
    }
    
    fn registerDefaultPasses(self: *Self) !void {
        const allocator = self.core.arena.allocator();
        
        // Import pattern-based pass modules
        const cse = @import("passes/cse.zig");
        const constant_folding = @import("passes/constant_folding.zig");
        const dead_code = @import("passes/dead_code.zig");
        const fusion = @import("passes/fusion.zig");
        
        // Pattern-based passes with better performance and maintainability
        const passes = [_]OptimizationPass{
            .{ .name = "dead_code_elimination", .apply = dead_code.apply },
            .{ .name = "constant_folding", .apply = constant_folding.apply },
            .{ .name = "math_identities", .apply = cse.applyMathIdentities },
            .{ .name = "cse", .apply = cse.apply },
            .{ .name = "fusion", .apply = fusion.apply },
        };
        
        try self.passes.appendSlice(allocator, &passes);
    }
    
    fn runOptimizationPasses(self: *Self) !void {
        var changed = true;
        var iteration: u32 = 0;
        
        while (changed and iteration < 10) : (iteration += 1) {
            changed = false;
            
            for (self.passes.items) |pass| {
                const pass_changed = try pass.apply(self);
                changed = changed or pass_changed;
                
                if (pass_changed) {
                    std.log.debug("Pass '{s}' made changes in iteration {}", .{ pass.name, iteration });
                }
            }
        }
    }
    
    fn createExecutionPlan(self: *Self) !ExecutionPlan {
        const allocator = self.core.arena.allocator();
        const graph = &self.core.graph;
        
        // Get topological order (returns u32 IDs)
        const topo_order_raw = try graph.topologicalSort();
        defer allocator.free(topo_order_raw);
        
        var operations = std.ArrayList(Operation).init(allocator);
        var memory_layout = try MemoryLayout.init(allocator, self.consumer_tracker);
        
        for (topo_order_raw) |raw_node_id| {
            const node_id = types.nodeIdFromU32(raw_node_id);
            const node = graph.getNode(node_id) orelse continue;
            
            // Get shape information from shape engine
            const view_desc = self.core.shape.getView(node.output_view_id);
            const shape = self.core.shape.getShape(view_desc.shape_id);
            
            // Calculate memory requirements
            const num_elements = try self.calculateNumElements(shape.*);
            const size_bytes = num_elements * node.dtype.sizeInBytes();
            
            // Convert input IDs from raw u32 to NodeId
            const inputs = try allocator.alloc(NodeId, node.inputs.len);
            for (node.inputs, 0..) |raw_input_id, i| {
                inputs[i] = types.nodeIdFromU32(@intFromEnum(raw_input_id));
            }
            
            try operations.append(.{
                .node_id = node_id,
                .op_type = node.op,
                .inputs = inputs,
                .output_view_id = node.output_view_id,
                .dtype = node.dtype,
                .memory_slot = try memory_layout.allocateSlot(node_id, size_bytes),
            });
        }
        
        return ExecutionPlan{
            .operations = try operations.toOwnedSlice(),
            .memory_layout = memory_layout,
            .allocator = allocator,
        };
    }
    
    fn calculateNumElements(self: *Self, shape: types.Shape) !usize {
        var total: usize = 1;
        for (shape.dims) |dim_expr| {
            const dim_value = try self.core.symbolic.evaluate(dim_expr, null);
            total *= @intCast(dim_value);
        }
        return total;
    }
};

/// Simple optimization pass structure - no vtables needed
pub const OptimizationPass = struct {
    name: []const u8,
    apply: *const fn (engine: *CompilerEngine) anyerror!bool,
};

/// Compiled graph representation
pub const CompiledGraph = struct {
    plan: ExecutionPlan,
    allocator: Allocator,
    
    pub fn deinit(self: *CompiledGraph) void {
        self.plan.deinit();
    }
};

/// Execution plan for the compiled graph
pub const ExecutionPlan = struct {
    operations: []Operation,
    memory_layout: MemoryLayout,
    allocator: Allocator,
    
    pub fn deinit(self: *ExecutionPlan) void {
        self.memory_layout.deinit();
        self.allocator.free(self.operations);
    }
};

/// Single operation in the execution plan
pub const Operation = struct {
    node_id: NodeId,
    op_type: graph_types.OpType,
    inputs: []NodeId,
    output_view_id: ViewId,
    dtype: types.DataType,
    memory_slot: MemorySlot,
};

/// Consumer tracking for automatic memory management
pub const ConsumerTracker = struct {
    allocator: Allocator,
    /// Map from node ID to number of consumers
    consumer_counts: std.AutoHashMapUnmanaged(NodeId, u32) = .{},
    /// Nodes that must persist (outputs, parameters)
    persistent_nodes: std.AutoHashMapUnmanaged(NodeId, void) = .{},
    
    pub fn init(allocator: Allocator) ConsumerTracker {
        return .{ 
            .allocator = allocator,
        };
    }
    
    pub fn deinit(self: *ConsumerTracker) void {
        self.consumer_counts.deinit(self.allocator);
        self.persistent_nodes.deinit(self.allocator);
    }
    
    pub fn analyze(self: *ConsumerTracker, graph: *graph_mod.GraphEngine) !void {
        self.consumer_counts.clearRetainingCapacity();
        self.persistent_nodes.clearRetainingCapacity();
        
        // Iterate through all nodes using getAllNodes
        const all_nodes = graph.getAllNodes();
        for (all_nodes) |node| {
            const node_id = types.nodeIdFromU32(@intFromEnum(node.id));
            
            // Mark outputs and parameters as persistent
            if (node.consumers.len == 0 or node.op == .variable) {
                try self.persistent_nodes.put(self.allocator, node_id, {});
            }
            
            // Count how many times each input is consumed
            for (node.inputs) |raw_input_id| {
                const input_id = types.nodeIdFromU32(@intFromEnum(raw_input_id));
                const count = self.consumer_counts.get(input_id) orelse 0;
                try self.consumer_counts.put(self.allocator, input_id, count + 1);
            }
        }
    }
    
    pub fn shouldFree(self: *ConsumerTracker, node_id: NodeId) bool {
        if (self.persistent_nodes.contains(node_id)) return false;
        const count = self.consumer_counts.get(node_id) orelse 0;
        return count == 0;
    }
    
    pub fn consumeNode(self: *ConsumerTracker, node_id: NodeId) void {
        if (self.consumer_counts.getPtr(node_id)) |count_ptr| {
            if (count_ptr.* > 0) {
                count_ptr.* -= 1;
            }
        }
    }
};

/// Memory layout for efficient execution
pub const MemoryLayout = struct {
    allocator: Allocator,
    slots: std.AutoHashMapUnmanaged(NodeId, MemorySlot) = .{},
    next_offset: usize = 0,
    
    pub fn init(allocator: Allocator, consumer_tracker: ConsumerTracker) !MemoryLayout {
        _ = consumer_tracker; // Will be used for optimization later
        return .{
            .allocator = allocator,
        };
    }
    
    pub fn deinit(self: *MemoryLayout) void {
        self.slots.deinit(self.allocator);
    }
    
    pub fn allocateSlot(self: *MemoryLayout, node_id: NodeId, size: usize) !MemorySlot {
        const slot = MemorySlot{
            .offset = self.next_offset,
            .size = size,
        };
        
        try self.slots.put(self.allocator, node_id, slot);
        self.next_offset += size;
        
        return slot;
    }
    
    pub fn getSlot(self: *MemoryLayout, node_id: NodeId) ?MemorySlot {
        return self.slots.get(node_id);
    }
};

/// Memory slot for a node's output
pub const MemorySlot = struct {
    offset: usize,
    size: usize,
};

/// Diagnostic information for compiler errors
pub const CompilerDiagnostic = struct {
    node_id: ?NodeId = null,
    pass_name: []const u8,
    message: []const u8,
    suggestion: ?[]const u8 = null,
    
    pub fn format(
        self: CompilerDiagnostic,
        comptime fmt: []const u8,
        options: std.fmt.FormatOptions,
        writer: anytype,
    ) !void {
        _ = fmt;
        _ = options;
        try writer.print("Compiler error in pass '{s}': {s}", .{ self.pass_name, self.message });
        if (self.node_id) |id| {
            try writer.print(" (node {})", .{@intFromEnum(id)});
        }
        if (self.suggestion) |s| {
            try writer.print("\n  Suggestion: {s}", .{s});
        }
    }
};