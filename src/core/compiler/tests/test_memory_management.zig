// Memory management tests for compiler system
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;

const CompilerEngine = @import("../engine.zig").CompilerEngine;
const NodeId = types.NodeId;

// ============================================================================
// Arena Allocator Tests
// ============================================================================

test "memory: arena allocator lifecycle" {
    // Test that arena allocator is used correctly throughout compiler
    const allocator = testing.allocator;
    
    // Using testing.allocator which already tracks leaks
    {
        const ctx = try Core.init(allocator);
        defer ctx.deinit();
        
        const compiler = try ctx.getCompiler();
        
        // Create many allocations
        for (0..100) |i| {
            const shape = try ctx.shape.newShape(&[_]*types.Expr{
                try ctx.symbolic.newIntegerExpr(@intCast(i)),
            });
            const view = try ctx.shape.newDefaultView(shape);
            _ = try ctx.graph.newNodeVariable(view, .f32);
        }
        
        // Add to compiler data structures
        try compiler.diagnostics.append(ctx.arena.allocator(), .{
            .node_id = null,
            .pass_name = "test",
            .message = "test diagnostic",
            .suggestion = null,
        });
        
        // Consumer tracker operations
        try compiler.consumer_tracker.analyze(&ctx.graph);
    }
    
    // testing.allocator will detect any leaks automatically
}

test "memory: no double-free with arena allocator" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // The current implementation correctly uses empty deinit
    // This test verifies it doesn't cause issues
    compiler.deinit();
    compiler.deinit(); // Should be safe to call multiple times
    
    // Verify we can still use the compiler
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    const node = try ctx.graph.newNodeVariable(view, .f32);
    
    try compiler.consumer_tracker.persistent_nodes.put(
        ctx.arena.allocator(),
        node,
        {}
    );
}

test "memory: consumer tracker memory usage" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create a large graph
    var nodes = std.ArrayList(NodeId).init(allocator);
    defer nodes.deinit();
    
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    // Create many nodes
    for (0..1000) |_| {
        const node = try ctx.graph.newNodeVariable(view, .f32);
        try nodes.append(node);
    }
    
    // Create many connections
    for (0..500) |i| {
        const a = nodes.items[i * 2];
        const b = nodes.items[i * 2 + 1];
        _ = try ctx.graph.newNodeAdd(&[_]NodeId{a, b}, view);
    }
    
    // Analyze should handle large graphs efficiently
    try compiler.consumer_tracker.analyze(&ctx.graph);
    
    // Verify consumer counts are reasonable
    try testing.expect(compiler.consumer_tracker.consumer_counts.count() > 0);
}

test "memory: memory layout allocation strategy" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create memory layout
    const MemoryLayout = @import("../engine.zig").MemoryLayout;
    var layout = try MemoryLayout.init(
        ctx.arena.allocator(),
        compiler.consumer_tracker
    );
    
    // Allocate slots of various sizes
    const small1 = try layout.allocateSlot(types.nodeIdFromU32(1), 64);
    const large1 = try layout.allocateSlot(types.nodeIdFromU32(2), 1024 * 1024);
    const small2 = try layout.allocateSlot(types.nodeIdFromU32(3), 128);
    
    // Verify sequential allocation
    try testing.expectEqual(@as(usize, 0), small1.offset);
    try testing.expectEqual(@as(usize, 64), large1.offset);
    try testing.expectEqual(@as(usize, 64 + 1024 * 1024), small2.offset);
    
    // Verify retrieval
    const retrieved = layout.getSlot(types.nodeIdFromU32(2));
    try testing.expect(retrieved != null);
    try testing.expectEqual(large1.offset, retrieved.?.offset);
    try testing.expectEqual(large1.size, retrieved.?.size);
}

// ============================================================================
// Pattern Context Memory Tests
// ============================================================================

test "memory: pattern match context allocation" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Track memory used by match contexts
    var contexts = std.ArrayList(*PatternEngine.Pattern(.any, .remove).MatchContext).init(allocator);
    defer contexts.deinit();
    
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    const node = try ctx.graph.newNodeVariable(view, .f32);
    
    // Create many match contexts
    for (0..100) |_| {
        const context_ptr = try allocator.create(PatternEngine.Pattern(.any, .remove).MatchContext);
        context_ptr.* = PatternEngine.Pattern(.any, .remove).MatchContext.init(allocator);
        try contexts.append(context_ptr);
        
        // Use the context
        _ = PatternEngine.Pattern(.any, .remove).matches(compiler, node, context_ptr);
    }
    
    // Clean up contexts
    for (contexts.items) |context_ptr| {
        context_ptr.bindings.deinit(allocator);
        allocator.destroy(context_ptr);
    }
}

test "memory: pattern binding memory growth" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create a pattern that binds many variables
    const ManyBindsPattern = PatternEngine.pattern(
        .{ .binary = .{
            .op = .add,
            .left = &.{ .bind = "a" },
            .right = &.{ .binary = .{
                .op = .multiply,
                .left = &.{ .bind = "b" },
                .right = &.{ .bind = "c" },
            }},
        }},
        .{ .forward_input = 0 }
    );
    
    // Create matching graph
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    const a = try ctx.graph.newNodeVariable(view, .f32);
    const b = try ctx.graph.newNodeVariable(view, .f32);
    const c = try ctx.graph.newNodeVariable(view, .f32);
    
    const mul = try ctx.graph.newNodeMultiply(&[_]NodeId{b, c}, view);
    const add = try ctx.graph.newNodeAdd(&[_]NodeId{a, mul}, view);
    
    // Test many matches
    for (0..100) |_| {
        var context = ManyBindsPattern.MatchContext.init(allocator);
        defer context.deinit();
        defer context.bindings.deinit(ctx.arena.allocator());
        
        try testing.expect(ManyBindsPattern.matches(compiler, add, &context));
        try testing.expectEqual(@as(usize, 3), context.bindings.count());
    }
}

// ============================================================================
// Stress Tests
// ============================================================================

test "memory: large graph stress test" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create a very large graph
    const shape = try ctx.shape.newShape(&[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(32),
        try ctx.symbolic.newIntegerExpr(32),
    });
    const view = try ctx.shape.newDefaultView(shape);
    
    // Create initial variables
    var current_layer = std.ArrayList(NodeId).init(allocator);
    defer current_layer.deinit();
    
    for (0..10) |_| {
        const node = try ctx.graph.newNodeVariable(view, .f32);
        try current_layer.append(node);
    }
    
    // Create layers of operations
    for (0..5) |_| {
        var next_layer = std.ArrayList(NodeId).init(allocator);
        defer next_layer.deinit();
        
        var i: usize = 0;
        while (i + 1 < current_layer.items.len) : (i += 2) {
            const a = current_layer.items[i];
            const b = current_layer.items[i + 1];
            const add = try ctx.graph.newNodeAdd(&[_]NodeId{a, b}, view);
            try next_layer.append(add);
        }
        
        current_layer.deinit();
        current_layer = next_layer;
        next_layer = std.ArrayList(NodeId).init(allocator);
    }
    
    // Mark final nodes as outputs
    for (current_layer.items) |node| {
        try compiler.consumer_tracker.persistent_nodes.put(
            ctx.arena.allocator(),
            node,
            {}
        );
    }
    
    // Run optimization passes - should handle large graphs
    _ = try constant_folding.apply(compiler);
    _ = try cse.apply(compiler);
    _ = try dead_code.apply(compiler);
}

test "memory: pattern engine with deep recursion" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create a deep expression tree
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    var current = try ctx.graph.newNodeVariable(view, .f32);
    
    // Create deep nesting
    for (0..50) |_| {
        const zero = try ctx.graph.newNodeConstantTyped(view, .f32);
        try ctx.data.setConstantData(f32, zero, &[_]f32{0.0}, ctx);
        current = try ctx.graph.newNodeAdd(&[_]NodeId{current, zero}, view);
    }
    
    // Pattern matching should handle deep recursion
    const AddZeroPattern = PatternEngine.pattern(
        .{ .binary = .{
            .op = .add,
            .left = &.any,
            .right = &.{ .constant_zero = {} },
        }},
        .{ .forward_input = 0 }
    );
    
    // Should match at any level
    var matched_count: usize = 0;
    var iter = ctx.graph.node_map.iterator();
    while (iter.next()) |entry| {
        const node_id = entry.key_ptr.*;
        var context = AddZeroPattern.MatchContext.init(allocator);
        defer context.deinit();
        if (AddZeroPattern.matches(compiler, node_id, &context)) {
            matched_count += 1;
        }
    }
    
    try testing.expect(matched_count > 0);
}

// ============================================================================
// Edge Case Tests
// ============================================================================

test "memory: empty graph handling" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Run passes on empty graph
    const changed1 = try constant_folding.apply(compiler);
    const changed2 = try cse.apply(compiler);
    const changed3 = try dead_code.apply(compiler);
    
    // Should handle empty graph gracefully
    try testing.expect(!changed1);
    try testing.expect(!changed2);
    try testing.expect(!changed3);
}

test "memory: single node graph" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create single node
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    const node = try ctx.graph.newNodeVariable(view, .f32);
    
    try compiler.consumer_tracker.persistent_nodes.put(
        ctx.arena.allocator(),
        node,
        {}
    );
    
    // Should handle single node
    try compiler.consumer_tracker.analyze(&ctx.graph);
    
    const consumers = compiler.consumer_tracker.consumer_counts.get(node) orelse 0;
    try testing.expectEqual(@as(u32, 0), consumers);
}

// Import needed modules for the tests
const PatternEngine = @import("../pattern_engine.zig");
const constant_folding = @import("../passes/constant_folding.zig");
const cse = @import("../passes/cse.zig");
const dead_code = @import("../passes/dead_code.zig");