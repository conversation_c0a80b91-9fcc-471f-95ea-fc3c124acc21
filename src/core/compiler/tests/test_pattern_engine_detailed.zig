// Detailed tests for pattern engine implementation
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const graph_types = core.graph.types;

const PatternEngine = @import("../pattern_engine.zig");
const CompilerEngine = @import("../engine.zig").CompilerEngine;

const pattern = PatternEngine.pattern;
const NodeId = types.NodeId;

// ============================================================================
// Pattern Matching Edge Cases
// ============================================================================

test "pattern engine: constant zero detection" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Test 1: Actual zero constant
    const zero_shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const zero_view = try ctx.shape.newDefaultView(zero_shape);
    const zero_node = try ctx.graph.newNodeConstantTyped(zero_view, .f32);
    try ctx.data.setConstantData(f32, zero_node, &[_]f32{0.0}, ctx);
    
    // Pattern that matches zero
    const ZeroPattern = pattern(
        .{ .constant_zero = {} },
        .{ .remove = {} }
    );
    
    var context1 = ZeroPattern.MatchContext.init(allocator);
    defer context1.deinit();
    try testing.expect(ZeroPattern.matches(compiler, zero_node, &context1));
    
    // Test 2: Non-zero constant
    const one_node = try ctx.graph.newNodeConstantTyped(zero_view, .f32);
    try ctx.data.setConstantData(f32, one_node, &[_]f32{1.0}, ctx);
    
    var context2 = ZeroPattern.MatchContext.init(allocator);
    defer context2.deinit();
    try testing.expect(!ZeroPattern.matches(compiler, one_node, &context2));
    
    // Test 3: Variable (not constant)
    const var_node = try ctx.graph.newNodeVariable(zero_view, .f32);
    
    var context3 = ZeroPattern.MatchContext.init(allocator);
    defer context3.deinit();
    try testing.expect(!ZeroPattern.matches(compiler, var_node, &context3));
}

test "pattern engine: constant one detection" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create different constants
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    const one_f32 = try ctx.graph.newNodeConstantTyped(view, .f32);
    try ctx.data.setConstantData(f32, one_f32, &[_]f32{1.0}, ctx);
    
    const one_i32 = try ctx.graph.newNodeConstantTyped(view, .i32);
    try ctx.data.setConstantData(i32, one_i32, &[_]i32{1}, ctx);
    
    const two_f32 = try ctx.graph.newNodeConstantTyped(view, .f32);
    try ctx.data.setConstantData(f32, two_f32, &[_]f32{2.0}, ctx);
    
    // Pattern that matches one
    const OnePattern = pattern(
        .{ .constant_one = {} },
        .{ .remove = {} }
    );
    
    // Should match both f32 and i32 versions of 1
    var ctx1 = OnePattern.MatchContext.init(allocator);
    defer ctx1.deinit();
    try testing.expect(OnePattern.matches(compiler, one_f32, &ctx1));
    
    var ctx2 = OnePattern.MatchContext.init(allocator);
    defer ctx2.deinit();
    try testing.expect(OnePattern.matches(compiler, one_i32, &ctx2));
    
    // Should not match 2
    var ctx3 = OnePattern.MatchContext.init(allocator);
    defer ctx3.deinit();
    try testing.expect(!OnePattern.matches(compiler, two_f32, &ctx3));
}

test "pattern engine: variable binding and references" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create x * x (square pattern)
    const shape = try ctx.shape.newShape(&[_]*types.Expr{
        try ctx.symbolic.newIntegerExpr(2),
        try ctx.symbolic.newIntegerExpr(3),
    });
    const view = try ctx.shape.newDefaultView(shape);
    
    const x = try ctx.graph.newNodeVariable(view, .f32);
    const square = try ctx.graph.newNodeMultiply(&[_]NodeId{x, x}, view);
    
    // Pattern: a * a (same variable on both sides)
    const SquarePattern = pattern(
        .{ .binary = .{
            .op = .multiply,
            .left = &.{ .bind = "a" },
            .right = &.{ .ref = "a" },
        }},
        .{ .forward_input = 0 }
    );
    
    var context = SquarePattern.MatchContext.init(allocator);
    defer context.deinit();
    try testing.expect(SquarePattern.matches(compiler, square, &context));
    try testing.expectEqual(x, context.bindings.get("a").?);
    
    // Test non-matching: x * y (different variables)
    const y = try ctx.graph.newNodeVariable(view, .f32);
    const mul_diff = try ctx.graph.newNodeMultiply(&[_]NodeId{x, y}, view);
    
    var context2 = SquarePattern.MatchContext.init(allocator);
    defer context2.deinit();
    try testing.expect(!SquarePattern.matches(compiler, mul_diff, &context2));
}

test "pattern engine: bound variable validation" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    const a = try ctx.graph.newNodeVariable(view, .f32);
    const b = try ctx.graph.newNodeVariable(view, .f32);
    
    // Test using .ref without prior binding
    const InvalidPattern = pattern(
        .{ .ref = "x" }, // x is not bound yet
        .{ .remove = {} }
    );
    
    var context = InvalidPattern.MatchContext.init(allocator);
    defer context.deinit();
    // Should not match because x is not bound
    try testing.expect(!InvalidPattern.matches(compiler, a, &context));
    
    // Now bind x and test
    try context.bindings.put(ctx.arena.allocator(), "x", a);
    
    // Should match a but not b
    try testing.expect(InvalidPattern.matches(compiler, a, &context));
    try testing.expect(!InvalidPattern.matches(compiler, b, &context));
}

test "pattern engine: nested pattern complexity" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create: ((a + b) * c) + d
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    const a = try ctx.graph.newNodeVariable(view, .f32);
    const b = try ctx.graph.newNodeVariable(view, .f32);
    const c = try ctx.graph.newNodeVariable(view, .f32);
    const d = try ctx.graph.newNodeVariable(view, .f32);
    
    const add1 = try ctx.graph.newNodeAdd(&[_]NodeId{a, b}, view);
    const mul = try ctx.graph.newNodeMultiply(&[_]NodeId{add1, c}, view);
    const add2 = try ctx.graph.newNodeAdd(&[_]NodeId{mul, d}, view);
    
    // Complex nested pattern
    const ComplexPattern = pattern(
        .{ .binary = .{
            .op = .add,
            .left = &.{ .binary = .{
                .op = .multiply,
                .left = &.{ .binary = .{
                    .op = .add,
                    .left = &.{ .bind = "a" },
                    .right = &.{ .bind = "b" },
                }},
                .right = &.{ .bind = "c" },
            }},
            .right = &.{ .bind = "d" },
        }},
        .{ .forward_input = 0 }
    );
    
    var context = ComplexPattern.MatchContext.init(allocator);
    defer context.deinit();
    try testing.expect(ComplexPattern.matches(compiler, add2, &context));
    
    // Verify all bindings
    try testing.expectEqual(a, context.bindings.get("a").?);
    try testing.expectEqual(b, context.bindings.get("b").?);
    try testing.expectEqual(c, context.bindings.get("c").?);
    try testing.expectEqual(d, context.bindings.get("d").?);
}

// ============================================================================
// Pattern Replacement Tests
// ============================================================================

test "pattern replacement: forward_input" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create x + 0
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    const x = try ctx.graph.newNodeVariable(view, .f32);
    const zero = try ctx.graph.newNodeConstantTyped(view, .f32);
    try ctx.data.setConstantData(f32, zero, &[_]f32{0.0}, ctx);
    
    const add = try ctx.graph.newNodeAdd(&[_]NodeId{x, zero}, view);
    
    // Pattern: x + 0 -> x (forward first input)
    const AddZeroPattern = pattern(
        .{ .binary = .{
            .op = .add,
            .left = &.{ .bind = "x" },
            .right = &.{ .constant_zero = {} },
        }},
        .{ .forward_input = 0 }
    );
    
    var context = AddZeroPattern.MatchContext.init(allocator);
    defer context.deinit();
    try testing.expect(AddZeroPattern.matches(compiler, add, &context));
    
    // Apply replacement
    const result = try AddZeroPattern.apply(compiler, add, &context);
    try testing.expectEqual(x, result);
}

test "pattern replacement: custom operations" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create pattern that needs custom replacement
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    const x = try ctx.graph.newNodeVariable(view, .f32);
    
    // Pattern with custom replacement
    const CustomPattern = pattern(
        .{ .unary = .{
            .op = .sin,
            .input = &.{ .bind = "x" },
        }},
        .{ .forward_input = 0 } // Forward the input instead
    );
    
    const sin_node = try ctx.graph.newNodeSin(x, view);
    
    var context = CustomPattern.MatchContext.init(allocator);
    defer context.deinit();
    try testing.expect(CustomPattern.matches(compiler, sin_node, &context));
    
    // Custom replacement returns original for now
    const result = try CustomPattern.apply(compiler, sin_node, &context);
    try testing.expectEqual(sin_node, result);
}

// ============================================================================
// Performance and Memory Tests
// ============================================================================

test "pattern engine: match context memory usage" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Create many bindings to test memory growth
    const TestPattern = struct {
        fn createDeepPattern(depth: usize) PatternEngine.MatchSpec {
            if (depth == 0) {
                return .{ .bind = "x0" };
            }
            
            // This is a compile-time recursive pattern builder
            // In practice, patterns are usually much simpler
            return .any;
        }
    }.createDeepPattern(5);
    
    _ = TestPattern;
    
    // Create nodes
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    const node = try ctx.graph.newNodeVariable(view, .f32);
    
    // Test that many matches don't leak memory
    for (0..100) |_| {
        var context = PatternEngine.Pattern(.any, .remove).MatchContext.init(allocator);
        defer context.deinit();
        _ = PatternEngine.Pattern(.any, .remove).matches(compiler, node, &context);
        // Context cleanup happens automatically
    }
}

test "pattern engine: pattern compilation overhead" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    // All patterns are compiled at comptime
    // This test verifies that pattern creation has zero runtime cost
    
    const Pattern1 = pattern(
        .{ .binary = .{ .op = .add, .left = &.any, .right = &.any }},
        .{ .remove = {} }
    );
    
    const Pattern2 = pattern(
        .{ .unary = .{ .op = .sin, .input = &.any }},
        .{ .remove = {} }
    );
    
    // These should have no runtime overhead
    _ = Pattern1;
    _ = Pattern2;
    
    // The actual matching is the only runtime cost
}

// ============================================================================
// Error Case Tests
// ============================================================================

test "pattern engine: invalid pattern combinations" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Test patterns that might cause issues
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    const node = try ctx.graph.newNodeVariable(view, .f32);
    
    // Pattern with conflicting bindings (would bind same var twice)
    const ConflictPattern = pattern(
        .{ .binary = .{
            .op = .add,
            .left = &.{ .bind = "x" },
            .right = &.{ .bind = "x" }, // Same binding name
        }},
        .{ .remove = {} }
    );
    
    // This should handle gracefully
    var context = ConflictPattern.MatchContext.init(allocator);
    defer context.deinit();
    const add_node = try ctx.graph.newNodeAdd(&[_]NodeId{node, node}, view);
    
    // Should still match since both sides are the same
    try testing.expect(ConflictPattern.matches(compiler, add_node, &context));
}

test "pattern engine: operation type validation" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    const shape = try ctx.shape.newShape(&[_]*types.Expr{});
    const view = try ctx.shape.newDefaultView(shape);
    
    // Create nodes with different operations
    const x = try ctx.graph.newNodeVariable(view, .f32);
    const sin_node = try ctx.graph.newNodeSin(x, view);
    const sqrt_node = try ctx.graph.newNodeSqrt(x, view);
    
    // Pattern that matches sin
    const SinPattern = pattern(
        .{ .unary = .{ .op = .sin, .input = &.any }},
        .{ .remove = {} }
    );
    
    var ctx1 = SinPattern.MatchContext.init(allocator);
    defer ctx1.deinit();
    var ctx2 = SinPattern.MatchContext.init(allocator);
    defer ctx2.deinit();
    
    // Should match sin but not cos
    try testing.expect(SinPattern.matches(compiler, sin_node, &ctx1));
    try testing.expect(!SinPattern.matches(compiler, sqrt_node, &ctx2));
}