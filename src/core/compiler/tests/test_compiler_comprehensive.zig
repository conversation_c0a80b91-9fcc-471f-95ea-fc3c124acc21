// Comprehensive unit tests for the compiler system
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;
const types = core.types;
const graph_types = core.graph.types;

// Import tensor operations for more idiomatic test setup
const tensor = @import("tensor");
const tensor_creation = tensor.creation;
const tensor_pointwise = tensor.pointwise;

// Import compiler components
const CompilerEngine = @import("../engine.zig").CompilerEngine;
const PatternEngine = @import("../pattern_engine.zig");
const constant_folding = @import("../passes/constant_folding.zig");
const cse = @import("../passes/cse.zig");
const dead_code = @import("../passes/dead_code.zig");

const NodeId = types.NodeId;
const ViewId = types.ViewId;
const ShapeId = types.ShapeId;
const parent_types = types;

// Test utilities
const TestContext = struct {
    ctx: Core,
    compiler: *CompilerEngine,
    
    fn init(allocator: std.mem.Allocator) !TestContext {
        var ctx = try Core.init(allocator);
        const compiler = try ctx.getCompiler();
        return .{ .ctx = ctx, .compiler = compiler };
    }
    
    fn deinit(self: *TestContext) void {
        self.ctx.deinit();
    }
    
    fn scalar(self: *TestContext, value: f32) !NodeId {
        // Use constant for scalar values
        return tensor_creation.constant(&self.ctx, value);
    }
    
    fn zeros(self: *TestContext, shape_dims: []const i64) !NodeId {
        // Convert i64 shape to expression shape
        var exprs = try self.ctx.arena.allocator().alloc(*types.Expr, shape_dims.len);
        for (shape_dims, 0..) |dim, i| {
            exprs[i] = try self.ctx.symbolic.newIntegerExpr(dim);
        }
        return tensor_creation.zeros(&self.ctx, exprs);
    }
    
    fn ones(self: *TestContext, shape_dims: []const i64) !NodeId {
        // Convert i64 shape to expression shape  
        var exprs = try self.ctx.arena.allocator().alloc(*types.Expr, shape_dims.len);
        for (shape_dims, 0..) |dim, i| {
            exprs[i] = try self.ctx.symbolic.newIntegerExpr(dim);
        }
        return tensor_creation.ones(&self.ctx, exprs);
    }
    
    fn variable(self: *TestContext, shape_dims: []const i64) !NodeId {
        // Create a variable node using graph API directly
        // Variables need to be created through the graph layer
        var exprs = try self.ctx.arena.allocator().alloc(*types.Expr, shape_dims.len);
        for (shape_dims, 0..) |dim, i| {
            exprs[i] = try self.ctx.symbolic.newIntegerExpr(dim);
        }
        const shape = try self.ctx.shape.newShape(exprs);
        const view = try self.ctx.shape.newDefaultView(shape);
        return self.ctx.graph.newNodeVariable(view, .f32);
    }
    
    fn markAsOutput(self: *TestContext, node: NodeId) !void {
        try self.compiler.consumer_tracker.persistent_nodes.put(
            self.ctx.arena.allocator(),
            node,
            {}
        );
    }
};

// ============================================================================
// Memory Management Tests
// ============================================================================

test "compiler memory management: arena allocator consistency" {
    // Test that compiler correctly uses arena allocator
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Verify arena is being used
    const arena_ptr = @intFromPtr(test_ctx.ctx.arena.allocator().ptr);
    const compiler_allocator_ptr = @intFromPtr(test_ctx.compiler.core.arena.allocator().ptr);
    try testing.expectEqual(arena_ptr, compiler_allocator_ptr);
    
    // Create many nodes to stress memory allocation
    var nodes = std.ArrayList(NodeId).init(allocator);
    defer nodes.deinit();
    
    for (0..100) |_| {
        const node = try test_ctx.scalar(1.0);
        try nodes.append(node);
    }
    
    // Verify no memory leaks after deinit
    // The arena should handle all cleanup
}

test "compiler memory management: consumer tracker lifecycle" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a graph with multiple consumers
    const a = try test_ctx.variable(&[_]i64{2, 3});
    const b = try test_ctx.variable(&[_]i64{2, 3});
    const add1 = try tensor_pointwise.add(&test_ctx.ctx, a, b);
    _ = try tensor_pointwise.add(&test_ctx.ctx, a, add1);
    
    // Analyze consumers
    try test_ctx.compiler.consumer_tracker.analyze(&test_ctx.ctx.graph);
    
    // Verify consumer counts
    const a_count = test_ctx.compiler.consumer_tracker.consumer_counts.get(a) orelse 0;
    try testing.expectEqual(@as(u32, 2), a_count); // Used by add1 and add2
    
    const b_count = test_ctx.compiler.consumer_tracker.consumer_counts.get(b) orelse 0;
    try testing.expectEqual(@as(u32, 1), b_count); // Used by add1
    
    const add1_count = test_ctx.compiler.consumer_tracker.consumer_counts.get(add1) orelse 0;
    try testing.expectEqual(@as(u32, 1), add1_count); // Used by add2
}

// ============================================================================
// Pattern Engine Tests
// ============================================================================

test "pattern engine: match context memory pooling" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a simple pattern
    const TestPattern = PatternEngine.pattern(
        .{ .binary = .{
            .op = .add,
            .left = &.{ .bind = "x" },
            .right = &.{ .bind = "y" },
        }},
        .{ .forward_input = 0 }
    );
    
    // Create multiple nodes to match against
    var matched_count: usize = 0;
    for (0..10) |_| {
        const a = try test_ctx.scalar(1.0);
        const b = try test_ctx.scalar(2.0);
        const add = try tensor_pointwise.add(&test_ctx.ctx, a, b);
        
        // Each match should use its own context
        var context = TestPattern.MatchContext.init(allocator);
        defer context.deinit();
        if (TestPattern.matches(test_ctx.compiler, add, &context)) {
            matched_count += 1;
            try testing.expect(context.bindings.count() == 2);
        }
    }
    
    try testing.expectEqual(@as(usize, 10), matched_count);
}

test "pattern engine: complex pattern matching" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a complex graph: (a + b) * c
    const a = try test_ctx.variable(&[_]i64{2, 3});
    const b = try test_ctx.variable(&[_]i64{2, 3});
    const c = try test_ctx.variable(&[_]i64{2, 3});
    
    const add = try tensor_pointwise.add(&test_ctx.ctx, a, b);
    const mul = try tensor_pointwise.mul(&test_ctx.ctx, add, c);
    
    // Test nested pattern matching
    const DistributivePattern = PatternEngine.pattern(
        .{ .binary = .{
            .op = .multiply,
            .left = &.{ .binary = .{
                .op = .add,
                .left = &.{ .bind = "x" },
                .right = &.{ .bind = "y" },
            }},
            .right = &.{ .bind = "z" },
        }},
        .{ .forward_input = 0 }
    );
    
    var context = DistributivePattern.MatchContext.init(allocator);
    defer context.deinit();
    try testing.expect(DistributivePattern.matches(test_ctx.compiler, mul, &context));
    try testing.expectEqual(a, context.bindings.get("x").?);
    try testing.expectEqual(b, context.bindings.get("y").?);
    try testing.expectEqual(c, context.bindings.get("z").?);
}

// ============================================================================
// Constant Folding Tests
// ============================================================================

test "constant folding: x + 0 optimization" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create x + 0
    const x = try test_ctx.variable(&[_]i64{2, 3});
    const zero = try test_ctx.scalar(0.0);
    const add = try tensor_pointwise.add(&test_ctx.ctx, x, zero);
    
    // Mark add as output
    try test_ctx.markAsOutput(add);
    
    // Apply constant folding
    const changed = try constant_folding.apply(test_ctx.compiler);
    try testing.expect(changed);
    
    // TODO: Verify the add node was replaced with x
    // This requires the pattern engine to actually perform replacements
}

test "constant folding: x * 0 optimization with edge cases" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Test 1: Regular x * 0 = 0
    const x = try test_ctx.variable(&[_]i64{2, 3});
    const zero = try test_ctx.scalar(0.0);
    const mul = try tensor_pointwise.mul(&test_ctx.ctx, x, zero);
    
    try test_ctx.markAsOutput(mul);
    
    // Apply constant folding
    const changed = try constant_folding.apply(test_ctx.compiler);
    try testing.expect(changed);
    
    // TODO: Test edge cases like NaN * 0, Inf * 0
    // These require proper handling in the pattern implementation
}

test "constant folding: multiple optimizations in sequence" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create: ((x + 0) * 1) + 0
    const x = try test_ctx.variable(&[_]i64{2, 3});
    const zero = try test_ctx.scalar(0.0);
    const one = try test_ctx.scalar(1.0);
    
    const add1 = try tensor_pointwise.add(&test_ctx.ctx, x, zero);
    const mul = try tensor_pointwise.mul(&test_ctx.ctx, add1, one);
    const add2 = try tensor_pointwise.add(&test_ctx.ctx, mul, zero);
    
    try test_ctx.markAsOutput(add2);
    
    // Apply constant folding multiple times
    var total_changed = false;
    for (0..3) |_| {
        const changed = try constant_folding.apply(test_ctx.compiler);
        total_changed = total_changed or changed;
        if (!changed) break;
    }
    
    try testing.expect(total_changed);
    // Should simplify to just x
}

// ============================================================================
// Common Subexpression Elimination Tests
// ============================================================================

test "CSE: identical operations deduplication" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create duplicate additions
    const a = try test_ctx.variable(&[_]i64{2, 3});
    const b = try test_ctx.variable(&[_]i64{2, 3});
    
    const add1 = try tensor_pointwise.add(&test_ctx.ctx, a, b);
    const add2 = try tensor_pointwise.add(&test_ctx.ctx, a, b);
    
    // Use both additions
    const result = try tensor_pointwise.mul(&test_ctx.ctx, add1, add2);
    
    try test_ctx.markAsOutput(result);
    
    const initial_count = test_ctx.ctx.graph.nodes.items.len;
    
    // Apply CSE
    const changed = try cse.apply(test_ctx.compiler);
    try testing.expect(changed);
    
    // One addition should be eliminated
    const final_count = test_ctx.ctx.graph.nodes.items.len;
    try testing.expect(final_count < initial_count);
}

test "CSE: hash collision handling" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create many operations to increase chance of hash collisions
    var nodes = std.ArrayList(NodeId).init(allocator);
    defer nodes.deinit();
    
    for (0..50) |i| {
        const a = try test_ctx.scalar(@floatFromInt(i));
        const b = try test_ctx.scalar(@floatFromInt(i + 1));
        const add = try tensor_pointwise.add(&test_ctx.ctx, a, b);
        try nodes.append(add);
    }
    
    // Mark last node as output
    try test_ctx.markAsOutput(nodes.items[nodes.items.len - 1]);
    
    // Apply CSE - should handle any hash collisions gracefully
    _ = try cse.apply(test_ctx.compiler);
    
    // Verify graph is still valid
    for (nodes.items) |node| {
        const n = test_ctx.ctx.graph.getNode(node);
        try testing.expect(n != null);
    }
}

test "CSE: commutative operation recognition" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a + b and b + a
    const a = try test_ctx.variable(&[_]i64{2, 3});
    const b = try test_ctx.variable(&[_]i64{2, 3});
    
    const add1 = try tensor_pointwise.add(&test_ctx.ctx, a, b);
    const add2 = try tensor_pointwise.add(&test_ctx.ctx, b, a);
    
    // Use both
    const result = try tensor_pointwise.mul(&test_ctx.ctx, add1, add2);
    
    try test_ctx.markAsOutput(result);
    
    // Apply CSE
    const changed = try cse.apply(test_ctx.compiler);
    try testing.expect(changed);
}

// ============================================================================
// Dead Code Elimination Tests
// ============================================================================

test "dead code elimination: unused nodes removal" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create used and unused operations
    const a = try test_ctx.variable(&[_]i64{2, 3});
    const b = try test_ctx.variable(&[_]i64{2, 3});
    
    // Unused branch
    const unused_add = try tensor_pointwise.add(&test_ctx.ctx, a, b);
    const unused_sin = try tensor_pointwise.sin(&test_ctx.ctx, unused_add);
    _ = unused_sin;
    
    // Used branch
    const used_mul = try tensor_pointwise.mul(&test_ctx.ctx, a, b);
    
    try test_ctx.markAsOutput(used_mul);
    
    const initial_count = test_ctx.ctx.graph.nodes.items.len;
    
    // Apply dead code elimination
    const changed = try dead_code.apply(test_ctx.compiler);
    try testing.expect(changed);
    
    // Unused nodes should be removed
    const final_count = test_ctx.ctx.graph.nodes.items.len;
    try testing.expect(final_count < initial_count);
}

test "dead code elimination: deep dependency chains" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a deep chain of unused operations
    var current = try test_ctx.variable(&[_]i64{2, 3});
    
    for (0..10) |_| {
        current = try tensor_pointwise.sin(&test_ctx.ctx, current);
    }
    
    // Create a separate used operation
    const used = try test_ctx.variable(&[_]i64{2, 3});
    try test_ctx.markAsOutput(used);
    
    // Apply dead code elimination
    const changed = try dead_code.apply(test_ctx.compiler);
    try testing.expect(changed);
    
    // The entire chain should be eliminated
}

// ============================================================================
// Optimization Pipeline Tests
// ============================================================================

test "optimization pipeline: full pipeline execution" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a complex graph with multiple optimization opportunities
    const x = try test_ctx.variable(&[_]i64{2, 3});
    const zero = try test_ctx.scalar(0.0);
    const one = try test_ctx.scalar(1.0);
    
    // Optimizable patterns
    const add_zero = try tensor_pointwise.add(&test_ctx.ctx, x, zero);
    const mul_one = try tensor_pointwise.mul(&test_ctx.ctx, add_zero, one);
    
    // Duplicate operation
    const add_zero_dup = try tensor_pointwise.add(&test_ctx.ctx, x, zero);
    
    // Dead code
    const dead = try tensor_pointwise.sin(&test_ctx.ctx, add_zero_dup);
    _ = dead;
    
    try test_ctx.markAsOutput(mul_one);
    
    const initial_count = test_ctx.ctx.graph.nodes.items.len;
    
    // Run all optimization passes
    var total_changed = false;
    
    // Constant folding
    total_changed = try constant_folding.apply(test_ctx.compiler) or total_changed;
    
    // CSE
    total_changed = try cse.apply(test_ctx.compiler) or total_changed;
    
    // Dead code elimination
    total_changed = try dead_code.apply(test_ctx.compiler) or total_changed;
    
    try testing.expect(total_changed);
    
    // Graph should be significantly simplified
    const final_count = test_ctx.ctx.graph.nodes.items.len;
    try testing.expect(final_count < initial_count);
}

test "optimization pipeline: iteration limit" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Track iteration count
    var iteration_count: u32 = 0;
    
    const CountingPass = struct {
        count: *u32,
        
        fn apply(self: @This(), engine: *CompilerEngine) !bool {
            _ = engine;
            self.count.* += 1;
            // Always return true to force iteration
            return true;
        }
    };
    
    // Clear default passes and add counting pass
    test_ctx.compiler.passes.clearRetainingCapacity();
    
    _ = CountingPass{ .count = &iteration_count };
    try test_ctx.compiler.passes.append(test_ctx.ctx.arena.allocator(), .{
        .name = "counting_pass",
        .apply = struct {
            fn apply(engine: *CompilerEngine) !bool {
                _ = engine;
                // Can't capture in anonymous function, so we test differently
                return false;
            }
        }.apply,
    });
    
    // The built-in iteration limit should prevent infinite loops
    _ = test_ctx.compiler.compile("cpu") catch {};
    
    // Should respect iteration limit (10 in the current implementation)
    // Since we can't easily test this with the current structure,
    // we just verify compilation doesn't hang
}

// ============================================================================
// Error Handling Tests
// ============================================================================

test "compiler error handling: invalid node references" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Try to mark an invalid node as output
    const invalid_node = NodeId.invalid;
    
    try test_ctx.compiler.consumer_tracker.persistent_nodes.put(
        test_ctx.ctx.arena.allocator(),
        invalid_node,
        {}
    );
    
    // Consumer analysis should handle invalid nodes gracefully
    try test_ctx.compiler.consumer_tracker.analyze(&test_ctx.ctx.graph);
    
    // Should not have any consumers for invalid node
    const count = test_ctx.compiler.consumer_tracker.consumer_counts.get(invalid_node);
    try testing.expect(count == null);
}

test "compiler error handling: shape calculation errors" {
    const allocator = testing.allocator;
    
    var test_ctx = try TestContext.init(allocator);
    defer test_ctx.deinit();
    
    // Create a node with symbolic dimensions
    const batch_sym = try test_ctx.ctx.symbolic.newSymbolExpr("batch_size");
    const shape = try test_ctx.ctx.shape.newShape(&[_]*types.Expr{batch_sym});
    const view = try test_ctx.ctx.shape.newDefaultView(shape);
    const node = try test_ctx.ctx.graph.newNodeVariable(view, .f32);
    
    try test_ctx.markAsOutput(node);
    
    // Compilation should handle symbolic dimensions gracefully
    _ = test_ctx.compiler.compile("cpu") catch {
        // Expected to fail with symbolic dimensions
        return;
    };
}