// Simple test to verify compiler basics work
const std = @import("std");
const testing = std.testing;
const core = @import("core");
const Core = core.Core;

test "compiler can be created" {
    const allocator = testing.allocator;
    
    const ctx = try Core.init(allocator);
    defer ctx.deinit();
    
    const compiler = try ctx.getCompiler();
    
    // Verify compiler has passes
    try testing.expect(compiler.passes.items.len > 0);
    
    // Check we have the expected passes
    var has_constant_folding = false;
    var has_cse = false;
    var has_dead_code = false;
    
    for (compiler.passes.items) |pass| {
        if (std.mem.eql(u8, pass.name, "constant_folding")) has_constant_folding = true;
        if (std.mem.eql(u8, pass.name, "cse")) has_cse = true;
        if (std.mem.eql(u8, pass.name, "dead_code_elimination")) has_dead_code = true;
    }
    
    try testing.expect(has_constant_folding);
    try testing.expect(has_cse);
    try testing.expect(has_dead_code);
}