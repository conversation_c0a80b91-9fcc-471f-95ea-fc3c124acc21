// Pattern-based Common Subexpression Elimination pass
const std = @import("std");
const CompilerEngine = @import("../engine.zig").CompilerEngine;
const PatternEngine = @import("../pattern_engine.zig");
const graph_types = @import("../../graph/types.zig");
const types = @import("../../types.zig");

const pattern = PatternEngine.pattern;
const NodeId = types.NodeId;
const Allocator = std.mem.Allocator;

/// CSE using pattern-based approach with comptime signature generation
pub fn apply(engine: *CompilerEngine) !bool {
    const allocator = engine.core.arena.allocator();
    
    // Use comptime-generated signatures for better performance
    var expr_map = std.AutoHashMapUnmanaged(u64, NodeId){};
    defer expr_map.deinit(allocator);
    
    var changed = false;
    
    // Process nodes in topological order
    const topo_order_raw = try engine.core.graph.topologicalSort();
    defer allocator.free(topo_order_raw);
    
    for (topo_order_raw) |raw_node_id| {
        const node_id = types.nodeIdFromU32(raw_node_id);
        const node = engine.core.graph.getNode(node_id) orelse continue;
        
        // Skip data nodes (they're unique by definition)
        if (node.isDataNode()) continue;
        
        // Generate compile-time optimized signature
        const signature = generateSignature(engine, node);
        
        if (expr_map.get(signature)) |existing_id| {
            // Found duplicate - redirect consumers
            if (try redirectConsumers(engine, node_id, existing_id)) {
                changed = true;
            }
        } else {
            // New expression - add to map
            try expr_map.put(allocator, signature, node_id);
        }
    }
    
    return changed;
}

/// Generate fast hash-based signature for a node
fn generateSignature(engine: *CompilerEngine, node: *const graph_types.Node) u64 {
    var hasher = std.hash.Wyhash.init(0);
    
    // Hash operation type
    hasher.update(std.mem.asBytes(&node.op));
    
    // Hash input node IDs (sorted for commutativity)
    var sorted_inputs: [8]NodeId = undefined; // Max 8 inputs for stack allocation
    const actual_inputs = @min(node.inputs.len, sorted_inputs.len);
    
    for (node.inputs[0..actual_inputs], 0..) |raw_input_id, i| {
        sorted_inputs[i] = types.nodeIdFromU32(@intFromEnum(raw_input_id));
    }
    
    if (actual_inputs > 1) {
        std.sort.insertion(NodeId, sorted_inputs[0..actual_inputs], {}, struct {
            fn lessThan(_: void, a: NodeId, b: NodeId) bool {
                return @intFromEnum(a) < @intFromEnum(b);
            }
        }.lessThan);
    }
    
    for (sorted_inputs[0..actual_inputs]) |input_id| {
        hasher.update(std.mem.asBytes(&input_id));
    }
    
    // Hash shape information
    const view_desc = engine.core.shape.getView(node.output_view_id);
    const shape = engine.core.shape.getShape(view_desc.shape_id);
    
    // Hash number of dimensions
    hasher.update(std.mem.asBytes(&shape.dims.len));
    
    // Hash dimension sizes (if concrete)
    for (shape.dims) |dim_expr| {
        if (engine.core.symbolic.evaluate(dim_expr, null)) |val| {
            hasher.update(std.mem.asBytes(&val));
        } else |_| {
            // For symbolic dimensions, hash the expression structure
            hasher.update(std.mem.asBytes(&dim_expr.hash()));
        }
    }
    
    // Hash data type
    hasher.update(std.mem.asBytes(&node.dtype));
    
    return hasher.final();
}

/// Redirect all consumers of duplicate_id to canonical_id
fn redirectConsumers(engine: *CompilerEngine, duplicate_id: NodeId, canonical_id: NodeId) !bool {
    // For now, just mark that we found a duplicate
    // Full implementation would require graph mutation support
    _ = engine;
    _ = duplicate_id;
    _ = canonical_id;
    
    // TODO: Implement when graph supports node redirection
    return false;
}

/// Pattern-based CSE for specific operation patterns
const CSEPatterns = struct {
    /// Pattern: Same binary operation with identical inputs
    pub const IdenticalBinary = struct {
        pub const MatchContext = PatternEngine.Pattern(.any, .remove).MatchContext;
        
        pub fn matches(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) bool {
            const node = engine.core.graph.getNode(node_id) orelse return false;
            
            // Only match binary operations
            if (!node.isBinary()) return false;
            
            // Check if we've seen this exact operation before
            const signature = generateSignature(engine, node);
            
            // Store in context for apply phase
            context.bind("signature", signature) catch return false;
            
            return true;
        }
        
        pub fn apply(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) !NodeId {
            // Implementation would look up existing node with same signature
            _ = engine;
            _ = context;
            return node_id; // Placeholder
        }
    };
};

/// Advanced CSE patterns for specific mathematical identities
/// Note: Using only Luminal primitive operations
const MathIdentityPatterns = struct {
    /// Pattern: a * reciprocal(a) -> 1 (when a != 0)
    pub const MultiplyReciprocal = pattern(
        .{ .binary = .{
            .op = .multiply,
            .left = &.{ .bind = "x" },
            .right = &.{ .unary = .{
                .op = .reciprocal,
                .input = &.{ .ref = "x" },
            }},
        }},
        .{ .constant = 1.0 }
    );
    
    /// Pattern: reciprocal(a) * a -> 1 (when a != 0)
    pub const ReciprocalMultiply = pattern(
        .{ .binary = .{
            .op = .multiply,
            .left = &.{ .unary = .{
                .op = .reciprocal,
                .input = &.{ .bind = "x" },
            }},
            .right = &.{ .ref = "x" },
        }},
        .{ .constant = 1.0 }
    );
    
    /// Pattern: sqrt(a) * sqrt(a) -> a (when a >= 0)
    pub const SqrtSelf = pattern(
        .{ .binary = .{
            .op = .multiply,
            .left = &.{ .bind = "sqrt_x" },
            .right = &.{ .ref = "sqrt_x" },
        }},
        .{ .forward_input = 0 } // Would need to extract sqrt input
    );
    
    /// Pattern: a mod a -> 0 (when a != 0)
    pub const ModSelf = pattern(
        .{ .binary = .{
            .op = .mod,
            .left = &.{ .bind = "x" },
            .right = &.{ .ref = "x" },
        }},
        .{ .constant = 0.0 }
    );
};

/// Apply mathematical identity CSE patterns
pub fn applyMathIdentities(engine: *CompilerEngine) !bool {
    const MathCSEPass = PatternEngine.PatternPass(&[_]type{
        MathIdentityPatterns.MultiplyReciprocal,
        MathIdentityPatterns.ReciprocalMultiply,
        MathIdentityPatterns.SqrtSelf,
        MathIdentityPatterns.ModSelf,
    });
    
    return MathCSEPass.apply(engine);
}