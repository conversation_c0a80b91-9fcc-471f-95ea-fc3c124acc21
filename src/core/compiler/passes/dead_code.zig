// Pattern-based dead code elimination pass
const std = @import("std");
const CompilerEngine = @import("../engine.zig").CompilerEngine;
const PatternEngine = @import("../pattern_engine.zig");
const graph_types = @import("../../graph/types.zig");
const types = @import("../../types.zig");

const pattern = PatternEngine.pattern;
const PatternPass = PatternEngine.PatternPass;
const NodeId = types.NodeId;

/// Dead code patterns - nodes that can be safely removed
const DeadCodePatterns = struct {
    /// Pattern: Node with no consumers that's not an output
    pub const UnusedNode = struct {
        pub const MatchContext = PatternEngine.Pattern(.any, .remove).MatchContext;
        
        pub fn matches(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) bool {
            _ = context;
            const node = engine.core.graph.getNode(node_id) orelse return false;
            
            // Don't remove data nodes (variables, constants, inputs)
            if (node.isDataNode()) return false;
            
            // Don't remove nodes with consumers
            if (node.consumers.len > 0) return false;
            
            // This is a dead computation node
            return true;
        }
        
        pub fn apply(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) !NodeId {
            // Mark node for removal
            _ = engine;
            _ = context;
            // TODO: Implement actual node removal
            return node_id;
        }
    };
    
    /// Pattern: Operations that produce unused intermediate results
    pub const UnusedIntermediate = struct {
        pub const MatchContext = PatternEngine.Pattern(.any, .remove).MatchContext;
        
        pub fn matches(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) bool {
            _ = context;
            const node = engine.core.graph.getNode(node_id) orelse return false;
            
            // Only consider computational nodes
            if (node.isDataNode()) return false;
            
            // Check if all consumers are also unused
            for (node.consumers) |raw_consumer_id| {
                const consumer_id = types.nodeIdFromU32(@intFromEnum(raw_consumer_id));
                const consumer = engine.core.graph.getNode(consumer_id) orelse continue;
                
                // If any consumer is live, this node is live
                if (consumer.consumers.len > 0 or consumer.isDataNode()) {
                    return false;
                }
            }
            
            return true;
        }
        
        pub fn apply(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) !NodeId {
            // Remove node and recursively check inputs
            _ = engine;
            _ = context;
            // TODO: Implement recursive removal
            return node_id;
        }
    };
};

/// Patterns for removing redundant computations
const RedundantPatterns = struct {
    /// Pattern: Identity operations that can be removed
    pub const Identity = pattern(
        .{ .unary = .{
            .op = .contiguous, // Example: contiguous(already_contiguous) 
            .input = &.{ .bind = "x" },
        }},
        .{ .forward_input = 0 }
    );
    
    /// Pattern: Double reciprocal: reciprocal(reciprocal(x)) -> x
    pub const DoubleReciprocal = pattern(
        .{ .unary = .{
            .op = .reciprocal,
            .input = &.{ .unary = .{
                .op = .reciprocal,
                .input = &.{ .bind = "x" },
            }},
        }},
        .{ .forward_input = 0 } // Would actually forward x
    );
    
    /// Pattern: Unused reshape that doesn't change shape
    pub const NoOpReshape = struct {
        pub const MatchContext = PatternEngine.Pattern(.any, .remove).MatchContext;
        
        pub fn matches(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) bool {
            _ = context;
            const node = engine.core.graph.getNode(node_id) orelse return false;
            
            // Check if this is a contiguous that doesn't change layout
            if (node.op != .contiguous) return false;
            
            // TODO: Compare input and output layouts
            return false; // Placeholder
        }
        
        pub fn apply(engine: *CompilerEngine, node_id: NodeId, context: *MatchContext) !NodeId {
            _ = engine;
            _ = context;
            return node_id; // Forward input
        }
    };
};

/// Comprehensive dead code elimination using multiple pattern types
pub fn apply(engine: *CompilerEngine) !bool {
    var changed = false;
    
    // First pass: Remove obviously dead nodes
    const DeadCodePass = PatternPass(&[_]type{
        DeadCodePatterns.UnusedNode,
    });
    changed = try DeadCodePass.apply(engine) or changed;
    
    // Second pass: Remove redundant operations
    const RedundantPass = PatternPass(&[_]type{
        RedundantPatterns.Identity,
        RedundantPatterns.DoubleReciprocal,
        RedundantPatterns.NoOpReshape,
    });
    changed = try RedundantPass.apply(engine) or changed;
    
    // Third pass: Remove unused intermediates (after redundant ops are gone)
    const IntermediatePass = PatternPass(&[_]type{
        DeadCodePatterns.UnusedIntermediate,
    });
    changed = try IntermediatePass.apply(engine) or changed;
    
    return changed;
}

/// Liveness analysis for more sophisticated dead code elimination
pub fn applyWithLivenessAnalysis(engine: *CompilerEngine) !bool {
    const allocator = engine.core.arena.allocator();
    
    // Build live set starting from outputs and variables
    var live_nodes = std.AutoHashMapUnmanaged(NodeId, void){};
    defer live_nodes.deinit(allocator);
    
    // Mark all output nodes as live
    const all_nodes = engine.core.graph.getAllNodes();
    for (all_nodes) |node| {
        const node_id = types.nodeIdFromU32(@intFromEnum(node.id));
        
        // Variables and nodes with no consumers are outputs
        if (node.op == .variable or node.consumers.len == 0) {
            try markLive(engine, &live_nodes, node_id, allocator);
        }
    }
    
    // Count dead nodes
    var dead_count: usize = 0;
    for (all_nodes) |node| {
        const node_id = types.nodeIdFromU32(@intFromEnum(node.id));
        if (!live_nodes.contains(node_id) and !node.isDataNode()) {
            dead_count += 1;
            // TODO: Actually remove dead nodes
        }
    }
    
    return dead_count > 0;
}

/// Recursively mark node and its inputs as live
fn markLive(
    engine: *CompilerEngine,
    live_nodes: *std.AutoHashMapUnmanaged(NodeId, void),
    node_id: NodeId,
    allocator: std.mem.Allocator,
) !void {
    // Already marked
    if (live_nodes.contains(node_id)) return;
    
    // Mark this node as live
    try live_nodes.put(allocator, node_id, {});
    
    // Recursively mark all inputs as live
    if (engine.core.graph.getNode(node_id)) |node| {
        for (node.inputs) |raw_input_id| {
            const input_id = types.nodeIdFromU32(@intFromEnum(raw_input_id));
            try markLive(engine, live_nodes, input_id, allocator);
        }
    }
}