const std = @import("std");
const testing = std.testing;
const core_mod = @import("core");
const Core = core_mod.Core;
const types = core_mod.types;
const errors = core_mod.errors;

test "graph engine initialization" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test initialization
    try testing.expect(core.graph.nodes.items.len == 0);
    try testing.expect(core.graph.next_node_id == 0);
    try testing.expect(core.graph.state == .building);
}

test "create constant node" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create a shape and view for the constant
    const shape_id = try core.shape.newShape(&[_]types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    const view_id = try core.shape.newDefaultView(shape_id);
    
    // Create constant node
    const node_id = try core.graph.newNodeConstant(view_id);
    
    // Verify node properties
    const node = core.graph.getNode(node_id).?;
    try testing.expectEqual(node.op, .constant);
    try testing.expectEqual(node.output_view_id, view_id);
    try testing.expectEqual(node.inputs.len, 0);
    try testing.expectEqual(node.id, node_id);
}

test "create add node" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create input nodes
    const shape_id = try core.shape.newShape(&[_]types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    const view1_id = try core.shape.newDefaultView(shape_id);
    const view2_id = try core.shape.newDefaultView(shape_id);
    
    const input1_id = try core.graph.newNodeConstant(view1_id);
    const input2_id = try core.graph.newNodeConstant(view2_id);
    
    // Create output view
    const output_view_id = try core.shape.newDefaultView(shape_id);
    
    // Create add node
    const add_id = try core.graph.newNodeAdd(&[_]u32{ input1_id, input2_id }, output_view_id);
    
    // Verify node properties
    const node = core.graph.getNode(add_id).?;
    try testing.expectEqual(node.op, .add);
    try testing.expectEqual(node.inputs.len, 2);
    try testing.expectEqual(node.inputs[0], input1_id);
    try testing.expectEqual(node.inputs[1], input2_id);
    
    // Verify consumer tracking
    const input1_consumers = core.graph.getNodeConsumers(input1_id);
    try testing.expectEqual(input1_consumers.len, 1);
    try testing.expectEqual(input1_consumers[0], add_id);
}

test "topological sort" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create a simple graph: input1 -> add -> multiply -> output
    //                       input2 ↗       ↗
    //                       input3 --------
    
    const shape_id = try core.shape.newShape(&[_]types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    const view1_id = try core.shape.newDefaultView(shape_id);
    const view2_id = try core.shape.newDefaultView(shape_id);
    const view3_id = try core.shape.newDefaultView(shape_id);
    
    const input1_id = try core.graph.newNodeConstant(view1_id);
    const input2_id = try core.graph.newNodeConstant(view2_id);
    const input3_id = try core.graph.newNodeConstant(view3_id);
    
    const add_view_id = try core.shape.newDefaultView(shape_id);
    const add_id = try core.graph.newNodeAdd(&[_]u32{ input1_id, input2_id }, add_view_id);
    
    const mult_view_id = try core.shape.newDefaultView(shape_id);
    const mult_id = try core.graph.newNodeMultiply(&[_]u32{ add_id, input3_id }, mult_view_id);
    
    // Get topological order
    const sorted = try core.graph.topologicalSort();
    
    // Verify order constraints
    const input1_pos = indexOfId(sorted, input1_id).?;
    const input2_pos = indexOfId(sorted, input2_id).?;
    const input3_pos = indexOfId(sorted, input3_id).?;
    const add_pos = indexOfId(sorted, add_id).?;
    const mult_pos = indexOfId(sorted, mult_id).?;
    
    // Inputs should come before add in the sorted order
    try testing.expect(input1_pos < add_pos);
    try testing.expect(input2_pos < add_pos);
    
    // Add should come before multiply
    try testing.expect(add_pos < mult_pos);
    
    // Input3 should come before multiply
    try testing.expect(input3_pos < mult_pos);
}

test "graph validation" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // The GraphEngine API doesn't allow creating cycles directly, 
    // so we'll test that validation on an empty graph doesn't fail
    try core.graph.validate();
    
    // If we get here, validation succeeded (no error thrown)
    
    // Note: To properly test cycle detection, we'd need to modify the internal
    // state of the graph to create a cycle, which isn't exposed through the public API
}

test "graph compilation state" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Initially in building state
    try testing.expect(!core.graph.isCompiled());
    try testing.expectEqual(core.graph.state, .building);
    
    // Mark as compiled
    core.graph.markCompiled();
    try testing.expect(core.graph.isCompiled());
    try testing.expectEqual(core.graph.state, .compiled);
    
    // Cannot add nodes after compilation
    const shape_id = try core.shape.newShape(&[_]types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    const view_id = try core.shape.newDefaultView(shape_id);
    
    const result = core.graph.newNodeConstant(view_id);
    try testing.expectError(errors.ZingError.InvalidGraphState, result);
}

test "node consumer tracking" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create nodes
    const shape_id = try core.shape.newShape(&[_]types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    const view_id = try core.shape.newDefaultView(shape_id);
    
    const input_id = try core.graph.newNodeConstant(view_id);
    
    // Initially no consumers
    try testing.expect(!core.graph.isNodeUsed(input_id));
    try testing.expectEqual(core.graph.getNodeConsumers(input_id).len, 0);
    
    // Create consumer
    const output_view_id = try core.shape.newDefaultView(shape_id);
    const consumer_id = try core.graph.newNodeReshape(input_id, output_view_id);
    
    // Now has one consumer
    try testing.expect(core.graph.isNodeUsed(input_id));
    const consumers = core.graph.getNodeConsumers(input_id);
    try testing.expectEqual(consumers.len, 1);
    try testing.expectEqual(consumers[0], consumer_id);
}

test "graph statistics" {
    const allocator = testing.allocator;
    
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Initially empty
    var stats = core.graph.getStats();
    try testing.expectEqual(stats.node_count, 0);
    
    // Add various node types
    const shape_id = try core.shape.newShape(&[_]types.Dim{
        .{ .concrete = 2 },
        .{ .concrete = 3 },
    });
    const view_id = try core.shape.newDefaultView(shape_id);
    
    _ = try core.graph.newNodeConstant(view_id);
    _ = try core.graph.newNodeConstant(view_id);
    const a = try core.graph.newNodeConstant(view_id);
    const b = try core.graph.newNodeConstant(view_id);
    
    const add_view = try core.shape.newDefaultView(shape_id);
    _ = try core.graph.newNodeAdd(&[_]u32{ a, b }, add_view);
    
    // Check updated stats
    stats = core.graph.getStats();
    try testing.expectEqual(stats.node_count, 5);
    try testing.expectEqual(stats.constant_count, 4);
    try testing.expectEqual(stats.binary_op_count, 1);
}

// Helper function
fn indexOfId(slice: []const u32, id: u32) ?usize {
    for (slice, 0..) |item, i| {
        if (item == id) return i;
    }
    return null;
}