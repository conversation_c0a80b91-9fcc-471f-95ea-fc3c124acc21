const std = @import("std");
const types = @import("types.zig");
const core_types = @import("../types.zig");

// ===== Operator Types =====

/// Input to an operator during execution
/// Contains both the data reference and the view descriptor
pub const OperatorInput = struct {
    data: TensorDataRef,
    view: core_types.ViewDesc,
};

/// Reference to tensor data with safe buffer access
pub const TensorDataRef = union(enum) {
    owned: []f32,
    borrowed: struct {
        data: [*]const f32,
        len: usize,
        
        pub fn asSlice(self: @This()) []const f32 {
            return self.data[0..self.len];
        }
    },
    
    pub fn asSlice(self: TensorDataRef) []const f32 {
        return switch (self) {
            .owned => |slice| slice,
            .borrowed => |b| b.asSlice(),
        };
    }
    
    pub fn len(self: TensorDataRef) usize {
        return switch (self) {
            .owned => |slice| slice.len,
            .borrowed => |b| b.len,
        };
    }
    
    /// Create a borrowed reference with explicit size
    pub fn fromBorrowed(data: [*]const f32, length: usize) TensorDataRef {
        return .{ .borrowed = .{ .data = data, .len = length } };
    }
    
    /// Create an owned reference 
    pub fn fromOwned(data: []f32) TensorDataRef {
        return .{ .owned = data };
    }
    
    /// Get data safely with bounds checking
    pub fn getData(self: TensorDataRef) []const f32 {
        return self.asSlice();
    }
};

/// Result from operator execution
pub const OperatorOutput = struct {
    data: []f32,
};

// ===== Operator Implementation =====

/// Operator trait for executing graph operations
/// Operators receive input data along with view descriptors
pub const Operator = struct {
    /// Function pointer for operator execution
    processFn: *const fn (
        self: *Operator,
        inputs: []const OperatorInput,
        allocator: std.mem.Allocator,
    ) anyerror![]OperatorOutput,
    
    /// Optional custom data for the operator
    data: ?*anyopaque = null,
    
    /// Execute the operator
    pub fn process(
        self: *Operator,
        inputs: []const OperatorInput,
        allocator: std.mem.Allocator,
    ) ![]OperatorOutput {
        return self.processFn(self, inputs, allocator);
    }
};

/// Get the value at a logical index using the view descriptor
pub fn getIndex(
    data: []const f32,
    view: *const shape_types.ViewDescriptor,
    logical_index: usize,
) f32 {
    // This would use the view's index expression to map logical to physical
    // For now, simplified implementation
    const physical_index = view.mapLogicalToPhysical(logical_index);
    return data[physical_index];
}

/// Standard implementations for primitive operators

pub const AddOperator = struct {
    pub fn create() Operator {
        return .{
            .processFn = process,
        };
    }
    
    fn process(
        op: *Operator,
        inputs: []const OperatorInput,
        allocator: std.mem.Allocator,
    ) ![]OperatorOutput {
        _ = op;
        
        const lhs = inputs[0];
        const rhs = inputs[1];
        
        // Get the number of elements from the view
        const n_elements = lhs.view.getNumElements();
        
        // Allocate output
        var output_data = try allocator.alloc(f32, n_elements);
        
        // Perform addition using view-aware indexing
        for (0..n_elements) |i| {
            const lhs_val = getIndex(lhs.data.getData(), &lhs.view, i);
            const rhs_val = getIndex(rhs.data.getData(), &rhs.view, i);
            output_data[i] = lhs_val + rhs_val;
        }
        
        return &[_]OperatorOutput{
            .{ .data = output_data },
        };
    }
};

pub const ContiguousOperator = struct {
    pub fn create() Operator {
        return .{
            .processFn = process,
        };
    }
    
    fn process(
        op: *Operator,
        inputs: []const OperatorInput,
        allocator: std.mem.Allocator,
    ) ![]OperatorOutput {
        _ = op;
        
        const input = inputs[0];
        const n_elements = input.view.getNumElements();
        
        // Allocate contiguous output
        var output_data = try allocator.alloc(f32, n_elements);
        
        // Copy data in logical order to physical order
        for (0..n_elements) |i| {
            output_data[i] = getIndex(input.data.getData(), &input.view, i);
        }
        
        return &[_]OperatorOutput{
            .{ .data = output_data },
        };
    }
};

// More operator implementations would follow...