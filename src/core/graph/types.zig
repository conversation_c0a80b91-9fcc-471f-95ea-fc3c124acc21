const std = @import("std");

// Import and re-export typed IDs from core types
pub const NodeId = @import("../types.zig").NodeId;
pub const ViewId = @import("../types.zig").ViewId;

/// Core graph types following V2 architecture
/// Now using type-safe enums instead of raw u32s

pub const OpType = enum {
    // Data nodes
    constant,    // Immutable data
    variable,    // Trainable parameters
    input,       // Placeholder for runtime data
    
    // Binary operations (ONLY Luminal primitives)
    add,
    multiply,
    mod,         // Modulo operation
    less_than,   // The only comparison primitive in Luminal
    
    // Unary operations (ONLY Luminal primitives)
    reciprocal,  // 1/x
    sqrt,
    sin,
    log2,        // Base-2 logarithm
    exp2,        // Base-2 exponential
    
    // Memory layout operations
    contiguous,  // Force contiguous memory layout
    
    // Reduction operations (ONLY Luminal primitives)
    reduce_sum,
    reduce_max,
    
    // NOTE: ALL other operations are decomposed in the tensor layer:
    // - Other comparisons (>, <=, >=, ==, !=) are built from less_than
    // - Element-wise min/max are built from comparisons
    // - reduce_min is built from reduce_max with negation
    // - where/conditional is built from multiplication with comparison results
    // - cast, gather, scatter if needed would be decomposed
    // - Shape operations (reshape, transpose, slice) modify view descriptors only
};

// DataType moved to core/types.zig
pub const DataType = @import("../types.zig").DataType;

pub const ConstantMetadata = struct {
    // Source information for the constant
    source: ConstantSource,
};

pub const ConstantSource = enum {
    pattern,     // Generated from a pattern (zeros, ones, etc.)
    literal,     // Literal data provided directly
    external,    // Loaded from external source
};

pub const NodeMetadata = union(enum) {
    constant: ConstantMetadata,
    reduction: ReductionMetadata,
    slice: SliceMetadata,
    transpose: TransposeMetadata,
};

pub const ReductionMetadata = struct {
    axes: []const i32,
    keep_dims: bool = false,
};

pub const SliceMetadata = struct {
    start: []const i64,
    end: []const i64,
    strides: ?[]const i64 = null,
};

pub const TransposeMetadata = struct {
    perm: []const u32,
};

pub const Node = struct {
    /// Unique identifier for this node
    id: NodeId,
    
    /// Operation type for this node
    op: OpType,
    
    /// Input node IDs (arena-allocated)
    inputs: []const NodeId,
    
    /// Output view ID in ShapeEngine
    output_view_id: ViewId,
    
    /// Data type of the output
    dtype: DataType,
    
    /// Operation metadata (for operations that need extra data)
    metadata: ?*NodeMetadata = null,
    
    /// Reference count for consumer tracking
    consumer_count: u32 = 0,
    
    /// Consumer node IDs (arena-allocated)
    consumers: []NodeId,
    
    pub fn isConstant(self: Node) bool {
        return self.op == .constant;
    }
    
    pub fn isVariable(self: Node) bool {
        return self.op == .variable;
    }
    
    pub fn isInput(self: Node) bool {
        return self.op == .input;
    }
    
    pub fn isDataNode(self: Node) bool {
        return switch (self.op) {
            .constant, .variable, .input => true,
            else => false,
        };
    }
    
    pub fn isUnary(self: Node) bool {
        return switch (self.op) {
            .reciprocal, .sqrt, .sin, .log2, .exp2, .contiguous => true,
            else => false,
        };
    }
    
    pub fn isBinary(self: Node) bool {
        return switch (self.op) {
            .add, .multiply, .mod, .less_than => true,
            else => false,
        };
    }
    
    pub fn isReduction(self: Node) bool {
        return switch (self.op) {
            .reduce_sum, .reduce_max => true,
            else => false,
        };
    }
};

pub const GraphState = enum {
    building,
    compiled,
    executed,
};

pub const GraphStats = struct {
    node_count: usize = 0,
    constant_count: usize = 0,
    binary_op_count: usize = 0,
    unary_op_count: usize = 0,
    reduction_count: usize = 0,
    // Note: shape_op_count removed - shape ops are no longer graph nodes!
    
    pub fn updateOpCount(self: *GraphStats, op: OpType, count: usize) void {
        switch (op) {
            .constant, .variable, .input => self.constant_count += count,
            .add, .multiply, .mod, .less_than => self.binary_op_count += count,
            .reciprocal, .sqrt, .sin, .log2, .exp2, .contiguous => self.unary_op_count += count,
            .reduce_sum, .reduce_max => self.reduction_count += count,
        }
    }
};

/// Import errors from unified core errors
const errors = @import("../errors.zig");
pub const GraphError = errors.ZingError;