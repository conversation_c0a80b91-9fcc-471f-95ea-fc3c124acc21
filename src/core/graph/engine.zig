const std = @import("std");
const types = @import("types.zig");
const parent_types = @import("../types.zig");
const topology = @import("topology.zig");
const memory = @import("memory.zig");
// Error handling uses direct error literals (idiomatic Zig 0.14)

// Forward declare Core to avoid circular dependency
const Core = @import("../core.zig").Core;

const Allocator = std.mem.Allocator;
const Node = types.Node;
const OpType = types.OpType;
const DataType = types.DataType;
const GraphState = types.GraphState;
const TopologyManager = topology.TopologyManager;
const MemoryManager = memory.MemoryManager;

/// Graph computational engine for managing nodes and their relationships
pub const GraphEngine = struct {
    /// Reference to parent Core
    core: *Core,
    
    /// Node storage
    nodes: std.ArrayList(Node),
    node_map: std.AutoHashMap(parent_types.NodeId, usize), // node_id -> array index
    
    /// Topology management
    topology_manager: TopologyManager,
    
    /// Memory management
    memory_manager: MemoryManager,
    
    /// Compilation state
    state: GraphState = .building,
    
    /// Node ID generation (raw counter, converted to NodeId when creating nodes)
    next_node_id: u32 = 1, // Start at 1, since 0 is reserved for invalid
    
    /// Statistics
    stats: types.GraphStats,
    
    const Self = @This();
    
    pub fn init(core: *Core) !Self {
        const allocator = core.arena.allocator();
        
        var nodes = std.ArrayList(Node).init(allocator);
        errdefer nodes.deinit();
        
        var node_map = std.AutoHashMap(parent_types.NodeId, usize).init(allocator);
        errdefer node_map.deinit();
        
        var topology_manager = TopologyManager.init(allocator);
        errdefer topology_manager.deinit();
        
        var memory_manager = try MemoryManager.init(allocator);
        errdefer memory_manager.deinit();
        
        return .{
            .core = core,
            .nodes = nodes,
            .node_map = node_map,
            .topology_manager = topology_manager,
            .memory_manager = memory_manager,
            .stats = .{},
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.nodes.deinit();
        self.node_map.deinit();
        self.topology_manager.deinit();
        self.memory_manager.deinit();
    }
    
    pub fn reset(self: *Self) void {
        self.nodes.clearRetainingCapacity();
        self.node_map.clearRetainingCapacity();
        self.topology_manager.reset();
        self.memory_manager.reset();
        self.next_node_id = 1; // Start at 1, since 0 is reserved for invalid
        self.state = .building;
        self.stats = .{};
    }
    
    // Helper function for binary operations - ELIMINATES DUPLICATION
    fn createBinaryOp(self: *Self, op_type: OpType, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        if (input_ids.len != 2) return error.InvalidArgumentCount;
        
        // Validate node IDs exist
        for (input_ids) |input_id| {
            _ = self.getNode(input_id) orelse return error.InvalidNodeId;
        }
        
        // For element-wise operations, validate shape compatibility
        switch (op_type) {
            .add, .multiply, .mod, .less_than => {
                const shapes = try self.getInputShapes(input_ids);
                if (!self.areShapesCompatibleForElementwise(shapes)) {
                    return error.IncompatibleShapes;
                }
            },
            else => return error.InvalidOperation,
        }
        
        // Allocate inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, input_ids.len);
        for (inputs, input_ids) |*input, id| {
            input.* = id;
        }
        
        return self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = op_type,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = self.inferDtype(input_ids),
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
    }
    
    // Helper function for unary operations - ELIMINATES DUPLICATION
    fn createUnaryOp(self: *Self, op_type: OpType, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        // Validate input node exists
        const input_node = self.getNode(input_id) orelse return error.InvalidNodeId;
        
        // Validate operation is a primitive unary operation
        switch (op_type) {
            .reciprocal, .sqrt, .sin, .log2, .exp2, .contiguous => {},
            else => return error.InvalidOperation,
        }
        
        // Allocate inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, 1);
        inputs[0] = input_id;
        
        return self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = op_type,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = input_node.dtype,
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
    }
    
    // Helper function for reduction operations - ELIMINATES DUPLICATION
    fn createReductionOp(self: *Self, op_type: OpType, input_id: parent_types.NodeId, axes: []const i32, output_view_id: parent_types.ViewId, keep_dims: bool) !parent_types.NodeId {
        // Validate operation is a primitive reduction operation
        switch (op_type) {
            .reduce_sum, .reduce_max => {},
            else => return error.InvalidOperation,
        }
        
        // Validate input node exists
        const input_node = self.getNode(input_id) orelse return error.InvalidNodeId;
        
        // Validate axes against input shape
        const input_view = self.core.shape.getView(input_node.output_view_id);
        const input_shape = self.core.shape.getShape(input_view.shape_id);
        for (axes) |axis| {
            const normalized_axis = if (axis < 0) @as(i32, @intCast(input_shape.dims.len)) + axis else axis;
            if (normalized_axis < 0 or normalized_axis >= input_shape.dims.len) {
                return error.InvalidAxes;
            }
        }
        
        // Allocate inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, 1);
        inputs[0] = input_id;
        
        const node_id = try self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = op_type,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = input_node.dtype,
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
        
        // Set metadata after creation
        const node = self.getNodeMut(node_id).?;
        const metadata = try self.core.arena.allocator().create(types.NodeMetadata);
        const axes_copy = try self.core.arena.allocator().dupe(i32, axes);
        
        metadata.* = .{
            .reduction = .{
                .axes = axes_copy,
                .keep_dims = keep_dims,
            },
        };
        
        node.metadata = metadata;
        return node_id;
    }
    
    /// Create an add node
    pub fn newNodeAdd(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createBinaryOp(.add, input_ids, output_view_id);
    }
    
    /// Create a multiply node
    pub fn newNodeMultiply(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createBinaryOp(.multiply, input_ids, output_view_id);
    }
    
    
    /// Create a mod node (modulo)
    pub fn newNodeMod(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createBinaryOp(.mod, input_ids, output_view_id);
    }
    
    /// Create comparison nodes
    pub fn newNodeLessThan(self: *Self, input_ids: []const parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createBinaryOp(.less_than, input_ids, output_view_id);
    }
    
    
    
    
    /// Create a sqrt node
    pub fn newNodeSqrt(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createUnaryOp(.sqrt, input_id, output_view_id);
    }
    
    
    /// Create sin node
    pub fn newNodeSin(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createUnaryOp(.sin, input_id, output_view_id);
    }
    
    
    /// Create reciprocal node (1/x)
    pub fn newNodeReciprocal(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createUnaryOp(.reciprocal, input_id, output_view_id);
    }
    
    
    /// Create log2 node
    pub fn newNodeLog2(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createUnaryOp(.log2, input_id, output_view_id);
    }
    
    /// Create exp2 node  
    pub fn newNodeExp2(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createUnaryOp(.exp2, input_id, output_view_id);
    }
    
    /// Create a contiguous node (forces data to be contiguous in memory)
    pub fn newNodeContiguous(self: *Self, input_id: parent_types.NodeId, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createUnaryOp(.contiguous, input_id, output_view_id);
    }
    
    /// Create a reduction sum node
    pub fn newNodeReduceSum(self: *Self, input_id: parent_types.NodeId, axes: []const i32, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createReductionOp(.reduce_sum, input_id, axes, output_view_id, false);
    }
    
    
    /// Create a reduce max node
    pub fn newNodeReduceMax(self: *Self, input_id: parent_types.NodeId, axes: []const i32, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        return self.createReductionOp(.reduce_max, input_id, axes, output_view_id, false);
    }
    
    
    
    /// Create a constant node
    pub fn newNodeConstant(self: *Self, output_view_id: parent_types.ViewId) !parent_types.NodeId {
        // Allocate empty inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, 0);
        
        return self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = .constant,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = .f32,
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
    }
    
    /// Create a constant node with specific data type
    pub fn newNodeConstantTyped(self: *Self, output_view_id: parent_types.ViewId, dtype: DataType) !parent_types.NodeId {
        // Allocate empty inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, 0);
        
        return self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = .constant,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = dtype,
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
    }
    
    /// Create a variable node
    pub fn newNodeVariable(self: *Self, output_view_id: parent_types.ViewId, dtype: DataType) !parent_types.NodeId {
        // Allocate empty inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, 0);
        
        return self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = .variable,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = dtype,
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
    }
    
    /// Create an input placeholder node
    pub fn newNodeInput(self: *Self, output_view_id: parent_types.ViewId, dtype: DataType) !parent_types.NodeId {
        // Allocate empty inputs array in arena
        const allocator = self.core.arena.allocator();
        const inputs = try allocator.alloc(parent_types.NodeId, 0);
        
        return self.createNode(.{
            .id = parent_types.NodeId.invalid, // Will be set by createNode
            .op = .input,
            .inputs = inputs,
            .output_view_id = output_view_id,
            .dtype = dtype,
            .consumers = &[_]parent_types.NodeId{}, // Will be managed by createNode
        });
    }
    
    
    
    /// Get a node by ID
    pub fn getNode(self: *Self, node_id: parent_types.NodeId) ?*const Node {
        const index = self.node_map.get(node_id) orelse return null;
        return &self.nodes.items[index];
    }
    
    /// Get a mutable node by ID
    pub fn getNodeMut(self: *Self, node_id: parent_types.NodeId) ?*Node {
        const index = self.node_map.get(node_id) orelse return null;
        return &self.nodes.items[index];
    }
    
    /// Get a node by ID or return error
    pub fn getNodeOrError(self: *const Self, node_id: parent_types.NodeId) !*const Node {
        return self.getNode(node_id) orelse error.InvalidNodeId;
    }
    
    /// Topologically sort nodes
    pub fn topologicalSort(self: *Self) ![]u32 {
        return self.topology_manager.topologicalSort(self.nodes.items);
    }
    
    /// Get consumers of a node
    pub fn getNodeConsumers(self: *Self, node_id: parent_types.NodeId) []const u32 {
        const node = self.getNode(node_id) orelse return &[_]u32{};
        const allocator = self.core.arena.allocator();
        const result = allocator.alloc(u32, node.consumers.len) catch return &[_]u32{};
        for (node.consumers, 0..) |consumer, i| {
            result[i] = @intFromEnum(consumer);
        }
        return result;
    }
    
    /// Check if a node is used
    pub fn isNodeUsed(self: *Self, node_id: parent_types.NodeId) bool {
        const node = self.getNode(node_id) orelse return false;
        return node.consumers.len > 0;
    }
    
    /// Create a node
    fn createNode(self: *Self, node_init: Node) !parent_types.NodeId {
        // Check if we can add new nodes
        if (self.state != .building) {
            return error.InvalidGraphState;
        }
        
        const node_id = @as(parent_types.NodeId, @enumFromInt(self.next_node_id));
        self.next_node_id += 1;
        
        // Create complete node with id and consumers
        var node = node_init;
        node.id = node_id;
        node.consumers = &[_]parent_types.NodeId{};
        
        // Store node
        const index = self.nodes.items.len;
        try self.nodes.append(node);
        try self.node_map.put(node_id, index);
        
        // Build topology
        for (node.inputs) |input_id| {
            if (self.getNodeMut(input_id)) |input_node| {
                const new_consumers = try self.core.arena.allocator().alloc(parent_types.NodeId, input_node.consumers.len + 1);
                @memcpy(new_consumers[0..input_node.consumers.len], input_node.consumers);
                new_consumers[input_node.consumers.len] = node_id;
                input_node.consumers = new_consumers;
            }
        }
        
        // Update stats
        self.stats.node_count += 1;
        self.stats.updateOpCount(node.op, 1);
        
        return node_id;
    }
    
    /// Get shapes of input nodes
    
    // ================== Convenience Methods for Tensor Layer ==================
    
    /// Add two tensors
    pub fn add(self: *Self, a: u32, b: u32) !u32 {
        // Get nodes to access their shapes
        const node_a = self.getNode(a) orelse return error.InvalidNodeId;
        const node_b = self.getNode(b) orelse return error.InvalidNodeId;
        
        // For element-wise operations, shapes must be compatible
        const view_a = self.core.shape.getView(node_a.output_view_id);
        const view_b = self.core.shape.getView(node_b.output_view_id);
        
        // Create output view (broadcast if needed)
        const output_shape_id = try self.core.shape.broadcast(view_a.shape_id, view_b.shape_id);
        const output_view_id = try self.core.shape.newDefaultView(output_shape_id);
        
        return self.newNodeAdd(&[_]u32{a, b}, output_view_id);
    }
    
    
    /// Multiply two tensors
    pub fn multiply(self: *Self, a: u32, b: u32) !u32 {
        const node_a = self.getNode(a) orelse return error.InvalidNodeId;
        const node_b = self.getNode(b) orelse return error.InvalidNodeId;
        
        const view_a = self.core.shape.getView(node_a.output_view_id);
        const view_b = self.core.shape.getView(node_b.output_view_id);
        
        const output_shape_id = try self.core.shape.broadcast(view_a.shape_id, view_b.shape_id);
        const output_view_id = try self.core.shape.newDefaultView(output_shape_id);
        
        return self.newNodeMultiply(&[_]u32{a, b}, output_view_id);
    }
    
    
    
    
    
    /// Square root
    pub fn sqrt(self: *Self, a: u32) !u32 {
        const node = self.getNode(a) orelse return error.InvalidNodeId;
        const output_view_id = try self.core.shape.cloneView(node.output_view_id);
        return self.newNodeSqrt(a, output_view_id);
    }
    
    /// Log base 2
    pub fn log2(self: *Self, a: u32) !u32 {
        const node = self.getNode(a) orelse return error.InvalidNodeId;
        const output_view_id = try self.core.shape.cloneView(node.output_view_id);
        return self.newNodeLog2(a, output_view_id);
    }
    
    /// Exp base 2
    pub fn exp2(self: *Self, a: u32) !u32 {
        const node = self.getNode(a) orelse return error.InvalidNodeId;
        const output_view_id = try self.core.shape.cloneView(node.output_view_id);
        return self.newNodeExp2(a, output_view_id);
    }
    
    /// Sin
    pub fn sin(self: *Self, a: u32) !u32 {
        const node = self.getNode(a) orelse return error.InvalidNodeId;
        const output_view_id = try self.core.shape.cloneView(node.output_view_id);
        return self.newNodeSin(a, output_view_id);
    }
    
    pub fn reciprocal(self: *Self, a: u32) !u32 {
        const node = self.getNode(a) orelse return error.InvalidNodeId;
        const output_view_id = try self.core.shape.cloneView(node.output_view_id);
        return self.newNodeReciprocal(a, output_view_id);
    }
    
    fn getInputShapes(self: *Self, input_ids: []const parent_types.NodeId) ![]const parent_types.Shape {
        const allocator = self.core.arena.allocator();
        var shapes = try allocator.alloc(parent_types.Shape, input_ids.len);
        
        for (input_ids, 0..) |input_id, i| {
            const node = self.getNode(input_id) orelse return error.InvalidNodeId;
            const view = self.core.shape.getView(node.output_view_id);
            shapes[i] = self.core.shape.getShape(view.shape_id).*;
        }
        
        return shapes;
    }
    
    /// Check shape compatibility for element-wise operations
    fn areShapesCompatibleForElementwise(self: *Self, shapes: []const parent_types.Shape) bool {
        if (shapes.len < 2) return true;
        
        // Check broadcasting compatibility for all shape pairs
        for (0..shapes.len-1) |i| {
            for (i+1..shapes.len) |j| {
                if (!self.areTwoShapesBroadcastable(shapes[i], shapes[j])) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    fn areTwoShapesBroadcastable(self: *Self, shape1: parent_types.Shape, shape2: parent_types.Shape) bool {
        // Two shapes are broadcastable if:
        // 1. Starting from the trailing dimension, the dimension sizes are either equal, or
        // 2. One of them is 1, or
        // 3. One shape has fewer dimensions (implicit leading 1s)
        
        const rank1 = shape1.dims.len;
        const rank2 = shape2.dims.len;
        const max_rank = @max(rank1, rank2);
        
        for (0..max_rank) |i| {
            // Index from the right (trailing dimensions)
            const idx1 = if (i < rank1) rank1 - 1 - i else null;
            const idx2 = if (i < rank2) rank2 - 1 - i else null;
            
            // Get dimensions (treat missing dimensions as 1)
            const default_one = self.core.symbolic.newIntegerExpr(1) catch return false;
            const dim1 = if (idx1) |idx| shape1.dims[idx] else default_one;
            const dim2 = if (idx2) |idx| shape2.dims[idx] else default_one;
            
            // Check if dimensions are compatible
            // First try symbolic equality (same expression reference)
            if (dim1 == dim2) {
                continue; // Same symbolic expression
            }
            
            // Try evaluating if possible (for concrete values)
            const size1_opt = self.core.symbolic.evaluate(dim1, null) catch null;
            const size2_opt = self.core.symbolic.evaluate(dim2, null) catch null;
            
            if (size1_opt != null and size2_opt != null) {
                const size1 = size1_opt.?;
                const size2 = size2_opt.?;
                // Dimensions are compatible if equal or one is 1
                if (size1 == size2 or size1 == 1 or size2 == 1) {
                    continue; // This dimension is compatible
                } else {
                    return false; // Incompatible concrete dimensions
                }
            } else {
                // At least one dimension is symbolic - assume compatible for now
                // This will be validated at execution time
                continue;
            }
        }
        
        return true;
    }
    
    /// Infer data type from inputs
    fn inferDtype(self: *Self, input_ids: []const parent_types.NodeId) DataType {
        // For now, default to f32. A more sophisticated implementation would
        // check input types and promote accordingly
        _ = input_ids;
        _ = self;
        return .f32;
    }
    
    /// Create a generic node with custom operation
    pub fn newNodeGeneric(self: *Self, op: OpType, input_ids: []const u32, output_view_id: u32) !u32 {
        return self.createNode(.{
            .op = op,
            .inputs = input_ids,
            .output_view_id = output_view_id,
            .dtype = self.inferDtype(input_ids),
        });
    }
    
    /// Get all nodes
    pub fn getAllNodes(self: *Self) []const Node {
        return self.nodes.items;
    }
    
    /// Find nodes by operation type
    pub fn findNodesByOp(self: *Self, op: OpType) ![]u32 {
        const allocator = self.core.arena.allocator();
        var result = std.ArrayList(u32).init(allocator);
        
        for (self.nodes.items) |node| {
            if (node.op == op) {
                try result.append(node.id);
            }
        }
        
        return result.toOwnedSlice();
    }
    
    /// Optimize graph by fusing operations
    pub fn optimize(self: *Self) !void {
        // Simple optimization pass - more sophisticated implementations would
        // include operation fusion, dead code elimination, etc.
        _ = self;
    }
    
    /// Compile graph to executable format
    pub fn compile(self: *Self) !void {
        if (self.state != .building) return error.InvalidGraphState;
        
        // Perform topological sort
        _ = try self.topologicalSort();
        
        // Run optimization passes
        try self.optimize();
        
        self.state = .compiled;
    }
    
    /// Execute compiled graph
    pub fn execute(self: *Self) !void {
        if (self.state != .compiled) return error.InvalidGraphState;
        
        // Execution would happen here in a real implementation
        self.state = .executed;
    }
    
    /// Validate graph integrity
    pub fn validate(self: *Self) !void {
        // Check for cycles
        _ = try self.topologicalSort();
        
        // Check all node references are valid
        for (self.nodes.items) |node| {
            for (node.inputs) |input_id| {
                _ = self.getNode(input_id) orelse return error.InvalidNodeId;
            }
        }
    }
    
    /// Check if graph is compiled
    pub fn isCompiled(self: *Self) bool {
        return self.state == .compiled;
    }
    
    /// Mark graph as compiled (for testing)
    pub fn markCompiled(self: *Self) void {
        self.state = .compiled;
    }
    
    /// Get stats
    pub fn getStats(self: *Self) *types.GraphStats {
        return &self.stats;
    }
    
    // === PLACEHOLDER IMPLEMENTATIONS FOR COMPLEX OPERATIONS ===
    // These will be implemented properly by decomposing into Luminal primitives
    
    /// Concatenate tensors along specified dimension
    pub fn concat(self: *Self, _: []const u32, _: i64) !u32 {
        _ = self;
        return error.NotImplemented;
    }
    
    /// Stack tensors along new dimension  
    pub fn stack(self: *Self, _: []const u32, _: i64) !u32 {
        _ = self;
        return error.NotImplemented;
    }
    
    /// Split tensor into chunks
    pub fn split(self: *Self, _: u32, _: i64, _: i64) ![]u32 {
        _ = self;
        return error.NotImplemented;
    }
    
    /// Split tensor with specific sizes
    pub fn splitWithSizes(self: *Self, _: u32, _: []const i64, _: i64) ![]u32 {
        _ = self;
        return error.NotImplemented;
    }
    
    /// Repeat tensor elements
    pub fn repeat(self: *Self, _: u32, _: []const i64) !u32 {
        _ = self;
        return error.NotImplemented;
    }
    
    /// Tile tensor
    pub fn tile(self: *Self, _: u32, _: []const i64) !u32 {
        _ = self;
        return error.NotImplemented;
    }
};