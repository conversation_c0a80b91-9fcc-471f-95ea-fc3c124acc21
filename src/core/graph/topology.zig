const std = @import("std");
const types = @import("types.zig");
const errors = @import("../errors.zig");

const Allocator = std.mem.Allocator;
const Node = types.Node;
const OpType = types.OpType;

pub const TopologyManager = struct {
    allocator: Allocator,
    sorted_nodes: std.ArrayList(u32),
    visited: std.AutoHashMap(u32, void),
    
    const Self = @This();
    
    pub fn init(allocator: Allocator) Self {
        return .{
            .allocator = allocator,
            .sorted_nodes = std.ArrayList(u32).init(allocator),
            .visited = std.AutoHashMap(u32, void).init(allocator),
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.sorted_nodes.deinit();
        self.visited.deinit();
    }
    
    pub fn reset(self: *Self) void {
        self.sorted_nodes.clearRetainingCapacity();
        self.visited.clearRetainingCapacity();
    }
    
    /// Perform topological sort on the graph
    pub fn topologicalSort(self: *Self, nodes: []const Node) ![]u32 {
        self.reset();
        
        // Reserve capacity
        try self.sorted_nodes.ensureTotalCapacity(nodes.len);
        try self.visited.ensureUnusedCapacity(@intCast(nodes.len));
        
        // DFS-based topological sort
        for (nodes) |node| {
            const id_raw = node.id;
            if (!self.visited.contains(@intFromEnum(id_raw))) {
                try self.dfsVisit(node.id, nodes);
            }
        }
        
        // Don't reverse - the DFS post-order already gives us the right order
        // std.mem.reverse(u32, self.sorted_nodes.items);
        return self.sorted_nodes.items;
    }
    
    fn dfsVisit(self: *Self, node_id: types.NodeId, nodes: []const Node) !void {
        const id_raw = @intFromEnum(node_id);
        
        // Mark as visited
        try self.visited.put(id_raw, {});
        
        // Find the node
        const node = findNode(nodes, node_id) orelse return errors.ZingError.InvalidNodeId;
        
        // Visit all inputs first
        for (node.inputs) |input_id| {
            const input_raw = @intFromEnum(input_id);
            if (!self.visited.contains(input_raw)) {
                try self.dfsVisit(input_id, nodes);
            }
        }
        
        // Add to sorted list
        try self.sorted_nodes.append(id_raw);
    }
    
    /// Check if graph has cycles
    pub fn hasCycles(self: *Self, nodes: []const Node) !bool {
        var white = std.AutoHashMap(u32, void).init(self.allocator);
        defer white.deinit();
        var gray = std.AutoHashMap(u32, void).init(self.allocator);
        defer gray.deinit();
        var black = std.AutoHashMap(u32, void).init(self.allocator);
        defer black.deinit();
        
        // Initialize all nodes as white
        for (nodes) |node| {
            const id_raw = node.id;
            try white.put(id_raw, {});
        }
        
        // Check each node
        for (nodes) |node| {
            const id_raw = node.id;
            if (white.contains(id_raw)) {
                if (try self.hasCyclesDfs(id_raw, nodes, &white, &gray, &black)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    fn hasCyclesDfs(
        self: *Self,
        node_id: u32,
        nodes: []const Node,
        white: *std.AutoHashMap(u32, void),
        gray: *std.AutoHashMap(u32, void),
        black: *std.AutoHashMap(u32, void),
    ) !bool {
        // Move from white to gray
        _ = white.remove(node_id);
        try gray.put(node_id, {});
        
        // Find the node
        const node_id_enum: types.NodeId = node_id;
        const node = findNode(nodes, node_id_enum) orelse return errors.ZingError.InvalidNodeId;
        
        // Check all inputs
        for (node.inputs) |input_id| {
            const input_raw = input_id;
            if (gray.contains(input_raw)) {
                // Found a cycle
                return true;
            }
            
            if (white.contains(input_raw)) {
                if (try self.hasCyclesDfs(input_raw, nodes, white, gray, black)) {
                    return true;
                }
            }
        }
        
        // Move from gray to black
        _ = gray.remove(node_id);
        try black.put(node_id, {});
        
        return false;
    }
    
    /// Find all nodes that depend on the given node
    pub fn findDependents(self: *Self, node_id: u32, nodes: []const Node) ![]u32 {
        var dependents = std.ArrayList(u32).init(self.allocator);
        errdefer dependents.deinit();
        
        for (nodes) |node| {
            for (node.inputs) |input_id| {
                const input_raw = input_id;
                if (input_raw == node_id) {
                    const node_raw = node.id;
                    try dependents.append(node_raw);
                    break;
                }
            }
        }
        
        return dependents.toOwnedSlice();
    }
    
    /// Find all nodes that this node depends on
    pub fn findDependencies(self: *Self, node_id: u32, nodes: []const Node) ![]u32 {
        var dependencies = std.AutoHashMap(u32, void).init(self.allocator);
        defer dependencies.deinit();
        
        const node = findNode(nodes, node_id) orelse return errors.ZingError.InvalidNodeId;
        
        // Collect all dependencies recursively
        try self.collectDependencies(node, nodes, &dependencies);
        
        var result = std.ArrayList(u32).init(self.allocator);
        errdefer result.deinit();
        
        var it = dependencies.iterator();
        while (it.next()) |entry| {
            try result.append(entry.key_ptr.*);
        }
        
        return result.toOwnedSlice();
    }
    
    fn collectDependencies(
        self: *Self,
        node: Node,
        nodes: []const Node,
        dependencies: *std.AutoHashMap(u32, void),
    ) !void {
        for (node.inputs) |input_id| {
            const input_raw = input_id;
            if (!dependencies.contains(input_raw)) {
                try dependencies.put(input_raw, {});
                
                if (findNode(nodes, input_id)) |input_node| {
                    try self.collectDependencies(input_node, nodes, dependencies);
                }
            }
        }
    }
    
    fn findNode(nodes: []const Node, id: types.NodeId) ?Node {
        for (nodes) |node| {
            if (node.id.eql(id)) return node;
        }
        return null;
    }
};