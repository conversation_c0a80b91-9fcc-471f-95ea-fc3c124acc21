const std = @import("std");
const types = @import("types.zig");
const errors = @import("../errors.zig");
const memory = @import("../memory.zig");

const Allocator = std.mem.Allocator;
const Node = types.Node;
const DataType = types.DataType;

/// Memory manager for graph nodes and their associated data
pub const MemoryManager = struct {
    allocator: Allocator,
    
    /// Pool for node structures using the core pool implementation
    node_pool: memory.ObjectPool(Node),
    
    /// Pool for input ID arrays
    input_pool: memory.ObjectPool([8]u32), // Common case: up to 8 inputs
    
    /// Pool for consumer ID arrays
    consumer_pool: memory.ObjectPool([16]u32), // Common case: up to 16 consumers
    
    /// Arena for variable-length arrays
    arena: std.heap.ArenaAllocator,
    
    /// Statistics
    stats: MemoryStats,
    
    const Self = @This();
    
    pub const MemoryStats = struct {
        nodes_allocated: usize = 0,
        nodes_freed: usize = 0,
        input_arrays_allocated: usize = 0,
        consumer_arrays_allocated: usize = 0,
        arena_bytes_used: usize = 0,
    };
    
    pub fn init(allocator: Allocator) !Self {
        return .{
            .allocator = allocator,
            .node_pool = memory.ObjectPool(Node).init(allocator),
            .input_pool = memory.ObjectPool([8]u32).init(allocator),
            .consumer_pool = memory.ObjectPool([16]u32).init(allocator),
            .arena = std.heap.ArenaAllocator.init(allocator),
            .stats = .{},
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.node_pool.deinit();
        self.input_pool.deinit();
        self.consumer_pool.deinit();
        self.arena.deinit();
    }
    
    pub fn allocateNode(self: *Self) !*Node {
        const node = try self.node_pool.acquire();
        self.stats.nodes_allocated += 1;
        return node;
    }
    
    pub fn deallocateNode(self: *Self, node: *Node) void {
        self.node_pool.release(node) catch {};
        self.stats.nodes_freed += 1;
    }
    
    pub fn allocateInputArray(self: *Self, count: usize) ![]u32 {
        if (count <= 8) {
            // Use pool for small arrays
            const array = try self.input_pool.acquire();
            self.stats.input_arrays_allocated += 1;
            return array[0..count];
        } else {
            // Use arena for larger arrays
            const array = try self.arena.allocator().alloc(u32, count);
            self.stats.arena_bytes_used += count * @sizeOf(u32);
            return array;
        }
    }
    
    pub fn allocateConsumerArray(self: *Self, capacity: usize) ![]u32 {
        if (capacity <= 16) {
            // Use pool for small arrays
            const array = try self.consumer_pool.acquire();
            self.stats.consumer_arrays_allocated += 1;
            return array[0..0]; // Start empty
        } else {
            // Use arena for larger arrays
            const array = try self.arena.allocator().alloc(u32, capacity);
            self.stats.arena_bytes_used += capacity * @sizeOf(u32);
            return array[0..0]; // Start empty
        }
    }
    
    pub fn resetArena(self: *Self) !void {
        // Clear the arena but retain capacity
        _ = self.arena.reset(.retain_capacity);
        self.stats.arena_bytes_used = 0;
    }
    
    pub fn reset(self: *Self) void {
        // Reset pools and statistics
        self.node_pool.reset();
        self.input_pool.reset();
        self.consumer_pool.reset();
        _ = self.arena.reset(.retain_capacity);
        self.stats = .{};
    }
    
    pub fn getStats(self: *const Self) MemoryStats {
        return self.stats;
    }
};

test "MemoryManager basic operations" {
    var manager = try MemoryManager.init(std.testing.allocator);
    defer manager.deinit();
    
    // Test node allocation
    const node1 = try manager.allocateNode();
    const node2 = try manager.allocateNode();
    try std.testing.expect(node1 != node2);
    
    manager.deallocateNode(node1);
    manager.deallocateNode(node2);
    
    // Test input array allocation
    const small_inputs = try manager.allocateInputArray(4);
    try std.testing.expect(small_inputs.len == 4);
    
    const large_inputs = try manager.allocateInputArray(20);
    try std.testing.expect(large_inputs.len == 20);
    
    // Test consumer array allocation
    const small_consumers = try manager.allocateConsumerArray(8);
    try std.testing.expect(small_consumers.len == 0); // Starts empty
    
    const large_consumers = try manager.allocateConsumerArray(32);
    try std.testing.expect(large_consumers.len == 0); // Starts empty
    
    // Check stats
    const stats = manager.getStats();
    try std.testing.expect(stats.nodes_allocated == 2);
    try std.testing.expect(stats.nodes_freed == 2);
}