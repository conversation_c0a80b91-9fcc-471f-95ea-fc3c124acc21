const std = @import("std");

/// Unified error set for the entire Zing system
/// Following COMPREHENSIVE_SYSTEM_REDESIGN_PLAN.md recommendations
pub const ZingError = error{
    // Memory errors
    OutOfMemory,
    AllocationFailed,
    ResourceExhausted,
    
    // Shape errors  
    ShapeIncompatible,
    DimensionMismatch,
    BroadcastError,
    InvalidShape,
    InvalidDimension,
    RankMismatch,
    InvalidAxes,
    InvalidSliceParameters,
    InvalidPaddingParameters,
    ZeroSliceStep,
    ElementCountMismatch,
    
    // Graph errors
    NodeNotFound,
    InvalidNodeId,
    InvalidNodeType,
    DuplicateNode,
    CyclicDependency,
    InvalidTopology,
    GraphHasCycles,
    InvalidTensor,
    InvalidTensorContext,
    NotCompiled,
    CompilationFailed,
    ExecutionFailed,
    InvalidInput,
    InvalidOutput,
    InvalidArgumentCount,
    InvalidState,
    InvalidGraphState,
    
    // Symbolic errors
    ExpressionTooComplex,
    SimplificationFailed,
    InvalidSymbol,
    UndefinedSymbol,
    InvalidSymbolName,
    InvalidExpression,
    ExpressionTooLarge,
    EvaluationFailed,
    DivisionByZero,
    IntegerOverflow,
    NotAConstant,
    ConstraintConflict,
    RecursionDepthExceeded,
    NodeLimitExceeded,
    NonLinearExpression,
    ComplexLinearExpression,
    UnsupportedLinearOperation,
    UnsupportedExpansionOperation,
    UnsupportedCollectionOperation,
    UnsupportedFactorizationOperation,
    CircularReference,
    InvalidCache,
    PoolExhausted,
    EggFFIError,
    InvalidContext,
    
    // Tensor errors
    InvalidTensorOperation,
    IncompatibleShapes,
    
    // Data errors
    InvalidDataLayout,
    UnsupportedDataType,
    DataAccessError,
    
    // System errors
    InternalError,
    NotImplemented,
};

/// Get a human-readable description for any ZingError
pub fn describe(err: ZingError) []const u8 {
    return switch (err) {
        // Memory errors
        error.OutOfMemory => "Out of memory",
        error.AllocationFailed => "Memory allocation failed",
        error.ResourceExhausted => "System resources exhausted",
        
        // Shape errors
        error.ShapeIncompatible => "Incompatible shapes for operation",
        error.DimensionMismatch => "Dimension mismatch",
        error.BroadcastError => "Cannot broadcast shapes",
        error.InvalidShape => "Invalid shape specification",
        error.InvalidDimension => "Invalid dimension value",
        error.RankMismatch => "Tensor rank mismatch",
        error.InvalidAxes => "Invalid axes specification",
        error.InvalidSliceParameters => "Invalid slice parameters",
        error.InvalidPaddingParameters => "Invalid padding parameters",
        error.ZeroSliceStep => "Slice step cannot be zero",
        error.ElementCountMismatch => "Element count mismatch in reshape",
        
        // Graph errors
        error.NodeNotFound => "Node not found in graph",
        error.InvalidNodeId => "Invalid node identifier",
        error.InvalidNodeType => "Invalid node type",
        error.DuplicateNode => "Duplicate node identifier",
        error.CyclicDependency => "Cyclic dependency detected",
        error.InvalidTopology => "Invalid graph topology",
        error.GraphHasCycles => "Graph contains cycles",
        error.InvalidTensor => "Invalid tensor",
        error.InvalidTensorContext => "Invalid tensor context",
        error.NotCompiled => "Graph not compiled",
        error.CompilationFailed => "Graph compilation failed",
        error.ExecutionFailed => "Graph execution failed",
        error.InvalidInput => "Invalid input provided",
        error.InvalidOutput => "Invalid output specification",
        error.InvalidArgumentCount => "Incorrect number of arguments",
        error.InvalidState => "Invalid state",
        error.InvalidGraphState => "Invalid graph state",
        
        // Symbolic errors
        error.ExpressionTooComplex => "Expression too complex to handle",
        error.SimplificationFailed => "Expression simplification failed",
        error.InvalidSymbol => "Invalid symbol",
        error.UndefinedSymbol => "Undefined symbol in expression",
        error.InvalidSymbolName => "Invalid symbol name",
        error.InvalidExpression => "Invalid expression structure",
        error.ExpressionTooLarge => "Expression exceeds size limits",
        error.EvaluationFailed => "Expression evaluation failed",
        error.DivisionByZero => "Division by zero",
        error.IntegerOverflow => "Integer overflow during evaluation",
        error.NotAConstant => "Expression is not a constant",
        error.ConstraintConflict => "Conflicting constraints",
        error.RecursionDepthExceeded => "Maximum recursion depth exceeded",
        error.NodeLimitExceeded => "Expression node limit exceeded",
        error.NonLinearExpression => "Expression is not linear in the variable",
        error.ComplexLinearExpression => "Linear expression too complex to solve",
        error.UnsupportedLinearOperation => "Operation not supported for linear solving",
        error.UnsupportedExpansionOperation => "Operation not supported for polynomial expansion",
        error.UnsupportedCollectionOperation => "Operation not supported for term collection",
        error.UnsupportedFactorizationOperation => "Operation not supported for factorization",
        error.CircularReference => "Circular reference detected",
        error.InvalidCache => "Invalid cache state",
        error.PoolExhausted => "Resource pool exhausted",
        error.EggFFIError => "Error in Egg FFI interface",
        error.InvalidContext => "Invalid context state",
        
        // Tensor errors
        error.InvalidTensorOperation => "Invalid tensor operation",
        error.IncompatibleShapes => "Incompatible tensor shapes",
        
        // Data errors
        error.InvalidDataLayout => "Invalid data layout",
        error.UnsupportedDataType => "Unsupported data type",
        error.DataAccessError => "Error accessing data",
        
        // System errors
        error.InternalError => "Internal system error",
        error.NotImplemented => "Operation not yet implemented",
    };
}

/// Error translation helpers for module-specific errors
/// Following COMPREHENSIVE_SYSTEM_REDESIGN_PLAN.md pattern

pub fn translateShapeError(err: anytype) ZingError {
    return switch (err) {
        error.OutOfMemory => error.OutOfMemory,
        error.InvalidShape => error.InvalidShape,
        error.InvalidDimension => error.InvalidDimension,
        error.IncompatibleShapes => error.ShapeIncompatible,
        error.RankMismatch => error.RankMismatch,
        error.InvalidAxes => error.InvalidAxes,
        error.InvalidSliceParameters => error.InvalidSliceParameters,
        error.InvalidPaddingParameters => error.InvalidPaddingParameters,
        error.ZeroSliceStep => error.ZeroSliceStep,
        error.BroadcastMismatch => error.BroadcastError,
        error.ElementCountMismatch => error.ElementCountMismatch,
        error.NotImplemented => error.NotImplemented,
        else => error.InternalError,
    };
}

pub fn translateGraphError(err: anytype) ZingError {
    return switch (err) {
        error.OutOfMemory => error.OutOfMemory,
        error.InvalidNode => error.NodeNotFound,
        error.InvalidNodeId => error.InvalidNodeId,
        error.InvalidNodeType => error.InvalidNodeType,
        error.NodeNotFound => error.NodeNotFound,
        error.DuplicateNode => error.DuplicateNode,
        error.CyclicDependency => error.CyclicDependency,
        error.InvalidTopology => error.InvalidTopology,
        error.GraphHasCycles => error.GraphHasCycles,
        error.InvalidTensor => error.InvalidTensor,
        error.InvalidTensorContext => error.InvalidTensorContext,
        error.IncompatibleShapes => error.IncompatibleShapes,
        error.NotCompiled => error.NotCompiled,
        error.CompilationFailed => error.CompilationFailed,
        error.ExecutionFailed => error.ExecutionFailed,
        error.InvalidInput => error.InvalidInput,
        error.InvalidOutput => error.InvalidOutput,
        error.InvalidArgumentCount => error.InvalidArgumentCount,
        error.InvalidState => error.InvalidState,
        error.InvalidGraphState => error.InvalidGraphState,
        error.NotImplemented => error.NotImplemented,
        else => error.InternalError,
    };
}

pub fn translateSymbolicError(err: anytype) ZingError {
    return switch (err) {
        error.OutOfMemory => error.OutOfMemory,
        error.InvalidSymbol => error.InvalidSymbol,
        error.UndefinedSymbol => error.UndefinedSymbol,
        error.InvalidSymbolName => error.InvalidSymbolName,
        error.InvalidExpression => error.InvalidExpression,
        error.ExpressionTooLarge => error.ExpressionTooLarge,
        error.EvaluationFailed => error.EvaluationFailed,
        error.DivisionByZero => error.DivisionByZero,
        error.IntegerOverflow => error.IntegerOverflow,
        error.NotAConstant => error.NotAConstant,
        error.ConstraintConflict => error.ConstraintConflict,
        error.RecursionDepthExceeded => error.RecursionDepthExceeded,
        error.NodeLimitExceeded => error.NodeLimitExceeded,
        error.NonLinearExpression => error.NonLinearExpression,
        error.ComplexLinearExpression => error.ComplexLinearExpression,
        error.UnsupportedLinearOperation => error.UnsupportedLinearOperation,
        error.UnsupportedExpansionOperation => error.UnsupportedExpansionOperation,
        error.UnsupportedCollectionOperation => error.UnsupportedCollectionOperation,
        error.UnsupportedFactorizationOperation => error.UnsupportedFactorizationOperation,
        error.SimplificationFailed => error.SimplificationFailed,
        error.CircularReference => error.CircularReference,
        error.InvalidCache => error.InvalidCache,
        error.PoolExhausted => error.PoolExhausted,
        error.EggFFIError => error.EggFFIError,
        error.InvalidContext => error.InvalidContext,
        else => error.InternalError,
    };
}

/// Format error with description for display
pub fn format(
    err: ZingError,
    comptime fmt: []const u8,
    options: std.fmt.FormatOptions,
    writer: anytype,
) !void {
    _ = fmt;
    _ = options;
    try writer.writeAll(describe(err));
}