const std = @import("std");
const parent_types = @import("../types.zig");

/// Shape inference operations for ShapeEngine
/// This module handles shape inference for various operations

pub fn inferBroadcastShape(
    self: anytype,
    shape_id_a: parent_types.ShapeId,
    shape_id_b: parent_types.ShapeId,
) !parent_types.ShapeId {
    const shape_a = self.getShape(shape_id_a);
    const shape_b = self.getShape(shape_id_b);

    // Result has rank of the larger shape
    const max_rank = @max(shape_a.dims.len, shape_b.dims.len);
    var result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, max_rank);

    // Work backwards from the end
    var i: usize = 0;
    while (i < max_rank) : (i += 1) {
        const idx_a = if (i < shape_a.dims.len) shape_a.dims.len - 1 - i else null;
        const idx_b = if (i < shape_b.dims.len) shape_b.dims.len - 1 - i else null;

        const dim_a = if (idx_a) |idx| shape_a.dims[idx] else null;
        const dim_b = if (idx_b) |idx| shape_b.dims[idx] else null;

        // Determine result dimension
        result_dims[max_rank - 1 - i] = try self.inferBroadcastDimension(dim_a, dim_b);
    }

    return self.newShape(result_dims);
}

pub fn inferReshapeShape(
    self: anytype,
    current_shape: parent_types.ShapeId,
    new_dims: []const *parent_types.Expr,
) !parent_types.ShapeId {
    const shape = self.getShape(current_shape);
    
    // Calculate total elements in current shape
    var total_elements = try self.core.symbolic.newIntegerExpr(1);
    for (shape.dims) |dim| {
        total_elements = try self.core.symbolic.newBinaryExpr(.multiply, total_elements, dim);
    }

    // Check for at most one -1 dimension
    var negative_idx: ?usize = null;
    var known_product = try self.core.symbolic.newIntegerExpr(1);
    
    for (new_dims, 0..) |dim, i| {
        // Check if this is a constant
        const val = self.core.symbolic.evaluate(dim, null) catch continue;
        
        if (val == -1) {
            if (negative_idx != null) {
                return error.InvalidShape; // Multiple -1 dimensions
            }
            negative_idx = i;
        } else if (val <= 0) {
            return error.InvalidDimension;
        } else {
            known_product = try self.core.symbolic.newBinaryExpr(.multiply, known_product, dim);
        }
    }

    // Create result dimensions
    var result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, new_dims.len);
    
    if (negative_idx) |idx| {
        // Infer the -1 dimension
        const inferred_dim = try self.core.symbolic.inferReshapeDimension(
            total_elements,
            known_product,
            null,
        );
        
        for (new_dims, 0..) |dim, i| {
            if (i == idx) {
                result_dims[i] = inferred_dim;
            } else {
                result_dims[i] = dim;
            }
        }
    } else {
        // No -1 dimension, just copy
        for (new_dims, 0..) |dim, i| {
            result_dims[i] = dim;
        }
        
        // Verify total elements match
        var new_total = try self.core.symbolic.newIntegerExpr(1);
        for (result_dims) |dim| {
            new_total = try self.core.symbolic.newBinaryExpr(.multiply, new_total, dim);
        }
        
        // Check if totals are equal
        if (!self.exprsEqual(total_elements, new_total)) {
            // Try to evaluate both to give better error
            const current_count = self.core.symbolic.evaluate(total_elements, null) catch 
                return error.ElementCountMismatch;
            const new_count = self.core.symbolic.evaluate(new_total, null) catch 
                return error.ElementCountMismatch;
            
            if (current_count != new_count) {
                return error.ElementCountMismatch;
            }
        }
    }

    return self.newShape(result_dims);
}

pub fn canBroadcastShapes(
    self: anytype,
    shape_id_a: parent_types.ShapeId,
    shape_id_b: parent_types.ShapeId,
) bool {
    const shape_a = self.getShape(shape_id_a);
    const shape_b = self.getShape(shape_id_b);
    
    return self.canBroadcast(shape_a.dims, shape_b.dims) catch false;
}

pub fn inferBroadcastDimension(
    self: anytype,
    dim_a: ?*parent_types.Expr,
    dim_b: ?*parent_types.Expr,
) !*parent_types.Expr {
    // If either dimension is missing (due to rank difference), use the other
    if (dim_a == null) return dim_b.?;
    if (dim_b == null) return dim_a.?;

    // Evaluate dimensions
    const size_a = self.core.symbolic.evaluate(dim_a.?, null) catch {
        // If we can't evaluate, check if they're symbolically equal
        if (self.exprsEqual(dim_a.?, dim_b.?)) {
            return dim_a.?;
        }
        return error.InvalidDimension;
    };

    const size_b = self.core.symbolic.evaluate(dim_b.?, null) catch {
        // If we can't evaluate b but could evaluate a
        if (size_a == 1) return dim_b.?;
        return error.InvalidDimension;
    };

    // Standard broadcasting rules
    if (size_a == size_b) {
        return dim_a.?; // Could return either
    } else if (size_a == 1) {
        return dim_b.?;
    } else if (size_b == 1) {
        return dim_a.?;
    } else {
        return error.BroadcastError;
    }
}

pub fn inferConvOutputShape(
    self: anytype,
    input_shape_id: parent_types.ShapeId,
    kernel_size: []const usize,
    stride: []const usize,
    padding: []const usize,
    dilation: []const usize,
) !parent_types.ShapeId {
    const input_shape = self.getShape(input_shape_id);
    
    // Assuming NCHW format: [batch, channels, height, width]
    if (input_shape.dims.len != 4) {
        return error.InvalidShape;
    }
    
    if (kernel_size.len != 2 or stride.len != 2 or padding.len != 2 or dilation.len != 2) {
        return error.InvalidShape;
    }
    
    // Output shape: [batch, out_channels, out_height, out_width]
    var output_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, 4);
    
    // Batch and channel dimensions remain the same
    output_dims[0] = input_shape.dims[0]; // batch
    output_dims[1] = input_shape.dims[1]; // channels (will be updated by caller for out_channels)
    
    // Calculate spatial dimensions
    for (0..2) |i| {
        const spatial_idx = i + 2; // height=2, width=3
        const input_size = input_shape.dims[spatial_idx];
        
        const output_size = try self.core.symbolic.computeConvOutputDim(
            input_size,
            kernel_size[i],
            stride[i],
            padding[i],
            dilation[i],
        );
        
        output_dims[spatial_idx] = output_size;
    }
    
    return self.newShape(output_dims);
}

pub fn inferMatmulOutputShape(
    self: anytype,
    shape_id_a: parent_types.ShapeId,
    shape_id_b: parent_types.ShapeId,
) !parent_types.ShapeId {
    const shape_a = self.getShape(shape_id_a);
    const shape_b = self.getShape(shape_id_b);
    
    // Handle different cases based on dimensions
    if (shape_a.dims.len == 1 and shape_b.dims.len == 1) {
        // Vector dot product - returns scalar
        return self.newShape(&[_]*parent_types.Expr{});
    } else if (shape_a.dims.len == 1 and shape_b.dims.len == 2) {
        // Vector-matrix multiply: [n] x [n, m] -> [m]
        if (!self.exprsEqual(shape_a.dims[0], shape_b.dims[0])) {
            return error.DimensionMismatch;
        }
        return self.newShape(&[_]*parent_types.Expr{shape_b.dims[1]});
    } else if (shape_a.dims.len == 2 and shape_b.dims.len == 1) {
        // Matrix-vector multiply: [m, n] x [n] -> [m]
        if (!self.exprsEqual(shape_a.dims[1], shape_b.dims[0])) {
            return error.DimensionMismatch;
        }
        return self.newShape(&[_]*parent_types.Expr{shape_a.dims[0]});
    } else if (shape_a.dims.len == 2 and shape_b.dims.len == 2) {
        // Matrix multiply: [m, k] x [k, n] -> [m, n]
        if (!self.exprsEqual(shape_a.dims[1], shape_b.dims[0])) {
            return error.DimensionMismatch;
        }
        return self.newShape(&[_]*parent_types.Expr{ shape_a.dims[0], shape_b.dims[1] });
    } else if (shape_a.dims.len > 2 or shape_b.dims.len > 2) {
        // Batch matrix multiply
        const batch_dims_a = shape_a.dims[0 .. shape_a.dims.len - 2];
        const batch_dims_b = shape_b.dims[0 .. shape_b.dims.len - 2];
        
        // Broadcast batch dimensions
        const max_batch_dims = @max(batch_dims_a.len, batch_dims_b.len);
        var result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, max_batch_dims + 2);
        
        // Infer broadcast batch dimensions
        for (0..max_batch_dims) |i| {
            const idx_a = if (i < batch_dims_a.len) batch_dims_a.len - 1 - i else null;
            const idx_b = if (i < batch_dims_b.len) batch_dims_b.len - 1 - i else null;
            
            const dim_a = if (idx_a) |idx| batch_dims_a[idx] else null;
            const dim_b = if (idx_b) |idx| batch_dims_b[idx] else null;
            
            result_dims[max_batch_dims - 1 - i] = try self.inferBroadcastDimension(dim_a, dim_b);
        }
        
        // Check matrix dimensions
        const m = shape_a.dims[shape_a.dims.len - 2];
        const k_a = shape_a.dims[shape_a.dims.len - 1];
        const k_b = shape_b.dims[shape_b.dims.len - 2];
        const n = shape_b.dims[shape_b.dims.len - 1];
        
        if (!self.exprsEqual(k_a, k_b)) {
            return error.DimensionMismatch;
        }
        
        // Add matrix dimensions
        result_dims[max_batch_dims] = m;
        result_dims[max_batch_dims + 1] = n;
        
        return self.newShape(result_dims);
    } else {
        return error.InvalidShape;
    }
}

pub fn inferReductionShape(
    self: anytype,
    input_shape_id: parent_types.ShapeId,
    axes: []const i32,
    keep_dims: bool,
) !parent_types.ShapeId {
    const input_shape = self.getShape(input_shape_id);
    
    // Normalize axes
    var normalized_axes = try self.core.arena.allocator().alloc(usize, axes.len);
    defer self.core.arena.allocator().free(normalized_axes);
    
    for (axes, 0..) |axis, i| {
        const normalized = if (axis < 0) 
            @as(usize, @intCast(@as(i32, @intCast(input_shape.dims.len)) + axis))
        else 
            @as(usize, @intCast(axis));
            
        if (normalized >= input_shape.dims.len) {
            return error.InvalidAxes;
        }
        normalized_axes[i] = normalized;
    }
    
    if (keep_dims) {
        // Keep dimensions but set to 1
        var result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len);
        for (input_shape.dims, 0..) |dim, i| {
            var is_reduced = false;
            for (normalized_axes) |axis| {
                if (i == axis) {
                    is_reduced = true;
                    break;
                }
            }
            
            if (is_reduced) {
                result_dims[i] = try self.core.symbolic.newIntegerExpr(1);
            } else {
                result_dims[i] = dim;
            }
        }
        return self.newShape(result_dims);
    } else {
        // Remove reduced dimensions
        const new_rank = input_shape.dims.len - axes.len;
        var result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, new_rank);
        var out_idx: usize = 0;
        
        for (input_shape.dims, 0..) |dim, i| {
            var is_reduced = false;
            for (normalized_axes) |axis| {
                if (i == axis) {
                    is_reduced = true;
                    break;
                }
            }
            
            if (!is_reduced) {
                result_dims[out_idx] = dim;
                out_idx += 1;
            }
        }
        
        return self.newShape(result_dims);
    }
}

pub fn getNumElements(self: anytype, shape_id: parent_types.ShapeId) !usize {
    const shape = self.getShape(shape_id);
    
    if (shape.dims.len == 0) {
        return 1; // Scalar
    }
    
    var total_elements = try self.core.symbolic.newIntegerExpr(1);
    for (shape.dims) |dim| {
        total_elements = try self.core.symbolic.newBinaryExpr(.multiply, total_elements, dim);
    }
    
    // Evaluate to get concrete number
    const result = self.core.symbolic.evaluate(total_elements, null) catch 
        return error.InvalidDimension;
        
    if (result < 0) {
        return error.InvalidDimension;
    }
    
    return @as(usize, @intCast(result));
}

pub fn inferReshapeDimension(
    self: anytype,
    total_elements: *parent_types.Expr,
    known_product: *parent_types.Expr,
    bindings: ?std.StringHashMap(i64),
) !*parent_types.Expr {
    return self.core.symbolic.inferReshapeDimension(total_elements, known_product, bindings);
}

pub fn canBroadcast(self: anytype, shape1: []const *parent_types.Expr, shape2: []const *parent_types.Expr) !bool {
    return self.core.symbolic.canBroadcast(shape1, shape2);
}

pub fn inferPoolShape(
    self: anytype,
    input_shape_id: parent_types.ShapeId,
    kernel: usize,
    stride: usize,
    dilation: usize,
    axis: usize,
) !parent_types.ShapeId {
    const input_shape = self.getShape(input_shape_id);
    
    if (axis >= input_shape.dims.len) {
        return error.InvalidAxes;
    }
    
    // Calculate effective kernel size with dilation
    const effective_kernel = kernel + (kernel - 1) * dilation;
    
    // Get the dimension being pooled
    const dim_size = self.core.symbolic.evaluate(input_shape.dims[axis], null) catch 
        return error.InvalidDimension;
    
    // Calculate number of windows
    const num_windows = @divFloor(@as(usize, @intCast(dim_size)) - effective_kernel, stride) + 1;
    
    if (num_windows <= 0) {
        return error.InvalidShape;
    }
    
    // Create new shape with pooled dimension
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len + 1);
    
    // Copy dimensions before the pooled axis
    for (0..axis) |i| {
        new_dims[i] = input_shape.dims[i];
    }
    
    // Add the number of windows dimension
    new_dims[axis] = try self.core.symbolic.newIntegerExpr(@intCast(num_windows));
    
    // Add the kernel size dimension
    new_dims[axis + 1] = try self.core.symbolic.newIntegerExpr(@intCast(kernel));
    
    // Copy remaining dimensions
    for (axis + 1..input_shape.dims.len) |i| {
        new_dims[i + 1] = input_shape.dims[i];
    }
    
    return self.newShape(new_dims);
}

pub fn inferGatherShape(
    self: anytype,
    input_shape_id: parent_types.ShapeId,
    indices_shape_id: parent_types.ShapeId,
    axis: i64,
) !parent_types.ShapeId {
    const input_shape = self.getShape(input_shape_id);
    const indices_shape = self.getShape(indices_shape_id);
    
    // Normalize axis
    const norm_axis = if (axis < 0)
        @as(usize, @intCast(@as(i64, @intCast(input_shape.dims.len)) + axis))
    else
        @as(usize, @intCast(axis));
        
    if (norm_axis >= input_shape.dims.len) {
        return error.InvalidAxes;
    }
    
    // Result shape is:
    // - input_shape[0..axis] + indices_shape + input_shape[axis+1..]
    const result_rank = input_shape.dims.len - 1 + indices_shape.dims.len;
    var result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, result_rank);
    
    var out_idx: usize = 0;
    
    // Copy dimensions before gather axis
    for (0..norm_axis) |i| {
        result_dims[out_idx] = input_shape.dims[i];
        out_idx += 1;
    }
    
    // Add indices dimensions
    for (indices_shape.dims) |dim| {
        result_dims[out_idx] = dim;
        out_idx += 1;
    }
    
    // Copy dimensions after gather axis
    for (norm_axis + 1..input_shape.dims.len) |i| {
        result_dims[out_idx] = input_shape.dims[i];
        out_idx += 1;
    }
    
    return self.newShape(result_dims);
}

pub fn inferScatterShape(
    self: anytype,
    input_shape_id: parent_types.ShapeId,
    indices_shape_id: parent_types.ShapeId,
    src_shape_id: parent_types.ShapeId,
    axis: i64,
) !parent_types.ShapeId {
    const input_shape = self.getShape(input_shape_id);
    const src_shape = self.getShape(src_shape_id);
    
    // For scatter, the output shape is the same as input shape
    // But we need to validate that indices and src are compatible
    
    // Normalize axis
    const norm_axis = if (axis < 0)
        @as(usize, @intCast(@as(i64, @intCast(input_shape.dims.len)) + axis))
    else
        @as(usize, @intCast(axis));
        
    if (norm_axis >= input_shape.dims.len) {
        return error.InvalidAxes;
    }
    
    // src shape should be compatible with gather(input, indices, axis)
    const expected_src_shape = try self.inferGatherShape(input_shape_id, indices_shape_id, axis);
    const expected_shape = self.getShape(expected_src_shape);
    
    // Verify shapes match
    if (src_shape.dims.len != expected_shape.dims.len) {
        return error.ShapeIncompatible;
    }
    
    for (src_shape.dims, expected_shape.dims) |src_dim, exp_dim| {
        if (!self.exprsEqual(src_dim, exp_dim)) {
            return error.ShapeIncompatible;
        }
    }
    
    // Scatter output has same shape as input
    return input_shape_id;
}