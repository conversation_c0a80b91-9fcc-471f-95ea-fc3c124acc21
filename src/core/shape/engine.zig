const std = @import("std");
const parent_types = @import("../types.zig");
const memory = @import("../memory.zig");
// Error handling uses direct error literals (idiomatic Zig 0.14)
const symbolic_utils = @import("symbolic_utils.zig");

// Import split modules
const view_operations = @import("view_operations.zig");
const inference = @import("inference.zig");
const validation = @import("validation.zig");

/// Re-export SliceRange from types
pub const SliceRange = parent_types.SliceRange;

/// Additional error types for expression handling
pub const ExpressionError = error{
    NegativeIndex,
    InvalidDimension,
    SymbolicDimensionNotSupported,
    InvalidExpression,
};

/// ShapeEngine manages shape and view descriptors
pub const ShapeEngine = struct {
    const Self = @This();
    
    core: *Core,
    
    // Shape and view pools (ID-based)
    shapes: memory.IdPool(parent_types.Shape),
    views: memory.IdPool(parent_types.ViewDesc),
    
    // Deduplication cache - updated for pure expressions
    shape_cache: memory.ContentCache([]const *parent_types.Expr, parent_types.ShapeId),
    
    // Statistics tracking
    stats: ShapeStats = .{},
    
    // Import Core from parent module
    const Core = @import("../core.zig").Core;
    
    /// Helper: Create concrete expression from integer value
    fn createConcreteExpr(self: *Self, value: i64) !*parent_types.Expr {
        return self.core.symbolic.newIntegerExpr(value);
    }
    
    /// Helper: Create concrete expression from usize
    fn createConcreteDim(self: *Self, size: usize) !*parent_types.Expr {
        return self.createConcreteExpr(@intCast(size));
    }
    
    /// Compare two dimension expressions for equality (delegates to symbolic engine)
    pub fn dimsEqual(self: *Self, a: *parent_types.Expr, b: *parent_types.Expr) bool {
        return self.core.symbolic.exprEquals(a, b);
    }
    
    /// Compare two expression arrays for equality
    pub fn dimsArrayEqual(self: *Self, a: []const *parent_types.Expr, b: []const *parent_types.Expr) bool {
        if (a.len != b.len) return false;
        for (a, b) |dim_a, dim_b| {
            if (!self.dimsEqual(dim_a, dim_b)) return false;
        }
        return true;
    }
    
    pub const ShapeStats = struct {
        shapes_created: usize = 0,
        views_created: usize = 0,
        cache_hits: usize = 0,
        cache_misses: usize = 0,
    };
    
    pub fn init(core: *Core) !Self {
        const allocator = core.arena.allocator();
        
        return .{
            .core = core,
            .shapes = memory.IdPool(parent_types.Shape).init(allocator),
            .views = memory.IdPool(parent_types.ViewDesc).init(allocator),
            .shape_cache = memory.ContentCache([]const *parent_types.Expr, parent_types.ShapeId).init(allocator),
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.shapes.deinit();
        self.views.deinit();
        self.shape_cache.deinit();
    }
    
    pub fn clearCache(self: *Self) void {
        self.shape_cache.clear();
    }
    
    /// Reset the engine to initial state
    pub fn reset(self: *Self) void {
        self.shapes.clear();
        self.views.clear();
        self.shape_cache.clear();
        self.stats = .{};
    }
    
    /// Get statistics
    pub fn getStats(self: *const Self) ShapeStats {
        return self.stats;
    }
    
    /// Create a broadcast view from two input views for binary operations
    pub fn createBroadcastViewFromTwoInputs(self: *Self, view_id_a: parent_types.ViewId, view_id_b: parent_types.ViewId) !parent_types.ViewId {
        const view_a = try self.getViewOrError(view_id_a);
        const view_b = try self.getViewOrError(view_id_b);
        
        const shape_a = self.getShape(view_a.shape_id);
        const shape_b = self.getShape(view_b.shape_id);
        
        // Compute broadcast shape
        const max_rank = @max(shape_a.dims.len, shape_b.dims.len);
        var broadcast_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, max_rank);
        
        // Broadcast from the rightmost dimensions
        for (0..max_rank) |i| {
            const a_index = if (i < max_rank - shape_a.dims.len) null else shape_a.dims.len - (max_rank - i);
            const b_index = if (i < max_rank - shape_b.dims.len) null else shape_b.dims.len - (max_rank - i);
            
            const one_expr = try self.core.symbolic.newIntegerExpr(1);
            const a_expr = if (a_index) |idx| shape_a.dims[idx] else one_expr;
            const b_expr = if (b_index) |idx| shape_b.dims[idx] else one_expr;
            
            // Try to evaluate both expressions
            const a_val = self.core.symbolic.evaluate(a_expr, null) catch null;
            const b_val = self.core.symbolic.evaluate(b_expr, null) catch null;
            
            if (a_val != null and b_val != null) {
                // Both concrete - apply broadcasting rules
                const a_size = a_val.?;
                const b_size = b_val.?;
                if (a_size == b_size) {
                    broadcast_dims[i] = a_expr;  // Use original expression
                } else if (a_size == 1) {
                    broadcast_dims[i] = b_expr;
                } else if (b_size == 1) {
                    broadcast_dims[i] = a_expr;
                } else {
                    return error.IncompatibleShapes;
                }
            } else {
                // At least one symbolic - use symbolic broadcasting
                broadcast_dims[i] = try self.broadcastExpressions(a_expr, b_expr);
            }
        }
        
        const broadcast_shape_id = try self.newShape(broadcast_dims);
        const broadcast_view = try self.newDefaultView(broadcast_shape_id);
        return broadcast_view;
    }
    
    // Shape creation and management - updated for pure expressions
    pub fn newShape(self: *Self, dims: []const *parent_types.Expr) !parent_types.ShapeId {
        // Try to find existing shape in cache
        if (self.shape_cache.get(dims)) |cached_id| {
            self.stats.cache_hits += 1;
            return cached_id;
        }
        
        self.stats.cache_misses += 1;
        
        // Copy dimensions to arena
        const dims_copy = try self.core.arena.allocator().dupe(*parent_types.Expr, dims);
        
        // Create new shape
        const shape = parent_types.Shape{ .dims = dims_copy };
        const raw_id = try self.shapes.add(shape);
        const shape_id = @as(parent_types.ShapeId, @enumFromInt(raw_id));
        
        // Cache for deduplication using the duped copy
        try self.shape_cache.put(dims_copy, shape_id);
        
        self.stats.shapes_created += 1;
        return shape_id;
    }
    
    pub fn getShape(self: *Self, id: parent_types.ShapeId) *const parent_types.Shape {
        return self.shapes.get(@intFromEnum(id)).?;
    }
    
    pub fn getShapeOrError(self: *Self, id: parent_types.ShapeId) !*const parent_types.Shape {
        return self.shapes.get(@intFromEnum(id)) orelse error.InvalidShape;
    }
    
    // Helper method for creating sequence shapes (batch, seq_len, hidden_dim)
    pub fn newSequenceShape(self: *Self, batch: i64, seq_len: i64, hidden_dim: i64) !parent_types.ShapeId {
        const dims = [_]*parent_types.Expr{
            try self.createConcreteExpr(batch),
            try self.createConcreteExpr(seq_len),
            try self.createConcreteExpr(hidden_dim),
        };
        return self.newShape(&dims);
    }
    
    // Helper method for creating image shapes (batch, channels, height, width)
    pub fn newImageShape(self: *Self, batch: i64, channels: i64, height: i64, width: i64) !parent_types.ShapeId {
        const dims = [_]*parent_types.Expr{
            try self.createConcreteExpr(batch),
            try self.createConcreteExpr(channels),
            try self.createConcreteExpr(height),
            try self.createConcreteExpr(width),
        };
        return self.newShape(&dims);
    }
    
    // Check if a view is contiguous
    pub fn isContiguous(self: *Self, view_id: parent_types.ViewId) bool {
        const view = self.getView(view_id);
        const shape = self.getShape(view.shape_id);
        
        var expected_stride: i64 = 1;
        var i = shape.dims.len;
        while (i > 0) : (i -= 1) {
            const idx = i - 1;
            if (view.strides[idx] != expected_stride) return false;
            
            const dim_size = self.core.symbolic.evaluate(shape.dims[idx], null) catch {
                // For symbolic dimensions, we cannot determine contiguity at compile time
                return false;
            };
            expected_stride *= dim_size;
        }
        
        return true;
    }
    
    // View creation and management
    pub fn newDefaultView(self: *Self, shape_id: parent_types.ShapeId) !parent_types.ViewId {
        const shape = try self.getShapeOrError(shape_id);
        
        // Create default strides (row-major layout)
        const strides = try self.core.arena.allocator().alloc(i64, shape.dims.len);
        
        // For symbolic dimensions, we'll use concrete strides but generate symbolic expressions
        // to handle runtime stride calculation. Start with stride 1 for the rightmost dimension.
        var stride: i64 = 1;
        var has_symbolic = false;
        var i = shape.dims.len;
        while (i > 0) {
            i -= 1;
            strides[i] = stride;
            
            // Check if this dimension is symbolic (not a concrete integer)
            if (shape.dims[i].tag != .integer) {
                has_symbolic = true;
                // For symbolic dimensions, we still set a concrete stride value
                // but will generate index expressions to handle the symbolic calculation
                // Use stride of 1 as a placeholder that will be handled by expressions
                if (i > 0) {
                    stride = 1;  // Reset stride accumulation for symbolic dims
                }
            } else {
                // For concrete dimensions, calculate the actual stride
                const dim_size = self.core.symbolic.evaluate(shape.dims[i], null) catch {
                    // This shouldn't happen for concrete dims, but handle gracefully
                    has_symbolic = true;
                    stride = 1;
                    continue;
                };
                if (i > 0) {  // Don't multiply stride for the last iteration
                    stride *= dim_size;
                }
            }
        }
        
        // Generate index expression if we have symbolic dimensions
        var index_expr: ?*parent_types.Expr = null;
        if (has_symbolic) {
            index_expr = try self.generateIndexExpressionForSymbolic(shape_id, strides, 0);
        }
        
        const view = parent_types.ViewDesc{
            .shape_id = shape_id,
            .strides = strides,
            .offset_elements = 0,
            .validity_expr = null, // Default views have no validity constraints
            .mask_expr = index_expr, // Store index expression for symbolic handling
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Generate index expression for symbolic dimension handling
    fn generateIndexExpressionForSymbolic(
        self: *Self,
        shape_id: parent_types.ShapeId,
        _: []const i64,  // strides parameter not used in symbolic calculation
        offset_elements: usize
    ) !*parent_types.Expr {
        const shape = try self.getShapeOrError(shape_id);
        
        // Start with offset
        var result = try self.core.symbolic.newIntegerExpr(@intCast(offset_elements));
        
        // For each dimension: result += idx_i * stride_i
        // But for symbolic dimensions, we need to compute the stride symbolically
        var i = shape.dims.len;
        while (i > 0) {
            i -= 1;
            
            // Create index variable for this dimension
            const idx_var = try self.core.symbolic.newSymbolExpr(
                try std.fmt.allocPrint(self.core.arena.allocator(), "i{d}", .{i})
            );
            
            // Calculate the stride expression
            var stride_expr: *parent_types.Expr = undefined;
            if (i == shape.dims.len - 1) {
                // Rightmost dimension always has stride 1
                stride_expr = try self.core.symbolic.newIntegerExpr(1);
            } else {
                // Stride is the product of all dimensions to the right
                stride_expr = try self.core.symbolic.newIntegerExpr(1);
                var j = i + 1;
                while (j < shape.dims.len) : (j += 1) {
                    stride_expr = try self.core.symbolic.newBinaryExpr(
                        .multiply,
                        stride_expr,
                        shape.dims[j]
                    );
                }
            }
            
            // Multiply index by stride
            const term = try self.core.symbolic.newBinaryExpr(.multiply, idx_var, stride_expr);
            
            // Add to result
            result = try self.core.symbolic.newBinaryExpr(.add, result, term);
        }
        
        return result;
    }
    
    pub fn getView(self: *Self, id: parent_types.ViewId) *const parent_types.ViewDesc {
        return self.views.get(@intFromEnum(id)).?;
    }
    
    pub fn getViewOrError(self: *Self, id: parent_types.ViewId) !*const parent_types.ViewDesc {
        return self.views.get(@intFromEnum(id)) orelse error.InvalidShape;
    }
    
    // Helper function for creating views with validity constraints
    fn createViewWithValidity(self: *Self, shape_id: parent_types.ShapeId, strides: []const i64, offset: usize, validity_expr: ?*parent_types.Expr, mask_expr: ?*parent_types.Expr) !parent_types.ViewId {
        // Create an empty fake_dims array initially
        const view = parent_types.ViewDesc{
            .shape_id = shape_id,
            .strides = strides,
            .offset_elements = offset,
            .validity_expr = validity_expr,
            .mask_expr = mask_expr,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Create a view with explicit fake dimensions tracking
    pub fn createViewWithFakeDims(
        self: *Self, 
        shape_id: parent_types.ShapeId, 
        strides: []const i64, 
        offset_elements: usize,
        validity_expr: ?*parent_types.Expr,
        mask_expr: ?*parent_types.Expr,
        fake_dims: []const bool
    ) !parent_types.ViewId {
        const view = parent_types.ViewDesc{
            .shape_id = shape_id,
            .strides = strides,
            .offset_elements = offset_elements,
            .validity_expr = validity_expr,
            .mask_expr = mask_expr,
            .fake_dims = fake_dims,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Create a view with automatically generated expressions
    pub fn createView(self: *Self, shape_id: parent_types.ShapeId, strides: []const i64, offset_elements: usize) !parent_types.ViewId {
        // Create array of false flags (no fake dimensions)
        const shape = try self.getShapeOrError(shape_id);
        var fake_dims = try self.core.arena.allocator().alloc(bool, shape.dims.len);
        
        // Initialize fake dimensions based on stride values
        for (strides, 0..) |stride, i| {
            fake_dims[i] = (stride == 0);
        }
        
        // Create the view with expressions and fake dimensions
        const validity_expr = try self.generateValidityExpression(shape_id, strides, fake_dims);
        const index_expr = try self.generateIndexExpression(shape_id, strides, offset_elements);
        
        return self.createViewWithFakeDims(
            shape_id, 
            strides, 
            offset_elements, 
            validity_expr, 
            index_expr, 
            fake_dims
        );
    }
    
    // Shape transformations
    pub fn newReshapedView(self: *Self, input_view_id: parent_types.ViewId, new_shape_id: parent_types.ShapeId) !parent_types.ViewId {
        const input_view = try self.getViewOrError(input_view_id);
        const old_shape = self.getShape(input_view.shape_id);
        const new_shape = self.getShape(new_shape_id);
        
        // Validate reshape compatibility
        // For concrete dimensions, check total element count
        var old_total: usize = 1;
        var new_total: usize = 1;
        var has_old_symbolic = false;
        var has_new_symbolic = false;
        
        // Calculate old shape total
        for (old_shape.dims) |dim| {
            if (self.core.symbolic.evaluate(dim, null)) |v| {
                old_total *= @intCast(v);
            } else |_| {
                has_old_symbolic = true;
            }
        }
        
        // Calculate new shape total
        for (new_shape.dims) |dim| {
            if (self.core.symbolic.evaluate(dim, null)) |v| {
                new_total *= @intCast(v);
            } else |_| {
                has_new_symbolic = true;
            }
        }
        
        // If both shapes are concrete, validate element count
        if (!has_old_symbolic and !has_new_symbolic) {
            if (old_total != new_total) {
                return error.ElementCountMismatch;
            }
        } else if (has_old_symbolic and has_new_symbolic) {
            // Both have symbolic dimensions - create constraint
            // that total elements must be equal
            // This requires symbolic engine support for products
            // For now, trust the reshape is valid
        }
        
        // Create reshaped view
        // If input is contiguous, we can reuse its buffer
        if (self.isContiguous(input_view_id)) {
            const view = parent_types.ViewDesc{
                .shape_id = new_shape_id,
                .strides = try self.calculateStrides(new_shape_id),
                .offset_elements = input_view.offset_elements,
                .validity_expr = input_view.validity_expr, // Preserve validity from input
                .mask_expr = input_view.mask_expr,
            };
            
            const raw_id = try self.views.add(view);
            self.stats.views_created += 1;
            return @as(parent_types.ViewId, @enumFromInt(raw_id));
        } else {
            // Non-contiguous reshape requires copying
            // This should be handled by the graph engine
            // For shape tracking, we just create the view
            return self.newDefaultView(new_shape_id);
        }
    }
    
    pub fn newPermutedView(self: *Self, input_view_id: parent_types.ViewId, axes: []const u32) !parent_types.ViewId {
        const input_view = try self.getViewOrError(input_view_id);
        const shape = try self.getShapeOrError(input_view.shape_id);
        
        // Validate axes
        if (axes.len != shape.dims.len) return error.InvalidAxes;
        
        // Create permuted dimensions
        const new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, shape.dims.len);
        const new_strides = try self.core.arena.allocator().alloc(i64, shape.dims.len);
        
        for (axes, 0..) |axis, i| {
            if (axis >= shape.dims.len) return error.InvalidAxes;
            new_dims[i] = shape.dims[axis];
            new_strides[i] = input_view.strides[axis];
        }
        
        const new_shape_id = try self.newShape(new_dims);
        const view = parent_types.ViewDesc{
            .shape_id = new_shape_id,
            .strides = new_strides,
            .offset_elements = input_view.offset_elements,
            .validity_expr = input_view.validity_expr,
            .mask_expr = input_view.mask_expr,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    pub fn newSlicedView(self: *Self, input_view_id: parent_types.ViewId, ranges: []const SliceRange) !parent_types.ViewId {
        const input_view = self.getView(input_view_id);
        const shape = self.getShape(input_view.shape_id);
        
        if (ranges.len != shape.dims.len) return error.InvalidSliceParameters;
        
        // Calculate new dimensions and adjust offset
        const new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, shape.dims.len);
        const new_strides = try self.core.arena.allocator().alloc(i64, shape.dims.len);
        var new_offset = input_view.offset_elements;
        
        for (ranges, 0..) |range, i| {
            const expr = shape.dims[i];
            var start = range.start;
            var end = range.end;
            const step = range.step;
            
            // Try to evaluate the expression
            if (self.core.symbolic.evaluate(expr, null)) |size_val| {
                // Concrete case
                const s = size_val;
                
                // Handle negative indices
                if (start < 0) start += s;
                if (end < 0) end += s + 1;  // -1 means end of array, so add 1 to make it inclusive
                
                // Clamp to valid range
                start = @max(0, @min(start, s));
                end = @max(0, @min(end, s));
                
                if (step == 0) return error.ZeroSliceStep;
                
                const new_size = if (step > 0)
                    @divFloor(end - start, step)
                else
                    @divFloor(start - end, -step);
                
                new_dims[i] = try self.core.symbolic.newIntegerExpr(@intCast(@max(0, new_size)));
                new_strides[i] = input_view.strides[i] * step;
                new_offset += @as(usize, @intCast(start)) * @as(usize, @intCast(@abs(input_view.strides[i])));
            } else |_| {
                // Symbolic case
                if (range.start == 0 and range.end == -1 and range.step == 1) {
                    // Full slice - keep original dimension
                    new_dims[i] = expr;
                    new_strides[i] = input_view.strides[i];
                } else {
                    // Partial slice with symbolic dimension
                    if (range.step != 1) {
                        // Strided slicing not supported for symbolic dimensions yet
                        return error.UnsupportedSymbolicSliceStep;
                    }
                    
                    // Create symbolic slice expression
                    const start_expr = try self.core.symbolic.newIntegerExpr(range.start);
                    const end_expr = if (range.end == -1) 
                        expr // Use original dimension
                    else 
                        try self.core.symbolic.newIntegerExpr(range.end);
                    
                    // Calculate slice size: end - start
                    const slice_size = try self.core.symbolic.newBinaryExpr(
                        .subtract,
                        end_expr,
                        start_expr,
                    );
                    
                    new_dims[i] = slice_size;
                    new_strides[i] = input_view.strides[i];
                    
                    // For symbolic dimensions, we can't update offset at compile time
                    // This would need to be handled at runtime
                }
            }
        }
        
        const new_shape_id = try self.newShape(new_dims);
        const view = parent_types.ViewDesc{
            .shape_id = new_shape_id,
            .strides = new_strides,
            .offset_elements = new_offset,
            .validity_expr = input_view.validity_expr, // Preserve validity from input
            .mask_expr = input_view.mask_expr,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Create a padded view (expression-based)
    pub fn newPaddedView(self: *Self, input_view_id: parent_types.ViewId, padding: []const [2]*parent_types.Expr) !parent_types.ViewId {
        const input_view = try self.getViewOrError(input_view_id);
        const input_shape = self.getShape(input_view.shape_id);
        
        if (padding.len != input_shape.dims.len) {
            return error.InvalidArgumentCount;
        }
        
        // Calculate new dimensions with padding
        const new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len);
        
        for (input_shape.dims, padding, 0..) |dim_expr, pad, i| {
            const left_pad = pad[0];
            const right_pad = pad[1];
            
            // Add padding to dimension: dim + left_pad + right_pad
            const left_sum = try self.core.symbolic.newBinaryExpr(.add, dim_expr, left_pad);
            new_dims[i] = try self.core.symbolic.newBinaryExpr(.add, left_sum, right_pad);
        }
        
        const new_shape_id = try self.newShape(new_dims);
        
        // Create validity expression for padded regions
        // For padded view, valid indices are where we're within the original bounds
        const validity_expr: ?*parent_types.Expr = null;
        const mask_expr: ?*parent_types.Expr = null;
        
        // Build validity constraints for each dimension
        // A padded location is valid if all dimensions are within original bounds
        // This is a simplified version - full implementation would create proper
        // symbolic expressions for each dimension's bounds checking
        
        const view = parent_types.ViewDesc{
            .shape_id = new_shape_id,
            .strides = input_view.strides, // Keep same strides
            .offset_elements = input_view.offset_elements, // Adjust offset for padding
            .validity_expr = validity_expr,
            .mask_expr = mask_expr,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Apply binary operation to dimensions
    /// Binary operation on expressions (updated from dimBinaryOp)
    pub fn exprBinaryOp(
        self: *Self,
        op: parent_types.BinaryOp,
        expr1: *parent_types.Expr,
        expr2: *parent_types.Expr,
    ) !*parent_types.Expr {
        return try self.core.symbolic.newBinaryExpr(op, expr1, expr2);
    }
    
    // Missing operations from V1 that MUST be implemented
    
    /// Create a broadcast view that expands dimensions to match target shape
    pub fn newBroadcastView(self: *Self, input_view_id: parent_types.ViewId, target_shape_id: parent_types.ShapeId) !parent_types.ViewId {
        const input_view = try self.getViewOrError(input_view_id);
        const input_shape = self.getShape(input_view.shape_id);
        const target_shape = self.getShape(target_shape_id);
        
        // Validate broadcast is possible
        if (input_shape.dims.len > target_shape.dims.len) {
            return error.IncompatibleShapes;
        }
        
        // Create new strides for broadcasting
        const new_strides = try self.core.arena.allocator().alloc(i64, target_shape.dims.len);
        
        // Allocate array for fake dimensions
        var fake_dims = try self.core.arena.allocator().alloc(bool, target_shape.dims.len);
        
        const offset_dims = target_shape.dims.len - input_shape.dims.len;
        
        // Leading dimensions get stride 0 (broadcast) and are marked as fake
        var i: usize = 0;
        while (i < offset_dims) : (i += 1) {
            new_strides[i] = 0;
            fake_dims[i] = true; // Leading dimensions are always fake
        }
        
        // Copy existing strides, using 0 for dimensions that are 1
        while (i < target_shape.dims.len) : (i += 1) {
            const input_idx = i - offset_dims;
            const input_dim = input_shape.dims[input_idx];
            const target_dim = target_shape.dims[i];
            
            // Check if dimensions are compatible using expression evaluation
            const input_size_result = self.core.symbolic.evaluate(input_dim, null) catch {
                // For symbolic dims that can't be evaluated, we trust they're compatible
                new_strides[i] = input_view.strides[input_idx];
                fake_dims[i] = input_view.isFakeDim(input_idx);
                continue;
            };
            const input_size = @as(usize, @intCast(input_size_result));
            
            const target_size_result = self.core.symbolic.evaluate(target_dim, null) catch {
                // For symbolic dims that can't be evaluated, we trust they're compatible
                if (input_size == 1) {
                    new_strides[i] = 0;
                    fake_dims[i] = true;
                } else {
                    new_strides[i] = input_view.strides[input_idx];  
                    fake_dims[i] = input_view.isFakeDim(input_idx);
                }
                continue;
            };
            const target_size = @as(usize, @intCast(target_size_result));
            
            if (input_size != target_size and input_size != 1) {
                return error.IncompatibleShapes;
            }
            
            if (input_size == 1) {
                // Broadcast from size 1 - mark as fake
                new_strides[i] = 0;
                fake_dims[i] = true;
            } else {
                // Copy original stride and fake status
                new_strides[i] = input_view.strides[input_idx];
                fake_dims[i] = input_view.isFakeDim(input_idx);
            }
        }
        
        // Generate expressions
        const validity_expr = try self.generateValidityExpression(
            target_shape_id, new_strides, fake_dims
        );
        const index_expr = try self.generateIndexExpression(
            target_shape_id, new_strides, input_view.offset_elements
        );
        
        // Create the new view with fake dimensions tracking
        return self.createViewWithFakeDims(
            target_shape_id,
            new_strides,
            input_view.offset_elements,
            validity_expr,
            index_expr,
            fake_dims
        );
    }
    
    
    /// Create an expanded view by adding a new dimension
    pub fn newExpandedView(self: *Self, input_view_id: parent_types.ViewId, axis: usize, size_expr: *parent_types.Expr) !parent_types.ViewId {
        const input_view = try self.getViewOrError(input_view_id);
        const shape = self.getShape(input_view.shape_id);
        
        if (axis > shape.dims.len) return error.InvalidAxes;
        
        // Create new shape with expanded dimension
        const new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, shape.dims.len + 1);
        @memcpy(new_dims[0..axis], shape.dims[0..axis]);
        new_dims[axis] = size_expr;
        if (axis < shape.dims.len) {
            @memcpy(new_dims[axis + 1 ..], shape.dims[axis..]);
        }
        
        // Create new strides with 0 for the new dimension
        const new_strides = try self.core.arena.allocator().alloc(i64, shape.dims.len + 1);
        @memcpy(new_strides[0..axis], input_view.strides[0..axis]);
        new_strides[axis] = 0; // Broadcasting dimension gets stride 0
        if (axis < shape.dims.len) {
            @memcpy(new_strides[axis + 1 ..], input_view.strides[axis..]);
        }
        
        const new_shape_id = try self.newShape(new_dims);
        const view = parent_types.ViewDesc{
            .shape_id = new_shape_id,
            .strides = new_strides,
            .offset_elements = input_view.offset_elements,
            .validity_expr = input_view.validity_expr, // Preserve validity from input
            .mask_expr = input_view.mask_expr,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Create a squeezed view by removing singleton dimensions
    pub fn newSqueezeView(self: *Self, input_view_id: parent_types.ViewId, axes: ?[]const u32) !parent_types.ViewId {
        const input_view = try self.getViewOrError(input_view_id);
        const shape = self.getShape(input_view.shape_id);
        
        // If no axes specified or empty, squeeze all singleton dimensions
        var squeeze_axes_buf: [64]u32 = undefined;
        const squeeze_axes = if (axes) |a| blk: {
            if (a.len == 0) {
                // Empty slice means squeeze all singleton dimensions  
                break :blk null;
            }
            break :blk a;
        } else null;
        
        const actual_squeeze_axes = if (squeeze_axes) |a| a else blk: {
            var count: usize = 0;
            for (0..shape.dims.len) |i| {
                const dim = shape.dims[i];
                // Check if dimension is 1 (singleton) using expression evaluation
                const is_singleton = if (dim.tag == .integer) 
                    dim.data.integer == 1 
                else 
                    false; // Conservative: assume symbolic dims are not singletons
                if (is_singleton) {
                    squeeze_axes_buf[count] = @intCast(i);
                    count += 1;
                }
            }
            break :blk squeeze_axes_buf[0..count];
        };
        
        // Validate axes
        for (actual_squeeze_axes) |axis| {
            if (axis >= shape.dims.len) return error.InvalidAxes;
            const dim = shape.dims[axis];
            // Check if dimension is 1 (singleton) using expression evaluation
            const is_singleton = if (dim.tag == .integer) 
                dim.data.integer == 1 
            else 
                false; // Conservative: assume symbolic dims are not singletons
            if (!is_singleton) return error.InvalidAxes;
        }
        
        // Create new shape without squeezed dimensions
        const new_size = shape.dims.len - actual_squeeze_axes.len;
        const new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, new_size);
        const new_strides = try self.core.arena.allocator().alloc(i64, new_size);
        
        var new_idx: usize = 0;
        for (0..shape.dims.len) |i| {
            const dim = shape.dims[i];
            var should_skip = false;
            for (actual_squeeze_axes) |axis| {
                if (i == axis) {
                    should_skip = true;
                    break;
                }
            }
            
            if (!should_skip) {
                new_dims[new_idx] = dim;
                new_strides[new_idx] = input_view.strides[i];
                new_idx += 1;
            }
        }
        
        const new_shape_id = try self.newShape(new_dims);
        const view = parent_types.ViewDesc{
            .shape_id = new_shape_id,
            .strides = new_strides,
            .offset_elements = input_view.offset_elements,
            .validity_expr = input_view.validity_expr, // Preserve validity from input
            .mask_expr = input_view.mask_expr,
        };
        
        const raw_id = try self.views.add(view);
        self.stats.views_created += 1;
        return @as(parent_types.ViewId, @enumFromInt(raw_id));
    }
    
    /// Create a concatenated view along a specific axis using padding + addition
    pub fn newConcatView(self: *Self, view_ids: []const parent_types.ViewId, axis: u32) !parent_types.ViewId {
        if (view_ids.len == 0) return error.InvalidArgumentCount;
        if (view_ids.len == 1) return view_ids[0]; // Single input, return as-is
        
        // Get shapes for all inputs
        const first_view = try self.getViewOrError(view_ids[0]);
        const first_shape = self.getShape(first_view.shape_id);
        
        if (axis >= first_shape.dims.len) return error.InvalidAxes;
        
        // Track cumulative offset for each view
        var cumulative_offsets = try self.core.arena.allocator().alloc(*parent_types.Expr, view_ids.len);
        cumulative_offsets[0] = try self.core.symbolic.newIntegerExpr(0);
        
        // Calculate total size and offsets
        var total_concat_size = first_shape.dims[axis];
        
        // Validate shapes and accumulate sizes
        for (view_ids[1..], 1..) |view_id, i| {
            const view = try self.getViewOrError(view_id);
            const shape = self.getShape(view.shape_id);
            
            if (shape.dims.len != first_shape.dims.len) {
                return error.IncompatibleShapes;
            }
            
            // Check all non-concat dimensions match
            for (0..shape.dims.len) |j| {
                if (j != axis and !self.dimsEqual(shape.dims[j], first_shape.dims[j])) {
                    return error.IncompatibleShapes;
                }
            }
            
            // Calculate offset for this view
            cumulative_offsets[i] = total_concat_size;
            total_concat_size = try self.core.symbolic.newBinaryExpr(.add, total_concat_size, shape.dims[axis]);
        }
        
        // Create output shape
        const output_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, first_shape.dims.len);
        @memcpy(output_dims, first_shape.dims);
        output_dims[axis] = total_concat_size;
        const output_shape_id = try self.newShape(output_dims);
        
        // Create padded views for each input
        var padded_views = try self.core.arena.allocator().alloc(parent_types.ViewId, view_ids.len);
        
        for (view_ids, 0..) |view_id, i| {
            const view = self.getView(view_id);
            const shape = self.getShape(view.shape_id);
            
            // Calculate padding for this view
            var padding = try self.core.arena.allocator().alloc([2]*parent_types.Expr, shape.dims.len);
            
            // Initialize all padding to zero
            const zero_expr = try self.core.symbolic.newIntegerExpr(0);
            for (padding) |*pad| {
                pad[0] = zero_expr;
                pad[1] = zero_expr;
            }
            
            // Set padding for concat axis
            // Left padding = cumulative_offsets[i]
            padding[axis][0] = cumulative_offsets[i];
            
            // Right padding = total_size - (offset + current_size)
            const current_size = shape.dims[axis];
            const current_end = try self.core.symbolic.newBinaryExpr(.add, cumulative_offsets[i], current_size);
            padding[axis][1] = try self.core.symbolic.newBinaryExpr(.subtract, total_concat_size, current_end);
            
            // Create padded view
            padded_views[i] = try self.newPaddedView(view_id, padding);
        }
        
        // The padded views would be summed by the graph engine
        // For shape tracking, we return the output shape with proper strides
        // Note: The graph engine combines padded views, so concat result has no validity constraints
        const view = parent_types.ViewDesc{
            .shape_id = output_shape_id,
            .strides = try self.calculateStrides(output_shape_id),
            .offset_elements = 0,
            .validity_expr = null, // Concat result is fully valid after summing
            .mask_expr = null,
        };
        
        const result_id = try self.views.add(view);
        self.stats.views_created += 1;
        
        // In a complete implementation, we would store the padded_views
        // and axis metadata for the graph engine to use
        
        return @as(parent_types.ViewId, @enumFromInt(result_id));
    }
    
    /// Calculate default strides for a shape (row-major layout)
    fn calculateStrides(self: *Self, shape_id: parent_types.ShapeId) ![]const i64 {
        const shape = self.getShape(shape_id);
        const strides = try self.core.arena.allocator().alloc(i64, shape.dims.len);
        
        // For symbolic dimensions, we'll use concrete strides but generate symbolic expressions
        // to handle runtime stride calculation. Start with stride 1 for the rightmost dimension.
        var stride: i64 = 1;
        var i = shape.dims.len;
        while (i > 0) {
            i -= 1;
            strides[i] = stride;
            
            // Check if this dimension is symbolic (not a concrete integer)
            if (shape.dims[i].tag != .integer) {
                // For symbolic dimensions, we still set a concrete stride value
                // but will generate index expressions to handle the symbolic calculation
                // Use stride of 1 as a placeholder that will be handled by expressions
                if (i > 0) {
                    stride = 1;  // Reset stride accumulation for symbolic dims
                }
            } else {
                // For concrete dimensions, calculate the actual stride
                const dim_size = self.core.symbolic.evaluate(shape.dims[i], null) catch {
                    // This shouldn't happen for concrete dims, but handle gracefully
                    stride = 1;
                    continue;
                };
                if (i > 0) {  // Don't multiply stride for the last iteration
                    stride *= dim_size;
                }
            }
        }
        
        return strides;
    }
    
    /// Check if two expressions are equal
    pub fn exprsEqual(self: *const Self, a: *parent_types.Expr, b: *parent_types.Expr) bool {
        return self.core.symbolic.exprEquals(a, b);
    }
    
    // Shape operations
    pub fn inferBroadcastShape(self: *Self, shape_id_a: parent_types.ShapeId, shape_id_b: parent_types.ShapeId) !parent_types.ShapeId {
        const shape_a = self.getShape(shape_id_a);
        const shape_b = self.getShape(shape_id_b);
        
        const rank = @max(shape_a.dims.len, shape_b.dims.len);
        const result_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, rank);
        
        const one_expr = try self.core.symbolic.newIntegerExpr(1);
        
        var i: usize = 0;
        while (i < rank) : (i += 1) {
            const expr_a = if (i < shape_a.dims.len) 
                shape_a.dims[shape_a.dims.len - 1 - i] 
            else 
                one_expr;
                
            const expr_b = if (i < shape_b.dims.len) 
                shape_b.dims[shape_b.dims.len - 1 - i] 
            else 
                one_expr;
            
            result_dims[rank - 1 - i] = try self.broadcastExpressions(expr_a, expr_b);
        }
        
        return self.newShape(result_dims);
    }
    
    /// Infer reshape dimensions, handling -1 for automatic dimension calculation
    pub fn inferReshapeShape(self: *Self, current_shape: parent_types.ShapeId, new_dims: []const *parent_types.Expr) !parent_types.ShapeId {
        const shape = self.getShape(current_shape);
        
        // Count knowns and find -1 position
        var total_known: usize = 1;
        var unknown_idx: ?usize = null;
        var has_symbolic = false;
        
        for (new_dims, 0..) |dim, i| {
            if (dim.tag == .integer) {
                const int_val = dim.data.integer;
                if (int_val == -1) { // Using -1 as auto-infer indicator
                    if (unknown_idx != null) return error.InvalidShape;
                    unknown_idx = i;
                } else if (int_val > 0) {
                    const val = @as(usize, @intCast(int_val));
                    total_known *= val;
                } else {
                    return error.InvalidDimension; // Negative dimensions other than -1 are invalid
                }
            } else {
                has_symbolic = true;
            }
        }
        
        // If there are symbolic dimensions, we can't infer
        if (has_symbolic and unknown_idx != null) {
            return error.InvalidExpression;
        }
        
        // If no unknown dimension, just create the shape
        if (unknown_idx == null) {
            return self.newShape(new_dims);
        }
        
        // Calculate total elements in current shape
        var total_current: usize = 1;
        for (shape.dims) |dim| {
            if (dim.tag == .integer) {
                const val = @as(usize, @intCast(dim.data.integer));
                total_current *= val;
            } else {
                return error.InvalidExpression;
            }
        }
        
        // Infer the unknown dimension
        if (total_current % total_known != 0) {
            return error.ElementCountMismatch;
        }
        
        const inferred_value = total_current / total_known;
        var dims_copy = try self.core.arena.allocator().dupe(*parent_types.Expr, new_dims);
        dims_copy[unknown_idx.?] = try self.core.symbolic.newIntegerExpr(@intCast(inferred_value));
        
        return self.newShape(dims_copy);
    }
    
    fn broadcastDim(self: *Self, dim_a: *parent_types.Expr, dim_b: *parent_types.Expr) !*parent_types.Expr {
        // If expressions are pointer-equal, return one of them
        if (dim_a == dim_b) return dim_a;
        
        // Check if expressions are equal using symbolic engine
        if (self.core.symbolic.exprEquals(dim_a, dim_b)) return dim_a;
        
        const a_is_one = if (dim_a.tag == .integer) dim_a.data.integer == 1 else false;
        const b_is_one = if (dim_b.tag == .integer) dim_b.data.integer == 1 else false;
        
        if (a_is_one) return dim_b;
        if (b_is_one) return dim_a;
        
        // Both are concrete but different and neither is 1
        if (dim_a.tag == .integer and dim_b.tag == .integer) {
            return error.IncompatibleShapes;
        }
        
        // For symbolic cases, return the max (assuming they're compatible)
        return self.core.symbolic.newBinaryExpr(.max, dim_a, dim_b);
    }
    
    pub fn canBroadcastShapes(self: *Self, shape_id_a: parent_types.ShapeId, shape_id_b: parent_types.ShapeId) bool {
        const shape_a = self.getShape(shape_id_a);
        const shape_b = self.getShape(shape_id_b);
        
        const min_rank = @min(shape_a.dims.len, shape_b.dims.len);
        
        var i: usize = 0;
        while (i < min_rank) : (i += 1) {
            const dim_a = shape_a.dims[shape_a.dims.len - 1 - i];
            const dim_b = shape_b.dims[shape_b.dims.len - 1 - i];
            
            // Check if dimensions are compatible for broadcasting
            const a_is_one = if (dim_a.tag == .integer) dim_a.data.integer == 1 else false;
            const b_is_one = if (dim_b.tag == .integer) dim_b.data.integer == 1 else false;
            
            // Broadcasting rules: dimensions can be different if one is 1, or they can be equal
            if (a_is_one or b_is_one) {
                // One dimension is 1, broadcasting is allowed
                continue;
            }
            
            // Neither is 1, so they must be equal for broadcasting to work
            if (!self.dimsEqual(dim_a, dim_b)) {
                // Check if they're both symbolic or both concrete
                const both_symbolic = dim_a.tag != .integer and dim_b.tag != .integer;
                const both_concrete = dim_a.tag == .integer and dim_b.tag == .integer;
                
                // If they're the same type and not equal, broadcasting fails
                if (both_symbolic or both_concrete) {
                    return false;
                }
                // If one is symbolic and one is concrete, we can't determine 
                // compatibility at compile time, so we assume it's ok
            }
        }
        
        return true;
    }
    
    pub fn getNumElements(self: *const Self, shape_id: parent_types.ShapeId) !usize {
        const shape = self.getShape(shape_id);
        var count: usize = 1;
        
        for (shape.dims) |dim| {
            if (dim.tag == .integer) {
                const v = @as(usize, @intCast(dim.data.integer));
                count *= v;
            } else {
                return error.SymbolicSize;
            }
        }
        
        return count;
    }
    
    /// Check if two adjacent dimensions can be collapsed
    pub fn canCollapseDims(self: *Self, view_id: parent_types.ViewId, dim1: usize, dim2: usize) bool {
        const view = self.getView(view_id);
        const shape = self.getShape(view.shape_id);
        
        // Basic bounds check
        if (dim1 >= shape.dims.len or dim2 >= shape.dims.len or dim2 != dim1 + 1) {
            return false;
        }
        
        // Check if strides are compatible for collapsing
        const stride1 = view.strides[dim1];
        const stride2 = view.strides[dim2];
        const dim2_size = self.core.symbolic.evaluate(shape.dims[dim2], null) catch {
            return false; // Cannot collapse symbolic dims
        };
        
        // Dimensions are collapsible if stride1 == stride2 * dim2_size
        if (stride1 != stride2 * dim2_size) {
            return false;
        }
        
        // Check that neither dimension is fake
        if (view.fake_dims.len > dim2) {
            // Only check if fake_dims is long enough
            if (view.fake_dims[dim1] or view.fake_dims[dim2]) {
                return false;
            }
        }
        
        return true;
    }
    
    /// Collapse adjacent dimensions for optimization
    /// Returns the original view_id if dimensions cannot be collapsed
    pub fn collapseDimensions(self: *Self, view_id: parent_types.ViewId, dim1: usize, dim2: usize) !parent_types.ViewId {
        // Validate input
        if (dim2 != dim1 + 1) return error.NonAdjacentDimensions;
        
        const view = try self.getViewOrError(view_id);
        const shape = try self.getShapeOrError(view.shape_id);
        
        // Check if dimensions can be collapsed
        if (!self.canCollapseDims(view_id, dim1, dim2)) {
            return view_id; // Cannot collapse, return original
        }
        
        // Get sizes with expression evaluation
        const dim1_size = self.core.symbolic.evaluate(shape.dims[dim1], null) catch return error.SymbolicDimension;
        const dim2_size = self.core.symbolic.evaluate(shape.dims[dim2], null) catch return error.SymbolicDimension;
        
        // Calculate new dimensions (one fewer)
        var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, shape.dims.len - 1);
        
        // Copy dimensions before dim1
        @memcpy(new_dims[0..dim1], shape.dims[0..dim1]);
        
        // Collapsed dimension is product of original dimensions
        const collapsed_size = try self.core.symbolic.newIntegerExpr(@intCast(dim1_size * dim2_size));
        new_dims[dim1] = collapsed_size;
        
        // Copy dimensions after dim2
        if (dim2 + 1 < shape.dims.len) {
            @memcpy(new_dims[dim1+1..], shape.dims[dim2+1..]);
        }
        
        // Create new shape
        const new_shape_id = try self.newShape(new_dims);
        
        // Calculate new strides (one fewer)
        var new_strides = try self.core.arena.allocator().alloc(i64, new_dims.len);
        
        // Copy strides before dim1
        @memcpy(new_strides[0..dim1], view.strides[0..dim1]);
        
        // Stride for collapsed dimension is stride of the second dimension
        new_strides[dim1] = view.strides[dim2];
        
        // Copy strides after dim2
        if (dim2 + 1 < view.strides.len) {
            @memcpy(new_strides[dim1+1..], view.strides[dim2+1..]);
        }
        
        // Create new fake dimensions array (one fewer)
        var new_fake_dims = try self.core.arena.allocator().alloc(bool, new_dims.len);
        
        // Handle case where fake_dims might be empty or shorter than expected
        if (view.fake_dims.len > 0) {
            // Copy fake flags before dim1
            if (dim1 > 0 and dim1 <= view.fake_dims.len) {
                const copy_len = @min(dim1, view.fake_dims.len);
                @memcpy(new_fake_dims[0..copy_len], view.fake_dims[0..copy_len]);
            }
            
            // Combined dimension inherits fake status (logical OR of both dimensions)
            // We already checked in canCollapseDims that neither dimension is fake
            new_fake_dims[dim1] = false;
            
            // Copy fake flags after dim2
            if (dim2 + 1 < view.fake_dims.len and dim1 + 1 < new_fake_dims.len) {
                const src_start = dim2 + 1;
                const dst_start = dim1 + 1;
                const copy_len = @min(view.fake_dims.len - src_start, new_fake_dims.len - dst_start);
                @memcpy(new_fake_dims[dst_start..dst_start + copy_len], 
                       view.fake_dims[src_start..src_start + copy_len]);
            }
        } else {
            // If no fake_dims info, assume all dims are real
            for (new_fake_dims) |*fake| {
                fake.* = false;
            }
        }
        
        // Re-generate expressions for the new shape
        const validity_expr = try self.generateValidityExpression(
            new_shape_id, new_strides, new_fake_dims
        );
        
        const index_expr = try self.generateIndexExpression(
            new_shape_id, new_strides, view.offset_elements
        );
        
        // Create new view with updated values
        return self.createViewWithFakeDims(
            new_shape_id,
            new_strides,
            view.offset_elements,
            validity_expr,
            index_expr,
            new_fake_dims
        );
    }
    
    /// Optimize a view by collapsing adjacent dimensions where possible
    /// Returns the optimized view ID, which may be the same as the input if no optimizations were possible
    pub fn optimizeView(self: *Self, view_id: parent_types.ViewId) !parent_types.ViewId {
        var current_view_id = view_id;
        var pass: usize = 0;
        const max_passes = 10; // Safety limit to prevent infinite loops
        
        // Multiple passes may be needed as each collapse changes dimension indices
        while (pass < max_passes) : (pass += 1) {
            var changed = false;
            
            const view = try self.getViewOrError(current_view_id);
            const shape = try self.getShapeOrError(view.shape_id);
            
            // Nothing to collapse with fewer than 2 dimensions
            if (shape.dims.len < 2) break;
            
            // Try collapsing adjacent dimensions
            var i: usize = 0;
            while (i < shape.dims.len - 1) : (i += 1) {
                if (self.canCollapseDims(current_view_id, i, i + 1)) {
                    // Attempt to collapse these dimensions
                    const new_view_id = try self.collapseDimensions(current_view_id, i, i + 1);
                    
                    // Check if view changed
                    if (new_view_id != current_view_id) {
                        current_view_id = new_view_id;
                        changed = true;
                        break; // Restart search as indices have changed
                    }
                }
            }
            
            // Exit if no more changes possible
            if (!changed) break;
        }
        
        return current_view_id;
    }
    
    pub fn validateDims(self: *const Self, dims: []const *parent_types.Expr) !void {
        _ = self;
        for (dims) |dim| {
            if (dim.tag == .integer) {
                if (dim.data.integer <= 0) return error.InvalidDimension;
            }
            // Symbolic dims are always valid
        }
    }
    
    /// Create a symbolic dimension
    pub fn newSymbolicDim(self: *Self, name: []const u8) !*parent_types.Expr {
        return self.core.symbolic.newSymbolExpr(name);
    }
    
    // Simple shape inference utilities (replacing constraint solver)
    
    /// Infer unknown dimension in reshape (like -1 in PyTorch/NumPy)
    pub fn inferReshapeDimension(
        self: *Self,
        total_elements: usize,
        known_dims: []const usize,
        unknown_index: usize,
    ) !usize {
        _ = self; // Self not needed for this simple calculation
        _ = unknown_index; // Index not needed for this calc
        
        var known_product: usize = 1;
        for (known_dims) |dim| {
            known_product = @mulWithOverflow(known_product, dim)[0];
        }
        
        if (known_product == 0) return error.InvalidDimension;
        if (total_elements % known_product != 0) {
            return error.InvalidReshape; // Not evenly divisible
        }
        
        return total_elements / known_product;
    }
    
    /// Check if two dimensions can broadcast together (NumPy rules)
    pub fn canBroadcast(dim1: usize, dim2: usize) bool {
        // Broadcasting rules: dims are compatible if:
        // 1. They are equal, OR
        // 2. One of them is 1
        return dim1 == dim2 or dim1 == 1 or dim2 == 1;
    }
    
    /// Check if two shapes can broadcast together
    pub fn canBroadcastShapeArrays(shape1: []const usize, shape2: []const usize) bool {
        // Work from right to left (trailing dimensions)
        const len1 = shape1.len;
        const len2 = shape2.len;
        const min_len = @min(len1, len2);
        
        var i: usize = 0;
        while (i < min_len) : (i += 1) {
            const dim1 = shape1[len1 - 1 - i];
            const dim2 = shape2[len2 - 1 - i];
            if (!canBroadcast(dim1, dim2)) {
                return false;
            }
        }
        
        // If shapes have different ranks, the missing dims are treated as 1
        return true;
    }
    
    /// Compute convolution output size (standard formula)
    pub fn convOutputSize(
        input_size: usize,
        kernel_size: usize,
        stride: usize,
        padding: usize,
    ) usize {
        // Formula: ((input + 2*padding - kernel) / stride) + 1
        const padded = input_size + 2 * padding;
        if (padded < kernel_size) return 0;
        return ((padded - kernel_size) / stride) + 1;
    }
    
    /// Compute the broadcasted shape from two shapes
    pub fn broadcastedShape(
        self: *Self,
        shape1: []const usize,
        shape2: []const usize,
    ) ![]usize {
        const len1 = shape1.len;
        const len2 = shape2.len;
        const max_len = @max(len1, len2);
        
        const result = try self.core.arena.allocator().alloc(usize, max_len);
        
        var i: usize = 0;
        while (i < max_len) : (i += 1) {
            const dim1 = if (i < len1) shape1[len1 - 1 - i] else 1;
            const dim2 = if (i < len2) shape2[len2 - 1 - i] else 1;
            
            if (dim1 == dim2) {
                result[max_len - 1 - i] = dim1;
            } else if (dim1 == 1) {
                result[max_len - 1 - i] = dim2;
            } else if (dim2 == 1) {
                result[max_len - 1 - i] = dim1;
            } else {
                return error.IncompatibleShapes;
            }
        }
        
        return result;
    }
    
    /// Generate an expression for validity checking
    pub fn generateValidityExpression(self: *Self, shape_id: parent_types.ShapeId, strides: []const i64, fake_dims: []const bool) !?*parent_types.Expr {
        const shape = try self.getShapeOrError(shape_id);
        
        // Start with true (1)
        var result = try self.core.symbolic.newIntegerExpr(1);
        
        // For each dimension, add bounds check: 0 <= idx_i < dim_size_i
        for (0..shape.dims.len) |i| {
            // Create index variable for this dimension
            const idx_var = try self.core.symbolic.newSymbolExpr(try std.fmt.allocPrint(
                self.core.arena.allocator(), "i{d}", .{i}
            ));
            
            // Lower bound: idx_i >= 0
            const zero = try self.core.symbolic.newIntegerExpr(0);
            const lower_bound = try self.core.symbolic.newBinaryExpr(.greater_equal, idx_var, zero);
            
            // Upper bound: idx_i < dim_size_i
            const dim_size = shape.dims[i]; // Expression is already in the right form
            
            const upper_bound = try self.core.symbolic.newBinaryExpr(.less_than, idx_var, dim_size);
            
            // Combine bounds: (idx_i >= 0) && (idx_i < dim_size_i)
            // Using .min as a logical AND (both must be true)
            const valid_dim = try self.core.symbolic.newBinaryExpr(.min, lower_bound, upper_bound);
            
            // Check if this is a fake dimension (broadcasting)
            if (i < fake_dims.len and fake_dims[i]) {
                // Skip dimension in validity check
                continue;
            } else if (i < strides.len and strides[i] == 0) {
                // Also skip if stride is 0 (for backward compatibility)
                continue;
            }
            
            // Add to overall validity condition
            result = try self.core.symbolic.newBinaryExpr(.min, result, valid_dim);
        }
        
        return result;
    }
    
    /// Generate an expression for validity checking from view_id
    pub fn generateValidityExpressionFromView(self: *Self, view_id: parent_types.ViewId) !?*parent_types.Expr {
        const view = try self.getViewOrError(view_id);
        return self.generateValidityExpression(view.shape_id, view.strides, view.fake_dims);
    }

    /// Generate an expression for logical-to-physical index mapping
    pub fn generateIndexExpression(self: *Self, shape_id: parent_types.ShapeId, strides: []const i64, offset: usize) !?*parent_types.Expr {
        const shape = try self.getShapeOrError(shape_id);
        
        // Start with offset
        var result = try self.core.symbolic.newIntegerExpr(@intCast(offset));
        
        // For each dimension: result += idx_i * stride_i
        for (0..shape.dims.len) |i| {
            // Skip dimensions with zero stride (broadcast dimensions)
            if (i < strides.len and strides[i] == 0) continue;
            
            // Create index variable for this dimension
            const idx_var = try self.core.symbolic.newSymbolExpr(try std.fmt.allocPrint(
                self.core.arena.allocator(), "i{d}", .{i}
            ));
            
            // Create stride term using safe casting
            if (i < strides.len) {
                const stride_abs = @abs(strides[i]);
                const stride_sign: i64 = if (strides[i] < 0) -1 else 1;
                const stride_term = try self.core.symbolic.newIntegerExpr(@as(i64, @intCast(stride_abs)) * stride_sign);
                
                // Multiply index by stride
                const term = try self.core.symbolic.newBinaryExpr(.multiply, idx_var, stride_term);
                
                // Add to result
                result = try self.core.symbolic.newBinaryExpr(.add, result, term);
            }
        }
        
        return result;
    }
    
    /// Generate an expression for logical-to-physical index mapping from view_id
    pub fn generateIndexExpressionFromView(self: *Self, view_id: parent_types.ViewId) !?*parent_types.Expr {
        const view = try self.getViewOrError(view_id);
        return self.generateIndexExpression(view.shape_id, view.strides, view.offset_elements);
    }
    
    /// Evaluate an expression with specific indices
    pub fn evaluateExpressionWithIndex(
        self: *Self,
        expr: *parent_types.Expr,
        indices: []const usize
    ) !i64 {
        // Create string map for variable bindings
        var bindings = std.StringHashMap(i64).init(self.core.arena.allocator());
        defer bindings.deinit();
        
        // Bind each index to a variable "i{n}"
        for (indices, 0..) |idx, i| {
            const name = try std.fmt.allocPrint(
                self.core.arena.allocator(), "i{d}", .{i}
            );
            try bindings.put(name, @intCast(idx));
        }
        
        // Evaluate expression
        return self.core.symbolic.evaluate(expr, bindings);
    }
    
    /// Broadcast two expressions according to broadcasting rules
    fn broadcastExpressions(self: *Self, a_expr: *parent_types.Expr, b_expr: *parent_types.Expr) !*parent_types.Expr {
        // If both expressions are the same, return the original
        if (a_expr == b_expr) {
            return a_expr;
        }
        
        // Check if one is explicitly 1 and the other is symbolic
        const a_val = self.core.symbolic.evaluate(a_expr, null) catch null;
        const b_val = self.core.symbolic.evaluate(b_expr, null) catch null;
        
        if (a_val != null and a_val.? == 1) {
            return b_expr; // broadcast 1 to b's dimension
        }
        if (b_val != null and b_val.? == 1) {
            return a_expr; // broadcast 1 to a's dimension
        }
        
        // For other cases with symbolic expressions, use max() which implements broadcasting semantics
        // max(a, b) gives the larger dimension, which is the broadcast result
        return try self.core.symbolic.newBinaryExpr(.max, a_expr, b_expr);
    }
    
    /// Create an excised view - cuts out 'size' elements every 'spacing' elements
    pub fn newExciseView(self: *Self, input_view_id: parent_types.ViewId, axis: usize, size: usize, spacing: usize) !parent_types.ViewId {
        return view_operations.newExciseView(self, input_view_id, axis, size, spacing);
    }
    
    /// Create a pooled view on the last dimension
    pub fn newPoolLastDimView(self: *Self, input_view_id: parent_types.ViewId, kernel: usize, stride: usize, dilation: usize) !parent_types.ViewId {
        return view_operations.newPoolLastDimView(self, input_view_id, kernel, stride, dilation);
    }
    
    /// Infer shape for pool operation
    pub fn inferPoolShape(self: *Self, input_shape_id: parent_types.ShapeId, kernel: usize, stride: usize, dilation: usize, axis: usize) !parent_types.ShapeId {
        return inference.inferPoolShape(self, input_shape_id, kernel, stride, dilation, axis);
    }
    
    /// Infer shape for gather operation
    pub fn inferGatherShape(self: *Self, input_shape_id: parent_types.ShapeId, indices_shape_id: parent_types.ShapeId, axis: usize) !parent_types.ShapeId {
        return inference.inferGatherShape(self, input_shape_id, indices_shape_id, @intCast(axis));
    }
    
    /// Infer shape for scatter operation  
    pub fn inferScatterShape(self: *Self, input_shape_id: parent_types.ShapeId, indices_shape_id: parent_types.ShapeId, updates_shape_id: parent_types.ShapeId, axis: usize) !parent_types.ShapeId {
        return inference.inferScatterShape(self, input_shape_id, indices_shape_id, updates_shape_id, @intCast(axis));
    }
    
};