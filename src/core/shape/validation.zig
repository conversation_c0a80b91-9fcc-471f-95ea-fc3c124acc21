const std = @import("std");
const parent_types = @import("../types.zig");

/// Shape and view validation operations
/// This module handles validation of shapes, views, and their operations

pub fn isContiguous(self: anytype, view_id: parent_types.ViewId) bool {
    const view = self.getView(view_id);
    const shape = self.getShape(view.shape_id);
    
    // Check if strides match C-contiguous layout
    var expected_stride: i64 = 1;
    var i = shape.dims.len;
    while (i > 0) : (i -= 1) {
        const idx = i - 1;
        if (view.strides[idx] != expected_stride) {
            return false;
        }
        
        // Calculate expected stride for next dimension
        const dim_value = self.core.symbolic.evaluate(shape.dims[idx], null) catch return false;
        expected_stride *= dim_value;
    }
    
    return true;
}

pub fn canCollapseDims(
    self: anytype,
    view_id: parent_types.ViewId,
    dim1: usize,
    dim2: usize,
) bool {
    const view = self.getView(view_id);
    const shape = self.getShape(view.shape_id);
    
    // Ensure dims are valid and adjacent
    if (dim1 >= shape.dims.len or dim2 >= shape.dims.len) {
        return false;
    }
    
    if (@max(dim1, dim2) - @min(dim1, dim2) != 1) {
        return false; // Not adjacent
    }
    
    // Check if strides are compatible for collapsing
    const lower_dim = @min(dim1, dim2);
    const upper_dim = @max(dim1, dim2);
    
    const stride_lower = view.strides[lower_dim];
    const stride_upper = view.strides[upper_dim];
    
    // Get dimension size
    const dim_upper_size = self.core.symbolic.evaluate(shape.dims[upper_dim], null) catch {
        return false;
    };
    
    // For dimensions to be collapsible, stride[lower] should equal stride[upper] * size[upper]
    if (stride_lower != stride_upper * dim_upper_size) {
        return false;
    }
    
    return true;
}

pub fn validateDims(self: anytype, dims: []const *parent_types.Expr) !void {
    if (dims.len == 0) {
        return; // Scalar is valid
    }
    
    for (dims) |dim| {
        // Try to evaluate dimension
        const value = self.core.symbolic.evaluate(dim, null) catch {
            // If we can't evaluate, it might be symbolic - that's ok
            continue;
        };
        
        if (value <= 0) {
            return error.InvalidDimension;
        }
    }
}

pub fn validateViewConsistency(self: anytype, view_id: parent_types.ViewId) !void {
    const view = self.getView(view_id);
    const shape = self.getShape(view.shape_id);
    
    // Check that strides and shape have same length
    if (view.strides.len != shape.dims.len) {
        return error.InvalidShape;
    }
    
    // Check fake_dims length if present
    if (view.fake_dims.len > 0 and view.fake_dims.len != shape.dims.len) {
        return error.InvalidShape;
    }
    
    // Validate offset is reasonable (this is a soft check)
    if (view.offset_elements > 1_000_000_000) { // 1 billion elements
        return error.InvalidShape;
    }
}

pub fn validateBroadcastCompatibility(
    self: anytype,
    shape_id_a: parent_types.ShapeId,
    shape_id_b: parent_types.ShapeId,
) !void {
    if (!self.canBroadcastShapes(shape_id_a, shape_id_b)) {
        return error.BroadcastError;
    }
}

pub fn validateReshapeCompatibility(
    self: anytype,
    current_shape_id: parent_types.ShapeId,
    new_dims: []const *parent_types.Expr,
) !void {
    const current_shape = self.getShape(current_shape_id);
    
    // Calculate total elements in current shape
    var current_total = try self.createConcreteExpr(1);
    for (current_shape.dims) |dim| {
        current_total = try self.exprBinaryOp(.mul, current_total, dim);
    }
    
    // Calculate total elements in new shape (excluding -1)
    var new_total = try self.createConcreteExpr(1);
    var negative_count: usize = 0;
    
    for (new_dims) |dim| {
        const val = self.core.symbolic.evaluate(dim, null) catch {
            // Symbolic dimension, include it
            new_total = try self.exprBinaryOp(.mul, new_total, dim);
            continue;
        };
        
        if (val == -1) {
            negative_count += 1;
            if (negative_count > 1) {
                return error.InvalidShape; // Multiple -1 dimensions
            }
        } else if (val <= 0) {
            return error.InvalidDimension;
        } else {
            new_total = try self.exprBinaryOp(.mul, new_total, dim);
        }
    }
    
    // If there's a -1, the known product must divide evenly into total
    if (negative_count == 1) {
        const current_count = self.core.symbolic.evaluate(current_total, null) catch 
            return; // Can't validate symbolic shapes
        const new_count = self.core.symbolic.evaluate(new_total, null) catch 
            return; // Can't validate symbolic shapes
            
        if (current_count % new_count != 0) {
            return error.ElementCountMismatch;
        }
    } else {
        // No -1, totals must match exactly
        if (!self.exprsEqual(current_total, new_total)) {
            const current_count = self.core.symbolic.evaluate(current_total, null) catch 
                return; // Can't validate symbolic shapes
            const new_count = self.core.symbolic.evaluate(new_total, null) catch 
                return; // Can't validate symbolic shapes
                
            if (current_count != new_count) {
                return error.ElementCountMismatch;
            }
        }
    }
}

pub fn validateSliceRanges(
    self: anytype,
    shape_id: parent_types.ShapeId,
    ranges: []const parent_types.SliceRange,
) !void {
    const shape = self.getShape(shape_id);
    
    if (ranges.len != shape.dims.len) {
        return error.InvalidSliceParameters;
    }
    
    for (ranges, 0..) |range, i| {
        if (range.step == 0) {
            return error.ZeroSliceStep;
        }
        
        // Try to validate against dimension size
        const dim_size = self.core.symbolic.evaluate(shape.dims[i], null) catch {
            // Can't validate symbolic dimension
            continue;
        };
        
        // Normalize negative indices
        var start = range.start;
        var end = range.end;
        if (start < 0) start += @as(i64, @intCast(dim_size));
        if (end < 0) end += @as(i64, @intCast(dim_size));
        
        // Basic range validation
        if (start < 0 or start > @as(i64, @intCast(dim_size))) {
            return error.InvalidSliceParameters;
        }
        if (end < 0 or end > @as(i64, @intCast(dim_size))) {
            return error.InvalidSliceParameters;
        }
        
        // For positive step, start should be <= end
        // For negative step, start should be >= end
        if (range.step > 0 and start > end) {
            return error.InvalidSliceParameters;
        }
        if (range.step < 0 and start < end) {
            return error.InvalidSliceParameters;
        }
    }
}

pub fn validatePadding(
    self: anytype,
    shape_id: parent_types.ShapeId,
    padding: []const [2]*parent_types.Expr,
) !void {
    const shape = self.getShape(shape_id);
    
    if (padding.len != shape.dims.len) {
        return error.InvalidPaddingParameters;
    }
    
    // Validate each padding value is non-negative
    for (padding) |pad| {
        for (pad) |pad_expr| {
            const val = self.core.symbolic.evaluate(pad_expr, null) catch {
                // Can't validate symbolic padding
                continue;
            };
            
            if (val < 0) {
                return error.InvalidPaddingParameters;
            }
        }
    }
}

pub fn validateAxes(
    self: anytype,
    shape_id: parent_types.ShapeId,
    axes: []const u32,
) !void {
    const shape = self.getShape(shape_id);
    
    // Check for out of bounds axes
    for (axes) |axis| {
        if (axis >= shape.dims.len) {
            return error.InvalidAxes;
        }
    }
    
    // Check for duplicates
    if (axes.len > 1) {
        var seen = try self.core.persistent_allocator.alloc(bool, shape.dims.len);
        defer self.core.persistent_allocator.free(seen);
        @memset(seen, false);
        
        for (axes) |axis| {
            if (seen[axis]) {
                return error.InvalidAxes; // Duplicate axis
            }
            seen[axis] = true;
        }
    }
}

pub fn validateConcatCompatibility(
    self: anytype,
    view_ids: []const parent_types.ViewId,
    axis: u32,
) !void {
    if (view_ids.len == 0) {
        return error.InvalidInput;
    }
    
    const first_view = self.getView(view_ids[0]);
    const first_shape = self.getShape(first_view.shape_id);
    
    if (axis >= first_shape.dims.len) {
        return error.InvalidAxes;
    }
    
    // All views must have same rank and compatible shapes
    for (view_ids[1..]) |view_id| {
        const view = self.getView(view_id);
        const shape = self.getShape(view.shape_id);
        
        if (shape.dims.len != first_shape.dims.len) {
            return error.RankMismatch;
        }
        
        // Check all non-concat dimensions match
        for (shape.dims, 0..) |dim, i| {
            if (i != axis) {
                if (!self.exprsEqual(dim, first_shape.dims[i])) {
                    return error.ShapeIncompatible;
                }
            }
        }
    }
}

pub fn optimizeView(self: anytype, view_id: parent_types.ViewId) !parent_types.ViewId {
    const view = self.getView(view_id);
    const shape = self.getShape(view.shape_id);
    
    // Check if already contiguous
    if (self.isContiguous(view_id)) {
        return view_id; // No optimization needed
    }
    
    // Try to collapse adjacent dimensions
    var optimized = false;
    var new_shape_dims = std.ArrayList(*parent_types.Expr).init(self.core.persistent_allocator);
    defer new_shape_dims.deinit();
    var new_strides = std.ArrayList(i64).init(self.core.persistent_allocator);
    defer new_strides.deinit();
    
    var i: usize = 0;
    while (i < shape.dims.len) {
        if (i + 1 < shape.dims.len and self.canCollapseDims(view_id, i, i + 1)) {
            // Collapse these dimensions
            const collapsed_dim = try self.exprBinaryOp(.mul, shape.dims[i], shape.dims[i + 1]);
            try new_shape_dims.append(collapsed_dim);
            try new_strides.append(view.strides[i + 1]); // Use the inner stride
            i += 2; // Skip both dimensions
            optimized = true;
        } else {
            // Keep this dimension as is
            try new_shape_dims.append(shape.dims[i]);
            try new_strides.append(view.strides[i]);
            i += 1;
        }
    }
    
    if (!optimized) {
        return view_id; // No optimization performed
    }
    
    // Create optimized view
    const new_shape_id = try self.newShape(new_shape_dims.items);
    return self.createView(new_shape_id, new_strides.items, view.offset_elements);
}