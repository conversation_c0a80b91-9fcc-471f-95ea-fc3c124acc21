const std = @import("std");
const core_types = @import("../types.zig");

/// Utilities for working with symbolic dimensions in shapes
/// This is a simpler, more direct approach than a full bridge

/// Convert a dimension to a symbolic expression
pub fn dimToExpr(
    symbolic: anytype, // Type-erased to avoid circular dependency
    dim: core_types.Dim,
) !*core_types.Expr {
    return switch (dim) {
        .concrete => |value| try symbolic.newIntegerExpr(@intCast(value)),
        .symbolic => |expr| expr,
    };
}

/// Convert a symbolic expression to a dimension
pub fn exprToDim(expr: *core_types.Expr) core_types.Dim {
    // Try to evaluate as concrete first
    if (expr.tag == .integer) {
        const value = expr.data.integer;
        if (value >= 0) {
            return .{ .concrete = @intCast(value) };
        }
    }
    
    // Otherwise keep as symbolic
    return .{ .symbolic = expr };
}

/// Check if two dimensions are compatible for operations
pub fn dimsCompatible(a: core_types.Dim, b: core_types.Dim) bool {
    return switch (a) {
        .concrete => |a_val| switch (b) {
            .concrete => |b_val| a_val == b_val,
            .symbolic => false, // Can't guarantee compatibility
        },
        .symbolic => |a_expr| switch (b) {
            .concrete => false, // Can't guarantee compatibility
            .symbolic => |b_expr| a_expr == b_expr, // Pointer equality
        },
    };
}

/// Create dimension constraints for shape operations
pub fn createShapeConstraints(
    symbolic: anytype,
    shape_a: []const core_types.Dim,
    shape_b: []const core_types.Dim,
    comptime op: []const u8,
) ![]core_types.Constraint {
    var constraints = std.ArrayList(core_types.Constraint).init(symbolic.allocator);
    defer constraints.deinit();
    
    // Different operations have different constraint requirements
    if (std.mem.eql(u8, op, "broadcast")) {
        // For broadcasting, dimensions must be equal or one must be 1
        const min_len = @min(shape_a.len, shape_b.len);
        
        // Start from rightmost dimensions
        var i: usize = 0;
        while (i < min_len) : (i += 1) {
            const dim_a = shape_a[shape_a.len - 1 - i];
            const dim_b = shape_b[shape_b.len - 1 - i];
            
            // Create constraint: (a = b) or (a = 1) or (b = 1)
            const expr_a = try dimToExpr(symbolic, dim_a);
            const expr_b = try dimToExpr(symbolic, dim_b);
            const one = try symbolic.newIntegerExpr(1);
            
            // Create disjunctive constraint
            const constraint = core_types.Constraint{
                .tag = .disjunctive,
                .data = .{
                    .disjunctive = try symbolic.allocator.alloc(core_types.Constraint, 3),
                },
            };
            
            constraint.data.disjunctive[0] = .{
                .tag = .equality,
                .data = .{ .equality = .{ .lhs = expr_a, .rhs = expr_b } },
            };
            constraint.data.disjunctive[1] = .{
                .tag = .equality,
                .data = .{ .equality = .{ .lhs = expr_a, .rhs = one } },
            };
            constraint.data.disjunctive[2] = .{
                .tag = .equality,
                .data = .{ .equality = .{ .lhs = expr_b, .rhs = one } },
            };
            
            try constraints.append(constraint);
        }
    } else if (std.mem.eql(u8, op, "matmul")) {
        // For matrix multiplication: a[-1] = b[-2] (inner dimensions must match)
        if (shape_a.len >= 2 and shape_b.len >= 2) {
            const inner_a = shape_a[shape_a.len - 1];
            const inner_b = shape_b[shape_b.len - 2];
            
            const expr_a = try dimToExpr(symbolic, inner_a);
            const expr_b = try dimToExpr(symbolic, inner_b);
            
            try constraints.append(.{
                .tag = .equality,
                .data = .{ .equality = .{ .lhs = expr_a, .rhs = expr_b } },
            });
        }
    }
    
    return constraints.toOwnedSlice();
}

/// Validate that a symbolic operation is well-formed
pub fn validateSymbolicOperation(
    comptime op: core_types.BinaryOp,
    lhs: *core_types.Expr,
    rhs: *core_types.Expr,
) !void {
    // Validate based on operation type
    switch (op) {
        .divide => {
            // Check for division by zero for concrete values
            if (rhs.tag == .integer and rhs.data.integer == 0) {
                return error.DivisionByZero;
            }
        },
        .mod => {
            // Modulo requires integer operands
            if (lhs.tag != .integer and lhs.tag != .symbol) {
                return error.InvalidOperand;
            }
        },
        else => {},
    }
}

/// Perform binary operation on dimensions
pub fn dimBinaryOp(
    symbolic: anytype,
    op: core_types.BinaryOp,
    dim1: core_types.Dim,
    dim2: core_types.Dim,
) !core_types.Dim {
    // If both are concrete, perform concrete operation
    if (dim1 == .concrete and dim2 == .concrete) {
        const a = dim1.concrete;
        const b = dim2.concrete;
        
        const result = switch (op) {
            .add => a + b,
            .multiply => a * b,
            .divide => if (b != 0) a / b else return error.DivisionByZero,
            .subtract => if (a >= b) a - b else return error.NegativeResult,
            .max => @max(a, b),
            .min => @min(a, b),
            else => return error.UnsupportedOperation,
        };
        
        return .{ .concrete = result };
    }
    
    // Otherwise create symbolic expression
    const expr1 = try dimToExpr(symbolic, dim1);
    const expr2 = try dimToExpr(symbolic, dim2);
    
    const result_expr = try symbolic.newBinaryExpr(op, expr1, expr2);
    return .{ .symbolic = result_expr };
}