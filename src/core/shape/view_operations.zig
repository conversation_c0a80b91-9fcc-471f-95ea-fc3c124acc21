const std = @import("std");
const parent_types = @import("../types.zig");
const symbolic_utils = @import("symbolic_utils.zig");

/// View manipulation operations for ShapeEngine
/// This module handles all view transformations: reshape, permute, slice, pad, broadcast, expand, squeeze, concat

pub fn newReshapedView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    new_shape_id: parent_types.ShapeId,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);
    const new_shape = self.getShape(new_shape_id);

    // Check if the number of elements matches
    const input_elements = blk: {
        var num_elements = self.core.symbolic.newIntegerExpr(1) catch return error.OutOfMemory;
        for (input_shape.dims) |dim| {
            num_elements = try self.core.symbolic.newBinaryExpr(.multiply, num_elements, dim);
        }
        break :blk num_elements;
    };

    const new_elements = blk: {
        var num_elements = self.core.symbolic.newIntegerExpr(1) catch return error.OutOfMemory;
        for (new_shape.dims) |dim| {
            num_elements = try self.core.symbolic.newBinaryExpr(.multiply, num_elements, dim);
        }
        break :blk num_elements;
    };

    // Evaluate to check if reshaping is valid
    const input_elem_count = self.core.symbolic.evaluate(input_elements, null) catch return error.InvalidShape;
    const new_elem_count = self.core.symbolic.evaluate(new_elements, null) catch return error.InvalidShape;

    if (input_elem_count != new_elem_count) {
        return error.ElementCountMismatch;
    }

    // Compute strides for the new shape
    const new_strides = try self.core.arena.allocator().alloc(i64, new_shape.dims.len);
    var stride: i64 = 1;
    var i = new_shape.dims.len;
    while (i > 0) : (i -= 1) {
        const dim_idx = i - 1;
        new_strides[dim_idx] = stride;
        const dim_value = self.core.symbolic.evaluate(new_shape.dims[dim_idx], null) catch return error.InvalidDimension;
        stride *= dim_value;
    }

    // Create new view with the computed strides
    return self.createView(new_shape_id, new_strides, input_view.offset_elements);
}

pub fn newPermutedView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    axes: []const u32,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);

    // Validate axes
    if (axes.len != input_shape.dims.len) {
        return error.InvalidAxes;
    }

    // Check for duplicate axes
    var seen = try self.core.arena.allocator().alloc(bool, axes.len);
    defer self.core.arena.allocator().free(seen);
    @memset(seen, false);

    for (axes) |axis| {
        if (axis >= axes.len or seen[axis]) {
            return error.InvalidAxes;
        }
        seen[axis] = true;
    }

    // Create new shape with permuted dimensions
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, axes.len);
    for (axes, 0..) |axis, i| {
        new_dims[i] = input_shape.dims[axis];
    }

    const new_shape_id = try self.newShape(new_dims);

    // Permute strides according to axes
    var new_strides = try self.core.arena.allocator().alloc(i64, axes.len);
    for (axes, 0..) |axis, i| {
        new_strides[i] = input_view.strides[axis];
    }

    return self.createView(new_shape_id, new_strides, input_view.offset_elements);
}

pub fn newSlicedView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    ranges: []const parent_types.SliceRange,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);

    if (ranges.len != input_shape.dims.len) {
        return error.InvalidSliceParameters;
    }

    // Create new shape and calculate offset
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, ranges.len);
    var offset_adjustment: usize = 0;

    for (ranges, 0..) |range, i| {
        if (range.step == 0) {
            return error.ZeroSliceStep;
        }

        // Evaluate dimension for validation
        const dim_value = self.core.symbolic.evaluate(input_shape.dims[i], null) catch 
            return error.InvalidDimension;

        // Normalize negative indices
        var start = range.start;
        var end = range.end;
        if (start < 0) start += @as(i64, @intCast(dim_value));
        if (end < 0) end += @as(i64, @intCast(dim_value));

        // Clamp to valid range
        start = @max(0, @min(start, @as(i64, @intCast(dim_value))));
        end = @max(start, @min(end, @as(i64, @intCast(dim_value))));

        // Calculate new dimension size
        const size = if (range.step > 0)
            @divFloor(end - start + range.step - 1, range.step)
        else
            @divFloor(start - end - range.step - 1, -range.step);

        new_dims[i] = try self.core.symbolic.newIntegerExpr(@intCast(size));

        // Update offset
        offset_adjustment += @as(usize, @intCast(start)) * @as(usize, @intCast(input_view.strides[i]));
    }

    const new_shape_id = try self.newShape(new_dims);

    // Keep the same strides (slicing doesn't change strides)
    var new_strides = try self.core.arena.allocator().alloc(i64, ranges.len);
    for (ranges, 0..) |range, i| {
        new_strides[i] = input_view.strides[i] * range.step;
    }

    return self.createView(new_shape_id, new_strides, input_view.offset_elements + offset_adjustment);
}

pub fn newPaddedView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    padding: []const [2]*parent_types.Expr,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);

    if (padding.len != input_shape.dims.len) {
        return error.InvalidPaddingParameters;
    }

    // Create padded shape and validity expression
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len);
    var validity_exprs = std.ArrayList(*parent_types.Expr).init(self.core.arena.allocator());
    defer validity_exprs.deinit();

    for (padding, 0..) |pad, i| {
        // New dimension = original + left_pad + right_pad
        const left_pad = pad[0];
        const right_pad = pad[1];
        const original_dim = input_shape.dims[i];

        const dim_with_left = try self.core.symbolic.newBinaryExpr(.add, original_dim, left_pad);
        new_dims[i] = try self.core.symbolic.newBinaryExpr(.add, dim_with_left, right_pad);

        // Create index variable for this dimension
        const idx_var = try self.core.symbolic.newSymbolExpr(try std.fmt.allocPrint(
            self.core.arena.allocator(),
            "__idx_{d}",
            .{i},
        ));

        // Validity: idx >= left_pad AND idx < (original_dim + left_pad)
        const left_bound = try self.core.symbolic.newBinaryExpr(.greater_equal, idx_var, left_pad);
        const right_limit = try self.core.symbolic.newBinaryExpr(.add, original_dim, left_pad);
        const right_bound = try self.core.symbolic.newBinaryExpr(.less_than, idx_var, right_limit);
        const dim_valid = try self.core.symbolic.newBinaryExpr(.min, left_bound, right_bound);

        try validity_exprs.append(dim_valid);
    }

    // Combine all validity expressions with AND
    var combined_validity = validity_exprs.items[0];
    for (validity_exprs.items[1..]) |expr| {
        combined_validity = try self.core.symbolic.newBinaryExpr(.min, combined_validity, expr);
    }

    const new_shape_id = try self.newShape(new_dims);

    // For padding, we need to create a new view with validity expression
    return self.createViewWithFakeDims(
        new_shape_id,
        input_view.strides,
        input_view.offset_elements,
        combined_validity,
        null,
        &[_]bool{false} ** 16, // No fake dims for padding
    );
}

pub fn newBroadcastView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    target_shape_id: parent_types.ShapeId,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);
    const target_shape = self.getShape(target_shape_id);

    // Validate broadcast compatibility
    if (!try self.canBroadcast(input_shape.dims, target_shape.dims)) {
        return error.BroadcastError;
    }

    // Calculate new strides for broadcasting
    const rank_diff = target_shape.dims.len - input_shape.dims.len;
    var new_strides = try self.core.arena.allocator().alloc(i64, target_shape.dims.len);
    var fake_dims = try self.core.arena.allocator().alloc(bool, target_shape.dims.len);
    @memset(fake_dims, false);

    // Leading dimensions (added by broadcasting) have stride 0
    for (0..rank_diff) |i| {
        new_strides[i] = 0;
        fake_dims[i] = true;
    }

    // Process remaining dimensions
    for (input_shape.dims, 0..) |input_dim, i| {
        const target_idx = i + rank_diff;
        const target_dim = target_shape.dims[target_idx];

        // Check if dimensions are compatible
        const input_size = self.core.symbolic.evaluate(input_dim, null) catch 
            return error.InvalidDimension;
        const target_size = self.core.symbolic.evaluate(target_dim, null) catch 
            return error.InvalidDimension;

        if (input_size == 1 and target_size != 1) {
            // Broadcasting a dimension of size 1
            new_strides[target_idx] = 0;
            fake_dims[target_idx] = true;
        } else if (input_size == target_size) {
            // Dimensions match, keep stride
            new_strides[target_idx] = input_view.strides[i];
        } else {
            return error.BroadcastError;
        }
    }

    return self.createViewWithFakeDims(
        target_shape_id,
        new_strides,
        input_view.offset_elements,
        input_view.validity_expr,
        input_view.mask_expr,
        fake_dims,
    );
}

pub fn newExpandedView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    axis: usize,
    size_expr: *parent_types.Expr,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);

    if (axis > input_shape.dims.len) {
        return error.InvalidAxes;
    }

    // Create new shape with expanded dimension
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len + 1);
    
    // Copy dimensions before the insertion point
    for (0..axis) |i| {
        new_dims[i] = input_shape.dims[i];
    }
    
    // Insert the new dimension
    new_dims[axis] = size_expr;
    
    // Copy remaining dimensions
    for (axis..input_shape.dims.len) |i| {
        new_dims[i + 1] = input_shape.dims[i];
    }

    const new_shape_id = try self.newShape(new_dims);

    // Create new strides with 0 stride for the expanded dimension
    var new_strides = try self.core.arena.allocator().alloc(i64, new_dims.len);
    var fake_dims = try self.core.arena.allocator().alloc(bool, new_dims.len);
    @memset(fake_dims, false);

    for (0..axis) |i| {
        new_strides[i] = input_view.strides[i];
    }
    new_strides[axis] = 0; // Expanded dimension has stride 0
    fake_dims[axis] = true; // Mark as fake dimension
    for (axis..input_view.strides.len) |i| {
        new_strides[i + 1] = input_view.strides[i];
    }

    return self.createViewWithFakeDims(
        new_shape_id,
        new_strides,
        input_view.offset_elements,
        input_view.validity_expr,
        input_view.mask_expr,
        fake_dims,
    );
}

pub fn newSqueezeView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    axes: ?[]const u32,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);

    // Determine which dimensions to squeeze
    var squeeze_mask = try self.core.arena.allocator().alloc(bool, input_shape.dims.len);
    defer self.core.arena.allocator().free(squeeze_mask);
    @memset(squeeze_mask, false);

    if (axes) |specified_axes| {
        // Squeeze specified axes
        for (specified_axes) |axis| {
            if (axis >= input_shape.dims.len) {
                return error.InvalidAxes;
            }
            
            // Verify the dimension is actually 1
            const dim_value = self.core.symbolic.evaluate(input_shape.dims[axis], null) catch 
                return error.InvalidDimension;
            if (dim_value != 1) {
                return error.InvalidAxes; // Can only squeeze dimensions of size 1
            }
            
            squeeze_mask[axis] = true;
        }
    } else {
        // Squeeze all dimensions of size 1
        for (input_shape.dims, 0..) |dim, i| {
            const dim_value = self.core.symbolic.evaluate(dim, null) catch 
                return error.InvalidDimension;
            if (dim_value == 1) {
                squeeze_mask[i] = true;
            }
        }
    }

    // Count remaining dimensions
    var new_rank: usize = 0;
    for (squeeze_mask) |should_squeeze| {
        if (!should_squeeze) new_rank += 1;
    }

    // Create new shape and strides
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, new_rank);
    var new_strides = try self.core.arena.allocator().alloc(i64, new_rank);
    var new_fake_dims = try self.core.arena.allocator().alloc(bool, new_rank);

    var new_idx: usize = 0;
    for (squeeze_mask, 0..) |should_squeeze, i| {
        if (!should_squeeze) {
            new_dims[new_idx] = input_shape.dims[i];
            new_strides[new_idx] = input_view.strides[i];
            new_fake_dims[new_idx] = if (i < input_view.fake_dims.len) input_view.fake_dims[i] else false;
            new_idx += 1;
        }
    }

    const new_shape_id = try self.newShape(new_dims);
    return self.createViewWithFakeDims(
        new_shape_id,
        new_strides,
        input_view.offset_elements,
        input_view.validity_expr,
        input_view.mask_expr,
        new_fake_dims,
    );
}

pub fn newConcatView(
    self: anytype,
    view_ids: []const parent_types.ViewId,
    axis: u32,
) !parent_types.ViewId {
    if (view_ids.len == 0) {
        return error.InvalidInput;
    }

    const first_view = self.getView(view_ids[0]);
    const first_shape = self.getShape(first_view.shape_id);

    if (axis >= first_shape.dims.len) {
        return error.InvalidAxes;
    }

    // Validate all views have same rank and compatible shapes
    var concat_dim_size = first_shape.dims[axis];
    
    for (view_ids[1..]) |view_id| {
        const view = self.getView(view_id);
        const shape = self.getShape(view.shape_id);
        
        if (shape.dims.len != first_shape.dims.len) {
            return error.RankMismatch;
        }
        
        // Check all non-concat dimensions match
        for (shape.dims, 0..) |dim, i| {
            if (i != axis) {
                if (!self.exprsEqual(dim, first_shape.dims[i])) {
                    return error.ShapeIncompatible;
                }
            }
        }
        
        // Accumulate concat dimension size
        concat_dim_size = try self.core.symbolic.newBinaryExpr(.add, concat_dim_size, shape.dims[axis]);
    }

    // Create concatenated shape
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, first_shape.dims.len);
    for (first_shape.dims, 0..) |dim, i| {
        if (i == axis) {
            new_dims[i] = concat_dim_size;
        } else {
            new_dims[i] = dim;
        }
    }

    const new_shape_id = try self.newShape(new_dims);

    // Create validity expression for concat
    var validity_exprs = std.ArrayList(*parent_types.Expr).init(self.core.arena.allocator());
    defer validity_exprs.deinit();

    // Create index variable for concat axis
    const idx_var = try self.core.symbolic.newSymbolExpr(try std.fmt.allocPrint(
        self.core.arena.allocator(),
        "__concat_idx_{d}",
        .{axis},
    ));

    var current_offset = try self.core.symbolic.newIntegerExpr(0);
    
    for (view_ids) |view_id| {
        const view = self.getView(view_id);
        const shape = self.getShape(view.shape_id);
        
        // Create range check: current_offset <= idx < current_offset + dim_size
        const lower_bound = try self.core.symbolic.newBinaryExpr(.greater_equal, idx_var, current_offset);
        const upper_limit = try self.core.symbolic.newBinaryExpr(.add, current_offset, shape.dims[axis]);
        const upper_bound = try self.core.symbolic.newBinaryExpr(.less_than, idx_var, upper_limit);
        const range_check = try self.core.symbolic.newBinaryExpr(.min, lower_bound, upper_bound);
        
        try validity_exprs.append(range_check);
        
        // Update offset for next view
        current_offset = upper_limit;
    }

    // Combine validity expressions with OR (since only one range should be valid at a time)
    var combined_validity = validity_exprs.items[0];
    for (validity_exprs.items[1..]) |expr| {
        combined_validity = try self.core.symbolic.newBinaryExpr(.max, combined_validity, expr);
    }

    // Use the first view's strides as base (concat doesn't change strides)
    return self.createViewWithFakeDims(
        new_shape_id,
        first_view.strides,
        first_view.offset_elements,
        combined_validity,
        null,
        first_view.fake_dims,
    );
}

pub fn newExciseView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    axis: usize,
    size: usize,
    spacing: usize,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);
    
    if (axis >= input_shape.dims.len) {
        return error.InvalidAxes;
    }
    
    if (size == 0 or spacing == 0) {
        return error.InvalidExciseParameters;
    }
    
    // Calculate the dimension size along the axis
    const dim_value = self.core.symbolic.evaluate(input_shape.dims[axis], null) catch 
        return error.InvalidDimension;
    
    // Calculate number of cuts
    const num_cuts = @divFloor(@as(usize, @intCast(dim_value)), spacing);
    
    // Calculate new dimension size
    const new_dim_size = @as(i64, @intCast(dim_value)) - @as(i64, @intCast(num_cuts * size));
    
    if (new_dim_size <= 0) {
        return error.InvalidShape;
    }
    
    // Create new shape with excised dimension
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len);
    for (input_shape.dims, 0..) |dim, i| {
        if (i == axis) {
            new_dims[i] = try self.core.symbolic.newIntegerExpr(new_dim_size);
        } else {
            new_dims[i] = dim;
        }
    }
    
    const new_shape_id = try self.newShape(new_dims);
    
    // Create validity expression for excise
    const idx_var = try self.core.symbolic.newSymbolExpr(try std.fmt.allocPrint(
        self.core.arena.allocator(),
        "__excise_idx_{d}",
        .{axis},
    ));
    
    // The validity expression checks if the current index maps to a valid (non-excised) position
    // Formula: (idx + (idx / (spacing - size)) * size) < original_dim
    const adjusted_spacing = spacing - size;
    if (adjusted_spacing == 0) {
        return error.InvalidExciseParameters;
    }
    
    const idx_div_spacing = try self.core.symbolic.newBinaryExpr(.divide, idx_var, try self.core.symbolic.newIntegerExpr(@intCast(adjusted_spacing)));
    const excise_offset = try self.core.symbolic.newBinaryExpr(.multiply, idx_div_spacing, try self.core.symbolic.newIntegerExpr(@intCast(size)));
    const mapped_idx = try self.core.symbolic.newBinaryExpr(.add, idx_var, excise_offset);
    const validity_expr = try self.core.symbolic.newBinaryExpr(.less_than, mapped_idx, input_shape.dims[axis]);
    
    // Keep the same strides but with validity expression
    return self.createViewWithFakeDims(
        new_shape_id,
        input_view.strides,
        input_view.offset_elements,
        validity_expr,
        input_view.mask_expr,
        input_view.fake_dims,
    );
}

pub fn newPoolLastDimView(
    self: anytype,
    input_view_id: parent_types.ViewId,
    kernel: usize,
    stride: usize,
    dilation: usize,
) !parent_types.ViewId {
    const input_view = self.getView(input_view_id);
    const input_shape = self.getShape(input_view.shape_id);
    
    if (input_shape.dims.len == 0) {
        return error.InvalidShape;
    }
    
    if (kernel == 0 or stride == 0) {
        return error.InvalidPoolParameters;
    }
    
    const last_axis = input_shape.dims.len - 1;
    
    // Calculate effective kernel size with dilation
    const effective_kernel = kernel + (kernel - 1) * dilation;
    
    // Get the last dimension size
    const last_dim_value = self.core.symbolic.evaluate(input_shape.dims[last_axis], null) catch 
        return error.InvalidDimension;
    
    // Calculate number of windows
    const num_windows = @divFloor(@as(usize, @intCast(last_dim_value)) - effective_kernel, stride) + 1;
    
    if (num_windows <= 0) {
        return error.InvalidShape;
    }
    
    // Create new shape: [..., num_windows, kernel]
    var new_dims = try self.core.arena.allocator().alloc(*parent_types.Expr, input_shape.dims.len + 1);
    
    // Copy all dimensions except the last
    for (0..last_axis) |i| {
        new_dims[i] = input_shape.dims[i];
    }
    
    // Add the number of windows dimension
    new_dims[last_axis] = try self.core.symbolic.newIntegerExpr(@intCast(num_windows));
    
    // Add the kernel size dimension
    new_dims[last_axis + 1] = try self.core.symbolic.newIntegerExpr(@intCast(kernel));
    
    const new_shape_id = try self.newShape(new_dims);
    
    // Create new strides
    var new_strides = try self.core.arena.allocator().alloc(i64, new_dims.len);
    const new_fake_dims = try self.core.arena.allocator().alloc(bool, new_dims.len);
    @memset(new_fake_dims, false);
    
    // Copy strides for dimensions before the last
    for (0..last_axis) |i| {
        new_strides[i] = input_view.strides[i];
    }
    
    // The window dimension moves by stride * original_stride
    new_strides[last_axis] = input_view.strides[last_axis] * @as(i64, @intCast(stride));
    
    // The kernel dimension moves by dilation * original_stride
    new_strides[last_axis + 1] = input_view.strides[last_axis] * @as(i64, @intCast(dilation + 1));
    
    // Create validity expression if needed for dilation
    var validity_expr = input_view.validity_expr;
    if (dilation > 0) {
        // For dilated pooling, we need to ensure we don't access out-of-bounds elements
        const window_idx = try self.core.symbolic.newSymbolExpr("__pool_window_idx");
        const kernel_idx = try self.core.symbolic.newSymbolExpr("__pool_kernel_idx");
        
        // Calculate actual index: window_idx * stride + kernel_idx * (dilation + 1)
        const window_offset = try self.core.symbolic.newBinaryExpr(.multiply, window_idx, try self.core.symbolic.newIntegerExpr(@intCast(stride)));
        const kernel_offset = try self.core.symbolic.newBinaryExpr(.multiply, kernel_idx, try self.core.symbolic.newIntegerExpr(@intCast(dilation + 1)));
        const actual_idx = try self.core.symbolic.newBinaryExpr(.add, window_offset, kernel_offset);
        
        // Check if actual index is within bounds
        const bounds_check = try self.core.symbolic.newBinaryExpr(.less_than, actual_idx, input_shape.dims[last_axis]);
        
        if (validity_expr) |existing_validity| {
            validity_expr = try self.core.symbolic.newBinaryExpr(.min, existing_validity, bounds_check);
        } else {
            validity_expr = bounds_check;
        }
    }
    
    return self.createViewWithFakeDims(
        new_shape_id,
        new_strides,
        input_view.offset_elements,
        validity_expr,
        input_view.mask_expr,
        new_fake_dims,
    );
}