const std = @import("std");
const testing = std.testing;
const core_module = @import("core");
const parent_types = core_module.types;
const errors = core_module.errors;
const ShapeEngine = core_module.shape.engine.ShapeEngine;
const SymbolicEngine = core_module.symbolic.engine.SymbolicEngine;
const Core = core_module.Core;

// Comprehensive tests for V2 shape tracking parity with Luminal

test "concat with padding - comprehensive validation" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Basic concat with dimension calculation
    {
        const dims1 = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape1_id = try core.shape.newShape(&dims1);
        const view1_id = try core.shape.newDefaultView(shape1_id);
        
        const dims2 = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape2_id = try core.shape.newShape(&dims2);
        const view2_id = try core.shape.newDefaultView(shape2_id);
        
        // Concat along axis 1
        const views = [_]parent_types.ViewId{ view1_id, view2_id };
        const concat_view_id = try core.shape.newConcatView(&views, 1);
        
        const concat_view = core.shape.getView(concat_view_id);
        const concat_shape = core.shape.getShape(concat_view.shape_id);
        
        try testing.expectEqual(@as(usize, 2), concat_shape.dims.len);
        const dim0_value = try core.symbolic.evaluate(concat_shape.dims[0], null);
        const dim1_value = try core.symbolic.evaluate(concat_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 2), dim0_value);
        try testing.expectEqual(@as(i64, 7), dim1_value);
    }
    
    // Test 2: Multi-input concat
    {
        const dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(10),
            try core.symbolic.newIntegerExpr(5),
        };
        const shape_id = try core.shape.newShape(&dims);
        
        var views: [4]parent_types.ViewId = undefined;
        for (&views) |*view| {
            view.* = try core.shape.newDefaultView(shape_id);
        }
        
        // Concat 4 tensors along axis 0
        const concat_view_id = try core.shape.newConcatView(&views, 0);
        const concat_shape = core.shape.getShape(core.shape.getView(concat_view_id).shape_id);
        
        const dim0_value = try core.symbolic.evaluate(concat_shape.dims[0], null);
        const dim1_value = try core.symbolic.evaluate(concat_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 40), dim0_value);
        try testing.expectEqual(@as(i64, 5), dim1_value);
    }
}

test "reshape validation - all edge cases" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Valid reshape with exact element count
    {
        const dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        const new_dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(6),
            try core.symbolic.newIntegerExpr(4),
        };
        const new_shape_id = try core.shape.newShape(&new_dims);
        const reshaped_view_id = try core.shape.newReshapedView(view_id, new_shape_id);
        
        try testing.expect(reshaped_view_id != view_id);
    }
    
    // Test 2: Invalid reshape with mismatched element count
    {
        const dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        const new_dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(5), // 5 != 6
        };
        const new_shape_id = try core.shape.newShape(&new_dims);
        const result = core.shape.newReshapedView(view_id, new_shape_id);
        
        try testing.expectError(errors.ZingError.ElementCountMismatch, result);
    }
    
    // Test 3: Reshape with mixed symbolic/concrete dimensions
    {
        
        const batch_sym = try core.symbolic.newSymbolExpr("batch");
        const dims = [_]*parent_types.Expr{
            batch_sym,
            try core.symbolic.newIntegerExpr(10),
            try core.symbolic.newIntegerExpr(20),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Reshape to [batch, 200]
        const new_dims = [_]*parent_types.Expr{
            batch_sym,
            try core.symbolic.newIntegerExpr(200),
        };
        const new_shape_id = try core.shape.newShape(&new_dims);
        const reshaped_view_id = try core.shape.newReshapedView(view_id, new_shape_id);
        
        try testing.expect(reshaped_view_id != view_id);
    }
}

test "validity expressions - fields preserved through operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create a shape and default view
    const dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(10),
        try core.symbolic.newIntegerExpr(20),
    };
    const shape_id = try core.shape.newShape(&dims);
    const view_id = try core.shape.newDefaultView(shape_id);
    
    // Test 1: Reshape operation
    {
        const new_dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(200),
        };
        const new_shape_id = try core.shape.newShape(&new_dims);
        const reshaped_view_id = try core.shape.newReshapedView(view_id, new_shape_id);
        
        // Both views should have null validity expressions (default views)
        const original_view = core.shape.getView(view_id);
        const reshaped_view = core.shape.getView(reshaped_view_id);
        
        try testing.expectEqual(@as(?*parent_types.Expr, null), original_view.validity_expr);
        try testing.expectEqual(@as(?*parent_types.Expr, null), reshaped_view.validity_expr);
        try testing.expectEqual(original_view.validity_expr, reshaped_view.validity_expr);
        try testing.expectEqual(original_view.mask_expr, reshaped_view.mask_expr);
    }
    
    // Test 2: Permute operation  
    {
        const perm = [_]u32{ 1, 0 };
        const permuted_view_id = try core.shape.newPermutedView(view_id, &perm);
        
        // Re-fetch views to ensure fresh pointers
        const original_view = core.shape.getView(view_id);
        const permuted_view = core.shape.getView(permuted_view_id);
        
        try testing.expectEqual(original_view.validity_expr, permuted_view.validity_expr);
        try testing.expectEqual(original_view.mask_expr, permuted_view.mask_expr);
    }
    
    // Test 3: Slice operation
    {
        const ranges = [_]parent_types.SliceRange{
            .{ .start = 2, .end = 8, .step = 1 },
            .{ .start = 5, .end = 15, .step = 1 },
        };
        const sliced_view_id = try core.shape.newSlicedView(view_id, &ranges);
        
        // Re-fetch views to ensure fresh pointers
        const original_view = core.shape.getView(view_id);
        const sliced_view = core.shape.getView(sliced_view_id);
        
        try testing.expectEqual(original_view.validity_expr, sliced_view.validity_expr);
        try testing.expectEqual(original_view.mask_expr, sliced_view.mask_expr);
    }
    
    // Test 4: Complex operation chain
    {
        // Start fresh with a 3D shape
        const dims_3d = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(5),
            try core.symbolic.newIntegerExpr(10),
            try core.symbolic.newIntegerExpr(20),
        };
        const shape_3d_id = try core.shape.newShape(&dims_3d);
        const view_3d_id = try core.shape.newDefaultView(shape_3d_id);
        
        // Chain operations: permute -> reshape -> slice
        const perm = [_]u32{ 2, 0, 1 };
        const permuted_id = try core.shape.newPermutedView(view_3d_id, &perm);
        
        const new_dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(20),
            try core.symbolic.newIntegerExpr(50),
        };
        const new_shape_id = try core.shape.newShape(&new_dims);
        const reshaped_id = try core.shape.newReshapedView(permuted_id, new_shape_id);
        
        const ranges = [_]parent_types.SliceRange{
            .{ .start = 0, .end = 10, .step = 1 },
            .{ .start = 0, .end = 25, .step = 1 },
        };
        const sliced_id = try core.shape.newSlicedView(reshaped_id, &ranges);
        
        // Check that validity is preserved through the entire chain
        const original_view = core.shape.getView(view_3d_id);
        const final_view = core.shape.getView(sliced_id);
        
        try testing.expectEqual(original_view.validity_expr, final_view.validity_expr);
        try testing.expectEqual(original_view.mask_expr, final_view.mask_expr);
    }
}

test "padding operation - creates padded views" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create a 2D shape
    const dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(10),
        try core.symbolic.newIntegerExpr(20),
    };
    const shape_id = try core.shape.newShape(&dims);
    const view_id = try core.shape.newDefaultView(shape_id);
    
    // Apply padding
    const padding = [_][2]*parent_types.Expr{
        .{ try core.symbolic.newIntegerExpr(1), try core.symbolic.newIntegerExpr(1) }, // pad 1 on each side of dim 0
        .{ try core.symbolic.newIntegerExpr(2), try core.symbolic.newIntegerExpr(3) }, // pad 2 left, 3 right on dim 1
    };
    const padded_view_id = try core.shape.newPaddedView(view_id, &padding);
    
    // Check padded dimensions
    const padded_view = core.shape.getView(padded_view_id);
    const padded_shape = core.shape.getShape(padded_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), padded_shape.dims.len);
    const dim0_value = try core.symbolic.evaluate(padded_shape.dims[0], null);
    const dim1_value = try core.symbolic.evaluate(padded_shape.dims[1], null);
    try testing.expectEqual(@as(i64, 12), dim0_value); // 10 + 1 + 1
    try testing.expectEqual(@as(i64, 25), dim1_value); // 20 + 2 + 3
}

test "slice operation - handles ranges correctly" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create a 3D shape
    const dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(10),
        try core.symbolic.newIntegerExpr(20),
        try core.symbolic.newIntegerExpr(30),
    };
    const shape_id = try core.shape.newShape(&dims);
    const view_id = try core.shape.newDefaultView(shape_id);
    
    // Slice with various range types
    const ranges = [_]parent_types.SliceRange{
        .{ .start = 2, .end = 8, .step = 1 },    // Regular slice
        .{ .start = 5, .end = -1, .step = 1 },   // Slice to end
        .{ .start = 0, .end = -1, .step = 2 },   // Strided slice
    };
    const sliced_view_id = try core.shape.newSlicedView(view_id, &ranges);
    
    // Check sliced dimensions
    const sliced_view = core.shape.getView(sliced_view_id);
    const sliced_shape = core.shape.getShape(sliced_view.shape_id);
    
    try testing.expectEqual(@as(usize, 3), sliced_shape.dims.len);
    const dim0_value = try core.symbolic.evaluate(sliced_shape.dims[0], null);
    const dim1_value = try core.symbolic.evaluate(sliced_shape.dims[1], null);
    const dim2_value = try core.symbolic.evaluate(sliced_shape.dims[2], null);
    try testing.expectEqual(@as(i64, 6), dim0_value);   // 8 - 2
    try testing.expectEqual(@as(i64, 15), dim1_value);  // 20 - 5
    try testing.expectEqual(@as(i64, 15), dim2_value);  // (30 - 0) / 2
}

test "broadcast and expand operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test expand: add new dimension
    {
        const dims = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(10),
            try core.symbolic.newIntegerExpr(20),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        const expanded_view_id = try core.shape.newExpandedView(view_id, 1, try core.symbolic.newIntegerExpr(1));
        const expanded_shape = core.shape.getShape(core.shape.getView(expanded_view_id).shape_id);
        
        try testing.expectEqual(@as(usize, 3), expanded_shape.dims.len);
        const dim0_value = try core.symbolic.evaluate(expanded_shape.dims[0], null);
        const dim1_value = try core.symbolic.evaluate(expanded_shape.dims[1], null);
        const dim2_value = try core.symbolic.evaluate(expanded_shape.dims[2], null);
        try testing.expectEqual(@as(i64, 10), dim0_value);
        try testing.expectEqual(@as(i64, 1), dim1_value);
        try testing.expectEqual(@as(i64, 20), dim2_value);
    }
    
    // Test broadcast: match dimensions
    {
        const dims1 = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(1),
            try core.symbolic.newIntegerExpr(20),
        };
        const shape1_id = try core.shape.newShape(&dims1);
        const view1_id = try core.shape.newDefaultView(shape1_id);
        
        const dims2 = [_]*parent_types.Expr{
            try core.symbolic.newIntegerExpr(10),
            try core.symbolic.newIntegerExpr(20),
        };
        const shape2_id = try core.shape.newShape(&dims2);
        
        const broadcast_view_id = try core.shape.newBroadcastView(view1_id, shape2_id);
        const broadcast_shape = core.shape.getShape(core.shape.getView(broadcast_view_id).shape_id);
        
        try testing.expectEqual(@as(usize, 2), broadcast_shape.dims.len);
        const dim0_value = try core.symbolic.evaluate(broadcast_shape.dims[0], null);
        const dim1_value = try core.symbolic.evaluate(broadcast_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 10), dim0_value);
        try testing.expectEqual(@as(i64, 20), dim1_value);
    }
}

test "squeeze operation - removes singleton dimensions" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create shape with singleton dimensions
    const dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(1),
        try core.symbolic.newIntegerExpr(10),
        try core.symbolic.newIntegerExpr(1),
        try core.symbolic.newIntegerExpr(20),
        try core.symbolic.newIntegerExpr(1),
    };
    const shape_id = try core.shape.newShape(&dims);
    const view_id = try core.shape.newDefaultView(shape_id);
    
    // Squeeze all singleton dimensions
    const squeezed_view_id = try core.shape.newSqueezeView(view_id, &.{});
    const squeezed_shape = core.shape.getShape(core.shape.getView(squeezed_view_id).shape_id);
    
    try testing.expectEqual(@as(usize, 2), squeezed_shape.dims.len);
    const dim0_value = try core.symbolic.evaluate(squeezed_shape.dims[0], null);
    const dim1_value = try core.symbolic.evaluate(squeezed_shape.dims[1], null);
    try testing.expectEqual(@as(i64, 10), dim0_value);
    try testing.expectEqual(@as(i64, 20), dim1_value);
    
    // Squeeze specific dimensions only
    const partial_squeeze_id = try core.shape.newSqueezeView(view_id, &.{ 0, 2 });
    const partial_shape = core.shape.getShape(core.shape.getView(partial_squeeze_id).shape_id);
    
    try testing.expectEqual(@as(usize, 3), partial_shape.dims.len);
    const partial_dim0_value = try core.symbolic.evaluate(partial_shape.dims[0], null);
    const partial_dim1_value = try core.symbolic.evaluate(partial_shape.dims[1], null);
    const partial_dim2_value = try core.symbolic.evaluate(partial_shape.dims[2], null);
    try testing.expectEqual(@as(i64, 10), partial_dim0_value);
    try testing.expectEqual(@as(i64, 20), partial_dim1_value);
    try testing.expectEqual(@as(i64, 1), partial_dim2_value);
}

test "shape caching and deduplication" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create same shape multiple times
    const dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(10),
        try core.symbolic.newIntegerExpr(20),
        try core.symbolic.newIntegerExpr(30),
    };
    
    const shape1_id = try core.shape.newShape(&dims);
    const shape2_id = try core.shape.newShape(&dims);
    const shape3_id = try core.shape.newShape(&dims);
    
    // All should be the same due to caching
    try testing.expectEqual(shape1_id, shape2_id);
    try testing.expectEqual(shape2_id, shape3_id);
    
    // Check cache statistics
    const stats = core.shape.getStats();
    try testing.expect(stats.cache_hits >= 2);
    try testing.expect(stats.shapes_created >= 1);
}

test "all operations preserve contiguity tracking" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Create a contiguous view
    const dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(10),
        try core.symbolic.newIntegerExpr(20),
    };
    const shape_id = try core.shape.newShape(&dims);
    const view_id = try core.shape.newDefaultView(shape_id);
    
    // Default view should be contiguous
    try testing.expect(core.shape.isContiguous(view_id));
    
    // Permuted view should not be contiguous
    const perm = [_]u32{ 1, 0 };
    const permuted_view_id = try core.shape.newPermutedView(view_id, &perm);
    try testing.expect(!core.shape.isContiguous(permuted_view_id));
    
    // Reshape of contiguous view should remain contiguous
    const new_dims = [_]*parent_types.Expr{
        try core.symbolic.newIntegerExpr(200),
    };
    const new_shape_id = try core.shape.newShape(&new_dims);
    const reshaped_view_id = try core.shape.newReshapedView(view_id, new_shape_id);
    try testing.expect(core.shape.isContiguous(reshaped_view_id));
}