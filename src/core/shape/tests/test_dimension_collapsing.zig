const std = @import("std");
const testing = std.testing;
const arena = std.heap.ArenaAllocator;
const Core = @import("../../core.zig").Core;
const ShapeEngine = @import("../engine.zig").ShapeEngine;
const Dim = @import("../../types.zig").Dim;

// Test Suite for dimension collapsing and view optimization features
pub fn runTests(allocator: std.mem.Allocator) !void {
    try testDimensionCollapsing(allocator);
    try testFakeDimensionTracking(allocator);
    try testExpressionGeneration(allocator);
    try testOptimization(allocator);
}

// Test simple dimension collapsing functionality
fn testDimensionCollapsing(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var core = try Core.init(arena_instance.allocator());
    defer core.deinit();
    
    // Create a 3D shape
    const dims = [_]Dim{
        Dim{ .concrete = 2 },  // dim 0
        Dim{ .concrete = 3 },  // dim 1
        Dim{ .concrete = 4 },  // dim 2
    };
    
    const shape_id = try core.shape.newShape(&dims);
    
    // Create a view with default strides (contiguous)
    const view_id = try core.shape.createView(shape_id, &[_]i64{12, 4, 1}, 0);
    
    // Test that dimensions can be collapsed
    const view = core.shape.getView(view_id);
    try testing.expect(view.canCollapseDims(1, 2, core.shape));  // dims 1 and 2 should be collapsible
    try testing.expect(!view.canCollapseDims(0, 1, core.shape)); // dims 0 and 1 should not be collapsible
    
    // Collapse dimensions 1 and 2
    const collapsed_view_id = try core.shape.collapseDimensions(view_id, 1, 2);
    
    // Verify the collapsed shape
    const collapsed_view = core.shape.getView(collapsed_view_id);
    const collapsed_shape = core.shape.getShape(collapsed_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), collapsed_shape.dims.len);
    try testing.expectEqual(@as(usize, 2), collapsed_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 12), collapsed_shape.dims[1].concrete); // 3*4 = 12
    
    // Verify strides
    try testing.expectEqual(@as(i64, 12), collapsed_view.strides[0]);
    try testing.expectEqual(@as(i64, 1), collapsed_view.strides[1]);
}

// Test fake dimension tracking
fn testFakeDimensionTracking(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var core = try Core.init(arena_instance.allocator());
    defer core.deinit();
    
    // Create shapes for broadcasting
    const shape_a_dims = [_]Dim{
        Dim{ .concrete = 1 },  // Will be broadcast
        Dim{ .concrete = 3 },
    };
    
    const shape_b_dims = [_]Dim{
        Dim{ .concrete = 2 },
        Dim{ .concrete = 3 },
    };
    
    const shape_a_id = try core.shape.newShape(&shape_a_dims);
    const shape_b_id = try core.shape.newShape(&shape_b_dims);
    
    // Create views
    const view_a_id = try core.shape.createView(shape_a_id, &[_]i64{3, 1}, 0);
    const view_b_id = try core.shape.createView(shape_b_id, &[_]i64{3, 1}, 0);
    
    // Create broadcast view
    const broadcast_view_id = try core.shape.newBroadcastView(view_a_id, shape_b_id);
    
    // Test that the broadcast view correctly identifies fake dimensions
    const broadcast_view = core.shape.getView(broadcast_view_id);
    try testing.expect(broadcast_view.isFakeDim(0));   // First dimension should be fake (broadcast)
    try testing.expect(!broadcast_view.isFakeDim(1));  // Second dimension should not be fake
    
    // Verify that fake dimensions cannot be collapsed
    try testing.expect(!broadcast_view.canCollapseDims(0, 1, core.shape));
}

// Test generation of validity and index expressions
fn testExpressionGeneration(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var core = try Core.init(arena_instance.allocator());
    defer core.deinit();
    
    // Create a 2D shape
    const dims = [_]Dim{
        Dim{ .concrete = 3 },
        Dim{ .concrete = 4 },
    };
    
    const shape_id = try core.shape.newShape(&dims);
    
    // Create a view with automatic expression generation
    const view_id = try core.shape.createView(shape_id, &[_]i64{4, 1}, 0);
    
    // Verify that expressions were generated
    const view = core.shape.getView(view_id);
    try testing.expect(view.validity_expr != null);
    try testing.expect(view.mask_expr != null);
    
    // Test expression evaluation
    const indices = [_]usize{1, 2}; // Logical index (1, 2)
    const physical_index = try core.shape.evaluateExpressionWithIndex(view.mask_expr.?, &indices);
    try testing.expectEqual(@as(i64, 6), physical_index); // Should be 1*4 + 2*1 = 6
}

// Test view optimization
fn testOptimization(allocator: std.mem.Allocator) !void {
    var arena_instance = arena.init(allocator);
    defer arena_instance.deinit();
    
    var core = try Core.init(arena_instance.allocator());
    defer core.deinit();
    
    // Create a 4D shape with potential for collapsing
    const dims = [_]Dim{
        Dim{ .concrete = 2 },
        Dim{ .concrete = 3 },
        Dim{ .concrete = 4 },
        Dim{ .concrete = 5 },
    };
    
    const shape_id = try core.shape.newShape(&dims);
    
    // Create a view with default strides (contiguous)
    const view_id = try core.shape.createView(shape_id, &[_]i64{60, 20, 5, 1}, 0);
    
    // Optimize the view
    const optimized_view_id = try core.shape.optimizeView(view_id);
    
    // Verify optimization (should collapse to 2 dimensions)
    const optimized_view = core.shape.getView(optimized_view_id);
    const optimized_shape = core.shape.getShape(optimized_view.shape_id);
    
    try testing.expectEqual(@as(usize, 2), optimized_shape.dims.len);
    try testing.expectEqual(@as(usize, 2), optimized_shape.dims[0].concrete);
    try testing.expectEqual(@as(usize, 60), optimized_shape.dims[1].concrete); // 3*4*5 = 60
    
    // Verify strides
    try testing.expectEqual(@as(i64, 60), optimized_view.strides[0]);
    try testing.expectEqual(@as(i64, 1), optimized_view.strides[1]);
}