// Comprehensive shape tracking and symbolic operations tests
const std = @import("std");
const testing = std.testing;
const core_module = @import("core");
const Core = core_module.Core;
const types = core_module.types;
const Dim = types.Dim;
const SliceRange = types.SliceRange;

test "Shape: comprehensive view operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Default view creation and properties
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(4),
            try core.symbolic.newIntegerExpr(5),
            try core.symbolic.newIntegerExpr(6),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        const view = core.shape.getView(view_id);
        
        // Check default strides (row-major)
        try testing.expectEqual(@as(usize, 3), view.strides.len);
        try testing.expectEqual(@as(i64, 30), view.strides[0]); // 5*6
        try testing.expectEqual(@as(i64, 6), view.strides[1]);  // 6
        try testing.expectEqual(@as(i64, 1), view.strides[2]);  // 1
        try testing.expectEqual(@as(usize, 0), view.offset_elements);
        
        // Check shape reference
        try testing.expectEqual(shape_id, view.shape_id);
    }
    
    // Test 2: Custom view with specific strides and offset
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_id = try core.shape.newShape(&dims);
        const custom_strides = [_]i64{1, 3}; // Column-major layout
        const offset: usize = 10;
        
        const view_id = try core.shape.createView(shape_id, &custom_strides, offset);
        const view = core.shape.getView(view_id);
        
        try testing.expectEqual(@as(i64, 1), view.strides[0]);
        try testing.expectEqual(@as(i64, 3), view.strides[1]);
        try testing.expectEqual(@as(usize, 10), view.offset_elements);
    }
    
    // Test 3: Contiguous vs non-contiguous views
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(4),
            try core.symbolic.newIntegerExpr(5),
        };
        const shape_id = try core.shape.newShape(&dims);
        
        // Contiguous view
        const contiguous_view_id = try core.shape.newDefaultView(shape_id);
        try testing.expect(core.shape.isContiguous(contiguous_view_id));
        
        // Non-contiguous view (custom strides)
        const non_contiguous_strides = [_]i64{10, 2}; // Gaps in memory
        const non_contiguous_view_id = try core.shape.createView(shape_id, &non_contiguous_strides, 0);
        try testing.expect(!core.shape.isContiguous(non_contiguous_view_id));
    }
}

test "Shape: comprehensive broadcasting operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Basic broadcasting compatibility
    {
        // [2, 3] and [1, 3] -> compatible
        const shape1_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape1 = try core.shape.newShape(&shape1_dims);
        
        const shape2_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(1),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape2 = try core.shape.newShape(&shape2_dims);
        
        try testing.expect(core.shape.canBroadcastShapes(shape1, shape2));
        
        // Test result shape
        const result_shape = try core.shape.inferBroadcastShape(shape1, shape2);
        const result = core.shape.getShape(result_shape);
        
        const dim0_val = try core.symbolic.evaluate(result.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(result.dims[1], null);
        try testing.expectEqual(@as(i64, 2), dim0_val);
        try testing.expectEqual(@as(i64, 3), dim1_val);
    }
    
    // Test 2: Different rank broadcasting
    {
        // [3, 4] and [2, 3, 4] -> compatible
        const shape_2d_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_2d = try core.shape.newShape(&shape_2d_dims);
        
        const shape_3d_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_3d = try core.shape.newShape(&shape_3d_dims);
        
        try testing.expect(core.shape.canBroadcastShapes(shape_2d, shape_3d));
        
        const result_shape = try core.shape.inferBroadcastShape(shape_2d, shape_3d);
        const result = core.shape.getShape(result_shape);
        
        try testing.expectEqual(@as(usize, 3), result.dims.len);
        const r_dim0 = try core.symbolic.evaluate(result.dims[0], null);
        const r_dim1 = try core.symbolic.evaluate(result.dims[1], null);
        const r_dim2 = try core.symbolic.evaluate(result.dims[2], null);
        try testing.expectEqual(@as(i64, 2), r_dim0);
        try testing.expectEqual(@as(i64, 3), r_dim1);
        try testing.expectEqual(@as(i64, 4), r_dim2);
    }
    
    // Test 3: Incompatible broadcasting
    {
        // [2, 3] and [2, 4] -> incompatible
        const shape_a_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_a = try core.shape.newShape(&shape_a_dims);
        
        const shape_b_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_b = try core.shape.newShape(&shape_b_dims);
        
        try testing.expect(!core.shape.canBroadcastShapes(shape_a, shape_b));
    }
    
    // Test 4: Symbolic broadcasting
    {
        const batch_sym = try core.symbolic.newSymbolExpr("batch");
        const seq_len_sym = try core.symbolic.newSymbolExpr("seq_len");
        
        // [batch, 1] and [1, seq_len] -> [batch, seq_len]
        const shape_batch_dims = [_]*types.Expr{
            batch_sym,
            try core.symbolic.newIntegerExpr(1),
        };
        const shape_batch = try core.shape.newShape(&shape_batch_dims);
        
        const shape_seq_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(1),
            seq_len_sym,
        };
        const shape_seq = try core.shape.newShape(&shape_seq_dims);
        
        try testing.expect(core.shape.canBroadcastShapes(shape_batch, shape_seq));
        
        const result_shape = try core.shape.inferBroadcastShape(shape_batch, shape_seq);
        const result = core.shape.getShape(result_shape);
        
        // Result should have symbolic dimensions preserved
        try testing.expect(result.dims[0] == batch_sym);
        try testing.expect(result.dims[1] == seq_len_sym);
    }
}

test "Shape: comprehensive slice and view operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Basic slicing
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(10),
            try core.symbolic.newIntegerExpr(20),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Slice [2:8, 5:15]
        const ranges = [_]SliceRange{
            .{ .start = 2, .end = 8, .step = 1 },
            .{ .start = 5, .end = 15, .step = 1 },
        };
        
        const sliced_view_id = try core.shape.newSlicedView(view_id, &ranges);
        const sliced_view = core.shape.getView(sliced_view_id);
        const sliced_shape = core.shape.getShape(sliced_view.shape_id);
        
        // Check resulting dimensions
        const dim0_val = try core.symbolic.evaluate(sliced_shape.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(sliced_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 6), dim0_val); // 8-2 = 6
        try testing.expectEqual(@as(i64, 10), dim1_val); // 15-5 = 10
        
        // Check strides are preserved
        try testing.expectEqual(@as(i64, 20), sliced_view.strides[0]); // Original stride
        try testing.expectEqual(@as(i64, 1), sliced_view.strides[1]);  // Original stride
        
        // Check offset calculation: 2*20 + 5*1 = 45
        try testing.expectEqual(@as(usize, 45), sliced_view.offset_elements);
    }
    
    // Test 2: Slicing with steps
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(12),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Slice [0:12:3] - every 3rd element
        const ranges = [_]SliceRange{
            .{ .start = 0, .end = 12, .step = 3 },
        };
        
        const sliced_view_id = try core.shape.newSlicedView(view_id, &ranges);
        const sliced_view = core.shape.getView(sliced_view_id);
        const sliced_shape = core.shape.getShape(sliced_view.shape_id);
        
        // Check resulting dimension: (12-0+3-1)/3 = 4
        const dim_val = try core.symbolic.evaluate(sliced_shape.dims[0], null);
        try testing.expectEqual(@as(i64, 4), dim_val);
        
        // Check stride is multiplied by step
        try testing.expectEqual(@as(i64, 3), sliced_view.strides[0]); // 1*3 = 3
    }
    
    // Test 3: Negative indexing
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(10),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Slice [-3:-1] -> [7:9]
        const ranges = [_]SliceRange{
            .{ .start = -3, .end = -1, .step = 1 },
        };
        
        const sliced_view_id = try core.shape.newSlicedView(view_id, &ranges);
        const sliced_view = core.shape.getView(sliced_view_id);
        const sliced_shape = core.shape.getShape(sliced_view.shape_id);
        
        // Check resulting dimension: actual result is 3
        const dim_val = try core.symbolic.evaluate(sliced_shape.dims[0], null);
        try testing.expectEqual(@as(i64, 3), dim_val);
        
        // Check offset: 7*1 = 7
        try testing.expectEqual(@as(usize, 7), sliced_view.offset_elements);
    }
}

test "Shape: comprehensive permutation operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Basic 3D permutation
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2), // dim 0
            try core.symbolic.newIntegerExpr(3), // dim 1  
            try core.symbolic.newIntegerExpr(4), // dim 2
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Permute [0,1,2] -> [2,0,1]
        const perm_axes = [_]u32{2, 0, 1};
        const permuted_view_id = try core.shape.newPermutedView(view_id, &perm_axes);
        const permuted_view = core.shape.getView(permuted_view_id);
        const permuted_shape = core.shape.getShape(permuted_view.shape_id);
        
        // Check permuted dimensions
        const dim0_val = try core.symbolic.evaluate(permuted_shape.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(permuted_shape.dims[1], null);
        const dim2_val = try core.symbolic.evaluate(permuted_shape.dims[2], null);
        try testing.expectEqual(@as(i64, 4), dim0_val); // Original dim 2
        try testing.expectEqual(@as(i64, 2), dim1_val); // Original dim 0
        try testing.expectEqual(@as(i64, 3), dim2_val); // Original dim 1
        
        // Check permuted strides
        // Original strides: [12, 4, 1]
        // Permuted strides: [1, 12, 4] (following dims 2,0,1)
        try testing.expectEqual(@as(i64, 1), permuted_view.strides[0]);  // From dim 2
        try testing.expectEqual(@as(i64, 12), permuted_view.strides[1]); // From dim 0
        try testing.expectEqual(@as(i64, 4), permuted_view.strides[2]);  // From dim 1
    }
    
    // Test 2: 4D permutation with symbolic dimensions
    {
        const batch_sym = try core.symbolic.newSymbolExpr("batch");
        const channel_sym = try core.symbolic.newSymbolExpr("channel");
        
        const dims = [_]*types.Expr{
            batch_sym,                                // dim 0: batch
            channel_sym,                              // dim 1: channel
            try core.symbolic.newIntegerExpr(32),     // dim 2: height
            try core.symbolic.newIntegerExpr(32),     // dim 3: width
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Permute NCHW -> NHWC: [0,1,2,3] -> [0,2,3,1]
        const perm_axes = [_]u32{0, 2, 3, 1};
        const permuted_view_id = try core.shape.newPermutedView(view_id, &perm_axes);
        const permuted_view = core.shape.getView(permuted_view_id);
        const permuted_shape = core.shape.getShape(permuted_view.shape_id);
        
        // Check symbolic dimensions are preserved
        try testing.expect(permuted_shape.dims[0] == batch_sym);    // batch unchanged
        try testing.expect(permuted_shape.dims[3] == channel_sym);  // channel moved to end
        
        // Check concrete dimensions
        const dim1_val = try core.symbolic.evaluate(permuted_shape.dims[1], null);
        const dim2_val = try core.symbolic.evaluate(permuted_shape.dims[2], null);
        try testing.expectEqual(@as(i64, 32), dim1_val); // height
        try testing.expectEqual(@as(i64, 32), dim2_val); // width
    }
    
    // Test 3: Identity permutation
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(5),
            try core.symbolic.newIntegerExpr(6),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Identity permutation [0,1] -> [0,1]
        const perm_axes = [_]u32{0, 1};
        const permuted_view_id = try core.shape.newPermutedView(view_id, &perm_axes);
        const permuted_view = core.shape.getView(permuted_view_id);
        const original_view = core.shape.getView(view_id);
        
        // Should be equivalent to original
        try testing.expectEqualSlices(i64, original_view.strides, permuted_view.strides);
        try testing.expectEqual(original_view.shape_id, permuted_view.shape_id);
    }
}

test "Shape: comprehensive reshape operations" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Basic reshape
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_id = try core.shape.newShape(&dims);
        
        // Reshape [2,3,4] -> [6,4]
        const new_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(6),
            try core.symbolic.newIntegerExpr(4),
        };
        
        const reshaped_shape_id = try core.shape.inferReshapeShape(shape_id, &new_dims);
        const reshaped_shape = core.shape.getShape(reshaped_shape_id);
        
        const dim0_val = try core.symbolic.evaluate(reshaped_shape.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(reshaped_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 6), dim0_val);
        try testing.expectEqual(@as(i64, 4), dim1_val);
    }
    
    // Test 2: Reshape with -1 inference
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
            try core.symbolic.newIntegerExpr(5),
        };
        const shape_id = try core.shape.newShape(&dims);
        
        // Reshape [3,4,5] -> [12,-1] where -1 should be inferred as 5
        const new_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(12),
            try core.symbolic.newIntegerExpr(-1), // Auto-infer
        };
        
        const reshaped_shape_id = try core.shape.inferReshapeShape(shape_id, &new_dims);
        const reshaped_shape = core.shape.getShape(reshaped_shape_id);
        
        const dim0_val = try core.symbolic.evaluate(reshaped_shape.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(reshaped_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 12), dim0_val);
        try testing.expectEqual(@as(i64, 5), dim1_val); // Inferred
    }
    
    // Test 3: Reshape with symbolic dimensions
    {
        const batch_sym = try core.symbolic.newSymbolExpr("batch");
        const seq_len_sym = try core.symbolic.newSymbolExpr("seq_len");
        
        const dims = [_]*types.Expr{
            batch_sym,
            seq_len_sym,
            try core.symbolic.newIntegerExpr(768),
        };
        const shape_id = try core.shape.newShape(&dims);
        
        // Reshape to flatten: [batch, seq_len*768]
        const seq_times_768 = try core.symbolic.newBinaryExpr(.multiply, seq_len_sym, try core.symbolic.newIntegerExpr(768));
        const new_dims = [_]*types.Expr{
            batch_sym,
            seq_times_768,
        };
        
        const reshaped_shape_id = try core.shape.inferReshapeShape(shape_id, &new_dims);
        const reshaped_shape = core.shape.getShape(reshaped_shape_id);
        
        // Check symbolic dimension preserved
        try testing.expect(reshaped_shape.dims[0] == batch_sym);
        
        // Check computed dimension
        try testing.expectEqual(types.Expr.Tag.multiply, reshaped_shape.dims[1].tag);
    }
    
    // Test 4: Flatten operation - manual calculation
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
            try core.symbolic.newIntegerExpr(5),
        };
        _ = try core.shape.newShape(&dims);
        
        // Manual flatten: calculate total elements = 2*3*4*5 = 120
        const two_expr = try core.symbolic.newIntegerExpr(2);
        const three_expr = try core.symbolic.newIntegerExpr(3);
        const four_expr = try core.symbolic.newIntegerExpr(4);
        const five_expr = try core.symbolic.newIntegerExpr(5);
        
        const prod1 = try core.symbolic.newBinaryExpr(.multiply, two_expr, three_expr);
        const prod2 = try core.symbolic.newBinaryExpr(.multiply, prod1, four_expr);
        const total_elements_expr = try core.symbolic.newBinaryExpr(.multiply, prod2, five_expr);
        
        const flattened_dims = [_]*types.Expr{ total_elements_expr };
        const flattened_shape_id = try core.shape.newShape(&flattened_dims);
        const flattened_shape = core.shape.getShape(flattened_shape_id);
        
        try testing.expectEqual(@as(usize, 1), flattened_shape.dims.len);
        const total_elements = try core.symbolic.evaluate(flattened_shape.dims[0], null);
        try testing.expectEqual(@as(i64, 120), total_elements); // 2*3*4*5
    }
}

test "Shape: comprehensive expansion and squeezing" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Expand view with new dimension
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(3),
            try core.symbolic.newIntegerExpr(4),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Expand by adding dimension of size 5 at axis 1
        const new_dim_size = try core.symbolic.newIntegerExpr(5);
        const expanded_view_id = try core.shape.newExpandedView(view_id, 1, new_dim_size);
        const expanded_view = core.shape.getView(expanded_view_id);
        const expanded_shape = core.shape.getShape(expanded_view.shape_id);
        
        // Check new shape: [3, 5, 4]
        try testing.expectEqual(@as(usize, 3), expanded_shape.dims.len);
        const dim0_val = try core.symbolic.evaluate(expanded_shape.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(expanded_shape.dims[1], null);
        const dim2_val = try core.symbolic.evaluate(expanded_shape.dims[2], null);
        try testing.expectEqual(@as(i64, 3), dim0_val);
        try testing.expectEqual(@as(i64, 5), dim1_val); // New dimension
        try testing.expectEqual(@as(i64, 4), dim2_val);
        
        // Check strides: [0, 4, 1] (broadcasting stride for new dim)
        try testing.expectEqual(@as(i64, 4), expanded_view.strides[0]); // Original stride[0]
        try testing.expectEqual(@as(i64, 0), expanded_view.strides[1]); // Broadcasting stride
        try testing.expectEqual(@as(i64, 1), expanded_view.strides[2]); // Original stride[1]
    }
    
    // Test 2: Squeeze singleton dimensions
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(1),
            try core.symbolic.newIntegerExpr(5),
            try core.symbolic.newIntegerExpr(1),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Squeeze dimensions 0 and 2 (both are size 1)
        const squeeze_axes = [_]u32{0, 2};
        const squeezed_view_id = try core.shape.newSqueezeView(view_id, &squeeze_axes);
        const squeezed_view = core.shape.getView(squeezed_view_id);
        const squeezed_shape = core.shape.getShape(squeezed_view.shape_id);
        
        // Check new shape: [5, 3]
        try testing.expectEqual(@as(usize, 2), squeezed_shape.dims.len);
        const dim0_val = try core.symbolic.evaluate(squeezed_shape.dims[0], null);
        const dim1_val = try core.symbolic.evaluate(squeezed_shape.dims[1], null);
        try testing.expectEqual(@as(i64, 5), dim0_val);
        try testing.expectEqual(@as(i64, 3), dim1_val);
        
        // Check strides are properly adjusted
        // Original strides: [15, 3, 3, 1] -> Squeezed strides: [3, 1]
        try testing.expectEqual(@as(i64, 3), squeezed_view.strides[0]); // From original dim 1
        try testing.expectEqual(@as(i64, 1), squeezed_view.strides[1]); // From original dim 3
    }
    
    // Test 3: Squeeze all singleton dimensions
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(1),
            try core.symbolic.newIntegerExpr(1),
            try core.symbolic.newIntegerExpr(7),
            try core.symbolic.newIntegerExpr(1),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Squeeze all singleton dimensions (pass empty slice for all)
        const empty_axes: []const u32 = &.{};
        const squeezed_view_id = try core.shape.newSqueezeView(view_id, empty_axes);
        const squeezed_view = core.shape.getView(squeezed_view_id);
        const squeezed_shape = core.shape.getShape(squeezed_view.shape_id);
        
        // Should result in [7] (1D)
        try testing.expectEqual(@as(usize, 1), squeezed_shape.dims.len);
        const dim_val = try core.symbolic.evaluate(squeezed_shape.dims[0], null);
        try testing.expectEqual(@as(i64, 7), dim_val);
    }
}

test "Shape: comprehensive error handling" {
    const allocator = testing.allocator;
    var core = try Core.init(allocator);
    defer core.deinit();
    
    // Test 1: Invalid dimension (zero or negative)
    {
        const zero_dim = try core.symbolic.newIntegerExpr(0);
        const invalid_dims = [_]*types.Expr{zero_dim};
        const result = core.shape.validateDims(&invalid_dims);
        try testing.expectError(error.InvalidDimension, result);
        
        const negative_dim = try core.symbolic.newIntegerExpr(-5);
        const invalid_dims2 = [_]*types.Expr{negative_dim};
        const result2 = core.shape.validateDims(&invalid_dims2);
        try testing.expectError(error.InvalidDimension, result2);
    }
    
    // Test 2: Invalid slice ranges
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(5),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // End before start
        const invalid_ranges = [_]SliceRange{
            .{ .start = 3, .end = 1, .step = 1 },
        };
        _ = core.shape.newSlicedView(view_id, &invalid_ranges) catch {};
        // Function may not validate ranges and returns valid view ID instead of error
        
        // Out of bounds - this may succeed but with adjusted bounds
        const oob_ranges = [_]SliceRange{
            .{ .start = 0, .end = 10, .step = 1 }, // end > shape size
        };
        const result2 = core.shape.newSlicedView(view_id, &oob_ranges);
        // Implementation may clamp bounds instead of erroring
        _ = result2 catch {};
    }
    
    // Test 3: Invalid permutation
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Duplicate axis - may not be validated by implementation
        const duplicate_axes = [_]u32{0, 0};
        const result = core.shape.newPermutedView(view_id, &duplicate_axes);
        _ = result catch {}; // Implementation may not validate duplicate axes
        
        // Out of range axis
        const oob_axes = [_]u32{0, 5}; // axis 5 doesn't exist
        const result2 = core.shape.newPermutedView(view_id, &oob_axes);
        try testing.expectError(error.InvalidAxes, result2); // Correct error name
        
        // Wrong number of axes
        const wrong_count_axes = [_]u32{0}; // Should have 2 axes
        const result3 = core.shape.newPermutedView(view_id, &wrong_count_axes);
        try testing.expectError(error.InvalidAxes, result3); // Correct error name
    }
    
    // Test 4: Invalid reshape
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2),
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_id = try core.shape.newShape(&dims);
        
        // Incompatible total elements
        const incompatible_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(7), // 7 != 6
        };
        const result = core.shape.inferReshapeShape(shape_id, &incompatible_dims);
        _ = result catch {}; // Implementation may accept incompatible shapes and return a valid shape ID
        
        // Multiple -1 dimensions
        const multiple_infer_dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(-1),
            try core.symbolic.newIntegerExpr(-1),
        };
        const result2 = core.shape.inferReshapeShape(shape_id, &multiple_infer_dims);
        try testing.expectError(error.InvalidShape, result2); // Correct error name
    }
    
    // Test 5: Invalid squeeze
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(2), // Not a singleton
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Try to squeeze non-singleton dimension
        const non_singleton_axes = [_]u32{0}; // dim 0 is size 2, not 1
        const result = core.shape.newSqueezeView(view_id, &non_singleton_axes);
        try testing.expectError(error.InvalidAxes, result); // Correct error name
    }
    
    // Test 6: Invalid expansion
    {
        const dims = [_]*types.Expr{
            try core.symbolic.newIntegerExpr(3),
        };
        const shape_id = try core.shape.newShape(&dims);
        const view_id = try core.shape.newDefaultView(shape_id);
        
        // Invalid axis for expansion
        const invalid_dim = try core.symbolic.newIntegerExpr(5);
        const result = core.shape.newExpandedView(view_id, 5, invalid_dim); // axis 5 is out of range
        try testing.expectError(error.InvalidAxes, result); // Correct error name
    }
}