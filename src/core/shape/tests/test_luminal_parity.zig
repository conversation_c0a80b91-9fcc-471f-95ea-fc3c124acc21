const std = @import("std");
const testing = std.testing;
const core_module = @import("core");
const Core = core_module.Core;
const types = core_module.types;

test "excise operation" {
    const allocator = testing.allocator;
    var test_core = try Core.init(allocator);
    defer test_core.deinit();
    
    const shape_eng = &test_core.shape;
    
    // Create a shape [10]
    const dim_10 = try test_core.symbolic.newIntegerExpr(10);
    const shape_id = try shape_eng.newShape(&[_]*types.Expr{dim_10});
    const view_id = try shape_eng.newDefaultView(shape_id);
    
    // Apply excise: cut out 2 elements every 4 elements
    // This should give us shape [6] (10 - 2*2 = 6)
    const excised_view = try shape_eng.newExciseView(view_id, 0, 2, 4);
    
    const excised_shape = shape_eng.getShape(shape_eng.getView(excised_view).shape_id);
    try std.testing.expectEqual(@as(usize, 1), excised_shape.dims.len);
    
    const excised_size = try test_core.symbolic.evaluate(excised_shape.dims[0], null);
    try std.testing.expectEqual(@as(i64, 6), excised_size);
}

test "pool last dim operation" {
    const allocator = testing.allocator;
    var test_core = try Core.init(allocator);
    defer test_core.deinit();
    
    const shape_eng = &test_core.shape;
    
    // Create a shape [5, 10]
    const dim_5 = try test_core.symbolic.newIntegerExpr(5);
    const dim_10 = try test_core.symbolic.newIntegerExpr(10);
    const shape_id = try shape_eng.newShape(&[_]*types.Expr{dim_5, dim_10});
    const view_id = try shape_eng.newDefaultView(shape_id);
    
    // Apply pool_last_dim with kernel=3, stride=2, dilation=0
    // Output shape should be [5, 4, 3]
    // 4 windows: (10 - 3) / 2 + 1 = 4
    const pooled_view = try shape_eng.newPoolLastDimView(view_id, 3, 2, 0);
    
    const pooled_shape = shape_eng.getShape(shape_eng.getView(pooled_view).shape_id);
    try std.testing.expectEqual(@as(usize, 3), pooled_shape.dims.len);
    
    const first_dim = try test_core.symbolic.evaluate(pooled_shape.dims[0], null);
    const num_windows = try test_core.symbolic.evaluate(pooled_shape.dims[1], null);
    const kernel_size = try test_core.symbolic.evaluate(pooled_shape.dims[2], null);
    
    try std.testing.expectEqual(@as(i64, 5), first_dim);
    try std.testing.expectEqual(@as(i64, 4), num_windows);
    try std.testing.expectEqual(@as(i64, 3), kernel_size);
}

test "pool shape inference" {
    const allocator = testing.allocator;
    var test_core = try Core.init(allocator);
    defer test_core.deinit();
    
    const shape_eng = &test_core.shape;
    
    // Create a shape [10, 20, 30]
    const dims = try allocator.alloc(*types.Expr, 3);
    dims[0] = try test_core.symbolic.newIntegerExpr(10);
    dims[1] = try test_core.symbolic.newIntegerExpr(20);
    dims[2] = try test_core.symbolic.newIntegerExpr(30);
    const shape_id = try shape_eng.newShape(dims);
    
    // Infer pool shape on axis 1 with kernel=4, stride=2, dilation=1
    // Effective kernel = 4 + (4-1)*1 = 7
    // Num windows = (20 - 7) / 2 + 1 = 7
    // Output shape should be [10, 7, 4, 30]
    const pooled_shape_id = try shape_eng.inferPoolShape(shape_id, 4, 2, 1, 1);
    const pooled_shape = shape_eng.getShape(pooled_shape_id);
    
    try std.testing.expectEqual(@as(usize, 4), pooled_shape.dims.len);
    
    const dim0 = try test_core.symbolic.evaluate(pooled_shape.dims[0], null);
    const dim1 = try test_core.symbolic.evaluate(pooled_shape.dims[1], null);
    const dim2 = try test_core.symbolic.evaluate(pooled_shape.dims[2], null);
    const dim3 = try test_core.symbolic.evaluate(pooled_shape.dims[3], null);
    
    try std.testing.expectEqual(@as(i64, 10), dim0);
    try std.testing.expectEqual(@as(i64, 7), dim1);
    try std.testing.expectEqual(@as(i64, 4), dim2);
    try std.testing.expectEqual(@as(i64, 30), dim3);
}

test "gather shape inference" {
    const allocator = testing.allocator;
    var test_core = try Core.init(allocator);
    defer test_core.deinit();
    
    const shape_eng = &test_core.shape;
    
    // Create input shape [5, 10, 15]
    const input_dims = try allocator.alloc(*types.Expr, 3);
    input_dims[0] = try test_core.symbolic.newIntegerExpr(5);
    input_dims[1] = try test_core.symbolic.newIntegerExpr(10);
    input_dims[2] = try test_core.symbolic.newIntegerExpr(15);
    const input_shape = try shape_eng.newShape(input_dims);
    
    // Create indices shape [3, 7]
    const indices_dims = try allocator.alloc(*types.Expr, 2);
    indices_dims[0] = try test_core.symbolic.newIntegerExpr(3);
    indices_dims[1] = try test_core.symbolic.newIntegerExpr(7);
    const indices_shape = try shape_eng.newShape(indices_dims);
    
    // Gather along axis 1
    // Output shape should be [5, 3, 7, 15]
    const gathered_shape_id = try shape_eng.inferGatherShape(input_shape, indices_shape, 1);
    const gathered_shape = shape_eng.getShape(gathered_shape_id);
    
    try std.testing.expectEqual(@as(usize, 4), gathered_shape.dims.len);
    
    const dim0 = try test_core.symbolic.evaluate(gathered_shape.dims[0], null);
    const dim1 = try test_core.symbolic.evaluate(gathered_shape.dims[1], null);
    const dim2 = try test_core.symbolic.evaluate(gathered_shape.dims[2], null);
    const dim3 = try test_core.symbolic.evaluate(gathered_shape.dims[3], null);
    
    try std.testing.expectEqual(@as(i64, 5), dim0);
    try std.testing.expectEqual(@as(i64, 3), dim1);
    try std.testing.expectEqual(@as(i64, 7), dim2);
    try std.testing.expectEqual(@as(i64, 15), dim3);
}