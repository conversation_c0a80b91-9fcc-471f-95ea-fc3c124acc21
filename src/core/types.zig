// Core types shared across all engines
const std = @import("std");

// ===== Type-safe ID system using enums (Zig 0.14 feature) =====

/// Generic type-safe ID generator
pub fn Id(comptime _: []const u8) type {
    return enum(u32) { 
        invalid = 0,
        _,
        
        pub fn isValid(self: @This()) bool {
            return self != .invalid;
        }
        
        pub fn eql(self: @This(), other: @This()) bool {
            return self == other;
        }
    };
}

/// Type-safe node identifier
pub const NodeId = Id("Node");

/// Type-safe view identifier
pub const ViewId = Id("View");

/// Type-safe shape identifier  
pub const ShapeId = Id("Shape");

// ===== Conversion helpers for type-safe IDs =====

pub fn nodeIdFromU32(id: u32) NodeId {
    return @as(NodeId, @enumFromInt(id));
}

pub fn nodeIdToU32(id: NodeId) u32 {
    return @intFromEnum(id);
}

pub fn viewIdFromU32(id: u32) ViewId {
    return @as(ViewId, @enumFromInt(id));
}

pub fn viewIdToU32(id: ViewId) u32 {
    return @intFromEnum(id);
}

pub fn shapeIdFromU32(id: u32) ShapeId {
    return @as(ShapeId, @enumFromInt(id));
}

pub fn shapeIdToU32(id: ShapeId) u32 {
    return @intFromEnum(id);
}

// ===== Data types =====

/// Data type enum for tensor elements
pub const DataType = enum {
    f16,
    f32,
    f64,
    i32,
    i64,
    u32,
    u64,
    bool,
    
    pub fn sizeInBytes(self: DataType) usize {
        return switch (self) {
            .f16 => 2,
            .f32 => 4,
            .f64 => 8,
            .i32 => 4,
            .i64 => 8,
            .u32 => 4,
            .u64 => 8,
            .bool => 1,
        };
    }
};

// ===== Shape types =====

/// Shape representation using expression dimensions
pub const Shape = struct {
    dims: []const *Expr,
};

/// Slice range for tensor slicing operations
pub const SliceRange = struct {
    start: i64,
    end: i64,
    step: i64 = 1,
};

/// Simplified view descriptor that replaces the complex ShapeTracker
pub const ViewDesc = struct {
    shape_id: ShapeId,          // Reference to shape in engine's pool
    strides: []const i64,       // Arena-allocated
    offset_elements: usize,
    
    // Validity expressions for safe indexing (matching Luminal)
    validity_expr: ?*Expr,      // Expression defining valid indices
    mask_expr: ?*Expr,          // Masking for padded regions
    
    // Explicit tracking for fake (broadcast) dimensions
    fake_dims: []const bool = &[_]bool{},  // Default to empty array
    
    /// Check if this view represents a contiguous memory layout
    pub fn isContiguous(self: ViewDesc, shape_engine: anytype) bool {
        const shape = shape_engine.getShape(self.shape_id);
        if (shape.dims.len == 0) return true; // Scalar is contiguous
        
        // Check if strides match expected dense layout
        var expected_stride: i64 = 1;
        var i = shape.dims.len;
        while (i > 0) {
            i -= 1;
            if (self.strides[i] != expected_stride) return false;
            
            // With pure expressions, try to evaluate the dimension
            const dim_size = shape_engine.core.symbolic.evaluate(shape.dims[i], null) catch return false;
            expected_stride *= dim_size;
        }
        return true;
    }
    
    /// Convert logical index to physical memory index
    pub fn getPhysicalIndex(self: ViewDesc, logical_index: usize, shape_engine: anytype) usize {
        const shape = shape_engine.getShape(self.shape_id);
        var physical_index = self.offset_elements;
        var remaining = logical_index;
        
        // Convert logical index to multi-dimensional indices
        var i = shape.dims.len;
        while (i > 0) {
            i -= 1;
            const dim_size = shape_engine.core.symbolic.evaluate(shape.dims[i], null) catch {
                // This method requires concrete dimensions - caller should validate
                return 0; // Will cause out-of-bounds if used with symbolic dims
            };
            const dim_size_usize: usize = @intCast(dim_size);
            const coord = remaining % dim_size_usize;
            remaining /= dim_size_usize;
            
            physical_index += coord * @as(usize, @intCast(self.strides[i]));
        }
        
        return physical_index;
    }
    
    /// Map logical to physical index (same as getPhysicalIndex for compatibility)
    pub fn mapLogicalToPhysical(self: ViewDesc, logical_index: usize) usize {
        // Simplified implementation - proper one would use the shape engine
        var physical = self.offset_elements;
        var idx = logical_index;
        
        // Reverse iterate through strides  
        var i = self.strides.len;
        while (i > 0) {
            i -= 1;
            const stride: usize = @intCast(@abs(self.strides[i]));
            if (stride > 0) {
                physical += (idx % stride) * stride;
                idx /= stride;
            }
        }
        
        return physical;
    }
    
    /// Get number of elements in this view
    pub fn getNumElements(self: ViewDesc) usize {
        _ = self;
        // This would need the shape engine to compute properly
        // For now return a placeholder
        return 0;
    }
    
    /// Check if a dimension is fake (broadcast)
    pub fn isFakeDim(self: ViewDesc, dim: usize) bool {
        if (dim >= self.fake_dims.len) return false;
        return self.fake_dims[dim];
    }
};

// ===== Sentinel-terminated types (Zig feature) =====

/// String with null terminator for C interop
pub const CString = [:0]const u8;

/// Symbol for named dimensions with guaranteed null termination
pub const Symbol = struct {
    name: CString,
    
    /// Create a new symbol (simplified version without interning)
    pub fn create(allocator: std.mem.Allocator, name: []const u8) !*Symbol {
        const symbol = try allocator.create(Symbol);
        const name_z = try allocator.dupeZ(u8, name);
        symbol.* = .{ .name = name_z };
        return symbol;
    }
    
    /// Destroy a symbol and free its memory
    pub fn destroy(self: *Symbol, allocator: std.mem.Allocator) void {
        allocator.free(self.name);
        allocator.destroy(self);
    }
    
    /// For compatibility with existing code that expects intern()
    pub fn intern(allocator: std.mem.Allocator, name: []const u8) !*Symbol {
        return create(allocator, name);
    }
};

// ===== Expression type with comptime validation =====

/// Expression type for tensor shape arithmetic
pub const Expr = struct {
    tag: Tag,
    data: Data,
    
    // Note: testInt/testDim helpers removed as part of EXPRESSION_REFACTOR_PLAN.md
    // All tests now use proper Core architecture with SymbolicEngine.newIntegerExpr()
    
    /// Structural equality comparison (replaces unsafe pointer comparison)
    pub fn eql(self: *const Expr, other: *const Expr) bool {
        if (self.tag != other.tag) return false;
        
        return switch (self.tag) {
            .integer => self.data.integer == other.data.integer,
            .symbol => std.mem.eql(u8, self.data.symbol.name, other.data.symbol.name),
            .add, .subtract, .multiply, .divide, .mod,
            .equal, .not_equal, .less_than, .greater_than, .less_equal, .greater_equal,
            .max, .min => {
                return self.data.binary.left.eql(other.data.binary.left) and 
                       self.data.binary.right.eql(other.data.binary.right);
            },
        };
    }
    
    /// Hash for use in hash maps
    pub fn hash(self: *const Expr) u64 {
        var hasher = std.hash.Wyhash.init(0);
        hasher.update(std.mem.asBytes(&self.tag));
        switch (self.tag) {
            .integer => hasher.update(std.mem.asBytes(&self.data.integer)),
            .symbol => hasher.update(self.data.symbol.name),
            .add, .subtract, .multiply, .divide, .mod,
            .equal, .not_equal, .less_than, .greater_than, .less_equal, .greater_equal,
            .max, .min => {
                hasher.update(std.mem.asBytes(&self.data.binary.left.hash()));
                hasher.update(std.mem.asBytes(&self.data.binary.right.hash()));
            },
        }
        return hasher.final();
    }
    
    pub const Tag = enum(u8) {
        // Values
        integer,      // Concrete dimension value
        symbol,       // Named dimension (e.g., "batch_size")
        
        // Arithmetic operations
        add,          // Addition for concatenation
        subtract,     // Subtraction
        multiply,     // Multiplication for reshape/repeat
        divide,       // Division (integer)
        mod,          // Modulo
        
        // Comparison operations
        equal,        // Equality comparison
        not_equal,    // Not equal
        less_than,    // Less than
        greater_than, // Greater than
        less_equal,   // Less than or equal
        greater_equal,// Greater than or equal
        
        // Aggregation operations
        max,          // Maximum for broadcasting
        min,          // Minimum for slicing
        
        /// Comptime validation of binary operations
        pub fn isBinary(comptime self: Tag) bool {
            return switch (self) {
                .integer, .symbol => false,
                else => true,
            };
        }
    };
    
    pub const Data = union {
        integer: i64,
        symbol: *Symbol,
        binary: struct { left: *Expr, right: *Expr },
    };
};

// Note: Dim union removed - using *Expr directly throughout codebase
// This eliminates concrete/symbolic pattern matching for cleaner architecture

// ===== Binary operation type with metadata =====

pub const BinaryOp = enum {
    // Arithmetic
    add,
    subtract,
    multiply,
    divide,
    mod,
    
    // Comparison
    equal,
    not_equal,
    less_than,
    greater_than,
    less_equal,
    greater_equal,
    
    // Aggregation
    max,
    min,
    
    // Logical
    or_,
};

// ===== Constraint types with better error handling =====

/// Constraint system for symbolic expressions
pub const Constraint = union(enum) {
    equal: Pair,
    not_equal: Pair,
    less_than: Pair,
    greater_than: Pair,
    less_equal: Pair,
    greater_equal: Pair,
    or_: struct { constraints: []Constraint },
    
    pub const Pair = struct { lhs: *Expr, rhs: *Expr };
    
    /// Visitor pattern for constraint traversal
    pub fn visit(self: Constraint, visitor: anytype) !void {
        switch (self) {
            .equal, .not_equal, .less_than, .greater_than, .less_equal, .greater_equal => |pair| {
                try visitor.visitPair(self, pair);
            },
            .or_ => |or_constraint| {
                try visitor.visitOr(or_constraint.constraints);
            },
        }
    }
};

// ===== Range type with builder pattern =====

/// Range for slice operations
pub const Range = struct {
    start: ?i64 = null,  // null means beginning
    end: ?i64 = null,    // null means end
    step: i64 = 1,       // Never null, default 1
};

// ===== Memory-aligned buffer type =====

/// Tensor buffer with optimal alignment
pub const TensorBuffer = struct {
    data: []align(32) u8,  // AVX alignment for SIMD
    dtype: DataType,
    
    // V2: Using DataType from graph module for consistency
    // DataType is defined above in this file
    
    /// Get typed view of the data
    pub fn asType(self: TensorBuffer, comptime T: type) []align(32) T {
        const type_size = @sizeOf(T);
        const count = self.data.len / type_size;
        return @as([*]align(32) T, @ptrCast(@alignCast(self.data.ptr)))[0..count];
    }
};

// ===== Backend type (required by Core) =====

/// Backend interface for different execution targets
pub const Backend = struct {
    name: []const u8,
    
    // Function pointers for backend operations
    init: *const fn (allocator: std.mem.Allocator) anyerror!*Backend,
    deinit: *const fn (self: *Backend) void,
    compile: ?*const fn (self: *Backend, graph: *anyopaque) anyerror!void = null,
    execute: ?*const fn (self: *Backend, graph: *anyopaque) anyerror!void = null,
};

/// Enhanced backend capabilities for V2 compiler system
pub const BackendCapability = packed struct {
    compile: bool = true,
    execute: bool = true,
    optimize: bool = false,
    profile: bool = false,
    distributed: bool = false,
};

/// Enhanced backend interface that maintains compatibility
pub const BackendV2 = struct {
    // Keep reference to legacy backend if needed
    legacy: ?*Backend = null,
    
    // Basic info
    name: []const u8,
    capabilities: BackendCapability,
    
    // Lifecycle
    initWithCore: *const fn (core: *anyopaque) anyerror!*BackendV2,
    deinit: *const fn (self: *BackendV2) void,
    
    // Compilation - returns compiled graph
    compileGraph: *const fn (
        self: *BackendV2,
        engine: *anyopaque,
        plan: *anyopaque,
    ) anyerror!*anyopaque,
    
    // Execution using operator inputs/outputs
    executeGraph: *const fn (
        self: *BackendV2,
        compiled: *anyopaque,
        inputs: []const *anyopaque,
    ) anyerror![]*anyopaque,
    
    // Optional: Memory planning
    planMemory: ?*const fn (
        self: *BackendV2,
        plan: *anyopaque,
    ) anyerror!void = null,
};


