const std = @import("std");
const types = @import("types.zig");
const graph_types = @import("graph/types.zig");
const errors = @import("errors.zig");
const ShapeEngine = @import("shape/engine.zig").ShapeEngine;

// Forward declare Core to avoid circular dependency
const Core = @import("core.zig").Core;

const Allocator = std.mem.Allocator;
const DataType = graph_types.DataType;
const Shape = types.Shape;

/// Error types specific to the DataStore module
pub const DataError = error{
    // Data retrieval errors
    DataNotFound,
    ConstantNotFound,
    ParameterNotFound,
    InputNotFound,
    
    // Pattern generation errors
    PatternNotImplemented,
    LinspaceNotImplemented,
    
    // Type mismatch errors
    TypeMismatch,
    InvalidDataType,
    NotAPattern,
    
    // Size and shape errors
    SizeMismatch,
    InvalidDimension,
    
    // Gradient errors
    GradientNotAllocated,
    GradientAlreadyExists,
    ParameterFrozen,
    
    // Memory errors
    BufferTooLarge,
    InvalidAlignment,
};

/// Centralized storage for all tensor data in the computation graph
pub const DataStore = struct {
    /// Storage for constant tensor data
    constants: std.AutoHashMapUnmanaged(types.NodeId, ConstantData),
    /// Storage for variable (trainable parameter) data
    parameters: std.AutoHashMapUnmanaged(types.NodeId, ParameterData),
    /// Registry for input placeholders
    inputs: std.AutoHashMapUnmanaged(types.NodeId, InputInfo),
    /// Allocator for memory management
    allocator: Allocator,
    
    /// Statistics tracking
    stats: DataStats = .{},
    
    const Self = @This();
    
    pub const DataStats = struct {
        constants_count: usize = 0,
        parameters_count: usize = 0,
        inputs_count: usize = 0,
        total_memory: usize = 0,
        gradients_allocated: usize = 0,
    };
    
    /// Initialize the data store
    pub fn init(allocator: Allocator) !DataStore {
        return .{
            .constants = .{},
            .parameters = .{},
            .inputs = .{},
            .allocator = allocator,
        };
    }
    
    /// Clean up all stored data
    pub fn deinit(self: *Self) void {
        
        // Free constant data
        var const_iter = self.constants.iterator();
        while (const_iter.next()) |entry| {
            if (entry.value_ptr.value == .literal) {
                self.allocator.free(entry.value_ptr.value.literal.bytes);
            }
        }
        self.constants.deinit(self.allocator);
        
        // Free parameter data
        var param_iter = self.parameters.iterator();
        while (param_iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.data);
            if (entry.value_ptr.gradient) |grad| {
                self.allocator.free(grad);
            }
        }
        self.parameters.deinit(self.allocator);
        
        // Free input registry
        var input_iter = self.inputs.iterator();
        while (input_iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.name);
        }
        self.inputs.deinit(self.allocator);
    }
    
    /// Reset the data store to initial state
    pub fn reset(self: *Self) void {
        
        // Clear with proper cleanup
        var const_iter = self.constants.iterator();
        while (const_iter.next()) |entry| {
            if (entry.value_ptr.value == .literal) {
                self.allocator.free(entry.value_ptr.value.literal.bytes);
            }
        }
        self.constants.clearRetainingCapacity();
        
        var param_iter = self.parameters.iterator();
        while (param_iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.data);
            if (entry.value_ptr.gradient) |grad| {
                self.allocator.free(grad);
            }
        }
        self.parameters.clearRetainingCapacity();
        
        var input_iter = self.inputs.iterator();
        while (input_iter.next()) |entry| {
            self.allocator.free(entry.value_ptr.name);
        }
        self.inputs.clearRetainingCapacity();
        
        self.stats = .{};
    }
    
    /// Get data store statistics
    pub fn getStats(self: *const Self) DataStats {
        return self.stats;
    }
    
    // ===== Constants =====
    
    /// Data for constant tensors
    pub const ConstantData = struct {
        value: ConstantValue,
        dtype: DataType,
        shape_id: u32,
    };
    
    /// Types of constant values
    pub const ConstantValue = union(enum) {
        /// Pattern-based constants
        pattern: Pattern,
        /// Literal data
        literal: LiteralData,
    };
    
    /// Common patterns for tensor generation
    pub const Pattern = enum {
        zeros,
        ones,
        identity,
        arange,
        linspace,
    };
    
    /// Literal tensor data
    pub const LiteralData = struct {
        /// Raw bytes aligned for optimal access
        bytes: []align(@alignOf(f64)) u8,
        
        /// Get data as typed slice
        pub fn asSlice(self: *const LiteralData, comptime T: type) []const T {
            return std.mem.bytesAsSlice(T, self.bytes);
        }
        
        /// Get mutable data as typed slice
        pub fn asSliceMut(self: *LiteralData, comptime T: type) []T {
            return std.mem.bytesAsSlice(T, self.bytes);
        }
    };
    
    /// Register a constant with a pattern
    pub fn newConstantPattern(
        self: *Self,
        node_id: types.NodeId,
        pattern: Pattern,
        dtype: DataType,
        shape_id: u32,
    ) !void {
        try self.constants.put(self.allocator, node_id, .{
            .value = .{ .pattern = pattern },
            .dtype = dtype,
            .shape_id = shape_id,
        });
        self.stats.constants_count += 1;
    }
    
    /// Register a constant with literal data
    pub fn newConstantLiteral(
        self: *Self,
        comptime T: type,
        node_id: types.NodeId,
        data: []const T,
        shape_id: u32,
    ) !void {
        const dtype = comptime dtypeFromType(T);
        const bytes = try self.allocator.alignedAlloc(u8, @alignOf(f64), data.len * @sizeOf(T));
        @memcpy(bytes, std.mem.sliceAsBytes(data));
        self.stats.total_memory += bytes.len;
        
        try self.constants.put(self.allocator, node_id, .{
            .value = .{ .literal = .{ .bytes = bytes } },
            .dtype = dtype,
            .shape_id = shape_id,
        });
        self.stats.constants_count += 1;
    }
    
    /// Get constant data for a node
    pub fn getConstant(self: *Self, node_id: types.NodeId) ?ConstantData {
        return self.constants.get(node_id);
    }
    
    /// Helper method to set constant data (used by tensor creation)
    pub fn setConstantData(self: *Self, comptime T: type, node_id: types.NodeId, data: []const T, core: *Core) !void {
        // Get the shape ID from graph node's output view
        const node = core.graph.getNode(node_id) orelse return DataError.ConstantNotFound;
        const view = core.shape.getView(node.output_view_id);
        const shape_id = @intFromEnum(view.shape_id);
        try self.newConstantLiteral(T, node_id, data, shape_id);
    }
    
    /// Helper method to set constant pattern (used by tensor creation)
    pub fn setConstantPattern(self: *Self, node_id: types.NodeId, pattern: Pattern) !void {
        // Find the shape ID and dtype from graph node
        const shape_id = 0;  // TODO: get from node's output_view_id
        const dtype = DataType.f32;  // TODO: get from node
        try self.newConstantPattern(node_id, pattern, dtype, shape_id);
    }
    
    /// Helper method to set constant full value
    pub fn setConstantFull(self: *Self, node_id: types.NodeId, value: f32) !void {
        const shape_id = 0;  // TODO: get from node's output_view_id
        const dtype = DataType.f32;  // TODO: get from node
        // Create a pattern with value
        _ = value;  // TODO: implement full pattern with custom value
        try self.newConstantPattern(node_id, .ones, dtype, shape_id);
    }
    
    /// Check if a node has constant data
    pub fn hasConstantData(self: *Self, node_id: types.NodeId) bool {
        return self.constants.contains(node_id);
    }
    
    /// Get constant data as typed slice
    pub fn getConstantData(self: *Self, comptime T: type, node_id: types.NodeId) ![]const T {
        const constant = self.constants.get(node_id) orelse return DataError.ConstantNotFound;
        
        switch (constant.value) {
            .pattern => |pattern| {
                // Generate pattern data on the fly
                // TODO: get actual shape from shape engine
                _ = constant.shape_id;  // TODO: use to get actual shape
                const num_elements = 1;  // Placeholder
                const bytes = try self.generatePattern(pattern, constant.dtype, num_elements, self.allocator);
                return std.mem.bytesAsSlice(T, bytes);
            },
            .literal => |literal| {
                return literal.asSlice(T);
            },
        }
    }
    
    /// Get constant pattern if applicable
    pub fn getConstantPattern(self: *Self, node_id: types.NodeId) !Pattern {
        const constant = self.constants.get(node_id) orelse return DataError.ConstantNotFound;
        switch (constant.value) {
            .pattern => |pattern| return pattern,
            .literal => return DataError.NotAPattern,
        }
    }
    
    // ===== Parameters =====
    
    /// Data for variable (trainable) tensors
    pub const ParameterData = struct {
        data: []align(@alignOf(f64)) u8,
        gradient: ?[]align(@alignOf(f64)) u8,
        dtype: DataType,
        shape_id: u32,
        requires_grad: bool,
        is_frozen: bool,
    };
    
    /// Register a new parameter
    pub fn newParameter(
        self: *Self,
        comptime T: type,
        node_id: types.NodeId,
        initial_value: []const T,
        shape_id: u32,
        requires_grad: bool,
    ) !void {
        const dtype = comptime dtypeFromType(T);
        const data = try self.allocator.alignedAlloc(u8, @alignOf(f64), initial_value.len * @sizeOf(T));
        @memcpy(data, std.mem.sliceAsBytes(initial_value));
        self.stats.total_memory += data.len;
        
        try self.parameters.put(self.allocator, node_id, .{
            .data = data,
            .gradient = null,
            .dtype = dtype,
            .shape_id = shape_id,
            .requires_grad = requires_grad,
            .is_frozen = false,
        });
        self.stats.parameters_count += 1;
    }
    
    /// Get parameter data for a node
    pub fn getParameter(self: *Self, node_id: types.NodeId) ?*ParameterData {
        return self.parameters.getPtr(node_id);
    }
    
    /// Allocate gradient buffer for a parameter
    pub fn allocateGradient(self: *Self, node_id: types.NodeId) !void {
        if (self.parameters.getPtr(node_id)) |param| {
            if (param.gradient == null and param.requires_grad) {
                param.gradient = try self.allocator.alignedAlloc(
                    u8,
                    @alignOf(f64),
                    param.data.len,
                );
                @memset(param.gradient.?, 0);
                self.stats.gradients_allocated += 1;
                self.stats.total_memory += param.data.len;
            }
        }
    }
    
    /// Freeze a parameter (make it non-trainable)
    pub fn freezeParameter(self: *Self, node_id: types.NodeId) void {
        if (self.parameters.getPtr(node_id)) |param| {
            param.is_frozen = true;
            param.requires_grad = false;
        }
    }
    
    // ===== Inputs =====
    
    /// Information about input placeholders
    pub const InputInfo = struct {
        dtype: DataType,
        shape_id: u32,
        name: []const u8,
    };
    
    /// Register an input placeholder
    pub fn newInput(
        self: *Self,
        node_id: types.NodeId,
        name: []const u8,
        dtype: DataType,
        shape_id: u32,
    ) !void {
        const name_copy = try self.allocator.dupe(u8, name);
        try self.inputs.put(self.allocator, node_id, .{
            .dtype = dtype,
            .shape_id = shape_id,
            .name = name_copy,
        });
        self.stats.inputs_count += 1;
    }
    
    /// Get input info for a node
    pub fn getInput(self: *Self, node_id: types.NodeId) ?InputInfo {
        return self.inputs.get(node_id);
    }
    
    /// Helper method to register a parameter (used by tensor creation)
    pub fn registerParameter(self: *Self, node_id: types.NodeId) !void {
        // Initialize with empty data for now
        const shape_id = 0;  // TODO: get from node's output_view_id
        const initial_value = &[_]f32{};  // Empty initial value
        try self.newParameter(f32, node_id, initial_value, shape_id, true);
    }
    
    /// Helper method to register an input (used by tensor creation)
    pub fn registerInput(self: *Self, node_id: types.NodeId) !void {
        const shape_id = 0;  // TODO: get from node's output_view_id
        const dtype = DataType.f32;  // TODO: get from node
        try self.newInput(node_id, "input", dtype, shape_id);
    }
    
    /// Check if a node is a parameter
    pub fn isParameter(self: *Self, node_id: types.NodeId) bool {
        return self.parameters.contains(node_id);
    }
    
    /// Check if a node is an input
    pub fn isInput(self: *Self, node_id: types.NodeId) bool {
        return self.inputs.contains(node_id);
    }
    
    // ===== Data Materialization =====
    
    /// Materialize constant data for execution
    pub fn materializeConstant(
        self: *Self,
        node_id: types.NodeId,
        shape: Shape,
        temp_allocator: Allocator,
    ) ![]align(@alignOf(f64)) u8 {
        const constant = self.getConstant(node_id) orelse return DataError.ConstantNotFound;
        const num_elements = try shape.numElements();
        const bytes_per_element = constant.dtype.sizeInBytes();
        const total_bytes = num_elements * bytes_per_element;
        
        return switch (constant.value) {
            .pattern => |pattern| try self.generatePattern(
                pattern,
                constant.dtype,
                num_elements,
                temp_allocator,
            ),
            .literal => |literal| blk: {
                if (literal.bytes.len != total_bytes) {
                    return DataError.SizeMismatch;
                }
                break :blk try temp_allocator.dupe(u8, literal.bytes);
            },
        };
    }
    
    /// Generate pattern-based data
    fn generatePattern(
        self: *Self,
        pattern: Pattern,
        dtype: DataType,
        num_elements: usize,
        allocator: Allocator,
    ) ![]align(@alignOf(f64)) u8 {
        _ = self;
        const bytes_per_element = dtype.sizeInBytes();
        const bytes = try allocator.alignedAlloc(u8, @alignOf(f64), num_elements * bytes_per_element);
        
        // Generate pattern based on data type
        switch (dtype) {
            .f32 => try generatePatternTyped(f32, pattern, bytes, num_elements),
            .f64 => try generatePatternTyped(f64, pattern, bytes, num_elements),
            .i32 => try generatePatternTyped(i32, pattern, bytes, num_elements),
            .i64 => try generatePatternTyped(i64, pattern, bytes, num_elements),
            .u32 => try generatePatternTyped(u32, pattern, bytes, num_elements),
            .u64 => try generatePatternTyped(u64, pattern, bytes, num_elements),
            .f16 => try generatePatternTyped(f16, pattern, bytes, num_elements),
            .bool => try generatePatternTyped(bool, pattern, bytes, num_elements),
        }
        
        return bytes;
    }
    
    /// Generate pattern for specific type
    fn generatePatternTyped(
        comptime T: type,
        pattern: Pattern,
        bytes: []u8,
        num_elements: usize,
    ) !void {
        const data = std.mem.bytesAsSlice(T, bytes);
        
        switch (pattern) {
            .zeros => {
                const zero: T = switch (@typeInfo(T)) {
                    .bool => false,
                    .int => 0,
                    .float => 0.0,
                    else => @compileError("Unsupported type for zeros pattern"),
                };
                @memset(data, zero);
            },
            .ones => {
                const one: T = switch (@typeInfo(T)) {
                    .bool => true,
                    .int => 1,
                    .float => 1.0,
                    else => @compileError("Unsupported type for ones pattern"),
                };
                @memset(data, one);
            },
            .identity => {
                const zero: T = switch (@typeInfo(T)) {
                    .bool => false,
                    .int => 0,
                    .float => 0.0,
                    else => @compileError("Unsupported type for identity pattern"),
                };
                @memset(data, zero);
                const n = std.math.sqrt(num_elements);
                for (0..n) |i| {
                    const one: T = switch (@typeInfo(T)) {
                        .bool => true,
                        .int => 1,
                        .float => 1.0,
                        else => @compileError("Unsupported type for identity pattern"),
                    };
                    data[i * n + i] = one;
                }
            },
            .arange => {
                for (data, 0..) |*v, i| {
                    v.* = switch (@typeInfo(T)) {
                        .int => @as(T, @intCast(i)),
                        .float => @as(T, @floatFromInt(i)),
                        .bool => i % 2 == 1,  // alternating true/false
                        else => @compileError("Unsupported type for arange pattern"),
                    };
                }
            },
            .linspace => {
                // Linspace needs additional parameters - not implemented in this basic version
                return DataError.LinspaceNotImplemented;
            },
        }
    }
    
    // ===== Type Conversion Utilities =====
    
    /// Convert Zig type to DataType enum at compile time
    fn dtypeFromType(comptime T: type) DataType {
        return switch (T) {
            f16 => .f16,
            f32 => .f32,
            f64 => .f64,
            i32 => .i32,
            i64 => .i64,
            u32 => .u32,
            u64 => .u64,
            bool => .bool,
            else => @compileError("Unsupported tensor data type"),
        };
    }
    
};