const std = @import("std");
const Core = @import("../core/core.zig").Core;
const ops = @import("../ops/operations.zig");

pub const OptimizerConfig = struct {
    lr: f32 = 0.001,
    weight_decay: f32 = 0.0,
    gradient_clipping: ?f32 = null,
};

pub const Optimizer = struct {
    const Self = @This();
    
    core: *Core,
    config: OptimizerConfig,
    parameters: std.ArrayList(u32),
    
    pub const VTable = struct {
        step: *const fn (*Optimizer, []const u32) anyerror!void,
        zero_grad: *const fn (*Optimizer) anyerror!void,
    };
    
    vtable: VTable,
    
    pub fn init(core: *Core, config: OptimizerConfig) !Self {
        return Self{
            .core = core,
            .config = config,
            .parameters = std.ArrayList(u32).init(core.allocator),
            .vtable = undefined, // Must be set by concrete implementation
        };
    }
    
    pub fn deinit(self: *Self) void {
        self.parameters.deinit();
    }
    
    pub fn add_parameter(self: *Self, param_id: u32) !void {
        try self.parameters.append(param_id);
    }
    
    pub fn zero_grad(self: *Self) !void {
        try self.vtable.zero_grad(self);
    }
    
    pub fn step(self: *Self, gradients: []const u32) !void {
        try self.vtable.step(self, gradients);
    }
};

pub const SGD = struct {
    const Self = @This();
    
    optimizer: Optimizer,
    momentum: f32 = 0.0,
    momentum_buffers: std.AutoHashMap(u32, u32), // parameter_id -> momentum_buffer_id
    
    pub fn init(core: *Core, config: OptimizerConfig, momentum: f32) !Self {
        var self = Self{
            .optimizer = try Optimizer.init(core, config),
            .momentum = momentum,
            .momentum_buffers = std.AutoHashMap(u32, u32).init(core.allocator),
        };
        
        self.optimizer.vtable = .{
            .step = step,
            .zero_grad = zero_grad,
        };
        
        return self;
    }
    
    pub fn deinit(self: *Self) void {
        self.momentum_buffers.deinit();
        self.optimizer.deinit();
    }
    
    fn zero_grad(optimizer: *Optimizer) !void {
        const self = @fieldParentPtr(SGD, "optimizer", optimizer);
        for (optimizer.parameters.items) |param_id| {
            // Clear gradient on the parameter node
            const node = &optimizer.core.graph.nodes.items[param_id];
            node.gradient_id = null;
        }
    }
    
    fn step(optimizer: *Optimizer, gradients: []const u32) !void {
        const self = @fieldParentPtr(SGD, "optimizer", optimizer);
        const core = optimizer.core;
        
        if (gradients.len != optimizer.parameters.items.len) {
            return error.MismatchedGradients;
        }
        
        for (optimizer.parameters.items, gradients) |param_id, grad_id| {
            var update = grad_id;
            
            // Apply momentum if configured
            if (self.momentum > 0) {
                const momentum_result = try self.momentum_buffers.getOrPut(param_id);
                
                if (!momentum_result.found_existing) {
                    // Initialize momentum buffer
                    momentum_result.value_ptr.* = try ops.zeros_like(core, param_id);
                }
                
                const buf_id = momentum_result.value_ptr.*;
                
                // v = momentum * v + grad
                const momentum_val = try ops.scalar(core, self.momentum, core.graph.nodes.items[grad_id].dtype);
                const scaled_buf = try ops.multiply(core, buf_id, momentum_val);
                update = try ops.add(core, scaled_buf, grad_id);
                
                // Update momentum buffer
                momentum_result.value_ptr.* = update;
            }
            
            // Apply weight decay if configured
            if (optimizer.config.weight_decay > 0) {
                const decay_val = try ops.scalar(core, optimizer.config.weight_decay, core.graph.nodes.items[param_id].dtype);
                const decay_term = try ops.multiply(core, param_id, decay_val);
                update = try ops.add(core, update, decay_term);
            }
            
            // Apply gradient clipping if configured
            if (optimizer.config.gradient_clipping) |clip_val| {
                update = try ops.clip(core, update, -clip_val, clip_val);
            }
            
            // Apply learning rate
            const lr_val = try ops.scalar(core, optimizer.config.lr, core.graph.nodes.items[update].dtype);
            const scaled_update = try ops.multiply(core, update, lr_val);
            
            // Update parameter: param = param - lr * grad
            const new_param = try ops.subtract(core, param_id, scaled_update);
            
            // Update the parameter node value (this would be handled by the execution engine)
            // For now, we just update the node reference
            core.graph.nodes.items[param_id] = core.graph.nodes.items[new_param];
        }
    }
};

pub const Adam = struct {
    const Self = @This();
    
    optimizer: Optimizer,
    beta1: f32 = 0.9,
    beta2: f32 = 0.999,
    eps: f32 = 1e-8,
    step_count: u32 = 0,
    m_buffers: std.AutoHashMap(u32, u32), // parameter_id -> first moment buffer
    v_buffers: std.AutoHashMap(u32, u32), // parameter_id -> second moment buffer
    
    pub fn init(core: *Core, config: OptimizerConfig, beta1: f32, beta2: f32, eps: f32) !Self {
        var self = Self{
            .optimizer = try Optimizer.init(core, config),
            .beta1 = beta1,
            .beta2 = beta2,
            .eps = eps,
            .m_buffers = std.AutoHashMap(u32, u32).init(core.allocator),
            .v_buffers = std.AutoHashMap(u32, u32).init(core.allocator),
        };
        
        self.optimizer.vtable = .{
            .step = step,
            .zero_grad = zero_grad,
        };
        
        return self;
    }
    
    pub fn deinit(self: *Self) void {
        self.m_buffers.deinit();
        self.v_buffers.deinit();
        self.optimizer.deinit();
    }
    
    fn zero_grad(optimizer: *Optimizer) !void {
        const self = @fieldParentPtr(Adam, "optimizer", optimizer);
        for (optimizer.parameters.items) |param_id| {
            const node = &optimizer.core.graph.nodes.items[param_id];
            node.gradient_id = null;
        }
    }
    
    fn step(optimizer: *Optimizer, gradients: []const u32) !void {
        const self = @fieldParentPtr(Adam, "optimizer", optimizer);
        const core = optimizer.core;
        
        if (gradients.len != optimizer.parameters.items.len) {
            return error.MismatchedGradients;
        }
        
        self.step_count += 1;
        const float_step = @as(f32, @floatFromInt(self.step_count));
        
        // Bias correction
        const bias_correction1 = 1.0 - std.math.pow(f32, self.beta1, float_step);
        const bias_correction2 = 1.0 - std.math.pow(f32, self.beta2, float_step);
        
        for (optimizer.parameters.items, gradients) |param_id, grad_id| {
            // Get or create moment buffers
            const m_result = try self.m_buffers.getOrPut(param_id);
            if (!m_result.found_existing) {
                m_result.value_ptr.* = try ops.zeros_like(core, param_id);
            }
            const m_id = m_result.value_ptr.*;
            
            const v_result = try self.v_buffers.getOrPut(param_id);
            if (!v_result.found_existing) {
                v_result.value_ptr.* = try ops.zeros_like(core, param_id);
            }
            const v_id = v_result.value_ptr.*;
            
            // Update biased first moment estimate
            // m = beta1 * m + (1 - beta1) * grad
            const beta1_val = try ops.scalar(core, self.beta1, core.graph.nodes.items[grad_id].dtype);
            const one_minus_beta1 = try ops.scalar(core, 1.0 - self.beta1, core.graph.nodes.items[grad_id].dtype);
            
            const m_scaled = try ops.multiply(core, m_id, beta1_val);
            const grad_scaled = try ops.multiply(core, grad_id, one_minus_beta1);
            const new_m = try ops.add(core, m_scaled, grad_scaled);
            
            // Update biased second moment estimate
            // v = beta2 * v + (1 - beta2) * grad^2
            const beta2_val = try ops.scalar(core, self.beta2, core.graph.nodes.items[grad_id].dtype);
            const one_minus_beta2 = try ops.scalar(core, 1.0 - self.beta2, core.graph.nodes.items[grad_id].dtype);
            
            const grad_squared = try ops.multiply(core, grad_id, grad_id);
            const v_scaled = try ops.multiply(core, v_id, beta2_val);
            const grad_sq_scaled = try ops.multiply(core, grad_squared, one_minus_beta2);
            const new_v = try ops.add(core, v_scaled, grad_sq_scaled);
            
            // Apply bias correction
            const bc1_val = try ops.scalar(core, bias_correction1, core.graph.nodes.items[new_m].dtype);
            const bc2_val = try ops.scalar(core, bias_correction2, core.graph.nodes.items[new_v].dtype);
            
            const m_corrected = try ops.divide(core, new_m, bc1_val);
            const v_corrected = try ops.divide(core, new_v, bc2_val);
            
            // Compute update
            const eps_val = try ops.scalar(core, self.eps, core.graph.nodes.items[v_corrected].dtype);
            const v_eps = try ops.add(core, v_corrected, eps_val);
            const v_sqrt = try ops.sqrt(core, v_eps);
            const update = try ops.divide(core, m_corrected, v_sqrt);
            
            // Apply learning rate
            const lr_val = try ops.scalar(core, optimizer.config.lr, core.graph.nodes.items[update].dtype);
            const scaled_update = try ops.multiply(core, update, lr_val);
            
            // Update parameter
            const new_param = try ops.subtract(core, param_id, scaled_update);
            
            // Update buffers
            m_result.value_ptr.* = new_m;
            v_result.value_ptr.* = new_v;
            
            // Update the parameter node
            core.graph.nodes.items[param_id] = core.graph.nodes.items[new_param];
        }
    }
};