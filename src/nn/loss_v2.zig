// Loss functions - V2 architecture
const std = @import("std");
const Core = @import("../core/core.zig").Core;
const types = @import("../core/types.zig");
const ops = @import("../ops/prelude.zig");

/// Reduction method for loss functions
pub const Reduction = enum {
    none,   // No reduction, return loss per element
    sum,    // Sum all losses  
    mean,   // Average all losses
};

/// Mean Squared Error (MSE) loss
pub const MSELoss = struct {
    reduction: Reduction,
    
    const Self = @This();
    
    pub fn init(reduction: Reduction) MSELoss {
        return .{ .reduction = reduction };
    }
    
    pub fn forward(self: *const Self, core: *Core, predictions: u32, targets: u32) !u32 {
        // Compute squared difference: (predictions - targets)^2
        const diff = try ops.subtract(core, predictions, targets);
        var squared_diff = try ops.multiply(core, diff, diff);
        
        // Apply reduction
        switch (self.reduction) {
            .none => return squared_diff,
            .sum => {
                // Sum all elements
                const node = core.graph.getNode(squared_diff);
                const view = core.shape.getView(node.output_view_id);
                const shape = core.shape.getShape(view.shape_id);
                
                var reduce_dims = try core.arena.allocator().alloc(usize, shape.dims.len);
                for (0..shape.dims.len) |i| {
                    reduce_dims[i] = i;
                }
                
                const reduction = @import("../ops/reduction.zig");
                return reduction.sum(core, squared_diff, reduce_dims, false);
            },
            .mean => {
                // Average all elements
                const node = core.graph.getNode(squared_diff);
                const view = core.shape.getView(node.output_view_id);
                const shape = core.shape.getShape(view.shape_id);
                
                var reduce_dims = try core.arena.allocator().alloc(usize, shape.dims.len);
                for (0..shape.dims.len) |i| {
                    reduce_dims[i] = i;
                }
                
                const reduction = @import("../ops/reduction.zig");
                return reduction.mean(core, squared_diff, reduce_dims, false);
            },
        }
    }
};

/// Cross Entropy loss
pub const CrossEntropyLoss = struct {
    reduction: Reduction,
    
    const Self = @This();
    
    pub fn init(reduction: Reduction) CrossEntropyLoss {
        return .{ .reduction = reduction };
    }
    
    pub fn forward(self: *const Self, core: *Core, logits: u32, targets: u32) !u32 {
        // Apply log_softmax to logits
        const log_probs = try ops.softmax(core, logits); // TODO: implement log_softmax
        
        // Compute negative log likelihood
        // For now, simplified implementation
        _ = targets;
        
        // Apply reduction
        switch (self.reduction) {
            .none => return log_probs,
            .sum => {
                const reduction = @import("../ops/reduction.zig");
                const node = core.graph.getNode(log_probs);
                const view = core.shape.getView(node.output_view_id);
                const shape = core.shape.getShape(view.shape_id);
                
                var reduce_dims = try core.arena.allocator().alloc(usize, 1);
                reduce_dims[0] = 0; // Reduce over batch dimension
                
                return reduction.sum(core, log_probs, reduce_dims, false);
            },
            .mean => {
                const reduction = @import("../ops/reduction.zig");
                const node = core.graph.getNode(log_probs);
                const view = core.shape.getView(node.output_view_id);
                const shape = core.shape.getShape(view.shape_id);
                
                var reduce_dims = try core.arena.allocator().alloc(usize, 1);
                reduce_dims[0] = 0; // Reduce over batch dimension
                
                return reduction.mean(core, log_probs, reduce_dims, false);
            },
        }
    }
};

/// Binary Cross Entropy loss
pub const BCELoss = struct {
    reduction: Reduction,
    
    const Self = @This();
    
    pub fn init(reduction: Reduction) BCELoss {
        return .{ .reduction = reduction };
    }
    
    pub fn forward(self: *const Self, core: *Core, predictions: u32, targets: u32) !u32 {
        // BCE = -[y*log(p) + (1-y)*log(1-p)]
        // TODO: Implement log and other necessary operations
        _ = predictions;
        _ = targets;
        
        // For now, return a placeholder
        const scalar_shape = try core.shape.newShape(&.{});
        const scalar_view = try core.shape.newDefaultView(scalar_shape);
        return core.graph.newNodeConstant(scalar_view);
    }
};