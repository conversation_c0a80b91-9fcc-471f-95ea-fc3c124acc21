// Linear (fully connected) layer - V2 architecture
const std = @import("std");
const Core = @import("../core/core.zig").Core;
const types = @import("../core/types.zig");
const ops = @import("../ops/prelude.zig");

/// Linear layer (fully connected layer)
pub const Linear = struct {
    weight_id: u32,
    bias_id: ?u32,
    in_features: usize,
    out_features: usize,
    
    const Self = @This();
    
    pub fn init(
        core: *Core,
        in_features: usize,
        out_features: usize,
        use_bias: bool,
    ) !Linear {
        // Create weight shape [in_features, out_features]
        const weight_shape_id = try core.shape.newShape(&.{
            types.Dim{ .concrete = in_features },
            types.Dim{ .concrete = out_features },
        });
        
        // Create weight parameter
        const weight_id = try core.graph.newNodeParameter(weight_shape_id);
        
        // Create bias if needed
        var bias_id: ?u32 = null;
        if (use_bias) {
            const bias_shape_id = try core.shape.newShape(&.{
                types.Dim{ .concrete = out_features },
            });
            bias_id = try core.graph.newNodeParameter(bias_shape_id);
        }
        
        return Linear{
            .weight_id = weight_id,
            .bias_id = bias_id,
            .in_features = in_features,
            .out_features = out_features,
        };
    }
    
    pub fn forward(self: *const Self, core: *Core, input_id: u32) !u32 {
        // Get input shape to handle batching
        const input_node = core.graph.getNode(input_id);
        const input_view = core.shape.getView(input_node.output_view_id);
        const input_shape = core.shape.getShape(input_view.shape_id);
        
        // Reshape input if needed (flatten all but last dimension)
        var reshaped_input = input_id;
        if (input_shape.dims.len > 2) {
            // Calculate total batch size
            var batch_size: usize = 1;
            for (input_shape.dims[0 .. input_shape.dims.len - 1]) |dim| {
                switch (dim) {
                    .concrete => |size| batch_size *= size,
                    .symbolic => {
                        // For symbolic dimensions, create a symbolic product
                        // This is a simplification - full implementation would handle this
                        return error.SymbolicBatchNotImplemented;
                    },
                }
            }
            
            // Reshape to [batch_size, in_features]
            const new_shape_id = try core.shape.newShape(&.{
                types.Dim{ .concrete = batch_size },
                types.Dim{ .concrete = self.in_features },
            });
            reshaped_input = try ops.reshape(core, input_id, new_shape_id);
        }
        
        // Perform matrix multiplication: input @ weight
        var output = try ops.matmul(core, reshaped_input, self.weight_id);
        
        // Add bias if present
        if (self.bias_id) |bias_id| {
            output = try ops.add(core, output, bias_id);
        }
        
        return output;
    }
    
    pub fn parameters(self: *const Self, allocator: std.mem.Allocator) ![]u32 {
        const count: usize = if (self.bias_id != null) 2 else 1;
        var params = try allocator.alloc(u32, count);
        
        params[0] = self.weight_id;
        if (self.bias_id) |bias_id| {
            params[1] = bias_id;
        }
        
        return params;
    }
};

/// Xavier/Glorot uniform initialization
pub fn initXavierUniform(core: *Core, param_id: u32) !void {
    const node = core.graph.getNode(param_id);
    const view = core.shape.getView(node.output_view_id);
    const shape = core.shape.getShape(view.shape_id);
    
    if (shape.dims.len != 2) return error.InvalidShapeForXavier;
    
    const fan_in = switch (shape.dims[0]) {
        .concrete => |n| n,
        .symbolic => return error.SymbolicInitNotSupported,
    };
    
    const fan_out = switch (shape.dims[1]) {
        .concrete => |n| n,
        .symbolic => return error.SymbolicInitNotSupported,
    };
    
    const std_dev = @sqrt(6.0 / @as(f32, @floatFromInt(fan_in + fan_out)));
    
    // Create uniform distribution [-std_dev, std_dev]
    // TODO: Implement actual random initialization
    // For now, this is a placeholder
    _ = std_dev;
}

/// Initialize with zeros
pub fn initZeros(core: *Core, param_id: u32) !void {
    // TODO: Implement actual zero initialization
    _ = core;
    _ = param_id;
}