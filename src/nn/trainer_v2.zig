const std = @import("std");
const Core = @import("../core/core.zig").Core;
const Optimizer = @import("optimizer_v2.zig").Optimizer;
const Shape = @import("../core/types.zig").Shape;

pub const TrainingConfig = struct {
    batch_size: usize = 32,
    epochs: usize = 10,
    shuffle: bool = true,
    validation_split: f32 = 0.2,
    print_every: ?usize = null,
    save_every: ?usize = null,
    early_stopping_patience: ?usize = null,
};

pub const TrainingMetrics = struct {
    epoch: usize,
    total_loss: f32,
    avg_loss: f32,
    validation_loss: ?f32 = null,
    accuracy: ?f32 = null,
    time_elapsed: u64,
};

pub const DataSet = struct {
    core: *Core,
    data_id: u32,
    labels_id: u32,
    num_samples: usize,
    
    pub fn get_batch(self: *const DataSet, start: usize, end: usize) !struct { data: u32, labels: u32 } {
        const data_slice = try self.core.graph.slice(self.data_id, &[_][2]?usize{
            .{ start, end },
            .{ 0, null },
            .{ 0, null },
            .{ 0, null },
        });
        
        const labels_slice = try self.core.graph.slice(self.labels_id, &[_][2]?usize{
            .{ start, end },
            .{ 0, null },
        });
        
        return .{ .data = data_slice, .labels = labels_slice };
    }
};

pub const Trainer = struct {
    const Self = @This();
    
    core: *Core,
    config: TrainingConfig,
    optimizer: *Optimizer,
    model: *anyopaque, // Generic model interface
    loss_fn: *anyopaque, // Generic loss function
    
    // Callbacks
    on_epoch_start: ?*const fn (metrics: TrainingMetrics) anyerror!void = null,
    on_epoch_end: ?*const fn (metrics: TrainingMetrics) anyerror!void = null,
    on_batch_start: ?*const fn (batch: usize) anyerror!void = null,
    on_batch_end: ?*const fn (batch: usize, loss: f32) anyerror!void = null,
    
    pub fn init(
        core: *Core,
        config: TrainingConfig,
        optimizer: *Optimizer,
        model: *anyopaque,
        loss_fn: *anyopaque,
    ) Self {
        return Self{
            .core = core,
            .config = config,
            .optimizer = optimizer,
            .model = model,
            .loss_fn = loss_fn,
        };
    }
    
    pub fn train(
        self: *Self,
        train_set: DataSet,
        validation_set: ?DataSet,
    ) ![]TrainingMetrics {
        var rng = std.rand.DefaultPrng.init(@intCast(std.time.milliTimestamp()));
        const random = rng.random();
        
        const num_batches = (train_set.num_samples + self.config.batch_size - 1) / self.config.batch_size;
        var metrics_history = try self.core.allocator.alloc(TrainingMetrics, self.config.epochs);
        
        // Create indices for shuffling
        var indices = try self.core.allocator.alloc(usize, train_set.num_samples);
        defer self.core.allocator.free(indices);
        for (indices, 0..) |*idx, i| {
            idx.* = i;
        }
        
        var best_validation_loss: ?f32 = null;
        var patience_counter: usize = 0;
        
        for (0..self.config.epochs) |epoch| {
            var epoch_start_time = std.time.milliTimestamp();
            var total_loss: f32 = 0.0;
            var samples_processed: usize = 0;
            
            // Shuffle if configured
            if (self.config.shuffle) {
                random.shuffle(usize, indices);
            }
            
            // Training epoch
            for (0..num_batches) |batch_idx| {
                const start = batch_idx * self.config.batch_size;
                const end = @min(start + self.config.batch_size, train_set.num_samples);
                const batch_size = end - start;
                
                if (self.on_batch_start) |cb| {
                    try cb(batch_idx);
                }
                
                // Get batch data
                const batch = try train_set.get_batch(start, end);
                
                // Forward pass
                const output = try self.forward_pass(batch.data);
                
                // Compute loss
                const loss = try self.compute_loss(output, batch.labels);
                const loss_value = try self.core.graph.get_scalar_value(loss);
                
                // Backward pass
                try self.backward_pass(loss);
                
                // Optimizer step
                const gradients = try self.collect_gradients();
                try self.optimizer.step(gradients);
                try self.optimizer.zero_grad();
                
                total_loss += loss_value * @as(f32, @floatFromInt(batch_size));
                samples_processed += batch_size;
                
                if (self.on_batch_end) |cb| {
                    try cb(batch_idx, loss_value);
                }
                
                // Print progress if configured
                if (self.config.print_every) |print_interval| {
                    if (batch_idx % print_interval == 0) {
                        std.debug.print("Epoch {}/{}, Batch {}/{}, Loss: {:.4}\n", .{
                            epoch + 1,
                            self.config.epochs,
                            batch_idx + 1,
                            num_batches,
                            loss_value,
                        });
                    }
                }
            }
            
            const avg_loss = total_loss / @as(f32, @floatFromInt(samples_processed));
            
            // Validation step
            var validation_loss: ?f32 = null;
            if (validation_set) |val_set| {
                validation_loss = try self.validate(val_set);
                
                // Early stopping check
                if (self.config.early_stopping_patience) |patience| {
                    if (best_validation_loss == null or validation_loss.? < best_validation_loss.?) {
                        best_validation_loss = validation_loss;
                        patience_counter = 0;
                    } else {
                        patience_counter += 1;
                        if (patience_counter >= patience) {
                            std.debug.print("Early stopping triggered at epoch {}\n", .{epoch + 1});
                            break;
                        }
                    }
                }
            }
            
            const epoch_end_time = std.time.milliTimestamp();
            
            metrics_history[epoch] = .{
                .epoch = epoch,
                .total_loss = total_loss,
                .avg_loss = avg_loss,
                .validation_loss = validation_loss,
                .accuracy = null, // TODO: Implement accuracy calculation
                .time_elapsed = @intCast(epoch_end_time - epoch_start_time),
            };
            
            if (self.on_epoch_end) |cb| {
                try cb(metrics_history[epoch]);
            }
            
            // Save checkpoint if configured
            if (self.config.save_every) |save_interval| {
                if ((epoch + 1) % save_interval == 0) {
                    try self.save_checkpoint(epoch);
                }
            }
            
            std.debug.print("Epoch {}/{} completed - Train Loss: {:.4}, Val Loss: {:.4}, Time: {}ms\n", .{
                epoch + 1,
                self.config.epochs,
                avg_loss,
                validation_loss orelse 0.0,
                metrics_history[epoch].time_elapsed,
            });
        }
        
        return metrics_history;
    }
    
    fn forward_pass(self: *Self, input: u32) !u32 {
        // This is a placeholder - the actual implementation would depend on the model interface
        // In practice, this would call the model's forward method
        _ = self;
        return input; // Placeholder
    }
    
    fn compute_loss(self: *Self, predictions: u32, targets: u32) !u32 {
        // This is a placeholder - the actual implementation would depend on the loss function interface
        _ = self;
        _ = targets;
        return predictions; // Placeholder
    }
    
    fn backward_pass(self: *Self, loss: u32) !void {
        // This would implement automatic differentiation
        try self.core.compiler.autograd.backward(loss);
    }
    
    fn collect_gradients(self: *Self) ![]u32 {
        // Collect gradients for all parameters
        var gradients = try self.core.allocator.alloc(u32, self.optimizer.parameters.items.len);
        for (self.optimizer.parameters.items, 0..) |param_id, i| {
            const node = self.core.graph.nodes.items[param_id];
            gradients[i] = node.gradient_id orelse return error.MissingGradient;
        }
        return gradients;
    }
    
    fn validate(self: *Self, validation_set: DataSet) !f32 {
        var total_loss: f32 = 0.0;
        var samples_processed: usize = 0;
        
        const num_batches = (validation_set.num_samples + self.config.batch_size - 1) / self.config.batch_size;
        
        for (0..num_batches) |batch_idx| {
            const start = batch_idx * self.config.batch_size;
            const end = @min(start + self.config.batch_size, validation_set.num_samples);
            const batch_size = end - start;
            
            const batch = try validation_set.get_batch(start, end);
            const output = try self.forward_pass(batch.data);
            const loss = try self.compute_loss(output, batch.labels);
            const loss_value = try self.core.graph.get_scalar_value(loss);
            
            total_loss += loss_value * @as(f32, @floatFromInt(batch_size));
            samples_processed += batch_size;
        }
        
        return total_loss / @as(f32, @floatFromInt(samples_processed));
    }
    
    fn save_checkpoint(self: *Self, epoch: usize) !void {
        // TODO: Implement model checkpoint saving
        _ = self;
        _ = epoch;
        std.debug.print("Checkpoint saving not yet implemented\n", .{});
    }
};