const std = @import("std");
const Core = @import("../core/core.zig").Core;
const Shape = @import("../core/types.zig").Shape;
const ReduceOp = @import("../core/types.zig").OpType.ReduceOp;
const ops = @import("../ops/operations.zig");

pub const LayerNorm = struct {
    const Self = @This();
    
    normalized_shape: []const usize,
    eps: f32 = 1e-5,
    elementwise_affine: bool = true,
    weight_id: ?u32 = null,
    bias_id: ?u32 = null,
    
    pub fn init(core: *Core, normalized_shape: []const usize, eps: f32, elementwise_affine: bool) !Self {
        var self = Self{
            .normalized_shape = core.arena.allocator().alloc(usize, normalized_shape.len) catch return error.OutOfMemory,
            .eps = eps,
            .elementwise_affine = elementwise_affine,
        };
        
        for (normalized_shape, 0..) |dim, i| {
            self.normalized_shape[i] = dim;
        }
        
        // Create learnable parameters if needed
        if (elementwise_affine) {
            const weight_shape = Shape{ .dims = self.normalized_shape };
            self.weight_id = try ops.ones(core, weight_shape);
            self.bias_id = try ops.zeros(core, weight_shape);
        }
        
        return self;
    }
    
    pub fn deinit(self: *Self) void {
        // Parameters are managed by Core
        _ = self;
    }
    
    pub fn forward(self: *const Self, core: *Core, input_id: u32) !u32 {
        const input_shape = core.graph.nodes.items[input_id].shape;
        const rank = input_shape.dims.len;
        const norm_rank = self.normalized_shape.len;
        
        // Verify dimensions match
        if (norm_rank > rank) {
            return error.InvalidDimensions;
        }
        
        // Calculate which dimensions to reduce over (complementary to normalized_shape)
        var reduce_dims = try core.arena.allocator().alloc(usize, rank - norm_rank);
        defer core.arena.allocator().free(reduce_dims);
        
        for (reduce_dims, 0..) |*dim, i| {
            dim.* = i;
        }
        
        // Calculate mean and variance over the reduction dimensions
        const mean = try ops.mean(core, input_id, reduce_dims, true);
        const centered = try ops.subtract(core, input_id, mean);
        const squared = try ops.multiply(core, centered, centered);
        const variance = try ops.mean(core, squared, reduce_dims, true);
        
        // Add epsilon and compute stddev
        const eps_val = try ops.scalar(core, self.eps, core.graph.nodes.items[input_id].dtype);
        const var_eps = try ops.add(core, variance, eps_val);
        const stddev = try ops.sqrt(core, var_eps);
        
        // Normalize
        const normalized = try ops.divide(core, centered, stddev);
        
        // Apply affine transformation if enabled
        if (self.elementwise_affine) {
            var result = try ops.multiply(core, normalized, self.weight_id.?);
            result = try ops.add(core, result, self.bias_id.?);
            return result;
        }
        
        return normalized;
    }
};