# FFI Rules Implementation Issues

This document outlines issues found in the FFI rule implementation that are causing test failures, along with the solutions we've implemented to address them.

## Issue 1: Distribution Pattern Not Applied

**Test Case:** `ffi:distribution_pattern`

**Expected Behavior:** The expression `a * (b + c)` should be simplified to `a*b + a*c` when using the FFI implementation.

**Actual Behavior:** The FFI implementation keeps the expression as `a * (b + c)` without distributing.

**Rule Definition:** The distribution rule exists in the codebase:
```rust
// In rules.rs
rewrite!("distribute"; "(* ?a (+ ?b ?c))" => "(+ (* ?a ?b) (* ?a ?c))"),
```

**Root Cause:**
The FFI implementation (`egg_simplify` function) was using a minimal set of rules for safety and performance, which did not include the distribution rule:

```rust
// Fallback to standard rules-based approach, but with minimal rules
let mut all_rules = Vec::new();

// Identity rules only - we skip all other rules for safety
all_rules.push(egg::rewrite!("mul-id-left"; "(* 1 ?a)" => "?a"));
all_rules.push(egg::rewrite!("mul-id-right"; "(* ?a 1)" => "?a"));
// ...
```

## Issue 2: Factoring Pattern Not Applied

**Test Case:** `ffi:factoring_pattern`

**Expected Behavior:** The expression `a*c + b*c` should be simplified to `(a+b)*c` when using the FFI implementation.

**Actual Behavior:** The FFI implementation keeps the expression as `a*c + b*c` without factoring.

**Rule Definition:** The factoring rule exists in the codebase:
```rust
// In rules.rs
rewrite!("factor"; "(+ (* ?a ?b) (* ?a ?c))" => "(* ?a (+ ?b ?c))"),
```

**Root Cause:**
Same as for the distribution issue - the rule was not included in the minimal rule set used by the FFI implementation.

## Issue 3: Reflexive Equality Rule Missing

**Test Case:** `ffi:comparison`

**Expected Behavior:** The expression `a == a` should be simplified to `true` (integer 1) when using the FFI implementation.

**Actual Behavior:** The FFI implementation keeps the expression as `Equal(a, a)` without simplifying to a boolean value.

**Root Cause:**
1. There was no rule for reflexive equality (`a == a → true`) in the codebase
2. There was no `Comparison` category in the `RuleCategory` enum
3. Comparison rules were not included in the rule set

## Implemented Solutions

To address these issues while maintaining backward compatibility, we've implemented the following solutions:

### 1. Created Extended FFI Function with Full Ruleset

We created a new module `ffi_extended.rs` with a function `egg_simplify_with_all_rules` that uses the complete ruleset:

```rust
pub extern "C" fn egg_simplify_with_all_rules(
    nodes: *const FFINode,
    node_count: usize,
    symbols: *const FFISymbol,
    symbol_count: usize,
    result_ptr: *mut *mut FFINode,
    result_len: *mut usize,
    timeout_ms: u64,
    max_iterations: u32,
) -> bool {
    // ...
    
    // Step 3: Get full ruleset for comprehensive simplification
    let all_rules = rules::get_rules();
    
    // Step 4: Run the simplifier with all rules
    let runner = match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        egg::Runner::default()
            .with_egraph(egraph)
            .with_iter_limit(iterations as usize)
            .with_time_limit(remaining_time)
            .with_node_limit(100000) // Limit to 100K nodes
            .run(&all_rules)
    })) {
        // ...
    };
    
    // ...
}
```

### 2. Added Missing Rule Category for Comparison Operations

We added the `Comparison` category to the `RuleCategory` enum:

```rust
pub enum RuleCategory {
    // ...
    // Comparison operations (==, !=, <, <=, etc.)
    Comparison = 1 << 8,  // 256
    // All rules
    All = 0xFFFFFFFF,
}
```

### 3. Added Missing Reflexive Equality Rule

We added the reflexive equality rule to handle comparisons of a value with itself:

```rust
// Comparison operator rules
let comparison_rules = vec![
    CategorizedRule {
        rule: rewrite!("eq-reflexive"; "(== ?a ?a)" => "1"),
        category: RuleCategory::Comparison,
    },
];
```

### 4. Included Comparison Rules in All Rule Collections

We added the comparison rules to the all_rules collection:

```rust
// Then add all other rule categories
all_rules.extend(commutative_rules);
all_rules.extend(associative_rules);
all_rules.extend(zero_rules);
all_rules.extend(distributive_rules);
all_rules.extend(simplification_rules);
all_rules.extend(cancellation_rules);
all_rules.extend(shape_rules);
all_rules.extend(min_max_rules);
all_rules.extend(comparison_rules);  // Added this line
```

### 5. Updated the `get_rules_by_mask` Function

We updated the `get_rules_by_mask` function to include comparison rules:

```rust
// Create a vector of tuples mapping category masks to their rules
let category_rules = vec![
    (RuleCategory::Identity as u32, identity_rules),
    (RuleCategory::Commutative as u32, commutative_rules),
    (RuleCategory::Associative as u32, associative_rules),
    (RuleCategory::Arithmetic as u32, arithmetic_rules),
    (RuleCategory::Distributive as u32, distributive_rules),
    (RuleCategory::MinMax as u32, min_max_rules::get_min_max_rules()),
    (RuleCategory::Comparison as u32, vec![rewrite!("eq-reflexive"; "(== ?a ?a)" => "1")]),
];
```

## Usage Recommendations

We recommend using the new `egg_simplify_with_all_rules` function when comprehensive symbolic simplification is needed. The original `egg_simplify` function remains available for backward compatibility and for cases where a more conservative approach is preferred.

Example usage:

```rust
// Using the full ruleset for comprehensive simplification
let result = egg_simplify_with_all_rules(
    nodes.as_ptr(),
    nodes.len(),
    symbols.as_ptr(),
    symbols.len(),
    &mut result_ptr,
    &mut result_len,
    timeout_ms,
    max_iterations,
);
```

## Future Considerations

For future development, we should consider:

1. **Improved Rule Testing:** Add more direct tests for each rule in the FFI implementation to ensure they're working as expected.

2. **Enhanced Diagnostics:** Consider adding logging to the FFI implementation that records which rules are being applied during simplification.

3. **Rule Application Strategy:** Review the rule application strategy in the FFI implementation. Some rules might need multiple iterations or a different ordering to be effectively applied.

4. **Performance Optimization:** Investigate performance implications of using the full ruleset and consider optimizations.

5. **Configurable Rule Set:** Provide more fine-grained control over which rule categories are applied for specific use cases.