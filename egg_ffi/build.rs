use std::env;
use std::path::PathBuf;
use std::process::Command;

fn main() {
    // Tell cargo to invalidate the built crate whenever these files change
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=src/lib.rs");

    let crate_dir = env::var("CARGO_MANIFEST_DIR").unwrap();

    // Output path for the header
    let src_out_file = PathBuf::from(&crate_dir)
        .join("..")
        .join("src")
        .join("symbolic")
        .join("egraph")
        .join("egg_ffi.h");
    
    // Also write to the target/release directory for the build script to find
    let target_out_file = PathBuf::from(&crate_dir)
        .join("target")
        .join("release")
        .join("egg_ffi.h");
    
    // Use the src path as the canonical one for generation
    let out_file = src_out_file.clone();

    // Create a minimal config that won't introduce duplicate include guards
    let mut config = cbindgen::Config::default();
    config.language = cbindgen::Language::C;
    config.cpp_compat = true;
    config.include_guard = Some("EGG_FFI_H".to_string());

    // Fix includes to use proper format without extra quotes
    config.includes = vec!["stdint.h".into(), "stdbool.h".into(), "stddef.h".into()];
    config.sys_includes = vec!["stdint.h".into(), "stdbool.h".into(), "stddef.h".into()];
    config.no_includes = false;  // Change to false to let cbindgen handle includes properly

    config.documentation = true;
    config.pragma_once = false;
    config.autogen_warning = Some("/* This file is automatically generated by cbindgen. Don't edit it manually! */".into());

    // Exports
    config.export.include = vec![
        "BatchNode".into(),
        "egg_simplify_batch".into(),
        "BinaryOp".into(),
        "RuleCategory".into(),
        "EggErrorCode".into(),
        "ERR_OK".into(),
        "ERR_INVALID".into(),
        "ERR_MEMORY".into(),
        "ERR_PANIC".into(),
    ];

    // Parse configuration
    config.parse = cbindgen::ParseConfig {
        parse_deps: false,
        include: Some(vec!["egg_cshim".into()]),
        extra_bindings: vec![],
        ..Default::default()
    };

    // Generate the bindings
    match cbindgen::Builder::new()
        .with_crate(crate_dir.clone())
        .with_config(config)
        .generate() {
        Ok(bindings) => {
            bindings.write_to_file(&out_file);
            println!("Wrote header file to: {}", out_file.display());

            // Post-process the header file to fix includes
            let status = Command::new("sed")
                .args(["-i", "",
                       // Replace quotes for includes
                       "s/\"stdint.h\"/<stdint.h>/g",
                       &out_file.to_string_lossy()])
                .status()
                .expect("Failed to run sed command");

            if !status.success() {
                eprintln!("Warning: sed command failed");
            }

            let status = Command::new("sed")
                .args(["-i", "",
                       // Replace quotes for includes
                       "s/\"stdbool.h\"/<stdbool.h>/g",
                       &out_file.to_string_lossy()])
                .status()
                .expect("Failed to run sed command");

            if !status.success() {
                eprintln!("Warning: sed command failed");
            }

            let status = Command::new("sed")
                .args(["-i", "",
                       // Replace quotes for includes
                       "s/\"stddef.h\"/<stddef.h>/g",
                       &out_file.to_string_lossy()])
                .status()
                .expect("Failed to run sed command");

            if !status.success() {
                eprintln!("Warning: sed command failed");
            }

            // Fix duplicate includes
            let status = Command::new("awk")
                .args([
                    "BEGIN { seen_includes = 0; }
                    /^#include <std(int|bool|def)\\.h>/ {
                        if (seen_includes == 0) {
                            seen_includes = 1;
                            print \"#include <stdarg.h>\";
                            print \"#include <stdbool.h>\";
                            print \"#include <stdint.h>\";
                            print \"#include <stdlib.h>\";
                        }
                        next;
                    }
                    { print; }",
                    &out_file.to_string_lossy(),
                ])
                .output()
                .expect("Failed to run awk command");

            if !status.status.success() {
                eprintln!("Warning: awk command failed");
                eprintln!("Error: {}", String::from_utf8_lossy(&status.stderr));
            } else {
                // Write output back to file
                std::fs::write(&out_file, &status.stdout)
                    .expect("Failed to write processed header file");
                
                // Also copy to target/release/egg_ffi.h for the build system to find
                std::fs::write(&target_out_file, &status.stdout)
                    .expect("Failed to write processed header file to target directory");
                println!("Also wrote header file to: {}", target_out_file.display());
            }
        },
        Err(err) => {
            eprintln!("Failed to generate bindings: {}", err);
            std::process::exit(1);
        }
    }
}