language = "C"
header = """
/* egg-rs FFI Interface
 * Auto-generated - do not modify directly
 */
#ifndef EGG_FFI_H
#define EGG_FFI_H

#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>
"""

trailer = """
#endif /* EGG_FFI_H */
"""

include_guard = false
include_version = true
cpp_compat = true
documentation = true
style = "both"
line_length = 100
tab_width = 2
braces = "SameLine"

[defines]
"target_os = macos" = "TARGET_OS_MACOS"
"target_os = windows" = "TARGET_OS_WINDOWS"
"target_os = linux" = "TARGET_OS_LINUX"

# Export constants from enums
[export]
include = ["BinaryOp", "RuleCategory", "EggErrorCode", "BatchNode"]
prefix = "EGG_"

[export.rename]
"BinaryOp::Add" = "OP_ADD"
"BinaryOp::Sub" = "OP_SUB"
"BinaryOp::Mul" = "OP_MUL"
"BinaryOp::Div" = "OP_DIV"
"BinaryOp::Mod" = "OP_MOD"
"BinaryOp::Pow" = "OP_POW"
"BinaryOp::Min" = "OP_MIN"
"BinaryOp::Max" = "OP_MAX"
"BinaryOp::Eq" = "OP_EQ"
"BinaryOp::Ne" = "OP_NE"
"BinaryOp::Lt" = "OP_LT"
"BinaryOp::Le" = "OP_LE"

"RuleCategory::Arithmetic" = "RULE_ARITHMETIC"
"RuleCategory::Commutative" = "RULE_COMMUTATIVE"
"RuleCategory::Associative" = "RULE_ASSOCIATIVE"
"RuleCategory::Distributive" = "RULE_DISTRIBUTIVE"
"RuleCategory::Identity" = "RULE_IDENTITY"
"RuleCategory::ConstantFold" = "RULE_CONSTANT_FOLD"
"RuleCategory::Boolean" = "RULE_BOOLEAN"
"RuleCategory::All" = "RULE_ALL"

"EggErrorCode::Ok" = "ERROR_OK"
"EggErrorCode::NullPointer" = "ERROR_NULL_POINTER"
"EggErrorCode::MutexLockFailed" = "ERROR_MUTEX_LOCK_FAILED"
"EggErrorCode::Utf8Error" = "ERROR_UTF8_ERROR"
"EggErrorCode::IndexOutOfBounds" = "ERROR_INDEX_OUT_OF_BOUNDS"
"EggErrorCode::InvalidEGraphId" = "ERROR_INVALID_EGRAPH_ID"
"EggErrorCode::InvalidNodeId" = "ERROR_INVALID_NODE_ID"
"EggErrorCode::InvalidOperation" = "ERROR_INVALID_OPERATION"
"EggErrorCode::MemoryAllocationFailed" = "ERROR_MEMORY_ALLOCATION_FAILED"
"EggErrorCode::UnknownError" = "ERROR_UNKNOWN"
"EggErrorCode::NotImplemented" = "ERROR_NOT_IMPLEMENTED"

# Batch error codes
"ERR_OK" = "BATCH_OK"
"ERR_INVALID" = "BATCH_INVALID"
"ERR_MEMORY" = "BATCH_MEMORY"
"ERR_PANIC" = "BATCH_PANIC"
