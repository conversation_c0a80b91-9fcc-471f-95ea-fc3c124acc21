use egg_ffi::{FFIN<PERSON>, FFISymbol, INVALID_IDX, egg_simplify, egg_free_result};

#[test]
fn test_multiplication_identity() {
    // Create a symbol "x"
    let symbol_name = std::ffi::CString::new("x").unwrap();
    let symbol = FFISymbol {
        id: 0,
        name_ptr: symbol_name.as_ptr(),
        name_len: 1,
    };
    
    println!("Testing x * 1:");
    
    // Create an expression: x * 1 (should simplify to x)
    let nodes = [
        FFINode { tag: 1, value: 0, left: INVALID_IDX, right: INVALID_IDX }, // Symbol x
        FFINode { tag: 0, value: 1, left: INVALID_IDX, right: INVALID_IDX }, // Integer 1
        FFINode { tag: 4, value: 0, left: 0, right: 1 }, // x * 1 (tag 4 is Mul)
    ];
    
    // Call egg_simplify
    let mut result_ptr: *mut FFINode = std::ptr::null_mut();
    let mut result_len: usize = 0;
    
    let success = egg_simplify(
        nodes.as_ptr(),
        nodes.len(),
        &symbol,
        1,
        &mut result_ptr,
        &mut result_len,
    );
    
    // Check that the call succeeded
    assert!(success, "egg_simplify should succeed");
    assert!(!result_ptr.is_null(), "result_ptr should not be null");
    assert_ne!(result_len, 0, "result_len should not be zero");
    
    // Get the result slice
    let result_slice = unsafe { std::slice::from_raw_parts(result_ptr, result_len) };
    
    // Print detailed result for debugging
    println!("Result nodes: {} nodes", result_len);
    for (i, node) in result_slice.iter().enumerate() {
        println!("Node {}: tag={}, value={}, left={}, right={}",
                 i, node.tag, node.value, node.left, node.right);
    }
    
    // The simplified expression should either be just the symbol x
    // or could involve other operations but must have the symbol
    let has_symbol = result_slice.iter().any(|node| node.tag == 1 && node.value == 0);
    assert!(has_symbol, "Result should contain symbol 'x'");
    
    // In the ideal case, we'd only have one node (the symbol)
    if result_len == 1 {
        assert_eq!(result_slice[0].tag, 1, "Should be a symbol node");
        assert_eq!(result_slice[0].value, 0, "Should be symbol with ID 0");
        println!("SUCCESS: Simplified to just the symbol 'x'");
    } else {
        // Check if we still have a multiplication by 1, which would indicate failure
        let still_has_mul_by_1 = 
            result_slice.iter().any(|node| node.tag == 4) && // Has multiplication
            result_slice.iter().any(|node| node.tag == 0 && node.value == 1); // Has constant 1
        
        assert!(!still_has_mul_by_1, "Expression should not still contain x * 1");
        println!("Expression was simplified but not to a single symbol node");
    }
    
    // Free the result
    egg_free_result(result_ptr, result_len);
    
    // Now test 1 * x
    println!("\nTesting 1 * x:");
    
    // Create expression: 1 * x
    let nodes_reversed = [
        FFINode { tag: 0, value: 1, left: INVALID_IDX, right: INVALID_IDX }, // Integer 1
        FFINode { tag: 1, value: 0, left: INVALID_IDX, right: INVALID_IDX }, // Symbol x
        FFINode { tag: 4, value: 0, left: 0, right: 1 }, // 1 * x
    ];
    
    // Call egg_simplify
    result_ptr = std::ptr::null_mut();
    result_len = 0;
    
    let success = egg_simplify(
        nodes_reversed.as_ptr(),
        nodes_reversed.len(),
        &symbol,
        1,
        &mut result_ptr,
        &mut result_len,
    );
    
    // Check that the call succeeded
    assert!(success, "egg_simplify should succeed for 1 * x");
    
    // Get the result slice
    let result_slice = unsafe { std::slice::from_raw_parts(result_ptr, result_len) };
    
    // Print detailed result
    println!("Result nodes: {} nodes", result_len);
    for (i, node) in result_slice.iter().enumerate() {
        println!("Node {}: tag={}, value={}, left={}, right={}",
                 i, node.tag, node.value, node.left, node.right);
    }
    
    // Similarly check for successful simplification
    let has_symbol = result_slice.iter().any(|node| node.tag == 1 && node.value == 0);
    assert!(has_symbol, "Result should contain symbol 'x'");
    
    // Check if we still have a multiplication by 1, which would indicate failure
    let still_has_mul_by_1 = 
        result_slice.iter().any(|node| node.tag == 4) && // Has multiplication
        result_slice.iter().any(|node| node.tag == 0 && node.value == 1); // Has constant 1
    
    assert!(!still_has_mul_by_1, "Expression should not still contain 1 * x");
    
    // Free the result
    egg_free_result(result_ptr, result_len);
}