// FFI integration tests
// These tests verify that the FFI interface works correctly
use egg_ffi::{FFINode, FFISymbol, INVALID_IDX, egg_simplify, egg_free_result};

#[test]
fn test_basic_simplification() {
    // Create a simple expression: 1 + 2
    let nodes = [
        FFINode { tag: 0, value: 1, left: INVALID_IDX, right: INVALID_IDX }, // Integer 1
        FFINode { tag: 0, value: 2, left: INVALID_IDX, right: INVALID_IDX }, // Integer 2
        FFINode { tag: 2, value: 0, left: 0, right: 1 }, // 1 + 2
    ];
    
    // Call egg_simplify
    let mut result_ptr: *mut FFINode = std::ptr::null_mut();
    let mut result_len: usize = 0;
    
    let success = egg_simplify(
        nodes.as_ptr(),
        nodes.len(),
        std::ptr::null(),
        0,
        &mut result_ptr,
        &mut result_len,
    );
    
    // Check that the call succeeded
    assert!(success, "egg_simplify should succeed");
    assert!(!result_ptr.is_null(), "result_ptr should not be null");
    assert_ne!(result_len, 0, "result_len should not be zero");
    
    // Get the result slice
    let result_slice = unsafe { std::slice::from_raw_parts(result_ptr, result_len) };
    
    // The simplified expression should be just 3
    assert_eq!(result_len, 1, "Expected a single node (constant 3)");
    assert_eq!(result_slice[0].tag, 0, "Expected an integer node");
    assert_eq!(result_slice[0].value, 3, "Expected value 3");
    
    // Free the result
    egg_free_result(result_ptr, result_len);
}

#[test]
fn test_null_inputs() {
    // Test with null nodes
    let mut result_ptr: *mut FFINode = std::ptr::null_mut();
    let mut result_len: usize = 0;
    
    let success = egg_simplify(
        std::ptr::null(),
        0,
        std::ptr::null(),
        0,
        &mut result_ptr,
        &mut result_len,
    );
    
    // Should fail with error
    assert!(!success, "egg_simplify should fail with null inputs");
    
    // Note: We're not testing the error message here as it might be implementation-dependent
    // The important part is that the function failed as expected
}

#[test]
fn test_expression_with_symbols() {
    // Create a symbol "x"
    let symbol_name = std::ffi::CString::new("x").unwrap();
    let symbol = FFISymbol {
        id: 0,
        name_ptr: symbol_name.as_ptr(),
        name_len: 1,
    };
    
    // Create an expression: x + 0 (should simplify to x)
    let nodes = [
        FFINode { tag: 1, value: 0, left: INVALID_IDX, right: INVALID_IDX }, // Symbol x
        FFINode { tag: 0, value: 0, left: INVALID_IDX, right: INVALID_IDX }, // Integer 0
        FFINode { tag: 2, value: 0, left: 0, right: 1 }, // x + 0
    ];
    
    // Call egg_simplify
    let mut result_ptr: *mut FFINode = std::ptr::null_mut();
    let mut result_len: usize = 0;
    
    let success = egg_simplify(
        nodes.as_ptr(),
        nodes.len(),
        &symbol,
        1,
        &mut result_ptr,
        &mut result_len,
    );
    
    // Check that the call succeeded
    assert!(success, "egg_simplify should succeed");
    assert!(!result_ptr.is_null(), "result_ptr should not be null");
    assert_ne!(result_len, 0, "result_len should not be zero");
    
    // Get the result slice
    let result_slice = unsafe { std::slice::from_raw_parts(result_ptr, result_len) };
    
    // The simplified expression should include a symbol node
    let has_symbol = result_slice.iter().any(|node| node.tag == 1); // 1 = Symbol
    assert!(has_symbol, "Result should contain a symbol node");
    
    // Free the result
    egg_free_result(result_ptr, result_len);
}

#[test]
fn test_memory_management() {
    // Create a series of expressions and simplify them
    // This tests that memory is properly allocated and freed
    
    for _ in 0..10 {
        // Create a simple expression: 1 + 2
        let nodes = [
            FFINode { tag: 0, value: 1, left: INVALID_IDX, right: INVALID_IDX }, // Integer 1
            FFINode { tag: 0, value: 2, left: INVALID_IDX, right: INVALID_IDX }, // Integer 2
            FFINode { tag: 2, value: 0, left: 0, right: 1 }, // 1 + 2
        ];
        
        // Call egg_simplify
        let mut result_ptr: *mut FFINode = std::ptr::null_mut();
        let mut result_len: usize = 0;
        
        let success = egg_simplify(
            nodes.as_ptr(),
            nodes.len(),
            std::ptr::null(),
            0,
            &mut result_ptr,
            &mut result_len,
        );
        
        assert!(success, "egg_simplify should succeed");
        assert!(!result_ptr.is_null(), "result_ptr should not be null");
        assert_ne!(result_len, 0, "result_len should not be zero");
        
        // Free the result (this should not cause any memory errors)
        egg_free_result(result_ptr, result_len);
    }
    
    // If we get here without memory errors, the test passes
}

#[test]
fn test_complex_arithmetic() {
    // Create a complex expression: (1 + 2) * (3 - 4) + 5
    let nodes = [
        FFINode { tag: 0, value: 1, left: INVALID_IDX, right: INVALID_IDX }, // Integer 1
        FFINode { tag: 0, value: 2, left: INVALID_IDX, right: INVALID_IDX }, // Integer 2
        FFINode { tag: 2, value: 0, left: 0, right: 1 },                    // 1 + 2
        FFINode { tag: 0, value: 3, left: INVALID_IDX, right: INVALID_IDX }, // Integer 3
        FFINode { tag: 0, value: 4, left: INVALID_IDX, right: INVALID_IDX }, // Integer 4
        FFINode { tag: 3, value: 0, left: 3, right: 4 },                    // 3 - 4
        FFINode { tag: 4, value: 0, left: 2, right: 5 },                    // (1 + 2) * (3 - 4)
        FFINode { tag: 0, value: 5, left: INVALID_IDX, right: INVALID_IDX }, // Integer 5
        FFINode { tag: 2, value: 0, left: 6, right: 7 },                    // (1 + 2) * (3 - 4) + 5
    ];
    
    // Call egg_simplify
    let mut result_ptr: *mut FFINode = std::ptr::null_mut();
    let mut result_len: usize = 0;
    
    let success = egg_simplify(
        nodes.as_ptr(),
        nodes.len(),
        std::ptr::null(),
        0,
        &mut result_ptr,
        &mut result_len,
    );
    
    // Check that the call succeeded
    assert!(success, "egg_simplify should succeed");
    assert!(!result_ptr.is_null(), "result_ptr should not be null");
    assert_ne!(result_len, 0, "result_len should not be zero");
    
    // Get the result slice
    let result_slice = unsafe { std::slice::from_raw_parts(result_ptr, result_len) };
    
    // The result should be the constant 2
    // (1 + 2) * (3 - 4) + 5 = 3 * (-1) + 5 = -3 + 5 = 2
    assert_eq!(result_len, 1, "Expected a single node (constant 2)");
    assert_eq!(result_slice[0].tag, 0, "Expected an integer node");
    assert_eq!(result_slice[0].value, 2, "Expected value 2");
    
    // Free the result
    egg_free_result(result_ptr, result_len);
}