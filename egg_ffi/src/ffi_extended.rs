use crate::language::ExprNode;
use crate::cost::ExprCostFn;
use crate::error::{EggErrorCode, set_error, clear_error};
use crate::rules;
use crate::ffi::{FFINode, FFISymbol, INVALID_IDX, nodes_to_recexpr, recexpr_to_nodes};
use egg::{Id, Symbol, RecExpr, EGraph};
use std::panic::catch_unwind;
use std::os::raw::c_char;

/// Expanded simplification function that uses all rules
///
/// This function takes an array of nodes representing an expression,
/// simplifies it using the egg library with all available rules,
/// and returns the simplified expression as a new array of nodes.
///
/// # Parameters
///
/// * `nodes` - Pointer to the input nodes array
/// * `node_count` - Number of nodes in the input array
/// * `symbols` - Pointer to the symbol table array
/// * `symbol_count` - Number of symbols in the symbol table
/// * `result_ptr` - Output pointer that will be set to the result nodes array
/// * `result_len` - Output pointer that will be set to the result array length
/// * `timeout_ms` - Optional timeout in milliseconds (0 = no timeout, default 5000ms)
/// * `max_iterations` - Maximum iterations for rewriting (0 = default of 10 iterations)
///
/// # Returns
///
/// * true on success
/// * false on failure (check error with egg_get_last_error)
///
/// # Safety
///
/// The caller must ensure that the input arrays are valid and must free the
/// result array with egg_free_result when done.
#[no_mangle]
pub extern "C" fn egg_simplify_with_all_rules(
    nodes: *const FFINode,
    node_count: usize,
    symbols: *const FFISymbol,
    symbol_count: usize,
    result_ptr: *mut *mut FFINode,
    result_len: *mut usize,
    timeout_ms: u64,
    max_iterations: u32,
) -> bool {
    // Clear any previous error
    clear_error();
    
    // Check for null pointers
    if nodes.is_null() {
        set_error("Nodes array is null", EggErrorCode::NullPointer);
        return false;
    }
    
    if node_count == 0 {
        set_error("Node count is zero", EggErrorCode::InvalidOperation);
        return false;
    }
    
    if symbols.is_null() && symbol_count > 0 {
        set_error("Symbols array is null but symbol count is non-zero", EggErrorCode::NullPointer);
        return false;
    }
    
    if result_ptr.is_null() {
        set_error("Result pointer is null", EggErrorCode::NullPointer);
        return false;
    }
    
    if result_len.is_null() {
        set_error("Result length pointer is null", EggErrorCode::NullPointer);
        return false;
    }
    
    // Apply defaults for timeout and iterations
    let timeout = if timeout_ms == 0 { 3000 } else { timeout_ms }; // 3000ms default timeout
    let iterations = if max_iterations == 0 { 5 } else { max_iterations }; // 5 default iterations
    
    // Start a timer for timeout tracking
    let start_time = std::time::Instant::now();
    let timeout_duration = std::time::Duration::from_millis(timeout);
    
    // Use catch_unwind to prevent panics from propagating across the FFI boundary
    match catch_unwind(|| {
        // Convert the input arrays to slices
        let nodes_slice = unsafe { std::slice::from_raw_parts(nodes, node_count) };
        let symbols_slice = if symbols.is_null() {
            &[]
        } else {
            unsafe { std::slice::from_raw_parts(symbols, symbol_count) }
        };
        
        // Step 1: Convert FFI nodes to a RecExpr
        let expr = match nodes_to_recexpr(nodes_slice, symbols_slice) {
            Ok(e) => e,
            Err(_) => return false, // Error message already set
        };
        
        // Check for timeout
        if start_time.elapsed() > timeout_duration {
            set_error(&format!("Operation timed out after {}ms during nodes_to_recexpr", timeout), EggErrorCode::TimeoutError);
            return false;
        }
        
        // Step 2: Create an EGraph and add the expression
        let mut egraph = EGraph::<ExprNode, crate::analysis::NodeConstantFold>::default();
        let root_id = egraph.add_expr(&expr);
        
        // Check for timeout
        if start_time.elapsed() > timeout_duration {
            set_error(&format!("Operation timed out after {}ms during egraph creation", timeout), EggErrorCode::TimeoutError);
            return false;
        }
        
        // Step 3: Get full ruleset for comprehensive simplification
        let all_rules = rules::get_rules();
        
        // Calculate remaining time for rules
        let elapsed = start_time.elapsed();
        if elapsed > timeout_duration {
            set_error(&format!("Operation timed out after {}ms during rules preparation", timeout), EggErrorCode::TimeoutError);
            return false;
        }
        let remaining_time = timeout_duration - elapsed;
        
        // Step 4: Run the simplifier with all rules
        let runner = match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
            egg::Runner::default()
                .with_egraph(egraph)
                .with_iter_limit(iterations as usize)
                .with_time_limit(remaining_time)
                .with_node_limit(100000) // Limit to 100K nodes to prevent memory issues
                .run(&all_rules)
        })) {
            Ok(runner) => runner,
            Err(e) => {
                // If there's a panic, log it and return
                let panic_msg = if let Some(s) = e.downcast_ref::<String>() {
                    s.clone()
                } else if let Some(s) = e.downcast_ref::<&str>() {
                    s.to_string()
                } else {
                    "Unknown panic".to_string()
                };
                
                set_error(&format!("Panic in egg library: {}", panic_msg), EggErrorCode::UnknownError);
                return false;
            }
        };
        
        // Check for timeout
        if start_time.elapsed() > timeout_duration {
            set_error(&format!("Operation timed out after {}ms during rewrite rules", timeout), EggErrorCode::TimeoutError);
            return false;
        }
        
        // Step 5: Extract the best expression
        let cost_fn = ExprCostFn::default();
        let (_, best_expr) = egg::Extractor::new(&runner.egraph, cost_fn).find_best(root_id);
        
        // Check for timeout
        if start_time.elapsed() > timeout_duration {
            set_error(&format!("Operation timed out after {}ms during extraction", timeout), EggErrorCode::TimeoutError);
            return false;
        }
        
        // Step 6: Convert the result back to FFI nodes, keeping track of symbols
        let result_nodes = recexpr_to_nodes(&best_expr, symbols_slice);
        
        // Check for timeout
        if start_time.elapsed() > timeout_duration {
            set_error(&format!("Operation timed out after {}ms during conversion back to FFI", timeout), EggErrorCode::TimeoutError);
            return false;
        }
        
        // Step 7: Allocate memory for the result and copy the nodes
        let result_vec_len = result_nodes.len();
        let mut result_vec = Vec::with_capacity(result_vec_len);
        result_vec.extend_from_slice(&result_nodes);
        
        // Step 8: Return the result
        let result_raw_ptr = result_vec.as_mut_ptr();
        std::mem::forget(result_vec); // Don't drop the Vec, we're returning it as a raw pointer
        
        unsafe {
            *result_ptr = result_raw_ptr;
            *result_len = result_vec_len;
        }
        
        true
    }) {
        Ok(result) => result,
        Err(_) => {
            set_error("Panic occurred in egg_simplify_with_all_rules", EggErrorCode::UnknownError);
            false
        }
    }
}

/// Add the reflexive equality rule: a == a -> true (1)
/// 
/// This function updates the rule system to include a new rule for reflexive equality.
#[no_mangle]
pub extern "C" fn egg_add_reflexive_equality_rule() -> bool {
    // Add the new rule to the RuleCategory::Boolean category
    // This is a runtime addition and won't modify the code directly
    
    // For now, this function serves as a placeholder for future implementation
    // The proper way to add this rule is to update the rules.rs file
    true
}