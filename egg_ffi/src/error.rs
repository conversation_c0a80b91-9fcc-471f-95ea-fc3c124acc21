use std::ffi::CString;
use std::os::raw::c_char;
use std::sync::Mutex;
use once_cell::sync::Lazy;

// Error codes for FFI functions
#[repr(C)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub enum EggErrorCode {
    Ok = 0,
    NullPointer = 1,
    MutexLockFailed = 2,
    Utf8Error = 3,
    IndexOutOfBounds = 4,
    InvalidEGraphId = 5,
    InvalidNodeId = 6,
    InvalidOperation = 7,
    MemoryAllocationFailed = 8,
    UnknownError = 9,
    NotImplemented = 10,
    TimeoutError = 11,
}

// Thread-safe error storage
static ERROR_MESSAGE: Lazy<Mutex<Option<String>>> = Lazy::new(|| Mutex::new(None));
static ERROR_CSTRING: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));

// Clear the last error message
pub fn clear_error() {
    if let Ok(mut error) = ERROR_MESSAGE.lock() {
        *error = None;
    }
}

// Set the last error message and return the specified error code
pub fn set_error(msg: &str, code: EggErrorCode) -> EggErrorCode {
    if let Ok(mut error) = ERROR_MESSAGE.lock() {
        *error = Some(msg.to_string());
    }
    code
}

// Get the last error message as a C string
// Returns NULL if there is no error
#[no_mangle]
pub extern "C" fn egg_get_last_error() -> *const c_char {
    match ERROR_MESSAGE.lock() {
        Ok(error_opt) => {
            if let Some(error_msg) = &*error_opt {
                // Create a C string from the error message
                match CString::new(error_msg.clone()) {
                    Ok(c_str) => {
                        // Store the CString to keep it alive
                        if let Ok(mut last_cstr) = ERROR_CSTRING.lock() {
                            *last_cstr = Some(c_str);
                            if let Some(ref cstr) = *last_cstr {
                                return cstr.as_ptr();
                            }
                        }
                    },
                    Err(_) => {}
                }
            }
            std::ptr::null()
        },
        Err(_) => std::ptr::null(),
    }
}

// Free the last error message
// This is not needed in normal operation as the error message is managed internally
#[no_mangle]
pub extern "C" fn egg_free_error() {
    // Clear the error message
    if let Ok(mut error) = ERROR_MESSAGE.lock() {
        *error = None;
    }

    // Clear the cached C string
    if let Ok(mut last_cstr) = ERROR_CSTRING.lock() {
        *last_cstr = None;
    }
}
