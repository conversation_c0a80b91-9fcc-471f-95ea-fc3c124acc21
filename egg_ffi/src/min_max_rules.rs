use egg::{rewrite, Rewrite};
use crate::language::ExprNode;
use crate::analysis::NodeConstantFold;

/// Get min/max specific rules for the egg library
pub fn get_min_max_rules() -> Vec<Rewrite<ExprNode, NodeConstantFold>> {
    vec![
        // Min rules - prefixed with mmr- to avoid naming conflicts
        rewrite!("mmr-min-same"; "(min ?a ?a)" => "?a"),
        
        // Max rules - prefixed with mmr- to avoid naming conflicts
        rewrite!("mmr-max-same"; "(max ?a ?a)" => "?a"),
        
        // Simple min/max with integer constants - hardcode a few common cases
        rewrite!("mmr-min-with-max-int"; "(min ?a 2147483647)" => "?a"),
        rewrite!("mmr-max-with-min-int"; "(max ?a -2147483648)" => "?a"),
    ]
}

// Simplified rules for better performance and stability

#[cfg(test)]
mod tests {
    use super::*;
    use egg::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Extractor, Symbol};
    use crate::language::ExprNode;
    use crate::analysis::NodeConstantFold;
    use crate::cost::ExprCostFn;
    
    #[test]
    fn test_min_same() {
        // Create an expression: min(x, x)
        let mut expr = RecExpr::default();
        let id_x = expr.add(ExprNode::Symbol(Symbol::from("x")));
        let id_min = expr.add(ExprNode::Min([id_x, id_x]));
        
        // Create an e-graph and add the expression
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();
        let root_id = egraph.add_expr(&expr);
        
        // Apply the min-same rule with mmr- prefix
        let rules = vec![
            rewrite!("mmr-min-same"; "(min ?a ?a)" => "?a"),
        ];
        
        let runner = Runner::default()
            .with_egraph(egraph)
            .with_iter_limit(10)
            .run(&rules);
            
        // Extract the best expression
        let (_, best_expr) = Extractor::new(&runner.egraph, ExprCostFn::default()).find_best(root_id);
        
        // Check that the result is x
        assert_eq!(best_expr.as_ref().len(), 1);
        assert!(matches!(best_expr.as_ref()[0], ExprNode::Symbol(_)));
    }
}