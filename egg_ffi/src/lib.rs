pub mod language;
pub mod analysis;
pub mod rules;
pub mod cost;
pub mod error;
pub mod ffi;
pub mod ffi_extended;
pub mod min_max_rules;

// Re-export the FFI functions
pub use ffi::{egg_simplify, egg_free_result, FFINode, FFISymbol, INVALID_IDX};
pub use ffi_extended::{egg_simplify_with_all_rules, egg_add_reflexive_equality_rule};

// Version information
pub const EGG_FFI_VERSION: &str = "1.2.0";  // Updated version number