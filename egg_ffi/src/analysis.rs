use egg::{Analysis, DidMerge, Id};
use crate::language::ExprNode;

/// Analysis for constant folding
///
/// This analysis tracks constant values for nodes in the e-graph
/// and performs constant folding during rebuilding.
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct NodeConstantFold {
    pub constant: Option<i64>,
}

impl Analysis<ExprNode> for NodeConstantFold {
    type Data = NodeConstantFold;

    fn merge(&mut self, to: &mut Self::Data, from: Self::Data) -> DidMerge {
        match (to.constant, from.constant) {
            (Some(ca), Some(cb)) if ca == cb => DidMerge(false, false),
            (Some(_), Some(_)) => {
                to.constant = None;
                DidMerge(true, true)
            }
            (None, Some(c)) => {
                to.constant = Some(c);
                DidMerge(true, false)
            }
            (Some(_), None) | (None, None) => DidMerge(false, false),
        }
    }

    fn make(egraph: &mut egg::EGraph<ExprNode, Self>, enode: &ExprNode) -> Self::Data {
        let constant = match enode {
            // Leaf nodes
            ExprNode::Integer(n) => Some(*n),
            ExprNode::Symbol(_) => None,

            // Binary operations
            ExprNode::Add([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(a + b),
                    _ => None,
                }
            }
            ExprNode::Sub([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(a - b),
                    _ => None,
                }
            }
            ExprNode::Mul([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(0), _) | (_, Some(0)) => Some(0), // Special case for multiplication by zero
                    (Some(a), Some(b)) => Some(a * b),
                    _ => None,
                }
            }
            ExprNode::Div([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(_), Some(0)) => None, // Division by zero
                    (Some(a), Some(b)) => Some(a / b),
                    _ => None,
                }
            }
            ExprNode::Mod([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(_), Some(0)) => None, // Modulo by zero
                    (Some(a), Some(b)) => Some(a % b),
                    _ => None,
                }
            }
            ExprNode::Pow([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) if *b >= 0 => Some(a.pow(*b as u32)),
                    _ => None,
                }
            }
            ExprNode::Min([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(std::cmp::min(*a, *b)),
                    _ => None,
                }
            }
            ExprNode::Max([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(std::cmp::max(*a, *b)),
                    _ => None,
                }
            }
            ExprNode::Eq([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(if a == b { 1 } else { 0 }),
                    _ => None,
                }
            }
            ExprNode::Ne([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(if a != b { 1 } else { 0 }),
                    _ => None,
                }
            }
            ExprNode::Lt([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(if a < b { 1 } else { 0 }),
                    _ => None,
                }
            }
            ExprNode::Le([id1, id2]) => {
                let a = &egraph[*id1].data.constant;
                let b = &egraph[*id2].data.constant;
                match (a, b) {
                    (Some(a), Some(b)) => Some(if a <= b { 1 } else { 0 }),
                    _ => None,
                }
            }
        };

        NodeConstantFold { constant }
    }

    fn modify(egraph: &mut egg::EGraph<ExprNode, Self>, id: Id) {
        if let Some(c) = egraph[id].data.constant {
            // If this node has a constant value, add a constant node to the same e-class
            let const_id = egraph.add(ExprNode::Integer(c));
            egraph.union(id, const_id);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use egg::{EGraph, RecExpr};
    use crate::language::ExprNode;

    #[test]
    fn test_constant_folding_add() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 1 + 2
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(1));
        let id2 = expr.add(ExprNode::Integer(2));
        let _id_add = expr.add(ExprNode::Add([id1, id2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis
        egraph.rebuild();

        // Check that the constant was computed
        assert_eq!(egraph[root_id].data.constant, Some(3));
    }

    #[test]
    fn test_constant_folding_mul() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 3 * 4
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(3));
        let id2 = expr.add(ExprNode::Integer(4));
        let _id_mul = expr.add(ExprNode::Mul([id1, id2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis
        egraph.rebuild();

        // Check that the constant was computed
        assert_eq!(egraph[root_id].data.constant, Some(12));
    }

    #[test]
    fn test_constant_folding_mul_zero() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 0 * x
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(0));
        let id2 = expr.add(ExprNode::Symbol(egg::Symbol::from("x")));
        let _id_mul = expr.add(ExprNode::Mul([id1, id2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis
        egraph.rebuild();

        // Check that the constant was computed to 0 (special case for multiplication by zero)
        assert_eq!(egraph[root_id].data.constant, Some(0));
    }

    #[test]
    fn test_constant_folding_div() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 10 / 2
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(10));
        let id2 = expr.add(ExprNode::Integer(2));
        let _id_div = expr.add(ExprNode::Div([id1, id2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis
        egraph.rebuild();

        // Check that the constant was computed
        assert_eq!(egraph[root_id].data.constant, Some(5));
    }

    #[test]
    fn test_constant_folding_div_by_zero() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 10 / 0
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(10));
        let id2 = expr.add(ExprNode::Integer(0));
        let _id_div = expr.add(ExprNode::Div([id1, id2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis
        egraph.rebuild();

        // Check that the constant is None (division by zero)
        assert_eq!(egraph[root_id].data.constant, None);
    }

    #[test]
    fn test_constant_folding_complex_expr() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: (1 + 2) * (3 + 4)
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(1));
        let id2 = expr.add(ExprNode::Integer(2));
        let id_add1 = expr.add(ExprNode::Add([id1, id2]));

        let id3 = expr.add(ExprNode::Integer(3));
        let id4 = expr.add(ExprNode::Integer(4));
        let id_add2 = expr.add(ExprNode::Add([id3, id4]));

        let _id_mul = expr.add(ExprNode::Mul([id_add1, id_add2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis
        egraph.rebuild();

        // Check that the constant was computed: (1 + 2) * (3 + 4) = 3 * 7 = 21
        assert_eq!(egraph[root_id].data.constant, Some(21));
    }

    #[test]
    fn test_modify_adds_constant_node() {
        // Create an e-graph with the NodeConstantFold analysis
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 1 + 2
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(1));
        let id2 = expr.add(ExprNode::Integer(2));
        let _id_add = expr.add(ExprNode::Add([id1, id2]));

        // Add to e-graph
        let root_id = egraph.add_expr(&expr);

        // Run the analysis and modify
        egraph.rebuild();

        // Check that the constant node was added and is equivalent to the expression
        let const_class = egraph.find(root_id);
        assert!(egraph.classes().any(|class| {
            class.id == const_class && class.nodes.iter().any(|node| {
                matches!(node, ExprNode::Integer(3))
            })
        }));
    }

    // Tests for previously untested operations
    #[test]
    fn test_constant_folding_min() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: min(5, 3)
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(5));
        let id2 = expr.add(ExprNode::Integer(3));
        let _id_min = expr.add(ExprNode::Min([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that min(5, 3) = 3
        assert_eq!(egraph[root_id].data.constant, Some(3));
    }

    #[test]
    fn test_constant_folding_max() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: max(5, 3)
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(5));
        let id2 = expr.add(ExprNode::Integer(3));
        let _id_max = expr.add(ExprNode::Max([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that max(5, 3) = 5
        assert_eq!(egraph[root_id].data.constant, Some(5));
    }

    #[test]
    fn test_constant_folding_eq() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 5 == 5
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(5));
        let id2 = expr.add(ExprNode::Integer(5));
        let _id_eq = expr.add(ExprNode::Eq([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 5 == 5 evaluates to 1 (true)
        assert_eq!(egraph[root_id].data.constant, Some(1));

        // Create an expression: 5 == 3
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(5));
        let id2 = expr.add(ExprNode::Integer(3));
        let _id_eq = expr.add(ExprNode::Eq([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 5 == 3 evaluates to 0 (false)
        assert_eq!(egraph[root_id].data.constant, Some(0));
    }

    #[test]
    fn test_constant_folding_ne() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 5 != 3
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(5));
        let id2 = expr.add(ExprNode::Integer(3));
        let _id_ne = expr.add(ExprNode::Ne([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 5 != 3 evaluates to 1 (true)
        assert_eq!(egraph[root_id].data.constant, Some(1));
    }

    #[test]
    fn test_constant_folding_lt() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 3 < 5
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(3));
        let id2 = expr.add(ExprNode::Integer(5));
        let _id_lt = expr.add(ExprNode::Lt([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 3 < 5 evaluates to 1 (true)
        assert_eq!(egraph[root_id].data.constant, Some(1));
    }

    #[test]
    fn test_constant_folding_le() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 5 <= 5
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(5));
        let id2 = expr.add(ExprNode::Integer(5));
        let _id_le = expr.add(ExprNode::Le([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 5 <= 5 evaluates to 1 (true)
        assert_eq!(egraph[root_id].data.constant, Some(1));
    }

    #[test]
    fn test_constant_folding_mod() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 10 % 3
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(10));
        let id2 = expr.add(ExprNode::Integer(3));
        let _id_mod = expr.add(ExprNode::Mod([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 10 % 3 = 1
        assert_eq!(egraph[root_id].data.constant, Some(1));
    }

    #[test]
    fn test_constant_folding_pow() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // Create an expression: 2 ^ 3
        let mut expr = RecExpr::default();
        let id1 = expr.add(ExprNode::Integer(2));
        let id2 = expr.add(ExprNode::Integer(3));
        let _id_pow = expr.add(ExprNode::Pow([id1, id2]));

        let root_id = egraph.add_expr(&expr);
        egraph.rebuild();

        // Check that 2 ^ 3 = 8
        assert_eq!(egraph[root_id].data.constant, Some(8));
    }

    #[test]
    fn test_fold_min_max_eq_ne() {
        let mut egraph = EGraph::<ExprNode, NodeConstantFold>::default();

        // min
        let expr_min: RecExpr<ExprNode> = "(min 3 5)".parse().unwrap();
        let id_min = egraph.add_expr(&expr_min);
        egraph.rebuild();
        assert_eq!(egraph[id_min].data.constant, Some(3));

        // max
        let expr_max: RecExpr<ExprNode> = "(max 3 5)".parse().unwrap();
        let id_max = egraph.add_expr(&expr_max);
        egraph.rebuild();
        assert_eq!(egraph[id_max].data.constant, Some(5));

        // ==
        let expr_eq: RecExpr<ExprNode> = "(== 4 4)".parse().unwrap();
        let id_eq = egraph.add_expr(&expr_eq);
        egraph.rebuild();
        assert_eq!(egraph[id_eq].data.constant, Some(1));

        // !=
        let expr_ne: RecExpr<ExprNode> = "(!= 4 5)".parse().unwrap();
        let id_ne = egraph.add_expr(&expr_ne);
        egraph.rebuild();
        assert_eq!(egraph[id_ne].data.constant, Some(1));
    }
}
