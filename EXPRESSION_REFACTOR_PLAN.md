# Pure Expression Refactor Plan

## Overview

This plan outlines the complete refactor to replace <PERSON><PERSON>'s `Dim` union type with pure `*Expr` usage everywhere, following Luminal's elegant expression-first architecture. This will eliminate the dual concrete/symbolic system complexity while maintaining performance through expression interning and immediate evaluation.

## Architecture Goals

- Remove `Dim` union entirely
- Use `*Expr` directly throughout the codebase
- Follow Luminal's expression-first approach
- Maintain performance through expression caching and immediate evaluation
- Simplify shape operations by eliminating concrete/symbolic pattern matching

## Performance Analysis

Based on Luminal analysis and neural network computational patterns:
- Shape operations: ~0.1% of total compute time
- Matrix operations: ~99.9% of total compute time
- Expression overhead is negligible compared to model parameters
- Immediate evaluation for concrete values maintains efficiency

## 10-Phase Refactor Plan

### Phase 1: Core Type Updates (Day 1)

**File: `src/core/types.zig`**
- Remove `Dim` union entirely
- Update shape type: `[]const usize` → `[]const *Expr`
- Update all related type signatures

**File: `src/core/shape/types.zig`**
- Update `ShapeInfo` to use `[]const *Expr`
- Remove all `Dim`-related type definitions
- Update view descriptor types

### Phase 2: Shape Engine Updates (Days 2-3)

**File: `src/core/shape/engine.zig`**
- Update `infer()` to return `[]const *Expr`
- Remove concrete/symbolic pattern matching
- Update all shape validation functions
- Modify view transformation operations

**Key Changes:**
```zig
// Before
pub fn infer(op: Op, inputs: []const []const Dim) ![]const Dim

// After  
pub fn infer(op: Op, inputs: []const []const *Expr) ![]const *Expr
```

### Phase 3: Tensor Layer API Updates (Day 4)

**File: `src/tensor/types.zig`**
- Update `TensorInfo.shape` to `[]const *Expr`
- Update all tensor creation functions

**File: `src/tensor/tensor.zig`**
- Update `getTensorShape()` to return `[]const *Expr`
- Remove shape pattern matching
- Update tensor metadata handling

### Phase 4: Manipulation Operations (Day 5)

**File: `src/tensor/manipulation.zig`**
- Update `flatten()` to work with expressions
- Update `reshape()` to use expression validation
- Update `permute()` and `slice()` operations
- Remove all `switch (dim)` patterns

**Key Changes:**
```zig
// Remove pattern matching like:
switch (dim) {
    .concrete => |c| return c,
    .symbolic => |s| return symbolic_engine.eval(s),
}

// Replace with direct expression usage:
return dim; // Direct *Expr usage
```

### Phase 5: Reduction Operations (Day 6)

**File: `src/tensor/reduction.zig`**
- Update `mean()` to count dimensions using expressions
- Update `sum()` and aggregation operations
- Remove dimension type checking

**File: `src/tensor/linalg.zig`**
- Update `matmul()` compatibility checking
- Use expression-based dimension validation

### Phase 6: Utility Function Updates (Day 7)

**File: `src/tensor/utils.zig`**
- Update broadcasting logic to use expressions
- Update shape compatibility functions
- Remove concrete dimension utilities

**File: `src/core/shape/symbolic_utils.zig`**
- Expand expression utilities for shape operations
- Add expression-based validation helpers

### Phase 7: Remove Pattern Matching (Day 8)

**Systematic removal across all files:**
- Search for `switch (.*\.concrete.*\.symbolic)` patterns
- Replace with direct expression operations
- Update error handling to use expression validation

**Files to update:**
- All tensor operation files
- Shape transformation utilities
- View operation implementations

### Phase 8: Test Updates (Day 9)

**Update all test files:**
- `src/core/tests/test_shape.zig`
- `src/tensor/tests/`
- `src/core/shape/tests/`

**Changes:**
- Update test fixtures to use expressions
- Remove concrete/symbolic test branches
- Add expression-specific test cases

### Phase 9: Performance Optimizations (Day 10)

**Expression Caching:**
- Implement expression interning for common patterns
- Add immediate evaluation for concrete expressions
- Optimize arena allocation patterns

**Benchmark Verification:**
- Run performance tests on transformer models
- Verify shape operation overhead remains <0.1%
- Compare against pre-refactor benchmarks

### Phase 10: Cleanup and Documentation

**Final cleanup:**
- Remove obsolete error types related to `Dim`
- Update documentation and examples
- Clean up unused imports and utilities

## Success Metrics

1. **Functional:** All existing tests pass with expression-based shapes
2. **Performance:** Shape operations remain <0.1% of total compute time
3. **Code Quality:** Elimination of concrete/symbolic pattern matching
4. **API Consistency:** Uniform `*Expr` usage throughout codebase

## Risk Mitigation

1. **Incremental Testing:** Run tests after each phase
2. **Performance Monitoring:** Benchmark critical paths during refactor
3. **Rollback Strategy:** Maintain git branches for each phase
4. **Expression Validity:** Extensive testing of expression evaluation edge cases

## Implementation Notes

### Expression Usage Patterns

Following Luminal's approach:
```zig
// Direct expression operations
const total_elements = shape_expr.product();
const is_valid = shape_expr.all_positive();

// Immediate evaluation for concrete values
const concrete_size = expr.evaluate() orelse return error.SymbolicSize;
```

### Memory Management

- Continue using arena allocation for expressions
- Leverage expression interning for common patterns
- Maintain existing memory safety guarantees

## Execution Timeline

- **Total Duration:** 10 days
- **Critical Path:** Phases 1-3 (core infrastructure)
- **Testing:** Continuous throughout all phases
- **Integration:** Phase 9-10 (performance and cleanup)

## Dependencies

- Existing symbolic engine infrastructure (no changes needed)
- Arena allocator patterns (maintained)
- Expression simplification system (enhanced usage)
- FFI integration (not used in current codebase)

## Expected Benefits

1. **Simplified Architecture:** Elimination of dual type system
2. **Improved Maintainability:** Single expression-based path for all operations
3. **Enhanced Elegance:** Following proven Luminal patterns
4. **Preserved Performance:** Negligible overhead for shape operations
5. **Future Extensibility:** Easier to add new symbolic operations